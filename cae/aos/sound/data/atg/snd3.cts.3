@INIT.CTS
SET DEF TEST/WAIT
D HPB T
! 
SAY "********************************************************************"
SAY "* Ensure that Engine #1 Condition Lever is @ OFF                   *"
SAY "*             Engine #2 Condition Lever is @ MAX                   *" 
SAY "*                        -----------------                         *"
SAY "* Type CONT to continue with Condition 3:Right Engine @ 18% Torque *"
SAY "********************************************************************"
STOP
!
D TCFFLPOS F
D HEMODE [2 0
D TCMFSTRT T
TEST FOR 100 IT
D HATGON T
D HEMODE [2 2
D HENT(1) 0
D HENT(2) 18
D TCMFSTRT F
TEST FOR 100 IT
D TCFFLPOS F
!
!
SAY "*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<*"
SAY "* Condition 3   :  RIGHT ENGINE @ 18% TORQUE                       *"
SAY "* Configuration :                                                  *"
SAY "*                - Engine 1        ->  OFF                         *"
SAY "*                - Bleeds 1 & 2    ->  ON                          *"
SAY "*                - Recirc. fan     ->  ON                          *"
SAY "*                - FC fan          ->  ON                          *"
SAY "*                - Eyeball vents   ->  OPEN & parallel with surface*"
SAY "*                - APU             ->  OFF                         *"
SAY "*                - GPU Elc., Pnu.  ->  OFF                         *"
SAY "*                                                                  *"
SAY "* Type CONT to end continue                                        *"
SAY "*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<*"
!
STOP
!
@OFF.CTS
!
