@INIT.CTS
SET DEF TEST/WAIT
D HPB T
! 
SAY "********************************************************************"
SAY "* Ensure that Engine #1 Condition Lever is @ OFF                   *"
SAY "*             Engine #2 Condition Lever is @ MAX                   *" 
SAY "*                        -----------------                         *"
SAY "* Type CONT to continue with Condition 2:Right Engine Discing      *"
SAY "********************************************************************"
STOP
!
D TCFFLPOS F
STOP
D HEMODE [2 0
STOP
D TCMFSTRT T
STOP
TEST FOR 100 IT
STOP
!
D NASATG T
STOP
D HEMODE [2 2
STOP
D HENT(1) 0
STOP
D HENT(2) 8.25
STOP
D ECLAD(1) 0
STOP
D ECLAD(2) 1
STOP
D HATGON T
STOP
!
D TCMFSTRT F
STOP
TEST FOR 100 IT
STOP
D TCFFLPOS F
!
!
SAY "*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<*"
SAY "* Condition 2   :  RIGHT ENGINE DISCING                            *"
SAY "* Configuration :                                                  *"
SAY "*                - Engine 1        ->  OFF                         *"
SAY "*                - Bleeds 1 & 2    ->  ON                          *"
SAY "*                - Recirc. fan     ->  ON                          *"
SAY "*                - FC fan          ->  ON                          *"
SAY "*                - Eyeball vents   ->  OPEN & parallel with surface*"
SAY "*                - APU             ->  OFF                         *"
SAY "*                - GPU Elc., Pnu.  ->  OFF                         *"
SAY "*                                                                  *"
SAY "* Type CONT to end continue                                        *"
SAY "*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<*"
!
STOP
D HATGON F
D HEMODE [2 0
D HENT [2 0
D NASATG F
TEST FOR 10.
!
@OFF.CTS
!
