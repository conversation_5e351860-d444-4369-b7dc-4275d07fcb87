d tcftot t
!SET NOVERIFY
!
! TRIMSET COMMANDS
!
DEF BRAKES_OFF "D HPB F HBPEDL 0. HBPEDR 0. "
DEF JAX     "D VHS 5000 VH 5000 VFZG[6 0. VEE[6 0. "
DEF TRIM    "D VNINIT 10 VTRIM 0.0 "
DEF MONR    "MONITOR VFXGEAR,VFYGEAR,VFZGEAR,VH,VFZAERO,VUG,VLGEAR,-
             VMGEAR,VNGEAR,VEFN,VTHETADG,VPHIDG,HHDG,VNWS"
DEF RESET   "D VP 0 VR 0 VQ 0 VPD 0 VRD 0 VQD 0 VVG 0 VZDS 0 -
             VWG 0 HRUD 0 HPEDAL 0 HCOL 0 HELV -1 HWHEEL 0 - 
             HAIL 0 HSBLEV 0 VTHETA 0 VTHETADG 0 VPHI 0 - 
             VPHIDG 0 VBETA 0 VBETASET 0 VDRSET F VBANKSET F -
             VSTABSET F VENGSET F VWHLSET F VHMACH F -
             VHIAS F VUGD 0 VVGD 0 VWGD 0 HUACC 0 HVACC 0 -
             HWACC 0 HPACC 0 HQACC 0 HRACC 0 HPRATS F HQRATS F -
             HRRATS F HPRAT 0 HQRAT 0 HRRAT 0 VTNZL F"
DEF AY      "D VDUMMYL(19) T VDUMMYR(19) "
DEF DEG_RAD  0.********
DEF MACH    "D VMSET T VIASSET F VMS "
DEF IAS     "D VIASSET T VMSET F VIASS "
DEF EAS     "D VIASSET F VMSET F VIASS "
DEF RUD     "D VDRSET T HRUD "
DEF BETA    "D VDRSET F VBETASET "
DEF BANKA   "D VBANKSET T VPHI "
DEF CLIMB   "D VENGSET F VZDS "
DEF ALT     "D VHS "
DEF ALTVH   "D VH "
DEF FLAP    "D HQUICK T HFLEV "
DEF GEAR    "D HQUICK T HGLEV "
DEF SPEEDB  "D HQUICK T HSBLEV "
DEF GROSS   "D TAGW "
DEF INERTIA "D VFINERT "
DEF IXX     "D VIXX "
DEF IYY     "D VIYY "
DEF IZZ     "D VIZZ "
DEF IXZ     "D VIXZ "
DEF ENG1    "D VENGSET T HEMODE(1) 1 HECMD(1) "
DEF ENG2    "D VENGSET T HEMODE(2) 1 HECMD(2) "
DEF STKFREE "D HCETMODE 3 HCATMODE 3 HCRTMODE 3 HCEMODE 1 "
DEF STABSET "D VSTABSET "
DEF STABA   "D HSTAB "
DEF IHOLD   "D VHMACH F VHIAS "
DEF MHOLD   "D VHIAS F VHMACH "
DEF XCG     "D VCGSET T VXCG "
DEF ZCG     "D VCGSET T VZCG "
DEF TAIL    "D HNOTAILF T"
DEF BANKSET "D VBANKSET "
DEF TOTFUEL "D TAFUEL "
DEF UDOT    "D HUACC "
DEF VDOT    "D HVACC "
DEF WDOT    "D HWACC "
DEF PDOT    "D HPACC "
DEF QDOT    "D HQACC "
DEF RDOT    "D HRACC "
DEF PRATE   "D HPRATS T HPRAT " 
DEF QRATE   "D HQRATS T HQRAT "
DEF RRATE   "D HRRATS T HRRAT "
DEF RWYHDG  "D HRWYHDG "
DEF FUELM1  "D TAFUEL01 "
DEF FUELM2  "D TAFUEL02 "
DEF FUELM3  "D TAFUEL03 "
DEF FUELM4  "D TAFUEL04 "
DEF FUELC   "D TAFUEL11 "
DEF FUELR1  "D TAFUEL06 "
DEF FUELR2  "D TAFUEL07 "
DEF FUELT   "D TAFUEL10 "
DEF ELEV    "D HELV "
DEF TEMP    "D HTEMP "
DEF SLOSH   "D VFSL "
!
SET INPUT=10          ! Input window size
SET DEF TEST/WAIT/FREEZE=TCFFLPOS/ALL
SET DEF COLLECT/WAIT/FREEZE=TCFFLPOS/ALL
!
! VDUMMYR(10&11) REPLACE HTOTWIND AND HTOTWDIR UNTIL CTS IS FIXED
!
DEF COND    "INITIALLY HMAN VW VXCG VZCG VOCG VIXX VIYY VIZZ VIXZ VTEMP -
                  VDUMMYR(10) VDUMMYR(11) VM VVE VVTK HGSPD HROC VHH VH VELV-
                       VETRIM VAIL VRUD VCSPLI VCSPLO VCSPRI VCSPRO VNWS -
                       VFLAPS VDUC VEFN(1)-
                       VEFN(2) EQI(1) EQI(2) ENHI(1) ENHI(2) VTHETADG VPHIDG-
                       HHDG VALPHA VBETA HP HQ HR VDUMMYR(6) "   
DEF TT      "TEST/LIMIT=2000/FREEZE=TCFFLPOS UNTIL (VTRIM=1.0) 
!
DEF TV      "TEST/LIMIT=12000/FREEZE=TCFFLPOS WHEN (HSTART) UNTIL (TCFFLPOS) -
             COLLECT-
             FINALLY   HMAN VW VXCG VZCG VOCG VIXX VIYY VIZZ VIXZ VTEMP - 
                  VDUMMYR(10) VDUMMYR(11) VM VVE VVTK HGSPD HROC VHH VH VELV- 
                       VETRIM VAIL VRUD VCSPLI VCSPLO VCSPRI VCSPRO VNWS - 
                       VFLAPS VDUC VEFN(1)-
                       VEFN(2) EQI(1) EQI(2) ENHI(1) ENHI(2) VTHETADG VPHIDG-
                       HHDG VALPHA VBETA HP HQ HR VDUMMYR(6) "    
!
DEF PRINTER "TO IMAGEN"
DEF CLT "D TAICAO1 75 TAICAO1(2) 67 TAICAO1(3) 76 TAICAO1(4) 84 "
DEF L36 "D TARWY1 51 TARWY1(2) 54 TARWY1(3) 76 TARWY1(4) 32 "
