!sp0c0d
!set noverify
@sndstrt.cts
d tcftot f
d hmessage 1
@ctsinit2.cts
clt
d tcrtot t
d tcrmaint t
!
! TEST RUNWAY
!
! Set-up definitions.
!
D VSITE T
D HGLEV  1
!D IDSLAPDR T
!D IDSLAPDL T
D TCFFUEL F
D TCFFLPOS F
D TCFTOT F
l36
D XZPOSN 130
D TCFPOS T
TEST UNTIL (TCFFLPOS)
D HTGU1 .4
D TCMCGLIM T
D TCMCRINB T
D TAFUEL 1000
D TCRENVI T
!
! CONDITION NUMBER TEST1
!
D HRESET T
TEST FOR 2 IT
D HINIT T
TEST FOR 2 IT
D HTRESET T
TEST FOR 2 IT
D TCFPOS T
D RTSETELV T
!
D VTRIM 0        ! CALL TRIM MODULE AND FREEZE INTEGRATIONS
D VNINIT 10      ! INITIALIZE TRIM
D TAFUEL 1000    ! SET FUEL WEIGHT
D TCMCGLIM T     ! ALLOW ANY X CG TO BE VALID
D VCGSET T       ! FREEZE Z CG
D VINERTIA T     ! FREEZE INERTIAS
D VENGSET F      ! SET ENGINES
D VIASS 0        ! SET IAS TO 0
!
!! IF VMSET=F AND VIASSET = F THEN TRIM TO EQUIVALENT AIRSPEED
D VMSET   F      ! TRIM TO MACH
D VIASSET F      ! TRIM TO CALIBRATED AIRSPEED
!
! REPRESENTATIVE INERTIAS ONLY
!
D VIXX 107922.
D VIYY 168795.
D VIZZ 250029.
D VIXZ 22802.
!
d hbygain t
d vbankset f
D TCFFUEL T
@frig
d h0start f
d hstart f
d tcrmaint f
d hcenohys t
d hcenofri t
d hcsmode  0
d VDUMMYR(1) 0.
D VDUMMYR(2) 0.
