*                 
*                 
*  USAir Dash 8
*                                   
*  DMC-BASED INTERFACE ADDRESS FILE 
*                 
*
*
***************************************
****  Chassis: L1A1      DMC: 01   ****
***************************************
*
CHASSIS=L1A1
BACKPLANE=SUPER_C-BUS
*
01/01          DO000       DOR-32          ON
01/02          DO002       DOR-32          ON
01/03          DO004       DOR-32          ON
01/04          DO006       DOR-32          ON
01/05          DO008       DOR-32          ON
01/06          DO010       DOR-32          ON
01/07          DO012       DOR-32          ON
01/08          DO014       DOR-32          ON
01/09          DO016       DOR-32          ON
01/10          DO018       DOR-32          ON
01/11          DO020       DOR-32          ON
01/12          DO022       DOR-32          ON
01/13          DO024       DOR-32          ON
01/14          DO026       DOR-32          ON
01/15          DO028       DOR-32          ON
01/16          DO030       DOR-32          ON
01/17          DO032       DOR-32          ON
01/18          DO034       DOR-32          ON
01/19          DO036       DOR-32          ON
01/20          DO038       DOR-32          ON
*
*
***************************************
****  Chassis: L1A2      DMC: 02   ****
***************************************
*
CHASSIS=L1A2
BACKPLANE=SUPER_C-BUS
*
02/01          DO048       DOR-32          ON
02/02          DO050       DOR-32          ON
02/03          DO052       DOR-32          ON
02/04          DO054       DOR-32          ON
02/05          DO056       DOR-32          ON
02/06          DO058       DOR-32          ON
02/07          DO060       DOR-32          ON
02/08          DO062       DOR-32          ON
02/09          DO064       DOR-32          ON
02/10          DO066       DOR-32          ON
02/11          DO068       DOR-32          ON
02/12          DO070       DOR-32          ON
02/13          DO072       DOR-32          ON
02/14          DO074       DOR-32          ON
02/15          DO076       DOR-32          ON
02/16          DO078       DOR-32          ON
02/17          DO080       DOR-32          ON
02/18          DO082       DOR-32          ON
02/19          DO084       DOR-32          ON
02/20          DO086       DOR-32          ON
*
*
***************************************
****  Chassis: L1A3      DMC: 03   ****
***************************************
*
CHASSIS=L1A3
BACKPLANE=SUPER_C-BUS
*
*
03/01          DI000       DI-64     (GGGG)       ON
03/02          DI004       DI-64     (GGGG)       ON
03/03          DI008       DI-64     (GGGG)       ON
03/04          DI012       DI-64     (GGGG)       ON
03/05          DI016       DI-64     (GGGG)       ON
03/06          DI020       DI-64     (GGGG)       ON
03/07          DI024       DI-64     (VGGG)       ON
03/08          DI028       DI-64     (GGVG)       ON
03/09          DI032       DI-64     (GGGG)       ON
03/10          DI036       DI-64     (GGGV)       ON
03/11          DI040       DI-64     (GGGG)       ON
03/12          DI044       DI-64     (GGGV)       ON
03/13          DI048       DI-64     (GGGG)       ON
03/14          DI052       DI-64     (GGGG)       ON
03/15          DI056       DI-64     (GGGG)       ON
03/16          DI060       DI-64     (GGGG)       ON
03/17          DI064       DI-64     (GGGG)       ON
03/18          DI068       DI-64     (GVGG)       ON
*
*
*
***************************************
****  Chassis: L1A4      DMC: 04   ****
***************************************
*
CHASSIS=L1A4
BACKPLANE=SUPER_C-BUS
*
*    ARINC chassis #1
*
04/01          CB192       DIO-CB          ON
04/02          CB194       DIO-CB          ON
04/03          CB196       DIO-CB          ON
04/04          CB198       DIO-CB          ON
04/05          CB200       DIO-CB          ON
04/06          CB202       DIO-CB          ON
04/07          CB204       DIO-CB          ON
04/08          CB206       DIO-CB          ON
04/09          CB208       DIO-CB          ON
04/10          CB210       DIO-CB          ON
04/11          CB212       DIO-CB          ON
04/12          CB214       DIO-CB          ON
04/13          CB216       DIO-CB          ON
04/14          CB218       DIO-CB          ON
04/15          CB220       DIO-CB          ON
04/16          CB222       DIO-CB          ON
04/17          CB224       DIO-CB          ON
04/18          CB226       DIO-CB          ON
04/19          CB228       DIO-CB          ON
*
*
***************************************
****  Chassis: L2A1      DMC: 05   ****
***************************************
*
CHASSIS=L2A1
BACKPLANE=SUPER_C-BUS
*
05/01          CO008       SOP/ACI         ON
05/02          CO012       SOP/ACI         ON
05/03          CO016       SOP/ACI         ON
05/04          CO020       SOP/ACI         ON
05/05          CO024       SOP/ACI         ON
05/06          SO010       SOP/ACI         ON
05/07          SO012       SOP/ACI         ON
05/08          SO014       SOP/ACI         ON
05/09          SO016       SOP/ACI         ON
05/10          SO018       SOP/ACI         ON
05/11          SO020       SOP/ACI         ON
05/12          CO004       SOP/ACI         ON
05/13          SO024       SOP/ACI         ON
05/14          SO026       SOP/ACI         ON
05/15          SO028       SOP/ACI         ON
05/16          SO030       SOP/ACI         ON
05/17          SO032       SOP/ACI         ON
05/18          SO034       SOP/ACI         ON
05/19          SO036       SOP/ACI         ON
05/20          SO038       SOP/ACI         ON
05/21          SO040       SOP/ACI         ON
05/22          SO042       SOP/ACI         ON
05/23          SO044       SOP/ACI         ON
*05/24          SO046       SOP/ACI         ON
*
*
***************************************
****  Chassis: L2A2      DMC: 06   ****
***************************************
*
CHASSIS=L2A2
BACKPLANE=SUPER_C-BUS
*
06/01          SO048       SOP/ACI         ON
06/02          CO032       SOP/ACI         ON
*
06/04          CI000       SOP/ACI         ON
06/05          CI004       SOP/ACI         ON
06/06          CI008       SOP/ACI         ON
06/07          CI012       SOP/ACI         ON
06/08          CI016       SOP/ACI         ON
06/09          CI020       SOP/ACI         ON
06/10          CI024       SOP/ACI         ON
06/11          CI028       SOP/ACI         ON
06/12          CI032       SOP/ACI         ON
06/13          CI036       SOP/ACI         ON
*
06/15          CO000       SOP/ACI         ON
*
* =============================
* Weather Radar Board ARINC 453
* =============================
*
SLOT=23
06/70000/0:255     MOC000    XXX   OFF
*
SLOT=23
06/70200/0:15      MIC000    XXX   OFF
*
***************************************
****  Chassis: L2A3      DMC: 07   ****
***************************************
*
CHASSIS=L2A3
BACKPLANE=SUPER_C-BUS
*
*
07/01          AO0000      AOP-32          ON
07/02          AO0032      AOP-32          ON
07/02/0:1      TO032       AOP-32          ON     
07/02/2:3      TO034       AOP-32          ON     
07/03          AO0064      AOP-32          ON
07/03/16:17    TO080       AOP-32          ON     
07/03/18:19    TO082       AOP-32          ON     
07/03/20:21    TO084       AOP-32          ON     
07/03/24:25    TO088       AOP-32          ON     
07/04          AO0096      AOP-32          ON
07/04/2:3      TO098       AOP-32          ON     
07/04/4:5      TO100       AOP-32          ON     
07/04/6:7      TO102       AOP-32          ON     
07/04/20:21    TO116       AOP-32          ON     
07/04/22:23    TO118       AOP-32          ON     
07/05          AO0128      AOP-32          ON     
07/05/0:1      TO128       AOP-32          ON     
07/06          AO0160      AOP-32          ON
07/06/2:3      TO162       AOP-32          ON     
07/06/4:5      TO164       AOP-32          ON     
07/06/6:7      TO166       AOP-32          ON     
07/07          AO0192      AOP-32          ON
07/08          AO0224      AOP-32          ON
*
07/12          AI0000      AIRO-16         ON
07/13          AI0016      AIRO-16         ON
07/14          AI0032      AIRO-16         ON
07/15          AI0048      AIRO-16         ON
07/16          AI0064      AIRO-16         ON
07/17          AI0080      AIRO-16         ON
*
*
***************************************
****  Chassis: FC601A1   DMC: 0D   ****
****  DASIU  Chassis               ****
***************************************
*
CHASSIS=FC601A1
*
* ================================
* Audio Logic Scanner (ALS) Boards
* ================================
*
0D/21/0:10   XO600     XXX          OFF    ! Capt. AUDIO LOGIC SCANNER
0D/21/11:60  XI600     XXX          OFF    ! Capt. AUDIO LOGIC SCANNER
*
0D/22/0:10   XO650     XXX          OFF    ! F/O   AUDIO LOGIC SCANNER
0D/22/11:60  XI650     XXX          OFF    ! F/O   AUDIO LOGIC SCANNER
*
0D/23/0:10   XO700     XXX          OFF    ! OBS   AUDIO LOGIC SCANNER
0D/23/11:60  XI700     XXX          OFF    ! OBS   AUDIO LOGIC SCANNER
*
*0D/24/0:10   XO750     XXX          OFF    ! INSTR AUDIO LOGIC SCANNER
*0D/24/11:60  XI750     XXX          OFF    ! INSTR AUDIO LOGIC SCANNER
*
*
***************************************
****  Chassis: FC601A2   DMC: 0A   ****
****  ASCB + ALS BOARDS            ****
***************************************
*
*CHASSIS=FC601A2
*
*
BACKPLANE=C-BUS
*
SLOT=05/4
*
0A/0800/0:15  XO200    XXX          OFF    ! PSWG (Nh, Nl, Np)
0A/4800/0:15  XO216    XXX          OFF    ! 
0A/8800/0:15  XO232    XXX          OFF    ! 
0A/C800/0:15  XO248    XXX          OFF    ! 
*
*
BACKPLANE=SUPER_C-BUS
*
0A/06/3:3      XI100     XXX          OFF    ! ALS (digital alt preselect)
0A/06/4:4      XO100     XXX          OFF    ! ALS (digital alt preselect)
0A/06/5:5      XI116     XXX          OFF    ! ALS (digital alt preselect)
0A/06/6:6      XO116     XXX          OFF    ! ALS (digital alt preselect)
*
*=========================================
*
*
***************************************
****  Chassis: FC601A2   DMC: 0B   ****
****  MISCELLANEOUS DRIVER BOARDS  ****
***************************************
*
CHASSIS=FC601A2
BACKPLANE=SUPER_C-BUS
*
0B/01/0:15   XI000     XXX          OFF    ! King Radio #1
0B/02/0:15   XO270     XXX          OFF    ! ALS (Fuel flow)
0B/03/0:15   XO300     XXX          OFF    ! AC variable freq phase a,b
0B/04/0:15   XO316     XXX          OFF    ! AC variable freq phase c
0B/05/0:15   XO332     XXX          OFF    ! inverter volt primary/auxil/sec
*
***************************************
****  Chassis: FC601A2   DMC: 10   ****
****  ARINC 429 + 561 BOARDS       ****
***************************************
*
*CHASSIS=FC601A2
*
10/05/0:15   XI016     XXX          OFF    ! King Radio #2
*
*
***************************************
****  Chassis: F2A1      DMC: 0E   ****
***************************************
*
CHASSIS=F2A1                                  !SOUND CABINET
BACKPLANE=C-BUS
*
*************************************************************************
*                                                                       *
*                                                                       *
*                                                                       *
*                         DMC-BASED INTERFACE                           *
*                                                                       *
*                   SOUND  ADDRESS  FILE  SECTION                       *
*                                                                       *
*                              VER : 1.0BV    (VAX)                     *
*                                    BASED ON 2.2AV                     *
*                                                                       *
*       NOTE :  IF SOMEBODY NEEDS TO MODIFY THE SOUND ADDRESS FILE      *
*               PLEASE CALL THE SOUND GROUP              THANK-YOU      *
*                                                                       *
*                                                                       *
*************************************************************************
*
*
CHASSIS=F2A1                                  !SOUND CABINET
*
*
* 1 = DMC NUMBER      2 = START ADDRESS      3 = CHANNEL DEFINITION
* 4 = OFFSET ADDRESS  5 = START ASSIGNATION  6 = CARD TYPE
* 7 = DIAGNOSTIC      8 = CONFIGURATION      9 = COMMENTS
*
*
* 1    2   3  4         5         6       7     8                 9
* V    V   V  V         V         V       V     V                 V
*0E/A7154/0:6/4       MO98AA     XXX     OFF   CON ='0014      !EXAMPLE
*
*
******* SLOT # 20 = TONE-1
SLOT=20                                                        !FREQUENCY
0E/A7FFC/0:1         MO9F00     XXX     OFF    CON ='0014'     !RATE # 1 & 2
SLOT=20                                                        !
0E/A7000/0:21        MO9800     XXX     OFF                    !gr#1  + inter-modul
SLOT=20                                                        !
0E/A702C/0:21        MO9816     XXX     OFF                    !gr#2  + inter-modul
SLOT=20                                                        !
0E/A7058/0:21        MO982C     XXX     OFF                    !gr#3  + inter-modul
SLOT=20                                                        !
0E/A7084/0:21        MO9842     XXX     OFF                    !gr#4  + inter-modul
SLOT=20                                                        !
0E/A70B0/0:71        MO9858     XXX     OFF                    !modulation
*
SLOT=20                                                        !AMPLITUDE
0E/A7154/0:6/4       MO98AA     XXX     OFF                    !gr#1
SLOT=20                                                        !
0E/A7180/0:6/4       MO98B4     XXX     OFF                    !gr#2
SLOT=20                                                        !
0E/A71AC/0:6/4       MO98BE     XXX     OFF                    !gr#3
SLOT=20                                                        !
0E/A71D8/0:6/4       MO98C8     XXX     OFF                    !gr#4
*
SLOT=20                                                        !AMPLITUDE INTER.
0E/A7176/0:2/4       MO98B1     XXX     OFF                    !gr#1
SLOT=20                                                        !
0E/A71A2/0:2/4       MO98BB     XXX     OFF                    !gr#2
SLOT=20                                                        !
0E/A71CE/0:2/4       MO98C5     XXX     OFF                    !gr#3
SLOT=20                                                        !
0E/A71FA/0:2/4       MO98CF     XXX     OFF                    !gr#4
*
SLOT=20                                                        !AMPLITUDE MODU.
0E/A7204             MO98D2     XXX     OFF                    !#45
SLOT=20                                                        !
0E/A7208/0:2         MO98D3     XXX     OFF                    !#45-46-47
SLOT=20                                                        !
0E/A7210/0:2         MO98D6     XXX     OFF                    !#47-48-49
SLOT=20                                                        !
0E/A7218/0:2         MO98D9     XXX     OFF                    !#49-50-51
SLOT=20                                                        !
0E/A7220/0:2         MO98DC     XXX     OFF                    !#51-52-53
SLOT=20                                                        !
0E/A7228/0:2         MO98DF     XXX     OFF                    !#53-54-55
SLOT=20                                                        !
0E/A7230/0:2         MO98E2     XXX     OFF                    !#55-56-57
SLOT=20                                                        !
0E/A7238/0:2         MO98E5     XXX     OFF                    !#57-58-59
SLOT=20                                                        !
0E/A7240/0:2         MO98E8     XXX     OFF                    !#59-60-61
SLOT=20                                                        !
0E/A7248/0:2         MO98EB     XXX     OFF                    !#61-62-63
SLOT=20                                                        !
0E/A7250/0:2         MO98EE     XXX     OFF                    !#63-64-65
SLOT=20                                                        !
0E/A7258/0:2         MO98F1     XXX     OFF                    !#65-66-67
SLOT=20                                                        !
0E/A7260/0:2         MO98F4     XXX     OFF                    !#67-68-69
SLOT=20                                                        !
0E/A7268/0:2         MO98F7     XXX     OFF                    !#69-70-71
SLOT=20                                                        !
0E/A7270/0:2         MO98FA     XXX     OFF                    !#71-72-73
SLOT=20                                                        !
0E/A7278/0:2         MO98FD     XXX     OFF                    !#73-74-75
SLOT=20                                                        !
0E/A7280/0:2         MO9900     XXX     OFF                    !#75-76-77
SLOT=20                                                        !
0E/A7288/0:2         MO9903     XXX     OFF                    !#77-78-79
SLOT=20                                                        !
0E/A7290/0:1         MO9906     XXX     OFF                    !#79-80
*
SLOT=20                                                        !CONTROL OUTPUT
0E/A729E/0:8         MO990D     XXX     OFF                    !shctou ADR.
SLOT=20                                                        !
0E/A72C0/0:7         MO991E     XXX     OFF                    !shctou POI.
SLOT=20                                                        !
0E/A72E0/0:8         MO992E     XXX     OFF                    !shphou ADR.
SLOT=20                                                        !
0E/A7302/0:15        MO993F     XXX     OFF                    !shphou PhL , PhH
SLOT=20                                                        !
0E/A7342             MO995F     XXX     OFF                    !Control register
*
SLOT=20                                                        !CONTROL INPUT
0E/A734E/0:7         MI9800     XXX     OFF                    !shctin pointer
SLOT=20                                                        !
0E/A736E/0:15        MI9810     XXX     OFF                    !Ph L, Ph H
SLOT=20                                                        !
0E/A73AE             MI9830     XXX     OFF                    !Status Register
SLOT=20                                                        !
0E/A7E16             MI9836     XXX     OFF                    !Counter register
*
*
*
******* SLOT # 19 = SLAP-1
SLOT=19                                                        !FREQUENCY
0E/A5000/0:3         MO9000     XXX     OFF    CON ='0014'     !bsg #1 (source #1-2)
SLOT=19
0E/A5008/0:3         MO9004     XXX     OFF                    !bsg #2 (source #3-4)
*
SLOT=19
0E/A5010/0:3         MO9008     XXX     OFF                    !fm #1 (source #5-6)
SLOT=19
0E/A5018/0:3         MO900C     XXX     OFF                    !fm #2 (source #7-8)
*
SLOT=19
0E/A5020/0:1         MO9010     XXX     OFF                    !fm variable #1 (source #9)
SLOT=19
0E/A5024/0:1         MO9012     XXX     OFF                    !fm variable #2 (source #10)
*
SLOT=19                                                        !AMPLITUDE
0E/A5028/0:1/4       MO9014     XXX     OFF                    !bsg #1 (source #1-2)
SLOT=19
0E/A5030/0:1/4       MO9016     XXX     OFF                    !bsg #2 (source #3-4)
*
SLOT=19
0E/A5038             MO9018     XXX     OFF                    !fm ampl. (source #5)
SLOT=19
0E/A503C/0:2         MO9019     XXX     OFF                    !fm ampl. (source #6-7)
SLOT=19
0E/A5044/0:1         MO901C     XXX     OFF                    !fm ampl. (source #8)
*
SLOT=19
0E/A5048/0:1/4       MO901E     XXX     OFF                    !variation fm (source #9-10)
*
SLOT=19
0E/A5050             MO9020     XXX     OFF                    !Multiplicator
*                                                              !NOISE
SLOT=19
0E/A5052/0:1         MO9021     XXX     OFF                    !cut-off frequency
*
SLOT=19
0E/A5056/0:1         MO9023     XXX     OFF                    !output amplitude
*
SLOT=19
0E/A505A/0:1         MO9025     XXX     OFF                    !damping factor
*
SLOT=19
0E/A505E/0:1         MO9027     XXX     OFF                    !input amplitude
*
SLOT=19
0E/A5062/0:1         MO9029     XXX     OFF                    !noise select
*
SLOT=19
0E/A5066/0:8         MO902B     XXX     OFF                    !SHCTOU ADR
SLOT=19
0E/A5088/0:7         MO903C     XXX     OFF                    !SHOUPO POI
SLOT=19
0E/A50A8/0:8         MO904C     XXX     OFF                    !SHPHOU ADR
SLOT=19
0E/A50CA/0:15        MO905D     XXX     OFF                    !SHOUPH PHL
SLOT=19
0E/A510A             MO907D     XXX     OFF                    !CONTROL REGISTER
*
SLOT=19
0E/A5116/0:7         MI9000     XXX     OFF                    !SHCTIN POI
SLOT=19
0E/A5136/0:15        MI9010     XXX     OFF                    !SHPHIN PHL,PHH
SLOT=19
0E/A5176             MI9030     XXX     OFF                    !STATUS REGISTER
SLOT=19
0E/A5E16             MI9036     XXX     OFF                    !COUNTER REGISTER
*
*
******* SLOT # 17 = IMPACT-1
SLOT=17                                                        !IMPACT
0E/A1000/0:1         MO8000     XXX     OFF    CON ='0015'     !control register 1 - 2
SLOT=17                                                        !
0E/A1004/0:1         MO8002     XXX     OFF                    !pointer register 1 - 2
SLOT=17                                                        !
0E/A100C/0:31        MO8004     XXX     OFF                    !32 parameters # 1
SLOT=17                                                        !
0E/A104C/0:31        MO8024     XXX     OFF                    !32 parameters # 2
SLOT=17                                                        !
0E/A108C/0:1         MO8044     XXX     OFF                    !amplitude 1 -2
SLOT=17                                                        !
0E/A1008/0:1         MI8000     XXX     OFF                    !status 1-2 register
SLOT=17                                                        !
0E/A1E16             MI8002     XXX     OFF                    !counter register
*
*
******* SLOT # 16 = NOISE-1
SLOT=16                                                        !NOISE
0E/9F000/0:14        MO7800     XXX     OFF    CON ='0015'     !frequency cut-off
SLOT=16                                                        !
0E/9F020/0:14        MO7810     XXX     OFF                    !amplitude
SLOT=16                                                        !
0E/9F040/0:14        MO7820     XXX     OFF                    !damping
SLOT=16                                                        !
0E/9F060/0:14        MO7830     XXX     OFF                    !Input amplitude
SLOT=16                                                        !
0E/9F080/0:14        MO7840     XXX     OFF                    !noise select
SLOT=16                                                        !
0E/9F0A0/0:8         MO7850     XXX     OFF                    !impulse noise
SLOT=16                                                        !
0E/9F0B2             MO7859     XXX     OFF                    !Control Register
SLOT=16                                                        !
0E/9F0B4             MI7800     XXX     OFF                    !Status Register
SLOT=16                                                        !
0E/9FE16             MI7801     XXX     OFF                    !counter register
*
*
******* SLOT # 14 = MIXER-1
SLOT=14                                                        !MIXER
0E/9B000/0:3         MO6800     XXX     OFF    CON ='0015'     !high ch # 1, gr 1-4
SLOT=14                                                        !
0E/9B008/0:1         MO68B0     XXX     OFF                    !high ch # 1, slap
SLOT=14                                                        !
0E/9B00C/0:7         MO6804     XXX     OFF                    !high ch # 1, impact, mod, gr 1-4 ch #2
SLOT=14                                                        !
0E/9B01C/0:1         MO68B2     XXX     OFF                    !high ch # 2, slap
SLOT=14                                                        !
0E/9B020/0:7         MO680C     XXX     OFF                    !high ch # 2, impact, mod, gr 1-4 ch #3
SLOT=14                                                        !
0E/9B030/0:1         MO68B4     XXX     OFF                    !high ch # 3, slap
SLOT=14                                                        !
0E/9B034/0:3         MO6814     XXX     OFF                    !high ch # 3, impact, mod ch # 3
SLOT=14                                                        !
0E/9B050/0:95        MO6820     XXX     OFF                    !low ch # 1-2-3
SLOT=14                                                        !
0E/9B150/0:2         MO68A0     XXX     OFF                    !volume
SLOT=14                                                        !
0E/9B170             MO68A4     XXX     OFF                    !Control Register
SLOT=14                                                        !
0E/9B180             MI6800     XXX     OFF                    !Status Register
SLOT=14                                                        !
0E/9BE16             MI6801     XXX     OFF                    !counter Register
*
*
******* SLOT # 13 = MIXER-2
SLOT=13                                                        !MIXER
0E/99000/0:3         MO6000     XXX     OFF    CON ='0015'     !high ch # 1, gr 1-4
SLOT=13                                                        !
0E/99008/0:1         MO60B0     XXX     OFF                    !high ch # 1, slap
SLOT=13                                                        !
0E/9900C/0:7         MO6004     XXX     OFF                    !high ch # 1, impact, mod, gr 1-4 ch #2
SLOT=13                                                        !
0E/9901C/0:1         MO60B2     XXX     OFF                    !high ch # 2, slap
SLOT=13                                                        !
0E/99020/0:7         MO600C     XXX     OFF                    !high ch # 2, impact, mod, gr 1-4 ch #3
SLOT=13                                                        !
0E/99030/0:1         MO60B4     XXX     OFF                    !high ch # 3, slap
SLOT=13                                                        !
0E/99034/0:3         MO6014     XXX     OFF                    !high ch # 3, impact, mod ch # 3
SLOT=13                                                        !
0E/99050/0:95        MO6020     XXX     OFF                    !low ch # 1-2-3
SLOT=13                                                        !
0E/99150/0:2         MO60A0     XXX     OFF                    !volume
SLOT=13                                                        !
0E/99170             MO60A4     XXX     OFF                    !Control Register
SLOT=13                                                        !
0E/99180             MI6000     XXX     OFF                    !Status Register
SLOT=13                                                        !
0E/99E16             MI6001     XXX     OFF                    !counter Register
*
*
******* SLOT # 12 = MIXER-3
SLOT=12                                                        !MIXER
0E/97000/0:3         MO5800     XXX     OFF    CON ='0015'     !high ch # 1, gr 1-4
SLOT=12                                                        !
0E/97008/0:1         MO58B0     XXX     OFF                    !high ch # 1, slap
SLOT=12                                                        !
0E/9700C/0:7         MO5804     XXX     OFF                    !high ch # 1, impact, mod, gr 1-4 ch #2
SLOT=12                                                        !
0E/9701C/0:1         MO58B2     XXX     OFF                    !high ch # 2, slap
SLOT=12                                                        !
0E/97020/0:7         MO580C     XXX     OFF                    !high ch # 2, impact, mod, gr 1-4 ch #3
SLOT=12                                                        !
0E/97030/0:1         MO58B4     XXX     OFF                    !high ch # 3, slap
SLOT=12                                                        !
0E/97034/0:3         MO5814     XXX     OFF                    !high ch # 3, impact, mod ch # 3
SLOT=12                                                        !
0E/97050/0:95        MO5820     XXX     OFF                    !low ch # 1-2-3
SLOT=12                                                        !
0E/97150/0:2         MO58A0     XXX     OFF                    !volume
SLOT=12                                                        !
0E/97170             MO58A4     XXX     OFF                    !Control Register
SLOT=12                                                        !
0E/97180             MI5800     XXX     OFF                    !Status Register
SLOT=12                                                        !
0E/97E16             MI5801     XXX     OFF                    !counter Register
*
*
******* SLOT # 03 = DAC-1
SLOT=03                                                        !D/A
0E/0424              XO986      XXX     OFF    CON ='0016'     !channel select
SLOT=03                                                        !
0E/0404              XO987      XXX     OFF                    !watch dog tim 1
SLOT=03                                                        !
0E/040C              XO988      XXX     OFF                    !watch dog tim 2
*
*
*
*
/SOUND_AUX_DATA
*
*
//DATA=0:7CE4                               !Chassis interrupt timer
//DATA=1:1E00                               !status display
//DATA=2:0000                               !Not used
//DATA=3:0000                               !Not used
*
*
//FUNCTION=0                                !DAC RESET
*
  0001                                      !NUMBER OF SLOT
*
  0015                                      !NUMBER OF TRANSFERT
*                Offset
  0006                                      !PIT 1 CONTROL
  0006                                      !PIT 1 CONTROL
  0006                                      !PIT 1 CONTROL
  0000                                      !COUNTER 0    [Rate]
  0000                                      !COUNTER 0
  0002                                      !COUNTER 1    [monostable]
  0002                                      !COUNTER 1
  0004                                      !COUNTER 2    [watch dog rate]
  0004                                      !COUNTER 2
  0016                                      !PIT 2 CONTROL
  0016                                      !PIT 2 CONTROL
  0016                                      !PIT 2 CONTROL
  0010                                      !COUNTER 0    [cut-off]
  0010                                      !COUNTER 0
  0012                                      !COUNTER 1    [monostable]
  0012                                      !COUNTER 1
  0014                                      !COUNTER 2    [Dummy]
  0014                                      !COUNTER 2
  0024                                      !CHANNEL SELECT  [Reset watch dog]
  0024                                      !CHANNEL SELECT  [Enable watch dog]
  0022                                      !DAC DATA REGISTER
*                Segment
  2040                                      !SLOT XA03  DAC
*                Data
  0034 0072 00B8                            !PIT 1         (8Bits)
  0085 0000                                 !COUNTER 0     2*(8Bits) LSB  MSB
  0010 0000                                 !COUNTER 1     2*(8Bits) LSB  MSB
  00FF 00FF                                 !COUNTER 2     2*(8Bits) LSB  MSB
  0034 0072 00B2                            !PIT 2         (8Bits)
  0007 0000                                 !COUNTER 0     2*(8Bits) LSB  MSB
  0004 0000                                 !COUNTER 1     2*(8Bits) LSB  MSB
  FFFF FFFF                                 !COUNTER 2     2*(8Bits) LSB  MSB
  000F 002F                                 !CHANNEL SELECT     (16Bits)
  FFFF                                      !DAC DATA REGISTER  (16Bits)
*
*
//FUNCTION=1                                !DSG/DSP RESET
*
  0007                                      !NUMBER OF SLOT
*
  0006                                      !NUMBER OF TRANSFERT
*                Offset
  1D80                                      !BTCW
  1F80                                      !TASK NUMBER
  1F82                                      !NSS
  1F84                                      !NBLKS
  1F86                                      !TMFF
  1D90                                      !SPCW
*                Segment
  A600                                      !SLOT XA20   DSG TONE
  A400                                      !SLOT XA19   DSG SLAP
  A000                                      !SLOT XA17   DSP IMPACT
  9E00                                      !SLOT XA16   DSP NOISE
  9A00                                      !SLOT XA14   DSP MIX-1
  9800                                      !SLOT XA13   DSP MIX-2
  9600                                      !SLOT XA12   DSP MIX-3
*                Data
*
  A000 0000 000B 0027 8000 0001             !DSG TONE    (12*40=480)
  A000 0004 000E 001F 8000 0001             !DSG SLAP    (15*32=480)
  A000 0001 000E 001F 8000 0001             !DSP IMPACT  (15*32=480)
  A000 0002 000E 001F 8000 0001             !DSP NOISE
  A000 0003 000B 0027 8000 0001             !DSP MIX-1
  A000 0003 000B 0027 8000 0001             !DSP MIX-2
  A000 0003 000B 0027 8000 0001             !DSP MIX-3
*
*
//FUNCTION=2                                !DSG/DSP RUN
*
  0007                                      !NUMBER OF SLOT
*
  0002                                      !NUMBER OF TRANSFERT
*                Offset
  1C80                                      !CMDBUF     WRITE  <---
  1CC0                                      !RSPBUF     READ   <---
*                Segment
  A600                                      !SLOT XA20   DSG TONE
  A400                                      !SLOT XA19   DSG SLAP
  A000                                      !SLOT XA17   DSP IMPACT
  9E00                                      !SLOT XA16   DSP NOISE
  9A00                                      !SLOT XA14   DSP MIX-1
  9800                                      !SLOT XA13   DSP MIX-2
  9600                                      !SLOT XA12   DSP MIX-3
*                Data
  000E 0000                                 !DSG TONE
  000E 0000                                 !DSG SLAP
  000E 0000                                 !DSP IMPACT
  000E 0000                                 !DSP NOISE
  000E 0000                                 !DSP MIX-1
  000E 0000                                 !DSP MIX-2
  000E 0000                                 !DSP MIX-3
*
*
//FUNCTION=3                                !DSG/DSP REFRESH
*
  0007                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  1D80                                      !BTCW
*                Segment
  A600                                      !SLOT XA20   DSG TONE
  A400                                      !SLOT XA19   DSG SLAP
  A000                                      !SLOT XA17   DSP IMPACT
  9E00                                      !SLOT XA16   DSP NOISE
  9A00                                      !SLOT XA14   DSP MIX-1
  9800                                      !SLOT XA13   DSP MIX-2
  9600                                      !SLOT XA12   DSP MIX-3
*                Data
  A000                                      !DSG TONE
  A000                                      !DSG SLAP
  A000                                      !DSP IMPACT
  A000                                      !DSP NOISE
  A000                                      !DSP MIX-1
  A000                                      !DSP MIX-2
  A000                                      !DSP MIX-3
*
*
//FUNCTION=4                                !DAC RESET/ENABLE  WATCH-DOG
*
  0001                                      !NUMBER OF SLOT
*
  0004                                      !NUMBER OF TRANSFERT
*                Offset
  0004                                      !PIT 1 COUNTER 2
  0004                                      !PIT 1 COUNTER 2
  0024                                      !CHANNEL SELECT
  0024                                      !CHANNEL SELECT
*                Segment
  2040                                      !SLOT XA03  DAC CONVERT
*                Data
  00FF                                      !TIMER 1 VALUE
  00FF                                      !TIMER 2 VALUE
  000F                                      !RESET WATCH-DOG
  002F                                      !ENABLE WATCH-DOG
*
*
//FUNCTION=5                                !DAC DISABLE  WATCH-DOG
*
  0001                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  0024                                      !CHANNEL SELECT
*                Segment
  2040                                      !SLOT XA03  DAC CONVERT
*                Data
  000F                                      !RESET WATCH-DOG ONLY (NOT ENABLE)
*
*
//FUNCTION=6                                !DAC KILL OUTPUT
*
  0001                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  0024                                      !CHANNEL SELECT
*                Segment
  2040                                      !SLOT XA03  DAC CONVERT
*                Data
  003F                                      !SET WATCH-DOG BY S/W WAY
*
//FUNCTION=7                                !RUN TEST
*
  0007                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  1E2A                                      !RUNTEST REGISTER
*                Segment
  A600                                      !SLOT XA20   DSG TONE
  A400                                      !SLOT XA19   DSG SLAP
  A000                                      !SLOT XA17   DSP IMPACT
  9E00                                      !SLOT XA16   DSP NOISE
  9A00                                      !SLOT XA14   DSP MIX-1
  9800                                      !SLOT XA13   DSP MIX-2
  9600                                      !SLOT XA12   DSP MIX-3
*                Data
  7000                                      !TONE
  7000                                      !SLAP
  7000                                      !IMPACT
  7000                                      !NOISE
  700F                                      !MIX-1
  700F                                      !MIX-2
  700F                                      !MIX-3
*
//FUNCTION=8                                !STOP TEST
*
  0007                                      !NUMBER OF SLOT
*
  0001                                      !NUMBER OF TRANSFERT
*                Offset
  1E2A                                      !RUNTEST REGISTER
*                Segment
  A600                                      !SLOT XA20   DSG TONE
  A400                                      !SLOT XA19   DSG SLAP
  A000                                      !SLOT XA17   DSP IMPACT
  9E00                                      !SLOT XA16   DSP NOISE
  9A00                                      !SLOT XA14   DSP MIX-1
  9800                                      !SLOT XA13   DSP MIX-2
  9600                                      !SLOT XA12   DSP MIX-3
*                Data
  0000                                      !TONE
  0000                                      !SLAP
  0000                                      !IMPACT
  0000                                      !NOISE
  0000                                      !MIX-1
  0000                                      !MIX-2
  0000                                      !MIX-3
*
*//FUNCTION=9                                !Not used
*//FUNCTION=10                               !Not used
*//FUNCTION=11                               !Not used
*
*END OF SOUND_AUX_DATA
*
