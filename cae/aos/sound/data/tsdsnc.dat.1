*
*                     SLOT CARD BY CHASSIS  Vs MNEMONICS
*                     -----------------------------------
*
SLOT_START
*
OPTION = XILINX
*
TITLE = SOUND CABINET F2A1
*
DMC = 0E
*
*        1         2         3         4         5         6
*23456789012345678901234567890123456789012345678901234567890
*
*          TOP(A)        TASK           BOT(B)      OPTION
*     -------------+--------------+--------------+-----------
XA01 =
XA02 =
XA03 =                 *CONVER    /    MIXER
XA04 =
XA05 =
XA06 =
XA07 =
XA08 =
XA09 =
XA10 =
XA11 =
XA12 =    SOURCE   /    MIX-3     /    MIXER     : OB
XA13 =    SOURCE   /    MIX-2     /    MIXER     : OB
XA14 =    SOURCE   /    MIX-1     /    MIXER     : OB,MB
XA15 =
XA16 =    SOURCE   /    NOISE-1                  : OA
XA17 =    SOURCE   /    IMPACT-1                 : OA
XA18 =
XA19 =    SOURCE   /    SLAP-1                   : OA
XA20 =    SOURCE   /    TONE-1                   : OA,MA
XA21 =
XA22 =
XA23 =
XA24 =
XA25 =
XA26 =
XA27 =
*
*
SLOT_END
*
*
*                         INPUT-OUTPUT/XREFERENCE
*                         -----------------------
*
IO_START
*
*        1         2         3         4         5         6
*23456789012345678901234567890123456789012345678901234567890
*
*     TASK            OUTPUT               INPUT         DESCRIPTION
*-------------+-------------------+-------------------+----------
     TONE-1        4/MIX-1,                                GROUP  1 - 4
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     SLAP-1        2/MIX-1,                                BSG+FM 1 - 2
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     IMPACT-1      2/MIX-1,                                IMPACT 1 - 2
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     TONE-1        2/MIX-1,                                MODUL  1 - 2
                    /MIX-2,
                    /MIX-3
                   4/MIX-1,                                GROUP  1 - 4
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     SLAP-1        2/MIX-1,                                BSG+FM 1 - 2
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     IMPACT-1      2/MIX-1,                                IMPACT 1 - 2
                    /MIX-2,
                    /MIX-3
*-------------+-------------------+-------------------+----------
     TONE-1        2/MIX-1,                                MODUL  1 - 2
                    /MIX-2,
                    /MIX-3
                  16/MIX-1,                                MODUL  3 - 18
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     NOISE-1      16/MIX-1,                                NOISE  1 - 16
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     TONE-1        1/MIX-1,                                TSD CLOCK
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     SLAP-1        1/MIX-1,                                TSD CLOCK
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     IMPACT-1      1/MIX-1,                                TSD CLOCK
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     NOISE-1       1/MIX-1,                                TSD CLOCK
                    /MIX-2,
                    /MIX-3
*-------------+--------------------+--------------------+---------------
     CONVER                            3/MIX-1             CHANNEL 1 - 3
                                       3/MIX-2             CHANNEL 4 - 6
                                       3/MIX-3             CHANNEL 7 - 9
                                      *1/MIX-1,            TSD CLOCK
                                        /MIX-2,
                                        /MIX-3
*-------------+--------------------+--------------------+---------------
       MIX-1                           4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                       4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                      16/TONE-1            MODUL   3 - 18
                                      16/NOISE-1           NOISE   1 - 16
                                       1/TONE-1,           TSD CLOCK
                                        /SLAP-1,
                                        /IMPACT-1,
                                        /NOISE-1
                  3/CONVER                                 CHANNEL 1 - 3
                  *1/CONVER                                TSD CLOCK
*   ----------+--------------------+--------------------+---------------
       MIX-2                           4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                       4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                      16/TONE-1            MODUL   3 - 18
                                      16/NOISE-1           NOISE   1 - 16
                                       1/TONE-1,           TSD CLOCK
                                        /SLAP-1,
                                        /IMPACT-1,
                                        /NOISE-1
                  3/CONVER                                 CHANNEL 1 - 3
                  *1/CONVER                                TSD CLOCK
*   ----------+--------------------+--------------------+---------------
       MIX-3                           4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                       4/TONE-1            GROUP   1 - 4
                                       2/SLAP-1            BSG+FM  1 - 2
                                       2/IMPACT-1          IMPACT  1 - 2
                                       2/TONE-1            MODUL   1 - 2
                                      16/TONE-1            MODUL   3 - 18
                                      16/NOISE-1           NOISE   1 - 16
                                       1/TONE-1,           TSD CLOCK
                                        /SLAP-1,
                                        /IMPACT-1,
                                        /NOISE-1
                  3/CONVER                                 CHANNEL 1 - 3
                  *1/CONVER                                TSD CLOCK
*   ----------+--------------------+--------------------+---------------
*
IO_END
