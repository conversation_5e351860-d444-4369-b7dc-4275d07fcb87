********************************************************************************
*                   FILE : WAVGEN_10B.DAT
********************************************************************************
*
*
*
TITLE = SOUND CABINET F2-A1
*
DMC = 0E
*
SLOT = 19
*
DECLA_START
*
*
      ADDRESS
*
      FREQ = 9000
      AMPL = 9404
      PHASE = B000
      CTRL = A000
*
*
      MACRO BSG_GEN (TBL1,TBL2,SUM,WRT,LAST)A1,A2,F1,F2,F3,F4,P1,P2
*
      CW1 = [ACC*SUM] + ACC_AM                             /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST]       /A2,F3,P2
      CW4 = TBL2                                           /0,F4,P2
*
      ENDM BSG_GEN
*
*
      MACRO FM_GEN (TBL1,TBL2,SUM,WRT,LAST)A1,A2,A3,F1,F2,F3,F4,P1,P2
*
      CW1 = ENA + [ACC*SUM] + ACC_AM                       /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ENB + ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST] /A2,F3,P2
      CW4 = TBL2                                           /A3,F4,P2
*
      ENDM FM_GEN
*
*
      MACRO FM_INIT (TBL1,SUM,WRT,LAST)A1,F1,F2,P1
*
      CW1 = [ACC*SUM] + [WEN*WRT] + [END*LAST]             /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
*
      ENDM FM_INIT
*
*
DECLA_END
*
*
CODE_START
*
*-------- BSG SECTION ---------
*
 1      BSG_GEN (SOURCE01,SOURCE02,,=,)
                 NABGAM01,NABGAM02,NABGFF01,NABGFQ01,
                 NABGFF02,NABGFQ02,BGPHAS01,BGPHAS02
*
 2      BSG_GEN (SOURCE03,SOURCE04,,=,)
                 NABGAM03,NABGAM04,NABGFF03,NABGFQ03,
                 NABGFF04,NABGFQ04,BGPHAS03,BGPHAS04
*
*-------- FM SECTION ---------
*
 3      FM_GEN (SOURCE05,SOURCE06,,=,)NAFMAM01,NAFMAM03,NAFXAM01,
                NAFMFF01,NAFMFQ01,NAFMFF03,NAFMFQ03,FMPHAS01,FMPHAS03
*
 4      FM_GEN (SOURCE07,SOURCE08,,=,)NAFMAM02,NAFMAM04,NAFXAM02,
                NAFMFF02,NAFMFQ02,NAFMFF04,NAFMFQ04,FMPHAS02,FMPHAS04
*
*-------- PREVIOUS FM SECTION ---------
*
 5      FM_INIT (SOURCE09,,=,)
                 NAFMAM05,NAFMFF05,NAFMFQ05,FMPHAS05
*
 6      FM_INIT (SOURCE10,,=,#)
                 NAFMAM06,NAFMFF06,NAFMFQ06,FMPHAS06
*
CODE_END
*
*
********************************************************************************
*
*
SLOT = 20
*
DECLA_START
*
*
      ADDRESS
*
      FREQ = 9000
      AMPL = 9404
      PHASE = B000
      CTRL = A000
*
*
      MACRO REPETIV1 (TBL1,TBL2,TBL3,TBL4,SUM,WRT,LAST)A1,A2,A3,A4,
                      F1,F2,F3,F4,F5,F6,F7,F8,P1,P2,P3,P4
*
      CW1 = [ACC*SUM] + ACC_AM                             /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ACC + ACC_AM                                   /A2,F3,P2
      CW4 = TBL2                                           /0,F4,P2
      CW5 = ACC + ACC_AM                                   /A3,F5,P3
      CW6 = TBL3                                           /0,F6,P3
      CW7 = ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST]       /A4,F7,P4
      CW8 = TBL4                                           /0,F8,P4
*
      ENDM REPETIV1
*
*
      MACRO REPETIV2 (TBL1,TBL2,TBL3,SUM,WRT,LAST)A1,A2,A3,F1,F2,F3,F4,F5,F6,
                      P1,P2,P3
*
      CW1 = [ACC*SUM] + ACC_AM                             /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ACC + ACC_AM                                   /A2,F3,P2
      CW4 = TBL2                                           /0,F4,P2
      CW5 = ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST]       /A3,F5,P3
      CW6 = TBL3                                           /0,F6,P3
*
      ENDM REPETIV2
*
*
      MACRO INTERMOD (TBL1,TBL2,TBL3,TBL4,SUM,WRT,LAST)A1,A2,A3,F1,F2,F3,F4,
                      F5,F6,F7,F8,P1,P2,P3,P4
*
      CW1 = ENA + [ACC*SUM] + ACC_AM                       /0,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ENB + ACC + ACC_AM                             /0,F3,P2
      CW4 = TBL2                                           /A1,F4,P2
      CW5 = ENA + ACC + ACC_AM                             /0,F5,P3
      CW6 = TBL3                                           /A2,F6,P3
      CW7 = ENB + ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST] /0,F7,P4
      CW8 = TBL4                                           /A3,F8,P4
*
      ENDM INTERMOD
*
*
      MACRO MODULA (TBL1,TBL2,SUM,WRT,LAST)A1,A2,A3,F1,F2,F3,F4,P1,P2
*
      CW1 = ENA + [ACC*SUM] + ACC_AM                       /A1,F1,P1
      CW2 = TBL1                                           /0,F2,P1
      CW3 = ENB + ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST] /A2,F3,P2
      CW4 = TBL2                                           /A3,F4,P2
*
      ENDM MODULA
*
*
DECLA_END
*
*
CODE_START
*
*---- HIGH SECTION + HIGH & LOW SECTION  ---------------------------------------
*
*-------- GROUP # 1 ---------
*
 1      REPETIV1 (SOURCE01,SOURCE02,SOURCE03,SOURCE04,,,)
                  NARPAM01,NARPAM02,NARPAM03,NARPAM04,NARPFQ01,NARPFF01,
                  NARPFQ02,NARPFF02,NARPFQ03,NARPFF03,NARPFQ04,NARPFF04,
                  RPPHAS01,RPPHAS02,RPPHAS03,RPPHAS04
*
 2      REPETIV2 (SOURCE05,SOURCE06,SOURCE07,+,,)
                  NARPAM05,NARPAM06,NARPAM07,NARPFQ05,NARPFF05,NARPFQ06,
                  NARPFF06,NARPFQ07,NARPFF07,RPPHAS05,RPPHAS06,RPPHAS07
*
 3      INTERMOD (SOURCE08,SOURCE09,SOURCE10,SOURCE11,+,=,)
                  NAIMAM01,NAIMAM02,NAIMAM03,NAIMFQ01,NAIMFF01,NAIMFQ02,
                  NAIMFF02,NAIMFQ03,NAIMFF03,NAIMFQ04,NAIMFF04,
                  IMPHAS01,IMPHAS02,IMPHAS03,IMPHAS04
*
*-------- GROUP # 2 ---------
*
 4      REPETIV1 (SOURCE12,SOURCE13,SOURCE14,SOURCE15,,,)
                  NARPAM08,NARPAM09,NARPAM10,NARPAM11,NARPFQ08,NARPFF08,
                  NARPFQ09,NARPFF09,NARPFQ10,NARPFF10,NARPFQ11,NARPFF11,
                  RPPHAS08,RPPHAS09,RPPHAS10,RPPHAS11
*
 5      REPETIV2 (SOURCE16,SOURCE17,SOURCE18,+,,)
                  NARPAM12,NARPAM13,NARPAM14,NARPFQ12,NARPFF12,NAPFQ013,
                  NARPFF13,NARPFQ14,NARPFF14,RPPHAS12,RPPHAS13,RPPHAS14
*
 6      INTERMOD (SOURCE19,SOURCE20,SOURCE21,SOURCE22,+,=,)
                  NAIMAM04,NAIMAM05,NAIMAM06,NAIMFQ05,NAIMFF05,NAIMFQ06,
                  NAIMFF06,NAIMFQ07,NAIMFF07,NAIMFQ08,NAIMFF08,
                  IMPHAS05,IMPHAS06,IMPHAS07,IMPHAS08
*
*-------- GROUP # 3 ---------
*
 7      REPETIV1 (SOURCE23,SOURCE24,SOURCE25,SOURCE26,,,)
                  NARPAM15,NARPAM16,NARPAM17,NARPAM18,NARPFQ15,NARPFF15,
                  NARPFQ16,NARPFF16,NARPFQ17,NARPFF17,NARPFQ18,NARPFF18,
                  RPPHAS15,RPPHAS16,RPPHAS17,RPPHAS18
*
 8      REPETIV2 (SOURCE27,SOURCE28,SOURCE29,+,,)
                  NARPAM19,NARPAM20,NARPAM21,NARPFQ19,NARPFF19,NARPFQ20,
                  NARPFF20,NARPFQ21,NARPFF21,RPPHAS19,RPPHAS20,RPPHAS21
*
 9      INTERMOD (SOURCE30,SOURCE31,SOURCE32,SOURCE33,+,=,)
                  NAIMAM07,NAIMAM08,NAIMAM09,NAIMFQ09,NAIMFF09,NAIMFQ10,
                  NAIMFF10,NAIMFQ11,NAIMFF11,NAIMFQ12,NAIMFF12,
                  IMPHAS08,IMPHAS09,IMPHAS10,IMPHAS11
*
*-------- GROUP # 4 ---------
*
 10     REPETIV1 (SOURCE34,SOURCE35,SOURCE36,SOURCE37,,,)
                  NARPAM22,NARPAM23,NARPAM24,NARPAM25,NARPFQ22,NARPFF22,
                  NARPFQ23,NARPFF23,NARPFQ24,NARPFF24,NARPFQ25,NARPFF25,
                  RPPHAS22,RPPHAS23,RPPHAS24,RPPHAS25
*
 11     REPETIV2 (SOURCE38,SOURCE39,SOURCE40,+,,)
                  NARPAM26,NARPAM27,NARPAM28,NARPFQ26,NARPFF26,NARPFQ27,
                  NARPFF27,NARPFQ28,NARPFF28,RPPHAS26,RPPHAS27,RPPHAS28
*
 12     INTERMOD (SOURCE41,SOURCE42,SOURCE43,SOURCE44,+,=,)
                  NAIMAM10,NAIMAM11,NAIMAM12,NAIMFQ13,NAIMFF13,NAIMFQ14,
                  NAIMFF14,NAIMFQ15,NAIMFF15,NAIMFQ16,NAIMFF16,
                  IMPHAS11,IMPHAS12,IMPHAS13,IMPHAS14
*
*
*-------- MODULATION SECTION ----------
*
*
 13     MODULA (SOURCE45,SOURCE46,,=,)NAMDAM01,NAMDAM02,NAMXAM01,
                NAMDFQ01,NAMDFF01,NAMDFQ02,NAMDFF02,MDPHAS01,MDPHAS02,
*
 14     MODULA (SOURCE47,SOURCE48,,=,#)NAMDAM03,NAMDAM04,NAMXAM02,
                NAMDFQ03,NAMDFF03,NAMDFQ04,NAMDFF04,MDPHAS03,MDPHAS04,
*
 15     MODULA (SOURCE49,SOURCE50,,=,)NAMDAM05,NAMDAM06,NAMXAM03,
                NAMDFQ05,NAMDFF05,NAMDFQ06,NAMDFF06,MDPHAS05,MDPHAS06,
*
 16     MODULA (SOURCE51,SOURCE52,,=,)NAMDAM07,NAMDAM08,NAMXAM04,
                NAMDFQ07,NAMDFF07,NAMDFQ08,NAMDFF08,MDPHAS07,MDPHAS08,
*
 17     MODULA (SOURCE53,SOURCE54,,=,)NAMDAM09,NAMDAM10,NAMXAM05,
                NAMDFQ09,NAMDFF09,NAMDFQ10,NAMDFF10,MDPHAS09,MDPHAS10,
*
 18     MODULA (SOURCE55,SOURCE56,,=,)NAMDAM11,NAMDAM12,NAMXAM06,
                NAMDFQ11,NAMDFF11,NAMDFQ12,NAMDFF12,MDPHAS11,MDPHAS12,
*
 19     MODULA (SOURCE57,SOURCE58,,=,)NAMDAM13,NAMDAM14,NAMXAM07,
                NAMDFQ13,NAMDFF13,NAMDFQ14,NAMDFF14,MDPHAS13,MDPHAS14,
*
 20     MODULA (SOURCE59,SOURCE60,,=,)NAMDAM15,NAMDAM16,NAMXAM08,
                NAMDFQ15,NAMDFF15,NAMDFQ16,NAMDFF16,MDPHAS15,MDPHAS16,
*
 21     MODULA (SOURCE61,SOURCE62,,=,)NAMDAM17,NAMDAM18,NAMXAM09,
                NAMDFQ17,NAMDFF17,NAMDFQ18,NAMDFF18,MDPHAS17,MDPHAS18,
*
 22     MODULA (SOURCE63,SOURCE64,,=,)NAMDAM19,NAMDAM20,NAMXAM10,
                NAMDFQ19,NAMDFF19,NAMDFQ20,NAMDFF20,MDPHAS19,MDPHAS20,
*
 23     MODULA (SOURCE65,SOURCE66,,=,)NAMDAM21,NAMDAM22,NAMXAM11,
                NAMDFQ21,NAMDFF21,NAMDFQ22,NAMDFF22,MDPHAS21,MDPHAS22,
*
 24     MODULA (SOURCE67,SOURCE68,,=,)NAMDAM23,NAMDAM24,NAMXAM12,
                NAMDFQ23,NAMDFF23,NAMDFQ24,NAMDFF24,MDPHAS23,MDPHAS24,
*
 25     MODULA (SOURCE69,SOURCE70,,=,)NAMDAM25,NAMDAM26,NAMXAM13,
                NAMDFQ25,NAMDFF25,NAMDFQ26,NAMDFF26,MDPHAS25,MDPHAS26,
*
 26     MODULA (SOURCE71,SOURCE72,,=,)NAMDAM27,NAMDAM28,NAMXAM14,
                NAMDFQ27,NAMDFF27,NAMDFQ28,NAMDFF28,MDPHAS27,MDPHAS28,
*
 27     MODULA (SOURCE73,SOURCE74,,=,)NAMDAM29,NAMDAM30,NAMXAM15,
                NAMDFQ29,NAMDFF29,NAMDFQ30,NAMDFF30,MDPHAS29,MDPHAS30,
*
 28     MODULA (SOURCE75,SOURCE76,,=,)NAMDAM31,NAMDAM32,NAMXAM16,
                NAMDFQ31,NAMDFF31,NAMDFQ32,NAMDFF32,MDPHAS31,MDPHAS32,
*
 29     MODULA (SOURCE77,SOURCE78,,=,)NAMDAM33,NAMDAM34,NAMXAM17,
                NAMDFQ33,NAMDFF33,NAMDFQ34,NAMDFF34,MDPHAS33,MDPHAS34,
*
 30     MODULA (SOURCE79,SOURCE80,,=,#)NAMDAM35,NAMDAM36,NAMXAM18,
                NAMDFQ35,NAMDFF35,NAMDFQ36,NAMDFF36,MDPHAS35,MDPHAS36,
*
CODE_END
*
