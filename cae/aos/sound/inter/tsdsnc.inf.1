                                                                                
                                                                                
                             **********************                             
                             **  TSDGEN UTILITY  **                             
                             **    INFORMATION   **                             
                             **       FILE       **                             
                             **********************                             
                                                                                
                                                                                
                                                                                
 *******************************************************************************
 CHASSIS #  1      SOUND CABINET F2A1                                           
 *******************************************************************************
                                                                                
                             TSD BUS A = SOURCE                                 
                                                                                
    TASK               TSDBUS   INPUT OUTPUT    SEQUENCE                SLOT  M 
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |TONE-1   |  4/O|     |     |  2/O|  4/O|     |     |  2/O| 16/O|     |XA20 |Y|
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |SLAP-1   |     |  2/O|     |     |     |  2/O|     |     |     |     |XA19 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |IMPACT-1 |     |     |  2/O|     |     |     |  2/O|     |     |     |XA17 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |NOISE-1  |     |     |     |     |     |     |     |     |     | 16/O|XA16 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-1    |  4/I|  2/I|  2/I|  2/I|  4/I|  2/I|  2/I|  2/I| 16/I| 16/I|XA14 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-2    |  4/I|  2/I|  2/I|  2/I|  4/I|  2/I|  2/I|  2/I| 16/I| 16/I|XA13 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-3    |  4/I|  2/I|  2/I|  2/I|  4/I|  2/I|  2/I|  2/I| 16/I| 16/I|XA12 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
                                                                                
                                                                                
                                 < CONTINUED >                                  
                                                                                
    TASK               TSDBUS   INPUT OUTPUT    SEQUENCE                SLOT  M 
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |TONE-1   |  1/O|     |     |     |     |     |     |     |     |     |XA20 |Y|
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |SLAP-1   |  1/O|     |     |     |     |     |     |     |     |     |XA19 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |IMPACT-1 |  1/O|     |     |     |     |     |     |     |     |     |XA17 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |NOISE-1  |  1/O|     |     |     |     |     |     |     |     |     |XA16 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-1    |  1/I|     |     |     |     |     |     |     |     |     |XA14 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-2    |  1/I|     |     |     |     |     |     |     |     |     |XA13 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-3    |  1/I|     |     |     |     |     |     |     |     |     |XA12 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
                                                                                
                                                                                
                                                                                
                             TSD BUS B = MIXER                                  
                                                                                
    TASK               TSDBUS   INPUT OUTPUT    SEQUENCE                SLOT  M 
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |<CONVER >|  3/I|  3/I|  3/I|* 1/I|     |     |     |     |     |     |XA03 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-1    |  3/O|     |     |* 1/O|     |     |     |     |     |     |XA14 |Y|
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-2    |     |  3/O|     |* 1/O|     |     |     |     |     |     |XA13 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
 |MIX-3    |     |     |  3/O|* 1/O|     |     |     |     |     |     |XA12 | |
 +---------+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-----+-+
                                                                                
                                                                                
