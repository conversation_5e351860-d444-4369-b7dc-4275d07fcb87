********************************************************************************
* FILE : WAVEGEN.DAT
********************************************************************************
*
*
TITLE = DAS CHASSIS
*
DMC = 0C
*
*
********************************************************************************
*         DDDD    SSS    GGGG        K  K  EEEEE  Y   Y  EEEEE  RRRR
*         D   D  S      G            K K   E       Y Y   E      R   R
*         D   D   SSS   G GGG        KK    EEE      Y    EEE    RRRR
*         D   D      S  G   G        K K   E        Y    E      R  R
*         DDDD    SSS    GGG         K  K  EEEEE    Y    EEEEE  R   R
********************************************************************************
*
SLOT = 14
*
DECLA_START
*
*
      ADDRESS
*
      FREQ  = 9000
      AMPL  = 9404
      PHASE = B000
      CTRL  = A000
*
*
      MACRO ADF_GEN (TBL1,TBL2,TBL3,SUM,WRT,LAST)A1,A2,A3,P1,P2,P3,
                     F1,F2,F3,F4,F5,F6
*
      CW1 = [ACC*SUM] + ACC_AM                          /A1,F1,P1
      CW2 = TBL1                                        /0,F2,0
      CW3 = ACC + ACC_AM                                /A2,F3,P2
      CW4 = TBL2                                        /0,F4,0
      CW5 = ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST]    /A3,F5,P3
      CW6 = TBL3                                        /0,F6,0
*
      ENDM ADF_GEN
*
*
      MACRO KEY_GEN (TBL1,SUM,WRT,LAST)A1,P1,F1,F2
*
      CW1 = [ACC*SUM] + [WEN*WRT] + [END*LAST]          /A1,F1,P1
      CW2 = TBL1                                        /0,F2,0
*
      ENDM KEY_GEN
*
*
DECLA_END
*
CODE_START
*
*-------- ADF SECTION ---------
*
 1      ADF_GEN (SOURCE01,SOURCE02,SOURCE03,,=,)
                 WGAMP0,WGAMP1,WGAMP2,WGPHA0,WGPHA1,WGPHA2,
                 RFKBFRI1,RFKBFRF1,RFKFRI01,RFKFRF01,RFKFRI02,RFKFRF02
*
 2      ADF_GEN (SOURCE04,SOURCE05,SOURCE06,,=,)
                 WGAMP3,WGAMP4,WGAMP5,WGPHA3,WGPHA4,WGPHA5,
                 RFKBFRI2,RFKBFRF2,RFKFRI03,RFKFRF03,RFKFRI04,RFKFRF04,
*
*-------- KEYER SECTION ---------
*
 3      KEY_GEN (SOURCE07,,=,)
                  WGAMP6,WGPHA6,RFKFRI05,RFKFRF05
*
 4      KEY_GEN (SOURCE08,,=,)
                  WGAMP7,WGPHA7,RFKFRI06,RFKFRF06
*
 5      KEY_GEN (SOURCE09,,=,)
                  WGAMP8,WGPHA8,RFKFRI07,RFKFRF07
*
 6      KEY_GEN (SOURCE10,,=,)
                  WGAMP9,WGPHA9,RFKFRI08,RFKFRF08
*
 7      KEY_GEN (SOURCE11,,=,)
                  WGAMP10,WGPHA10,RFKFRI09,RFKFRF09
*
 8      KEY_GEN (SOURCE12,,=,)
                  WGAMP11,WGPHA11,RFKFRI10,RFKFRF10
*
 9      KEY_GEN (SOURCE13,,=,)
                  WGAMP12,WGPHA12,RFKFRI11,RFKFRF11
*
 10     KEY_GEN (SOURCE14,,=,)
                  WGAMP13,WGPHA13,RFKFRI12,RFKFRF12
*
 11     KEY_GEN (SOURCE15,,=,)
                  WGAMP14,WGPHA14,RFKFRI13,RFKFRF13
*
 12     KEY_GEN (SOURCE16,,=,)
                  WGAMP15,WGPHA15,RFKFRI14,RFKFRF14
*
 13     KEY_GEN (SOURCE17,,=,)
                  WGAMP16,WGPHA16,RFKFRI15,RFKFRF15
*
 14     KEY_GEN (SOURCE18,,=,)
                  WGAMP17,WGPHA17,RFKFRI16,RFKFRF16
*
 15     KEY_GEN (SOURCE19,,=,)
                  WGAMP18,WGPHA18,RFKFRI17,RFKFRF17
*
 16     KEY_GEN (SOURCE20,,=,)
                  WGAMP19,WGPHA19,RFKFRI18,RFKFRF18
*
 17     KEY_GEN (SOURCE21,,=,)
                  WGAMP20,WGPHA20,RFKFRI19,RFKFRF19
*
 18     KEY_GEN (SOURCE22,,=,#)
                  WGAMP21,WGPHA21,RFKFRI20,RFKFRF20
*
CODE_END
*
*
********************************************************************************
*       DDDD    SSS    GGGG         SSS   III   GGGG  N   N   AAA   L
*       D   D  S      G            S       I   G      NN  N  A   A  L
*       D   D   SSS   G GGG         SSS    I   G GGG  N N N  AAAAA  L
*       D   D      S  G   G            S   I   G   G  N  NN  A   A  L
*       DDDD    SSS    GGG          SSS   III   GGG   N   N  A   A  LLLL
********************************************************************************
*
SLOT = 15
*
DECLA_START
*
*
      ADDRESS
*
      FREQ  = 9000
      AMPL  = 9404
      PHASE = B000
      CTRL  = A000
*
*
      MACRO SOMM_1 (TBL1,TBL2,SUM,WRT,LAST)A1,A2,
                    F1,F2,F3,F4,P1,P2
*
      CW1 = [ACC*SUM] + ACC_AM                             /A1,F1,P1
      CW2 = TBL1                                           /0,F2,0
      CW3 = ACC + ACC_AM +[WEN_AM*WRT] + [END*LAST]        /A2,F3,P2
      CW4 = TBL2                                           /0,F4,0
*
      ENDM SOMM_1
*
*
      MACRO MODU_1 (TBL1,TBL2,SUM,WRT,LAST)A1,A2,A3,
                    F1,F2,F3,F4,P1,P2
*
      CW1 = ENA + [ACC*SUM] + ACC_AM                       /A1,F1,P1
      CW2 = TBL1                                           /0,F2,0
      CW3 = ENB + ACC + ACC_AM + [WEN_AM*WRT] + [END*LAST] /A2,F3,P2
      CW4 = TBL2                                           /A3,F4,0
*
      ENDM MODU_1
*
*
      MACRO MODU_2 (TBL1,TBL2,TBL3,SUM,WRT,LAST)A1,A2,A3,A4,
                    F1,F2,F3,F4,F5,F6,P1,P2,P3
*
      CW1 = ENA + [ACC*SUM] + ACC_AM                       /A1,F1,P1
      CW2 = TBL1                                           /0,F2,0
      CW3 = ENB + ACC + ACC_AM                             /A2,F3,P2
      CW4 = TBL2                                           /A3,F4,0
      CW5 = ACC + ACC_AM + [WEN*WRT] + [END*LAST]          /A4,F5,P3
      CW6 = TBL3                                           /0,F6,0
*
      ENDM MODU_2
*
*
DECLA_END
*
CODE_START
*
*-----    WAVE MODULATOR GENERATOR   -----
*
 1    MODU_2 (SOURCE01,SOURCE02,SOURCE03,,=,)
              RFMDAM01,RFMDAM02,RFMDAM03,RFMDAM04,RFMDFF01,
              RFMDFQ01,RFMDFF02,RFMDFQ02,RFMDFF03,RFMDFQ03,
              RFMDPH01,RFMDPH02,RFMDPH03
*
 2    MODU_2 (SOURCE04,SOURCE05,SOURCE06,,=,)
              RFMDAM01,RFMDAM02,RFMDAM03,RFMDAM04,RFMDFF01,
              RFMDFQ01,RFMDFF02,RFMDFQ02,RFMDFF03,RFMDFQ03,
              RFMDPH01,RFMDPH02,RFMDPH03
*
 3    MODU_2 (SOURCE07,SOURCE08,SOURCE09,,=,)
              RFMDAM05,RFMDAM06,RFMDAM07,RFMDAM08,RFMDFF04,
              RFMDFQ04,RFMDFF05,RFMDFQ05,RFMDFF06,RFMDFQ06,
              RFMDPH04,RFMDPH05,RFMDPH06
*
 4    MODU_2 (SOURCE10,SOURCE11,SOURCE12,,=,)
              RFMDAM05,RFMDAM06,RFMDAM07,RFMDAM08,RFMDFF04,
              RFMDFQ04,RFMDFF05,RFMDFQ05,RFMDFF06,RFMDFQ06,
              RFMDPH04,RFMDPH05,RFMDPH06
*
 5    MODU_2 (SOURCE13,SOURCE14,SOURCE15,,=,)
              RFMDAM09,RFMDAM10,RFMDAM11,RFMDAM12,RFMDFF07,
              RFMDFQ07,RFMDFF08,RFMDFQ08,RFMDFF09,RFMDFQ09,
              RFMDPH07,RFMDPH08,RFMDPH09
*
 6    MODU_2 (SOURCE16,SOURCE17,SOURCE18,,=,)
              RFMDAM09,RFMDAM10,RFMDAM11,RFMDAM12,RFMDFF07,
              RFMDFQ07,RFMDFF08,RFMDFQ08,RFMDFF09,RFMDFQ09,
              RFMDPH07,RFMDPH08,RFMDPH09
*
 7    MODU_2 (SOURCE19,SOURCE20,SOURCE21,,=,)
              RFMDAM09,RFMDAM10,RFMDAM11,RFMDAM12,RFMDFF07,
              RFMDFQ07,RFMDFF08,RFMDFQ08,RFMDFF09,RFMDFQ09,
              RFMDPH07,RFMDPH08,RFMDPH09
*
*-----   AMPLITUDE SHAPER GENERATOR   -----
*
 8    MODU_2 (SOURCE22,SOURCE23,SOURCE24,,=,)
              RFASAM01,RFASAM02,RFASAM03,RFASAM04,RFASFF01,
              RFASFQ01,RFASFF02,RFASFQ02,RFASFF03,RFASFQ03,
              RFASPH01,RFASPH02,RFASPH03
*
 9    MODU_2 (SOURCE25,SOURCE26,SOURCE27,,=,)
              RFASAM01,RFASAM02,RFASAM03,RFASAM04,RFASFF01,
              RFASFQ01,RFASFF02,RFASFQ02,RFASFF03,RFASFQ03,
              RFASPH01,RFASPH02,RFASPH03
*
 10   MODU_2 (SOURCE28,SOURCE29,SOURCE30,,=,)
              RFASAM01,RFASAM02,RFASAM03,RFASAM04,RFASFF01,
              RFASFQ01,RFASFF02,RFASFQ02,RFASFF03,RFASFQ03,
              RFASPH01,RFASPH02,RFASPH03
*
*-----   FREQUENCY SHAPER GENERATOR   -----
*
 11   MODU_1 (SOURCE31,SOURCE32,,=,)
              RFFSAM01,RFFSAM02,RFFSAM03,RFFSFF01,RFFSFQ01,
              RFFSFF02,RFFSFQ02,RFFSPH01,RFFSPH02
*
 12   MODU_1 (SOURCE33,SOURCE34,,=,)
              RFFSAM01,RFFSAM02,RFFSAM03,RFFSFF01,RFFSFQ01,
              RFFSFF02,RFFSFQ02,RFFSPH01,RFFSPH02
*
*-----   PHASE SHIFTER GENERATOR   -----
*
*
 13   SOMM_1 (SOURCE35,SOURCE36,,=,)
              RFPSAM01,RFPSAM02,RFPSFF01,RFPSFQ01,
              RFPSFF02,RFPSFQ02,RFPSPH01,RFPSPH02
*
 14   SOMM_1 (SOURCE37,SOURCE38,,=,#)
              RFPSAM01,RFPSAM02,RFPSFF01,RFPSFQ01,
              RFPSFF02,RFPSFQ02,RFPSPH01,RFPSPH02
*
*
CODE_END
