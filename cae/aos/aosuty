# *************************************************************************
# *                    AOSUTY  for UNIX systems                           *
# *                                                                       *
# *  Version 1.0    Original version           P. Daigle      7 Jan 1991  *
# *  Version 1.1    Added call to Firgen       P. Daigle     16 Dec 1991  *
# *************************************************************************
#/bin/csh -f
onintr INTER
#
# Setup environment variables for AOSUTY
#
source aos_env
#
# Check if AOSUTY is being used
#
clear
unalias rm
set AOSDISK = "`printenv aos_disk`"
set exec_dir = "${AOSDISK}/aos/uty/exec/"
set USERDIR = "`pwd`"
#
if (-e "aosuty_used.tmp") then
   echo "Sorry, AOSUTY is already being used..."
   echo "Check if the utility is running on another terminal"
   exit
else
   goto ONE_USER
endif
#
ONE_USER:
touch aosuty_used.tmp
#
# Check is AOSUTY executable exists and run it if it does
#
set CMD="${exec_dir}aosmain"
if (-e $CMD) then
   goto NO_EXIT
else
   clear
   echo ""
   echo "    ************************************************ "
   echo "    **             AOSUTY needs help              ** "
   echo "    **                                            ** "
   echo "    **         Can't find aosuty.exe file         ** "
   echo "    **           in executable directory          ** "
   echo "    **           FATAL ERROR - GOODBYE!           ** "
   echo "    **                                            ** "
   echo "    ************************************************ "
   #
   rm aosuty_used.tmp
   exit
endif
#
#
NO_EXIT:
#
#  Call Main Program
#
set CMD="${exec_dir}aosmain"
$CMD
if (-e "abort.tmp.1") goto GOODBYE
#
if (-e "exit.tmp.1") goto FINISHED
#
CHECK_NEXT:
if (-e "tsdgen.tmp.1") then
   #
   #  ------------------- TSDGEN ------------------------  
   #
   rm tsdgen.tmp.*
   set CMD = "${exec_dir}tsdgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " tsdgen.exe module not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "harmony.tmp.1") then
   #
   #  ------------------- HARMONY -----------------------
   #
   rm harmony.tmp.*
   set CMD = "${exec_dir}harmony"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable harmony file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "wavegen.tmp.1") then
   #
   #  ------------------- WAVEGEN -----------------------
   #
   rm wavegen.tmp.*
   set CMD = "${exec_dir}wavegen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable wavegen file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "tmsgen.tmp.1") then
   #
   #  ------------------- TMSGEN -----------------------
   #
   rm tmsgen.tmp.*
   set CMD = "${exec_dir}tmsgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable tmsgen file not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "formgen.tmp.1") then
   #
   #  ------------------- FORMGEN -----------------------
   #
   rm formgen.tmp.*
   set CMD = "${exec_dir}formgen"
   if (-e $CMD) then
      $CMD
   else
      echo " "
      echo "AOSUTY error :"
      echo " Exectuable formgen file module not found, returned to AOSUTY"
      echo " "
   endif
#
else if (-e "firgen.tmp.1") then
   #
   #  ------------------- FIRGEN  -----------------------
   #
   rm firgen.tmp.*
   set CMD = "${exec_dir}firgen"
   if (-e $CMD) then
      $CMD
      goto CHECK_NEXT
   else
      echo " "
      echo "AOSUTY error :"
      echo " Executable firgen file not found, returned to AOSUTY"
      echo " "
   endif
endif
#
if (-e "exit_ac.tmp.1") then
   rm exit_ac.tmp.*
   clear
   echo " "
   echo "Sound's Good ..."
   goto FINAL_EXIT
else
   goto NO_EXIT
endif
#
FINISHED:
   rm exit.tmp.*
   clear
   echo " "
   echo "Sound's Good ..."
   goto FINAL_EXIT
#
GOODBYE:
   rm abort.tmp.*
   clear
   echo " "
   echo "One of the AOSUTY programs has generated an internal error"
   echo "This error is unrecoverable, something is very wrong."
   echo "Sound's bad..."
   goto FINAL_EXIT
#
ERROR_BRANCH:
   echo " "
   echo "AOSUTY Internal error: "
   echo "This error is unrecoverable, something is wrong."
   echo "Sound's bad..."
   goto FINAL_EXIT
#
INTER:
   echo " "
   echo " You want to abort AOSUTY, OK your the boss. "
   echo " But it sound's bad ..."
   echo " "
#
FINAL_EXIT:
   rm aosuty_used.tmp
   rm *.tmp.*
   rm *.inf.*
   rm *.fmg.*
#   rm *.FMG*
#   find $USERDIR -size 0 -name '*.tmp.*' -exec rm {} \;
#   find $USERDIR -name '*.inf.*' -exec rm {} \;
#   find $USERDIR -name '*.fmg.*' -exec rm {} \;
#   find $USERDIR -name '*.FMG*' -exec rm {} \;
   exit
