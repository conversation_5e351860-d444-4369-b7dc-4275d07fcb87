#==============================================================================
#                          AOSCOMP  for  IBM  
#
# Version 1.0                Pierre <PERSON>gle             Date: 26 February 1991  
#==============================================================================
#! /bin/csh -f
#
# Set environment to run on site
source aos_env
#
#
onintr INTER
unalias rm
alias clr "echo -n '[7;1H[0J'"
alias reset_scr "echo -n '[1;24r'"
#
#
clear
echo -n '[7;24r'
echo -n '[2;14H'
echo "[1m*****    AOSCOMP  Version 1.0 - February 91   *****[0m"
echo -n '[5;1H'
echo -n "========================================"
echo "======================================="
#
set AOSDISK = "`printenv aos_disk`"
set AOSDIR = "${AOSDISK}/aos/"
set EXEDIR = "${AOSDISK}/aos/uty/exec/"
set USER_DIR = "`pwd`"
#
#
#
if ($#argv == 0) then
   goto LIBRARY
else if ("$argv[1]" == "aosmain") then
   goto AOSMAIN
else if ("$argv[1]" == "formgen") then
   goto FORMGEN
else if ("$argv[1]" == "tsdgen") then
   goto TSDGEN
else if ("$argv[1]" == "harmony") then
   goto HARMONY
else if ("$argv[1]" == "tmsgen") then
   goto TMSGEN
else if ("$argv[1]" == "wavegen") then
   goto WAVEGEN
else if ("$argv[1]" == "library") then
   goto LIBRARY
else
   echo
   echo " Invalid Utility name ..."
   echo
   goto END
endif
#
#    <<<<<<<<  LIBRARY  >>>>>>>>
#
LIBRARY:
clr
echo -n '[4;26H'
echo "[1m[5mCompiling LIBRARY modules [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/library/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
libcreate
#
if ($#argv != 0) then 
   goto END
endif
#
#      <<<<<<<<  AOSMAIN >>>>>>>>
#
AOSMAIN:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking AOSMAIN utility [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/main/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f makemain
#
if ($#argv != 0) then 
   goto END
endif
#
#    <<<<<<<<  FORMGEN >>>>>>>>
#
FORMGEN:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking FORMGEN utility  [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/formgen/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f makeform
#
if ($#argv != 0) then 
   goto END
endif
#
#
#    <<<<<<<<  TSDGEN >>>>>>>>
#
TSDGEN:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking TSDGEN utility  [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/tsdgen/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f maketsd
#
if ($#argv != 0) then 
   goto END
endif
#
#
#    <<<<<<<<  HARMONY >>>>>>>>
#
HARMONY:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking HARMONY utility  [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/harmony/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f makeharm
#
if ($#argv != 0) then 
   goto END
endif
#
#
#    <<<<<<<<  TMSGEN  >>>>>>>>
#
TMSGEN:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking TMSGEN utility  [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/tmsgen/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f maketms
#
if ($#argv != 0) then 
   goto END
endif
#
#
#    <<<<<<<<  WAVEGEN  >>>>>>>>
#
WAVEGEN:
clr
echo -n '[4;21H'
echo "[1m[5mCompiling and Linking WAVEGEN utility [0m"
echo -n '[7;1H'
#
set UTILDIR = "${AOSDIR}uty/wavegen/"
cd $UTILDIR
#
# Compile source file only if its object file does not exist
#
make -f makewave
#
goto END
#
#
#
INTER:
echo " "
echo " AOSCOMP Interrupted ... "
#
#
END:
reset_scr
echo -n '[23;1H'
rm *.err
cd $USER_DIR
exit
