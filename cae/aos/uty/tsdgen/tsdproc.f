C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C            THESE FILE PROCEEDS ALL DATAS , CHECK IF EVERYTHING
C
C            IS IN THE RIGHT SEQUENCE AND PROCESS THE RESULTS TO
C
C            GENERATE THE TSD CONFIGURATION ARRAY.
C
C
C                      PROC_DATA
C                      DAT_CHECK
C                      SPECTASK
C                      PROC_ASS
C                      PROC_MAT
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =================================================================
C                          PROC_DATA
C =================================================================
C
C   This routine do the second pass on the datas.  It process them
C   to generate a TSDBUS slot array stored in a temporary file.
C
C
      SUBROUTINE PROC_DATA(ERR_PROC)
C
      IMPLICIT NONE
C
        CHARACTER
     & OBUS(27)*8         ,!Other bus name
     & PARS2(27)*8        ,!Spare parameter
     & NBUS*8              !Bus name
C
        INTEGER*2
     & II                 ,!Loop counter
     & JJ                 ,!Loop counter
     & OBUS_C             ,!Other bus count
     & MSLOT              ,!Master slot number
     & CODE               ,!Assignment array code
     & PARS1               !Spare parameter
C
        INTEGER*4
     & RECN               ,!Record number
     & KK                 ,!Loop counter
     & ERR_PROC            !Proceed error counter
C
        LOGICAL*2
     & FIRSPASS            !First pass flag when looking for a bus
C
      INCLUDE 'tsdata.inc'
C
C     Check all the datas
C     -------------------
      CALL DAT_CHECK(ERR_PROC)
C
      RECN = 1
      II = 1
      DO WHILE(ERR_PROC.EQ.0.AND.II.LE.CH_MAX)
C
         JJ = 1
         DO WHILE(JJ.LE.2.AND.ERR_PROC.EQ.0)
C
C           Get all the tasks related to this PORT and CHASSIS
C           --------------------------------------------------
            FIRSPASS=.TRUE.
            OBUS_C = 0
C
C           Create the assignment array
C           ---------------------------
            NBUS = 'XXXXxxxx'
            CALL PROC_ASS(II,JJ,MSLOT,FIRSPASS,OBUS_C,OBUS,NBUS,
     &                      ERR_PROC)
C
            IF (ERR_PROC.EQ.0.AND.NBUS.NE.'XXXXxxxx') THEN
C
               CALL PROC_MAT(JJ,ERR_PROC)
C
C              Store all the information in the .TMP file
C              ------------------------------------------
               CODE = II*100+(JJ-1)*50
               CALL MAT_OPER(RECN,1,CODE,MSLOT,NBUS,ERR_PROC) !File TSDLIB.FOR
               RECN=RECN+1
C
               IF(OBUS_C.NE.0)THEN
C
C                Proceed the other bus on the PORT
C                ---------------------------------
                 KK = 1
                 DO WHILE(KK.LE.OBUS_C.AND.ERR_PROC.EQ.0)
                    FIRSPASS=.FALSE.
                    NBUS = OBUS(KK)
C
C                   Create the assignment array
C                   ---------------------------
                    CALL PROC_ASS(II,JJ,MSLOT,FIRSPASS,PARS1,PARS2,
     &                              NBUS,ERR_PROC)
                    CALL PROC_MAT(JJ,ERR_PROC)
C
C                   Store the generated array
C                   -------------------------
                    CODE = II*100 + (JJ-1)*50 + KK
                    CALL MAT_OPER(RECN,1,CODE,MSLOT,NBUS,ERR_PROC)    !File TSDLIB.FOR
                    RECN = RECN + 1
                    KK = KK + 1
                 ENDDO
               ENDIF
            ENDIF
            JJ = JJ + 1
         ENDDO
C
         II = II + 1
      ENDDO
C
      RETURN
      END
C
C
C =================================================================
C                              DAT_CHECK
C =================================================================
C
C   This subroutine checks for repeated tasks in SLOT and IO section.
C   It also do a check on the X-Ref section to match all the outputs
C   and inputs.
C
      SUBROUTINE DAT_CHECK(ERR_PROC)
C
      IMPLICIT NONE
C
      INCLUDE 'tsdata.inc'
C
        INTEGER*4
     & II             ,!Loop counter
     & JJ             ,!Loop counter
     & KK             ,!Loop counter
     & LL             ,!Loop counter
     & ERR_PROC       ,!Proceed error counter
     & FINDPNT        ,!Find the task in opposite X_ref section pointer
     & BANK           ,!Array pointer(1-Output,2-Input)
     & IBANK           !Inverse array pointer
C
        LOGICAL*1
     & ALLTK          ,!
     & SECPASS        ,!Second pass flag
     & FINDTASK        !Task found flag
C
C     ***************************************
C     Check for repeated task on slot section
C     ***************************************
C
      DO II= 1 , SLTASK_MAX
         DO JJ= II, SLTASK_MAX
            IF((II.NE.JJ).AND.(TASK_NAME(II).EQ.TASK_NAME(JJ)))THEN
                CALL MESSAGE(2)
                WRITE (6,100) ESCAPE,POSDOWN,TASK_NAME(II)
                ERR_PROC = 10
            ENDIF
         ENDDO
      ENDDO
C
C     ********************************************
C     Check if IO section task are in SLOT section
C     ********************************************
C
      DO II=1,IOTASK_MAX
C
C        Look if this IO task exist in slot section
C        ------------------------------------------
         FINDTASK=.FALSE.
         DO JJ=1,SLTASK_MAX
            IF(IO_TASK(II).EQ.TASK_NAME(JJ))THEN
C
C              Set task found for both IO and SLOT task
C              ----------------------------------------
               FINDTASK=.TRUE.
               SLOTASK_M(JJ)= .TRUE.
            ENDIF
         ENDDO
C
C        Write error message if IO task not exist in SLOT section
C        --------------------------------------------------------
         IF(.NOT.FINDTASK) THEN
            CALL MESSAGE(2)
            WRITE(6,130) ESCAPE,POSDOWN,IO_TASK(II)
            ERR_PROC = 13
         ENDIF
      ENDDO
C
C     Check if there is a SLOT task not found in IO section
C     -----------------------------------------------------
      DO JJ=1,SLTASK_MAX
         IF(.NOT.SLOTASK_M(JJ))THEN
            CALL MESSAGE(2)
            WRITE (6,140) ESCAPE,POSDOWN,TASK_NAME(JJ)
            ERR_PROC = 14
         ENDIF
      ENDDO
C
C     *********************************************
C     Check for Input-Output Match X-Reference Task
C     *********************************************
C
C     This check is done in two passes:
C      - First pass: check communication from 1 task to 1 task and delete
C                    the corresponding task to create only one slot for
C                    communication. Does not process communication when more
C                    than one task are involved.
C      - Second pass:Process the communication when more than one task are
C                    involved.
C     These 2 passes process will allow first to delete all the tasks which
C     are involved with single task communication and keep the other ones
C     for more complex communication scheme.
C     ----------------------------------------------------------------------
C
      DO II=1,IOTASK_MAX
         IOTKCK(II) =0
      ENDDO
      SECPASS=.FALSE.
      II = 1
      DO WHILE(II.LE.IOTASK_MAX)
C
C       Check for end of the output IO on that task
C       -------------------------------------------
        JJ = 1
        DO WHILE(IO_NUMB(1,II,JJ).NE.999)
C
C
C         Check if there is a X-Ref task and no-check sign off(>500)
C         ----------------------------------------------------------
          IF(IO_NUMB(1,II,JJ).NE.0) THEN
             BANK = 1
             IBANK = 2
          ELSEIF(IO_NUMB(2,II,JJ).NE.0) THEN
             BANK = 2
             IBANK = 1
          ELSE
             BANK = 0
          ENDIF
C
          IF (BANK.NE.0) THEN
C
             IF(IABS(IO_NUMB(BANK,II,JJ)).LT.500 ) THEN
C
                IF(.NOT.SECPASS) THEN
                   FINDTASK = .FALSE.
                ELSE
C
C                  If in second pass, set task found for all the tasks
C                  except the special check (set FALSE in sp. check section).
C                  ----------------------------------------------------------
                   FINDTASK = .TRUE.
                ENDIF
C
C               Find the corresponding task in the opposite section
C               ---------------------------------------------------
                ALLTK = .TRUE.
                DO WHILE(ALLTK)
                 FINDPNT=1
                 DO WHILE(((IO_NAME(BANK,II,JJ).NE.IO_TASK(FINDPNT)).OR.
     &             (IOTKCK(FINDPNT).GT.0)).AND.(FINDPNT.LE.IOTASK_MAX))
                   FINDPNT=FINDPNT+1
                 ENDDO
C
C                Skip next section if not found
C                ------------------------------
                 IF (FINDPNT.LE.IOTASK_MAX) THEN
C
                   IOTKCK(FINDPNT) = 1
C
                   KK=1
C
C                  Stop to look if end of the I/O in this task is found or
C                  if the task as been found.  If in second pass, ignored it
C                  ---------------------------------------------------------
                   DO WHILE((IO_NUMB(IBANK,FINDPNT,KK).NE.999).AND.
     &                         (.NOT.FINDTASK.OR.SECPASS) )
C
C                   Insure both tasks has not been check before
C                   -------------------------------------------
                    IF((IOCHECK(BANK,II,JJ).EQ.0).AND.
     &                   (IOCHECK(IBANK,FINDPNT,KK).EQ.0)) THEN
C
C                    Check if task name correspond
C                    -----------------------------
                     IF(IO_NAME(IBANK,FINDPNT,KK).EQ.IO_TASK(II))THEN
C
C                     Find if the number of words correspond
C                     --------------------------------------
                      IF(IABS(IO_NUMB(IBANK,FINDPNT,KK)).EQ.
     &                       IABS(IO_NUMB(BANK,II,JJ))    )THEN
C
C                      IO task correspond in X-Ref list
C                      --------------------------------
                       IF(((IO_NUMB(IBANK,FINDPNT,KK+1).GE.0).AND.
     &                     (IO_NUMB(IBANK,FINDPNT,KK)  .GE.0)).AND.
     &                    ((IO_NUMB(BANK,II,JJ+1).GE.0) .AND.
     &                     (IO_NUMB(BANK,II,JJ)  .GE.0) )     )THEN
C
C                        If the next tasks on both side are not negative
C                        (communication 1 task to 1 task), delete corres-
C                        ponding task to create only one row of TSD word
C                        transfert
C                        ------------------------------------------------
                         FINDTASK = .TRUE.
                         IOCHECK(BANK,II,JJ) = 1
                         IO_NUMB(IBANK,FINDPNT,KK) = 0
                         IO_NAME(IBANK,FINDPNT,KK) = '   '
                       ELSE
C
                         IF(SECPASS)THEN
                           FINDTASK=.FALSE.
                           IF(((IO_NUMB(IBANK,FINDPNT,KK+1).GE.0).AND.
     &                        (IO_NUMB(IBANK,FINDPNT,KK)  .GE.0) ).OR.
     &                        ((IO_NUMB(BANK,II,JJ+1).GE.0) .AND.
     &                         (IO_NUMB(BANK,II,JJ)  .GE.0) )     )THEN
C
C                            Special check: simple transfert if one of
C                            the next task is negative (many tasks to
C                            1 task communication)
C                            -----------------------------------------
                             CALL SIMPTASK(BANK,IBANK,II,JJ,FINDPNT
     &                                             ,KK,FINDTASK)
                           ELSE
C
C                            Special check: double transfert if both
C                            next tasks are negative (Many tasks to many
C                            tasks communication on one slot)

C                            -------------------------------------------
                             CALL DOUBTASK(BANK,IBANK,II,JJ,FINDPNT
     &                                             ,KK,FINDTASK)
                           ENDIF
                         ELSE
C
C                          If in first pass, set task found for this
C                          special group of tasks.
C                          -----------------------------------------
                           FINDTASK=.TRUE.
                         ENDIF
                       ENDIF
C
                      ENDIF
                     ENDIF
                    ENDIF
                    KK = KK+1
                   ENDDO
                 ELSE
                   ALLTK = .FALSE.
                 ENDIF
                ENDDO
                DO KK=1,IOTASK_MAX
                  IOTKCK(KK) = 0
                ENDDO
C
                IF(.NOT.FINDTASK)THEN
C
C                  X-Ref task not found, write unmatched error message
C                  ---------------------------------------------------
                   CALL MESSAGE(2)
                   WRITE(6,120) ESCAPE,POSDOWN,IO_TASK(II)
     &                              ,IO_NAME(BANK,II,JJ)
                   ERR_PROC=120
C
                ENDIF
C
             ENDIF
          ENDIF
C
          JJ = JJ + 1
        ENDDO
C
C       If at the end of all the IO tasks and on first pass, set second pass
C       --------------------------------------------------------------------
        II = II+1
        IF(.NOT.SECPASS.AND.II.EQ.IOTASK_MAX) THEN
           SECPASS=.TRUE.
           II = 1
        ENDIF
C
      ENDDO
C
      RETURN
C
C
 100  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Task ',A8,' appears m'
     &        ,'ore than 1 time in SLOT section  * SEVERE **')
 110  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Task ',A8,' appears m'
     &        ,'ore than one time in IO section  * SEVERE **')
 120  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Unmatched task in IO '
     &        ,'section : ',A8,' to ',a8,'   * SEVERE **')
 130  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** IO task ',A8,' not fo'
     &        ,'und in SLOT section              * SEVERE **')
 140  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** SLOT task ',A8,' not '
     &        ,'found in IO section              * SEVERE **')
C
      END
C
C
C ===================================================================
C                          DOUBTASK
C ===================================================================
C
C   This subroutine process the special task check when more than one
C   cards send datas to many cards on the same TSDBUS word slots.
C
      SUBROUTINE DOUBTASK(BANK,IBANK,PNT1,PNT2,IPNT1,IPNT2,FINDTASK)
C
      INCLUDE 'tsdata.inc'
C
        INTEGER*4
     & BANK           ,!Input/output bank pointer
     & IBANK          ,!Inverse input/output bank pointer
     & PNT1           ,!Array pointer for bank side
     & PNT2           ,!Array pointer for bank side
     & IPNT1          ,!Array pointer for ibank side
     & IPNT2          ,!Array pointer for ibank side
     & ML             ,!Loop counter
     & LL             ,!Loop counter
     & MM             ,!Loop counter
     & WHERE           !Location pointer
C
        INTEGER*2
     & SPECNUM        ,!Special check number
     & MAX1           ,!Maximum number of task on bank side
     & IMAX1          ,!Maximum number of task on ibank side
     & POSMAX1        ,!Highest array pointer in bank side
     & POSMAX2        ,!Highest array pointer in bank side
     & IPOSMAX1       ,!Highest array pointer in ibank side
     & IPOSMAX2       ,!Highest array pointer in ibank side
     & CNT            ,!Internal task found counter
     & STORPOS1       ,!Array pointer for storage
     & STORPOS2       ,!Array pointer for storage
     & FOUND           !Task found counter
C
        CHARACTER
     & TASKIN(10)*8   ,!Common Tasks for special check on bank side
     & TASKOUT(10)*8   !Common Tasks for special check on ibank side
C
        LOGICAL*1
     & ALLTK          ,!
     & CONTINU        ,!Look continued flag
     & FINDTASK        !Task found flag
C
      DO WHERE=1,IOTASK_MAX
         IOTKCK(WHERE)=0
      ENDDO
C
C     Store the number of word slots
C     ------------------------------
      SPECNUM = IO_NUMB(BANK,PNT1,PNT2)
C
C     Store all the tasks in the sending group
C     ----------------------------------------
      TASKIN(1) = IO_NAME(BANK,PNT1,PNT2)
      IO_NAME(BANK,PNT1,PNT2) = '        '
      IO_NUMB(BANK,PNT1,PNT2) = 0
      MM = 1
C
      DO WHILE(IO_NUMB(BANK,PNT1,PNT2+MM).LT.0)
         MM = MM+1
         TASKIN(MM) = IO_NAME(BANK,PNT1,PNT2+(MM-1))
         IO_NAME(BANK,PNT1,PNT2+(MM-1)) = '        '
         IO_NUMB(BANK,PNT1,PNT2+(MM-1)) = 0
      ENDDO
      MAX1 = MM
C
C     Store all the tasks in the receiving group
C     ------------------------------------------
      TASKOUT(1) = IO_NAME(IBANK,IPNT1,IPNT2)
      IO_NAME(IBANK,IPNT1,IPNT2) = '        '
      IO_NUMB(IBANK,IPNT1,IPNT2) = 0
      MM = 1
C
      DO WHILE(IO_NUMB(IBANK,IPNT1,IPNT2+MM).LT.0)
         MM = MM+1
         TASKOUT(MM) = IO_NAME(IBANK,IPNT1,IPNT2+(MM-1))
         IO_NAME(IBANK,IPNT1,IPNT2+(MM-1)) = '        '
         IO_NUMB(IBANK,IPNT1,IPNT2+(MM-1)) = 0
      ENDDO
      IMAX1 = MM
C
      FOUND = 2
C
C     Look now for all the receiving tasks in the other sending tasks
C     ---------------------------------------------------------------
      DO MM = 2,MAX1
C
       ALLTK=.TRUE.
       DO WHILE(ALLTK)
         WHERE = 1
         DO WHILE((IO_TASK(WHERE).NE.TASKIN(MM).OR.IOTKCK(WHERE).GT.0)
     &        .AND.WHERE.LE.IOTASK_MAX)
            WHERE = WHERE+1
         ENDDO
C
         IF (WHERE.LE.IOTASK_MAX) THEN
             LL = 1
             CONTINU =.TRUE.
             DO WHILE(IO_NUMB(IBANK,WHERE,LL).NE.999.AND.CONTINU)
                IF((IO_NUMB(IBANK,WHERE,LL+1).LT.0).AND.
     &             (IOCHECK(IBANK,WHERE,LL).EQ.0)      )THEN
                  IF((IABS(IO_NUMB(IBANK,WHERE,LL)).EQ.SPECNUM)
     &              .AND.(IO_NAME(IBANK,WHERE,LL).EQ.TASKOUT(1)))THEN
                     CNT = 1
                     DO WHILE((IO_NAME(IBANK,WHERE,LL+CNT).EQ.
     &                  TASKOUT(CNT+1)).AND.(IO_NUMB(IBANK,WHERE,LL+CNT)
     &                  .EQ.-SPECNUM) )
                         CNT = CNT+1
                     ENDDO
                     IF (CNT.EQ.IMAX1) THEN
C
C                       All receiving task found: delete them
C                       -------------------------------------
                        FOUND = FOUND+1
                        CONTINU=.FALSE.
                        POSMAX2 = LL
                        DO ML = LL,LL+CNT-1
                           IO_NUMB(IBANK,WHERE,ML) = 0
                           IO_NAME(IBANK,WHERE,ML) = '        '
                        ENDDO
                        ALLTK=.FALSE.
                     ENDIF
                  ENDIF
                ENDIF
                LL = LL + 1
             ENDDO
         ELSE
             ALLTK=.FALSE.
         ENDIF
         IOTKCK(WHERE) = 1
       ENDDO
       POSMAX1 = WHERE
       DO WHERE=1,IOTASK_MAX
          IOTKCK(WHERE)=0
       ENDDO
      ENDDO
C
C     Look now for all the sending tasks in the receiving tasks
C     ---------------------------------------------------------
      DO WHERE=1,IOTASK_MAX
          IOTKCK(WHERE)=0
      ENDDO
      DO MM = 2,IMAX1
C
       ALLTK=.TRUE.
       DO WHILE(ALLTK)
         WHERE = 1
         DO WHILE((IO_TASK(WHERE).NE.TASKOUT(MM).OR.IOTKCK(WHERE).GT.0)
     &        .AND.WHERE.LE.IOTASK_MAX)
            WHERE = WHERE+1
         ENDDO
C
         IF (WHERE.LE.IOTASK_MAX) THEN
             LL = 1
             CONTINU=.TRUE.
             DO WHILE(IO_NUMB(BANK,WHERE,LL).NE.999.AND.CONTINU)
                IF((IO_NUMB(BANK,WHERE,LL+1).LT.0).AND.
     &             (IOCHECK(BANK,WHERE,LL).EQ.0)      )THEN
                  IF((IABS(IO_NUMB(BANK,WHERE,LL)).EQ.SPECNUM)
     &              .AND.(IO_NAME(BANK,WHERE,LL).EQ.TASKIN(1)))THEN
                     CNT = 1
                     DO WHILE((IO_NAME(BANK,WHERE,LL+CNT).EQ.
     &                  TASKIN(CNT+1)).AND.(IO_NUMB(BANK,WHERE,LL+CNT)
     &                  .EQ.-SPECNUM) )
                         CNT = CNT+1
                     ENDDO
                     IF (CNT.EQ.MAX1) THEN
C
C                       All sending task found: delete them
C                       -----------------------------------
                        FOUND = FOUND+1
                        CONTINU=.FALSE.
                        IPOSMAX2 = LL
                        DO ML = LL,LL+CNT-1
                           IO_NUMB(BANK,WHERE,ML) = 0
                           IO_NAME(BANK,WHERE,ML) = '        '
                        ENDDO
                        ALLTK=.FALSE.
                     ENDIF
                  ENDIF
                ENDIF
                LL = LL + 1
             ENDDO
         ELSE
           ALLTK=.FALSE.
         ENDIF
         IOTKCK(WHERE) = 1
       ENDDO
       IPOSMAX1 = WHERE
       DO WHERE=1,IOTASK_MAX
          IOTKCK(WHERE)=0
       ENDDO
      ENDDO
C
C - Set the maximum parameters
C
      IF (FOUND.EQ.(MAX1+IMAX1) ) THEN
          FINDTASK=.TRUE.
      ENDIF
C
      IF (POSMAX1.GT.IPOSMAX1) THEN
          STORPOS1=POSMAX1
          STORPOS2=POSMAX2
      ELSE
          STORPOS1=IPOSMAX1
          STORPOS2=IPOSMAX2
      ENDIF
C
C - Find the last group of tasks (sending or receiving) to store
C   this special set of tasks
C
C   The tasks will be store with special number > 1000 which will
C   will be recognize in the generation of the matrix.
C
C
      SPECNUM = SPECNUM + 1000
      IOCHECK(BANK,STORPOS1,STORPOS2) = MAX1
      IO_NAME(BANK,STORPOS1,STORPOS2) = TASKIN(1)
      IO_NUMB(BANK,STORPOS1,STORPOS2) = SPECNUM
C
      DO MM=2,MAX1+IMAX1
C
C        If special end code, push it one more
C        -------------------------------------
         IF (IO_NUMB(BANK,STORPOS1,STORPOS2+(MM-1)).EQ.999) THEN
            IO_NUMB(BANK,STORPOS1,STORPOS2+MM)=999
            IO_NUMB(BANK,STORPOS1,STORPOS2+(MM-1))= 0
            IO_NUMB(IBANK,STORPOS1,STORPOS2+MM)=999
            IO_NUMB(IBANK,STORPOS1,STORPOS2+(MM-1))=0
         ENDIF
         IF (MM.LE.MAX1) THEN
            IOCHECK(BANK,STORPOS1,STORPOS2+(MM-1))= MAX1-(MM-1)
            IO_NAME(BANK,STORPOS1,STORPOS2+(MM-1))= TASKIN(MM)
            IO_NUMB(BANK,STORPOS1,STORPOS2+(MM-1))= -SPECNUM
         ELSE
            IOCHECK(IBANK,STORPOS1,STORPOS2+(MM-1))= -(MM-MAX1)
            IO_NAME(IBANK,STORPOS1,STORPOS2+(MM-1))=TASKOUT(MM-MAX1)
            IO_NUMB(IBANK,STORPOS1,STORPOS2+(MM-1))=-SPECNUM
         ENDIF
      ENDDO
C
      RETURN
      END
C
C ===================================================================
C                          SIMPTASK
C ===================================================================
C
C   This subroutine process the special task check when more than one
C   cards send datas to another one on the same TSDBUS word slots.
C
      SUBROUTINE SIMPTASK(BANK,IBANK,PNT1,PNT2,IPNT1,IPNT2,FINDTASK)
C
      INCLUDE 'tsdata.inc'
C
        INTEGER*4
     & BANK           ,!Input/output bank pointer
     & IBANK          ,!Inverse input/output bank pointer
     & PNT1           ,!Array pointer for bank side
     & PNT2           ,!Array pointer for bank side
     & IPNT1          ,!Array pointer for ibank side
     & IPNT2          ,!Array pointer for ibank side
     & LL             ,!Loop counter
     & MM             ,!Loop counter
     & WHERE           !Location pointer
C
        INTEGER*2
     & SPECNUM        ,!Special check number
     & MAX1           ,!Maximum number of task on bank side
     & IMAX1          ,!Maximum number of task on ibank side
     & FOUND           !Task found counter
C
        CHARACTER
     & TASKIN(10)*8   ,!Common Tasks for special check on bank side
     & TASKOUT(10)*8   !Common Tasks for special check on ibank side
C
        LOGICAL*1
     & ALLTK          ,!
     & CONTINU        ,!Look continued flag
     & FINDTASK        !Task found flag
C
C     Store number of word slots
C     --------------------------
      SPECNUM = IO_NUMB(BANK,PNT1,PNT2)
C
      IF(IO_NUMB(BANK,PNT1,PNT2+1).LT.0) THEN
C
        TASKIN(1) = IO_NAME(BANK,PNT1,PNT2)
        IOCHECK(BANK,PNT1,PNT2) = 1
        MM = 1
C
        DO WHILE(IO_NUMB(BANK,PNT1,PNT2+MM).LT.0)
          MM = MM+1
          TASKIN(MM) = IO_NAME(BANK,PNT1,PNT2+(MM-1))
          IOCHECK(BANK,PNT1,PNT2+(MM-1)) = 1
        ENDDO
        MAX1 = MM
C
        TASKOUT(1) = IO_NAME(IBANK,IPNT1,IPNT2)
        IO_NAME(IBANK,IPNT1,IPNT2) = '        '
        IO_NUMB(IBANK,IPNT1,IPNT2) = 0
        FOUND = 1
        DO MM = 2,MAX1
C
         DO WHERE=1,IOTASK_MAX
            IOTKCK(WHERE) = 0
         ENDDO
         ALLTK=.TRUE.
         DO WHILE(ALLTK)
          WHERE = 1
          DO WHILE(IO_TASK(WHERE).NE.TASKIN(MM).OR.IOTKCK(WHERE).GT.0)
            WHERE = WHERE+1
          ENDDO
C
          IF (WHERE.LE.IOTASK_MAX) THEN
             IOTKCK(WHERE) = 1
             LL = 1
             CONTINU =.TRUE.
             DO WHILE(IO_NUMB(IBANK,WHERE,LL).NE.999.AND.CONTINU)
                IF(( (IO_NUMB(IBANK,WHERE,LL+1).GE.0).OR.
     &               (IABS(IO_NUMB(IBANK,WHERE,LL+1)).GT.1000)).AND.
     &             (IOCHECK(IBANK,WHERE,LL).EQ.0)      )THEN
                  IF((IABS(IO_NUMB(IBANK,WHERE,LL)).EQ.SPECNUM)
     &              .AND.(IO_NAME(IBANK,WHERE,LL).EQ.TASKOUT(1)))THEN
                     FOUND = FOUND+1
                     CONTINU=.FALSE.
                     IO_NUMB(IBANK,WHERE,LL) = 0
                     IO_NAME(IBANK,WHERE,LL) = '        '
                     IOCHECK(BANK,PNT1,PNT2+(MM-1))=1
                     ALLTK = .FALSE.
                  ENDIF
                ENDIF
                LL = LL + 1
             ENDDO
          ELSE
             ALLTK=.FALSE.
          ENDIF
         ENDDO
        ENDDO
        IF (FOUND.EQ.MAX1 ) THEN
          FINDTASK=.TRUE.
        ENDIF
C
      ELSE
C
        TASKIN(1) = IO_NAME(BANK,PNT1,PNT2)
        IO_NAME(BANK,PNT1,PNT2) = '        '
        IO_NUMB(BANK,PNT1,PNT2) = 0
C
        TASKOUT(1) = IO_NAME(IBANK,IPNT1,IPNT2)
        IOCHECK(IBANK,IPNT1,IPNT2) = 1
        MM = 1
C
        DO WHILE(IO_NUMB(IBANK,IPNT1,IPNT2+MM).LT.0)
          MM = MM+1
          TASKOUT(MM) = IO_NAME(IBANK,IPNT1,IPNT2+(MM-1))
          IOCHECK(IBANK,IPNT1,IPNT2+(MM-1)) = 1
        ENDDO
        IMAX1 = MM
C
C
C
        FOUND = 1
        DO MM = 2,IMAX1
C
         DO WHERE=1,IOTASK_MAX
            IOTKCK(WHERE) = 0
         ENDDO
         ALLTK=.TRUE.
         DO WHILE(ALLTK)
          WHERE = 1
          DO WHILE(IO_TASK(WHERE).NE.TASKOUT(MM).OR.IOTKCK(WHERE).GT.0)
            WHERE = WHERE+1
          ENDDO
C
          IF (WHERE.LE.IOTASK_MAX) THEN
             IOTKCK(WHERE) = 1
             LL = 1
             CONTINU=.TRUE.
             DO WHILE(IO_NUMB(BANK,WHERE,LL).NE.999.AND.CONTINU)
                IF(( (IO_NUMB(BANK,WHERE,LL+1).GE.0).OR.
     &               (IABS(IO_NUMB(BANK,WHERE,LL+1)).GT.1000)).AND.
     &             (IOCHECK(BANK,WHERE,LL).EQ.0)      )THEN
                  IF((IABS(IO_NUMB(BANK,WHERE,LL)).EQ.SPECNUM)
     &              .AND.(IO_NAME(BANK,WHERE,LL).EQ.TASKIN(1)))THEN
                     FOUND = FOUND+1
                     CONTINU=.FALSE.
                     IO_NUMB(BANK,WHERE,LL) = 0
                     IO_NAME(BANK,WHERE,LL) = '        '
                     IOCHECK(IBANK,IPNT1,IPNT2+(MM-1))=1
                     ALLTK=.FALSE.
                  ENDIF
                ENDIF
                LL = LL + 1
             ENDDO
          ELSE
             ALLTK=.FALSE.
          ENDIF
         ENDDO
        ENDDO
C
C
C
        IF (FOUND.EQ.IMAX1 ) THEN
           FINDTASK=.TRUE.
        ENDIF
C
      ENDIF
C
      RETURN
      END
C
C
C ===================================================================
C                          PROC_ASS
C ===================================================================
C
C   This subroutine process the inputs and find all the tasks which
C   are involved with a specified chassis in a specicied port.  It
C   also return if there is more than 1 bus on the port (FIRSPASS=.TRUE.).
C
C
C   OUTPUT: ASSIGN(x) :Contains all the tasks related to the port
C                      processed on all the chassis.
C
C           ASLOT(x)  :Contains the slot number of the ASSIGN task
C
C           ACHAS(x)  :Contains the chassis number of the ASSIGN task.
C                      A negative chassis number means the task is not
C                      on the same chassis as the other tasks in the array.
C
C           MASSLOT   :Master slot number
C
C           O_BUS_CNT :Other bus on the chassis count
C
C           OTHER_BUS :Other bus name
C
C
      SUBROUTINE PROC_ASS(CC,BB,MAS_SLOT,FIRSPASS,
     &                      O_BUS_CNT,OTHER_BUS,BUS_NAME,ERR_PROC)
C
      IMPLICIT NONE
C
        INTEGER*4
     & ERR_PROC       ,!Error proceed counter
     & KK             ,!Loop counter
     & II             ,!Loop counter
     & MM              !Loop counter
C
        INTEGER*2
     & CC             ,!Chassis counter
     & BB             ,!Bus counter
     & PORT_NUM       ,!Port number
     & SLOT_NUM       ,!Slot number
     & CHAS_NUM       ,!Chassis number
     & O_BUS_CNT      ,!Other bus count
     & MAS_SLOT       ,!Master slot number
     & AS_CNT         ,!Assign array pointer
     & POSMIN         ,!Lowest position in the IO section
     & PINDEX         ,!Position of the task in IO section
     & NASLOT(20)     ,!Re-ordered assign slot task number
     & NASSWAY(20)    ,!Re-ordered assign option direction
     & NACHAS(20)     ,!Re-ordered assign chassis number
     & NPOSAS(20)     ,!Re-ordered assign task slot position
     & NIOPOS(20)     ,!Re-ordered assign task position in IO section
     & BUS_MIN(2)     ,!Minimum range number for the port
     & BUS_MAX(2)      !Maximum range number for the port
C
      LOGICAL*2
     & FIRSPASS        !First pass flag to look for other bus
C
      LOGICAL*1
     & DACARD         ,!D/A card flag
     & BELONG         ,!Bus belong to this chassis flag
     & SECPASS        ,!Second pass flag
     & MASTER         ,!Master slot flag
     & FOUND          ,!Task found flag
     & OBUSFOUND       !Other bus found flag
C
      CHARACTER*8
     & BUS_NAME       ,!Bus name
     & OTHER_BUS(3)   ,!Other bus name
     & NASSIGN(20)     !Re-ordered assign task name
C
      INCLUDE 'tsdata.inc'
C
      DATA BUS_MIN /0000,1000/,BUS_MAX/2000,3000/
C
C     *******************************
C     FIND ALL THE TASKS FOR THE PORT
C     *******************************
C
C     Store tasks related to this PORT
C     --------------------------------
      MASTER = .FALSE.
      AS_CNT=0
      KK = 1
      DO WHILE(KK.LE.SLTASK_MAX)
C
         PORT_NUM = TASK_ID(KK)
C
C        Check for special D/A card
C        --------------------------
         IF(PORT_NUM.GT.9999) THEN
            DACARD = .TRUE.
            PORT_NUM = PORT_NUM-10000
         ELSE
            DACARD = .FALSE.
         ENDIF
C
C        If  0<ID<2000 is PORT A  ; 1000<ID<3000 is PORT B
C        -------------------------------------------------
         IF((PORT_NUM.GT.BUS_MIN(BB)).AND.
     &      (PORT_NUM.LT.BUS_MAX(BB))        ) THEN
C
C           Get the chassis number by removing port number
C           ----------------------------------------------
            CHAS_NUM = PORT_NUM - (1000*(BB))
            IF (CHAS_NUM.LT.0) CHAS_NUM=PORT_NUM-(BB-1)*1000
C
C           Check if the bus belong to the chassis processed
C           ------------------------------------------------
            IF(CHAS_NUM.GT.((CC-1)*100).AND.CHAS_NUM.LT.(CC*100)
     &                             )THEN
               BELONG=.TRUE.
C
C              Set the BUSNAME to allow more than 1 bus on each port
C              Also set the ready to go in second pass flag to start
C              looking again from beginning to check in the other
C              chassis if task(s) belong to this bus.
C              -----------------------------------------------------
               IF(FIRSPASS) THEN
                  BUS_NAME=TASK_PORT(KK,BB)
                  SECPASS = .TRUE.
               ENDIF
            ELSE
               BELONG=.FALSE.
            ENDIF
C
            IF (.NOT.FIRSPASS) THEN
C
               IF( BUS_NAME.EQ.TASK_PORT(KK,BB)) THEN
C
C                 ID xii where x is the chassis number and ii the slot number
C                 -----------------------------------------------------------
                  AS_CNT=AS_CNT+1
                  IF(BELONG) THEN
                     ACHAS(AS_CNT) =  CC !In this chassis
                     SLOT_NUM=CHAS_NUM - (CC-1)*100
                  ELSE
                     ACHAS(AS_CNT) = -(CHAS_NUM/100 + 1) !Not in this chassis
                     SLOT_NUM= CHAS_NUM - (CHAS_NUM/100)*100
                  ENDIF
C
C                 Check for MASTER and com way option XILINX
C                 ------------------------------------------
                  IF(IABS(TASK_OPT(KK,BB)).GT.1) THEN
                      IF(.NOT.MASTER) THEN
                         MASTER = .TRUE.
                         MAS_SLOT = SLOT_NUM
                      ELSE
C
C                        More than one master on the BUS, send error
C                        -------------------------------------------
                         CALL MESSAGE(2)
                         WRITE(6,300) ESCAPE,POSDOWN,BUS_NAME
                         ERR_PROC=120
                      ENDIF
                  ENDIF
C
C                 Set the input/output direction by OPTION
C                 TASK_OPT < 0 means input, >0 means output
C                 and = 0 means no XILINX option specified
C                 -----------------------------------------
                  IF (TASK_OPT(KK,BB).GT.0) THEN
                     ASSWAY(AS_CNT) = 1  !Output: set ASSWAY = BANK output
                  ELSEIF (TASK_OPT(KK,BB).LT.0) THEN
                     ASSWAY(AS_CNT) = 2  !Input: set ASSWAY = BANK input
                  ELSE
                     ASSWAY(AS_CNT) = 0  !No option
                  ENDIF
C
                  ASSIGN(AS_CNT) = TASK_NAME(KK)
                  POSASS(AS_CNT) = KK            !Set position in the slot task
C
C                 Inverse slot number if special D/A card
C                 ---------------------------------------
                  IF(DACARD) THEN
                     ASLOT(AS_CNT)=-SLOT_NUM
                  ELSE
                     ASLOT(AS_CNT) = SLOT_NUM
                  ENDIF
C
                  FOUND = .FALSE.
                  DO II=1,IOTASK_MAX
                    IF(TASK_NAME(KK).EQ.IO_TASK(II)) THEN
                      FOUND=.TRUE.
                      IOPOS(AS_CNT) = II
                    ENDIF
                  ENDDO
C
                  IF(.NOT.FOUND)THEN
                    CALL MESSAGE(2)
                    WRITE(6,200) ESCAPE,POSDOWN,TASK_NAME(KK)
                    ERR_PROC=20
                  ENDIF
               ELSEIF(BELONG)THEN
                  OBUSFOUND = .FALSE.
                  DO MM=1,O_BUS_CNT
                    IF(OTHER_BUS(MM).EQ.TASK_PORT(KK,BB)) THEN
                       OBUSFOUND = .TRUE.
                    ENDIF
                  ENDDO
C
                  IF(.NOT.OBUSFOUND)THEN
                    O_BUS_CNT = O_BUS_CNT + 1
                    OTHER_BUS(O_BUS_CNT) = TASK_PORT(KK,BB)
                  ENDIF
C
               ENDIF
            ENDIF
         ENDIF
C
         IF (.NOT.SECPASS) THEN
            KK = KK+1
         ELSE
            FIRSPASS = .FALSE.
            SECPASS = .FALSE.
            KK =1
         ENDIF
      ENDDO
C
      AS_CNT_MAX = AS_CNT  !Set number of element in assignment  array
C
C     *************************************************
C     RE-ORDER THE ASSIGN ARRAY ACCORDING TO IO SECTION
C     *************************************************
C
      DO KK=1,AS_CNT_MAX
C
         POSMIN = IOPOS(1)
         PINDEX = 1
         DO II=1,AS_CNT_MAX
            IF(IOPOS(II).LT.POSMIN) THEN
               POSMIN=IOPOS(II)
               PINDEX=II
            ENDIF
         ENDDO
C
         NASSWAY(KK) = ASSWAY(PINDEX)
         NASSIGN(KK) = ASSIGN(PINDEX)
         NASLOT(KK)  = ASLOT(PINDEX)
         NACHAS(KK)  = ACHAS(PINDEX)
         NIOPOS(KK)  = IOPOS(PINDEX)
         NPOSAS(KK)  = POSASS(PINDEX)
C
         IOPOS(PINDEX) = 101
C
      ENDDO
C
      DO KK=1,AS_CNT_MAX
         ASSWAY(KK) = NASSWAY(KK)
         ASSIGN(KK) = NASSIGN(KK)
         ASLOT(KK)  = NASLOT(KK)
         IOPOS(KK)  = NIOPOS(KK)
         ACHAS(KK)  = NACHAS(KK)
         POSASS(KK) = NPOSAS(KK)
      ENDDO
C
      RETURN
 200  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Unmatched task betwee'
     &        ,'n IO and SLOT section : ',A8,' * SEVERE **')
 300  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** More than one MASTER '
     &        ,'on BUS ',A8,'        **')
      END
C
C
C ===================================================================
C                          PROC_MAT
C ===================================================================
C
      SUBROUTINE PROC_MAT(PORT,ERR_PROC)
C
      IMPLICIT NONE
C
      INCLUDE 'tsdata.inc'
        CHARACTER
     & PORTID(2)*1    ,!Port string id (1-A,2-B)
     & BUFTASK*8       !Task buffer
C
        INTEGER*4
     & ERR_PROC       ,!Error process pointer
     & II             ,!Loop counter
     & KK             ,!Loop counter
     & M_CNT          ,!Look pointer
     & COUNT          ,!Loop counter
     & POINT          ,!Loop pointer
     & JJ              !Loop pointer
C
        INTEGER*2
     & PORT           ,!Port ID (1-A,2-B)
     & OLDSIGN        ,!Old sign variable
     & NEWSIGN        ,!New sign variable
     & INDICE         ,!Special check indice
     & SPECPNT        ,!Special check pointer
     & BUFMAT(20)     ,!Buffer variable for X-Ref array
     & BUFSLOT        ,!Buffer for slot number
     & BUFCHAS        ,!Buffer variable for chassis number
     & PINDEX         ,!Index for re-ordering
     & MASTER         ,!Master slot number
     & INVP           ,!Inverse port number
     & IBANK          ,!I/O inverse pointer
     & BANK           ,!I/O pointer
     & SIGN(2)/1,-1/  ,!Sign constant
     & STORNUMB       ,!Task identification number without port or chassis id
     & CHECKNUMB       !Check number counter
C
        LOGICAL*1
     & PROVALID       ,!Process valid
     & LOOK           ,!look flag
     & BEFORE         ,!Find before flag
     & ADDROW         ,!Add a row flag
     & BOTH            !No check inverse flag to store both input and output
C
      DATA PORTID /'A','B'/
C
      POINT = 0
C
C     Reset the array to zero
C     -----------------------
      DO II=1,20
         DO KK=1,20
            MATRIX(II,KK) = 0
         ENDDO
      ENDDO
C
C     Create an array of X-Reference Input/output
C     -------------------------------------------
      KK=1
      DO WHILE(KK.LE.IOTASK_MAX)
       II = 1
       DO WHILE(II.LE.AS_CNT_MAX.AND.ERR_PROC.EQ.0)
C
        IF(ASSIGN(II).EQ.IO_TASK(KK) )THEN
         COUNT = 1
C
C        Look if there are input/output on that IO task
C        ----------------------------------------------
         LOOK = .TRUE.
         DO WHILE(LOOK)
C
C           Check for end of look :999 is special end code
C           ----------------------------------------------
            IF(IO_NUMB(1,KK,COUNT).NE.999) THEN
C
               IF(IO_NUMB(1,KK,COUNT).NE.0) THEN
                  BANK = 1  !Check for an output
                  IBANK = 2
               ELSE
                  BANK = 2  !Check for an input
                  IBANK = 1
               ENDIF
C
C              Look for the receiving IO task the assign task position
C              -------------------------------------------------------
               M_CNT=1
               DO WHILE((M_CNT.LE.AS_CNT_MAX).AND.(ASSIGN(M_CNT)
     &            .NE.IO_NAME(BANK,KK,COUNT)))
                  M_CNT=M_CNT+1
               ENDDO
C
               IF (M_CNT.LE.AS_CNT_MAX) THEN
C
C                 Set the flag for the Option bus direction
C                 -----------------------------------------
                  IF(ASSWAY(II).GT.0) THEN
                     IF(ASSWAY(II).EQ.BANK.OR.
     &                  IABS(IO_NUMB(BANK,KK,COUNT)).GT.1000) THEN
                        PROVALID = .TRUE. !Option XILINX - right direction
C                                       or special com double task
                     ELSE
                        PROVALID = .FALSE.!Option XILINX - wrong direction
                     ENDIF
                  ELSE
                     PROVALID = .TRUE. !No option
                  ENDIF
C
                  IF (PROVALID) THEN
C
C                   This IO task belongs to this assign array
C                   Check for code sign
C                   -----------------------------------------
                    IF(IO_NUMB(BANK,KK,COUNT).GT.0)THEN
C
C                     Create a new row in the array
C                     -----------------------------
                      POINT=POINT+1
                      STORNUMB = IO_NUMB(BANK,KK,COUNT)
                    ELSE
C
C                     Change sign if negative and keep in the same row
C                     ------------------------------------------------
                      STORNUMB = -IO_NUMB(BANK,KK,COUNT)
                    ENDIF
C
C                   If code is greater than 500, just store the task one time
C                   ---------------------------------------------------------
                    IF(STORNUMB.GE.1000) THEN
C
C                     Special double com task storage
C                     -------------------------------
                      SPECPNT=1
                      INDICE=COUNT+IOCHECK(BANK,KK,COUNT)
                      DOWHILE(ASSIGN(SPECPNT).NE.
     &                   IO_NAME(IBANK,KK,INDICE).AND.
     &                      SPECPNT.LE.AS_CNT_MAX)
                         SPECPNT=SPECPNT+1
                      ENDDO
                      IF (SPECPNT.LE.AS_CNT_MAX) THEN
                         STORNUMB = STORNUMB -1000
                         MATRIX(M_CNT,POINT)  =
     &                            - STORNUMB *SIGN(BANK)
                      ELSEIF(IO_NUMB(BANK,KK,COUNT).GT.0) THEN
                         POINT=POINT-1
                      ENDIF
                    ELSEIF(STORNUMB .GE.500) THEN
                      STORNUMB = STORNUMB - 500
                      MATRIX(II,POINT)=STORNUMB *SIGN(BANK)
                    ELSE
                      MATRIX(II,POINT)=STORNUMB *SIGN(BANK)
                      MATRIX(M_CNT,POINT)  =
     &                           - STORNUMB *SIGN(BANK)
                    ENDIF
C
                  ELSE
C
C                   Declare invalid only if there is not 2 different BUS
C                   for communication between the 2 tasks
C                   ----------------------------------------------------
                    INVP = 2-PORT/2
                    IF(TASK_PORT(POSASS(II),INVP).NE.
     &                  TASK_PORT(POSASS(M_CNT),INVP)  )THEN
C
C                      Invalid com direction between OPTION and IO section
C                      ---------------------------------------------------
                       ERR_PROC = 101
                      LOOK = .FALSE.
                      CALL MESSAGE(2)
                      WRITE(6,210) ESCAPE,POSDOWN,ASSIGN(II),
     &                           PORTID(PORT)
                    ENDIF
                  ENDIF
               ENDIF
C
               COUNT=COUNT+1
            ELSE
               LOOK = .FALSE.  !End of this assign task, no more check
            ENDIF
C
         ENDDO
C
        ENDIF
        II = II + 1
C
       ENDDO
       KK = KK+1
      ENDDO
C
C     Store the dimension of the array
C     --------------------------------
      MAT_DIM(1) = II - 1
      MAT_DIM(2) = POINT
C
      IF (ERR_PROC.EQ.0) THEN
C
C      ***********************************************************************
C      CHECK IF ALL THE LINE OF THE MATRICE IS THE SAME MODE (INPUT or OUTPUT)
C      ***********************************************************************
C
       II = 1
       DO WHILE(II.LE.MAT_DIM(1).AND.ACHAS(II).GT.0)
          OLDSIGN = 0
          DO KK=1,MAT_DIM(2)
             IF(MATRIX(II,KK).NE.0)THEN
                IF(MATRIX(II,KK).LT.0)THEN
                  NEWSIGN=-1
                ELSE
                  NEWSIGN=1
                ENDIF
                IF(NEWSIGN+OLDSIGN.EQ.0)THEN
                   CALL MESSAGE(2)
                   WRITE(6,201) ESCAPE,POSDOWN,
     &                         IABS(ACHAS(II)),IABS(ASLOT(II))
                   ERR_PROC=31
                ENDIF
                OLDSIGN=NEWSIGN
             ENDIF
          ENDDO
          II= II +1
       ENDDO
C
C      *****************************************
C      RE-ORDER THE X-REF ARRAY IN CHASSIS ORDER
C      *****************************************
C
       PINDEX = 1
       DO II= 1,MAT_DIM(1)
C
C         Store all task which belong to this chassis first
C         -------------------------------------------------
          IF(ACHAS(II).GT.0) THEN
            DO KK=1,MAT_DIM(2)
               BUFMAT(KK) = MATRIX(II,KK)
            ENDDO
            BUFCHAS = ACHAS(II)
            BUFSLOT = ASLOT(II)
            BUFTASK = ASSIGN(II)
             DO KK=II-1,PINDEX,-1
               DO JJ=1,MAT_DIM(2)
                  MATRIX(KK+1,JJ) = MATRIX(KK,JJ)
               ENDDO
               ACHAS(KK+1)=ACHAS(KK)
               ASLOT(KK+1)=ASLOT(KK)
               ASSIGN(KK+1) = ASSIGN(KK)
            ENDDO
            DO KK=1,MAT_DIM(2)
               MATRIX(PINDEX,KK) = BUFMAT(KK)
            ENDDO
            ACHAS(PINDEX) = BUFCHAS
            ASLOT(PINDEX) = BUFSLOT
            ASSIGN(PINDEX) = BUFTASK
            PINDEX = PINDEX+1
          ENDIF
       ENDDO
C
C      Store all the task in the other chassis at the end
C      --------------------------------------------------
       DO WHILE(PINDEX.LE.MAT_DIM(1))
           DO II=PINDEX,MAT_DIM(1)
              IF(ACHAS(II).GE.ACHAS(PINDEX)) THEN
                DO KK=1,MAT_DIM(2)
                   BUFMAT(KK) = MATRIX(II,KK)
                ENDDO
                BUFCHAS = ACHAS(II)
                BUFSLOT = ASLOT(II)
                BUFTASK = ASSIGN(II)
                DO KK=II-1,PINDEX,-1
                  DO JJ=1,MAT_DIM(2)
                    MATRIX(KK+1,JJ) = MATRIX(KK,JJ)
                  ENDDO
                  ACHAS(KK+1)=ACHAS(KK)
                  ASLOT(KK+1)=ASLOT(KK)
                  ASSIGN(KK+1) = ASSIGN(KK)
                ENDDO
                DO KK=1,MAT_DIM(2)
                  MATRIX(PINDEX,KK) = BUFMAT(KK)
                ENDDO
                ACHAS(PINDEX) = BUFCHAS
                ASLOT(PINDEX) = BUFSLOT
                ASSIGN(PINDEX) = BUFTASK
                PINDEX = PINDEX+1
              ENDIF
           ENDDO
       ENDDO
      ENDIF
C
      RETURN
 201  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Slot wth input and ou'
     &        ,'tput on the same BUS: ch#',I2,',sl#',I2,'* SVR *')
 210  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR-Mismatch between Slot O'
     &        ,'PTION and IO section,task:',A8,' PORT ',A1)
      END
