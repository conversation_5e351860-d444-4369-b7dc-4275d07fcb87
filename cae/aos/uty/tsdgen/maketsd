INCLUDE = tsdata.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
tsdgen: tsdgen.o tsdunix.o tsdproc.o tsdread.o tsdwrite.o tsdlib.o \
$(CAELIB)/libcae.a $(LIBDIR)/libaos.a
#
#
	xlf -C -qcharlen=1024 tsdgen.o tsdunix.o tsdproc.o tsdread.o \
tsdwrite.o tsdlib.o -L$(CAELIB) -lcae -lc \
-L$(LIBDIR) -laos -o $(EXEDIR)/tsdgen
#
#
tsdgen.o: tsdgen.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdgen.f
#
tsdunix.o: tsdunix.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdunix.f
#
tsdproc.o: tsdproc.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdproc.f
#
tsdread.o: tsdread.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdread.f
#
tsdwrite.o: tsdwrite.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdwrite.f
#
tsdlib.o: tsdlib.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tsdlib.f
#
