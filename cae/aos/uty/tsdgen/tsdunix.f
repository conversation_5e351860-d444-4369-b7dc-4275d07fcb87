C
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C              THESE FILES INCLUDES ALL IO SUBROUTINES
C                       USED IN TSDGEN UTILITY
C
C                         FIL_OPEN
C                         IO_R_W: IO_READ1,IO_READ2,
C                                 IO_WRIT1,IO_WRIT2,IO_WRIT3
C
C
C       Version 0.1       14 September 1987      By: Gaetan De Serre
C               1.0       13 April 1988
C               1.1       13 December 1988
C               1.2       31 July 1990           By: <PERSON>
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C =====================================================
C                       FIL_OPEN
C =====================================================
C
C  This subroutine open and close allthe files used
C  in TSDGEN utility.
C
      SUBROUTINE FIL_OPEN(FILE,OPER,IERR)
      IMPLICIT NONE
C
       INTEGER*4
     &  FILE             ,!File identification number
     &  OPER              !Type of operation (1-Open, 2-Close)
C
       INTEGER*4
     &  revstat          ,!Status of rev_curr()
     &  IERR              !Error counter
C
       CHARACTER
     &  N_FILENAME*80    ,!Revised filename
     &  FILENAME*80      ,!File name
     &  MESSA(8)*9        !File ID message for error
C
      DATA MESSA/'DATA FILE','TMP FILE ','INF FILE ','INT FILE ',
     &            'XILINX FL','XILINX DL','UPDAT.LOG','SOUND.LOG'/
C
      INCLUDE 'tsdata.inc'
C
      IERR = 0
C
      IF (FILE.EQ.1) THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open DATA file
C          --------------
           FILENAME = DATA_DIR(1:L_DATA_DIR)//INPDATFIL(1:INPLEN)
           CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           DATFILNAM = N_FILENAME
           OPEN(UNIT=1,FILE=N_FILENAME,STATUS='OLD',IOSTAT=IERR)
         ELSE
C
C          Close DATA file
C          ---------------
           CLOSE(UNIT=1)
         ENDIF
C
      ELSEIF(FILE.EQ.2) THEN
C
         IF (OPER.EQ.1) THEN
C
C           Open temporary storage file
C           ---------------------------
            OPEN(UNIT=2,FILE='TSDGEN.TMP',ACCESS='DIRECT',IOSTAT=IERR,
     &           FORM='UNFORMATTED',RECL=1200,STATUS='NEW')

         ELSE
C
C           Close temporary storage file
C           ----------------------------
            CLOSE(UNIT=2,STATUS='DELETE')
         ENDIF
C
      ELSEIF(FILE.EQ.3)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open .INF file
C           --------------
            FILENAME = INT_DIR(1:L_INT_DIR)//INPDATFIL
     &                   (1:(INPLEN-4))//'.inf'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            INFFILNAM = N_FILENAME
            OPEN(UNIT=3,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR)
         ELSE
C
C           Close .INF file
C           ---------------
            CLOSE(UNIT=3)
         ENDIF
      ELSEIF(FILE.EQ.4)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open .INT file
C           --------------
            FILENAME = INT_DIR(1:L_INT_DIR)//Config_S(9)
     &                 (1:Config_L(9))//Filetters(1:2)//'cs.int'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            DLDFILNAM = N_FILENAME
            OPEN(UNIT=4,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR)
         ELSE
C
C           Close .INT file
C           ---------------
            CLOSE(UNIT=4)
         ENDIF
C
      ELSEIF(FILE.EQ.5)THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open XILINX DATA file
C          ---------------------
           FILENAME = DATA_DIR(1:L_DATA_DIR)//'xil'//
     &                Filetters(1:2)//'c.dat'
           CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           OPEN(UNIT=11,FILE=N_FILENAME,STATUS='OLD',IOSTAT=IERR)
         ELSE
C
C          Close XILINX DATA file
C          ----------------------
           CLOSE(UNIT=11)
         ENDIF
      ELSEIF(FILE.EQ.6)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open .INT file
C           --------------
            FILENAME = INT_DIR(1:L_INT_DIR)//Config_S(9)
     &                 (1:Config_L(9))//Filetters(1:2)//'cx.int'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            DLXFILNAM = N_FILENAME
            OPEN(UNIT=12,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR)
         ELSE
C
C           Close .INT file
C           ---------------
            CLOSE(UNIT=12)
         ENDIF
      ENDIF
C
      IF (IERR.NE.0.AND.FILE.NE.1) THEN
C
C        Write error message during IO operation
C        ---------------------------------------
         CALL MESSAGE(2)
         WRITE(6,100) ESCAPE,POSDOWN,MESSA(FILE)
C
      ENDIF
C
      RETURN
 100  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** ERROR DURING OPENING/C'
     &        ,'LOSING OF ',A9,'        * SEVERE **')
      END
C
C =====================================================================
C                             IO_R_W
C =====================================================================
C
C
C     This subroutine perform all I/O on
C     unit 1,2 and 3
C
      SUBROUTINE IO_R_W
      IMPLICIT NONE
      CHARACTER
     &  DATLINE*43           ,!Download file write data
     &  BUSNAME*8            ,!Bus name
     &  P_LINE*80             !Information file data line
C
      INTEGER*2
     &  CODE                 ,!Array code
     &  MSLOT                ,!Master slot number
     &  RLEN                  !Length of record for download file
C
      INTEGER*4
     &  RECN                 ,!Record number
     &  IERR                 ,!Error counter
     &  I                    ,!Loop counter
     &  J                     !Loop counter
C
      INCLUDE 'tsdata.inc'
C
C
C     ***************
C     READ STATEMENTS
C     ***************
C
C     DATA file
C     ---------
      ENTRY IO_READ1(IERR)
C
      READ(1,100,IOSTAT=IERR) LINE
 100  FORMAT(A)
      RETURN
C
C     Temporary storage file
C     ----------------------
      ENTRY IO_READ2(RECN,CODE,MSLOT,BUSNAME,IERR)
C
      READ(2,REC=RECN,IOSTAT=IERR)
     -       CODE,BUSNAME,MSLOT,(ASLOT(I),I=1,20),(ASSIGN(I),I=1,20),
     -       ((MATRIX(I,J),I=1,20),J=1,20),(ACHAS(I),I=1,20),
     &       (MAT_DIM(I),I=1,2)
      RETURN
C
C     ****************
C     WRITE STATEMENTS
C     ****************
C
C     Temporary storage file
C     ----------------------
      ENTRY IO_WRIT1(RECN,CODE,MSLOT,BUSNAME,IERR)
C
      WRITE(2,REC=RECN,IOSTAT=IERR)
     -       CODE,BUSNAME,MSLOT,(ASLOT(I),I=1,20) ,(ASSIGN(I),I=1,20),
     -       ((MATRIX(I,J),I=1,20),J=1,20),(ACHAS(I),I=1,20),
     &       (MAT_DIM(I),I=1,2)
      NB_REC = RECN
      RETURN
C
C     Information file
C     ----------------
      ENTRY IO_WRIT2(P_LINE,IERR)
C
      WRITE(3,110,IOSTAT=IERR) P_LINE(1:80)
 110  FORMAT(A)
      RETURN
C
C     Download file
C     -------------
      ENTRY IO_WRIT3(RLEN,DATLINE,IERR)
C
      WRITE(4,120,IOSTAT=IERR) DATLINE(1:RLEN)
 120  FORMAT(A)
      RETURN
C
      END
C
C
C     ======================
      SUBROUTINE COMPUTER_ID
C     ======================
C
C     This routine identified the computer
C
      IMPLICIT NONE
C
      INCLUDE 'tsdata.inc'
C
      VAXSEL = .TRUE.   !VAX
C
      RETURN
      END
