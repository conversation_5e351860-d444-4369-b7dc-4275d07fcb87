C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  TSDGEN UTILITY PROGRAM :  This utility processes a high level
C                            TSD BUS configuration and generates
C                            specific download files.
C
C  V0.1   14 Sep 87   FIRST DEBUG REVISION             Gaetan De Serre
C
C  V1.0   13 Apr 88   INITIAL RELEASE                  Gaetan De Serre
C
C  V1.1   13 Dec 88   INSTALL NEW XLINK DRIVER         Gaetan De Serre
C
C  V1.2    3 Mar 90   FIX BUG WITH MORE THAN 12        Gaetan De Serre 
C                     DAC WORDS
C
C  V1.3   31 Jul 90   MODIFICATIONS FOR COMBINED       Pierre Daigle
C                     AUDIO & SOUND UTILITIES
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
      PROGRAM TSDGEN
C
      IMPLICIT NONE
C
        CHARACTER*2
     & QPOS /'19'/                 ,!Question position
     & QLEN /'34'/                  !Question length
C
        CHARACTER*40
     & FILEDATA                   ,!DATA File name
     & FILEINF                    ,!information file name
     & FILEDLD                     !Download file name
C
        CHARACTER*38
     & QUESTION(2)                 !Question for terminal prompt
        CHARACTER*80
     & Tmp_Str
C
        CHARACTER*8
     & ANSWER           /'YES     '/! Check answer character constant
C
        INTEGER*4
     & WORD_NM1                      ,!Number of word from terminal answer
     & WORD_NM2                      ,!Number of word from terminal answer
     & STAT                          ,!Status
     & IERR                          ,!Data file opening error
     & ERR_READ                      ,!Read error counter
     & ERR_PROC                      ,!Process error counter
     & ERR_DLD                       ,!DOWNLOAD file error counter
     & ERR_INF                        !Information file error counter
C
        INTEGER*2
     & CNT                           ,!Counter for length of input
     & QSA                           ,!Initialisation loop counter
     & RSA                           ,!Initialisation loop counter
     & TSA                            !Initialisation loop counter
C
        INTEGER*4
     & LINE_CNT                      ,!Number of valid line in DATA file
     & Enable(3)                     ,
     & Len_Str
C
        LOGICAL*1
     & STUPFLG /.TRUE./              ,!SEL stupid flag
     & ERRSEVR                        !Severe error flag
C
      INCLUDE 'tsdata.inc'     !INITIALISATION/DATA FILE
C
      DATA QUESTION /'Do you want the INFORMATION file [Y]? '
     &              ,'Do you want the   DOWNLOAD  file [Y]? '/
C
C     Initialization for the library routines
C     ---------------------------------------
      CALL Init_Libgd(1,3)
C
C - Initialise all the task variables
C
      DO RSA=1,MXIOTK
         TASK_ID(RSA) = 0
         DO QSA=1,2
            DO TSA=1,MXIOTKIN
               IO_NAME(QSA,RSA,TSA)='  '
               IO_NUMB(QSA,RSA,TSA)=0
               IOCHECK(QSA,RSA,TSA)=0
C
            END DO
         END DO
      END DO
      DO QSA = 1,MXSLOTK
         OPCODE(QSA) = 0
         TASK_OPT(QSA,1) = 0
         TASK_OPT(QSA,2) = 0
      ENDDO
C
      CALL COMPUTER_ID
C
      ESCAPE = CHAR(27)//'['                  !Set ESCAPE in ASCII
C
C - Set the standalone flag for sound or sonar
C
      STDLONE = .FALSE.
C
      IF (.NOT.STDLONE) THEN
C
C - If not in standalone mode: read the parameter from SOUND
C   logical name file.  Call subroutine to decode directory
C   and shipname .
C
C        OUTPUT: Config_S(9)=SHIPID
C                Config_S(2)=SIMEXDIR
C                Config_S(1)=LIBRARY DIR
C                Config_S(3)=SHIPDIR
C                LinkFlag(1)=ON SITE FLAG
C
C
        CALL XLINK_READ(Config_S,Config_L,DMC_Num,Page_Num,Filetters,
     &                  Comp_Id,LinkFlag,ComFlag,File_N,File_Len,IERR)
C
        IF (IERR.NE.0) THEN
C
C          Error opening LOG file: quit TSDGEN
C          -----------------------------------
           WRITE(6,5)
           CALL EXIT
        ENDIF
C
      ELSE
C
C - Set the sound directory to null for standalone mode
C
        Config_S(1)=' '
        Config_L(1)= 0
      ENDIF
C
      T_ECHO = .FALSE.   !set No Echo on  terminal
C
C     ========== Call header routine for utility ID ==========
C
      CALL HEADER               !FILE: TSDLIB.FOR
C
C     ========== Ask the data file name ======================
C
      IF (Filetters(1:2).EQ.'sn') THEN
         DATA_DIR = Config_S(1)(1:Config_L(1))//'sound/data/'
         INT_DIR = Config_S(1)(1:Config_L(1))//'sound/inter/'
CC         WRITE(6,50) ESCAPE,'6'
      ELSE
         DATA_DIR = Config_S(1)(1:Config_L(1))//'audio/data/'
         INT_DIR = Config_S(1)(1:Config_L(1))//'audio/inter/'
CC         WRITE(6,52) ESCAPE,'6'
      ENDIF
      L_DATA_DIR = Config_L(1)+11
      L_INT_DIR = Config_L(1)+12
C
      Tmp_Str = 'Enter DATA filename ['//File_N(2)(1:File_Len(2))//']?'
      Len_Str = 21 + File_Len(2) + 2
      CALL T_WRITE(16,1,Tmp_Str,Len_Str)
      READ(5,'(A40)') INPDATFIL
      CNT = LEN(INPDATFIL)
      DO WHILE(INPDATFIL(CNT:CNT).EQ.' '.AND.CNT.GT.0)
         CNT=CNT-1
      ENDDO
      INPLEN = CNT
      IF(INPLEN.EQ.0)THEN
        INPLEN=File_Len(2)
        INPDATFIL = File_N(2)
      ENDIF
      WRITE(6,51) ESCAPE,'6'
C
C     ========== Call subroutine to open data file ===========
C
      CALL FIL_OPEN(1,1,IERR)        !FILE: TSDIO.FOR
C
C     ========== Execute processing file only if no open error ========
C
      IF (IERR.EQ.0) THEN
C
C        ===== No error condition ====
C
         CALL MESSAGE(1)  !Find the position for the message
         WRITE(6,100) ESCAPE,POSUP,DATFILNAM ! Send no error message 
C                                              and filename
C
C        Read all data file and check if error
C        -------------------------------------
         CALL READ_DATA(ERR_READ,LINE_CNT)   !File TSDREAD.FOR
C
         IF (ERR_READ.EQ.0) THEN
C
C           Send # of line read and continue to process
C           -------------------------------------------
            CALL MESSAGE(1)
            WRITE(6,200) ESCAPE,POSUP,LINE_CNT
C
C           If no error, proceed data
C           -------------------------
            CALL PROC_DATA(ERR_PROC)          !File TSDPROC.FOR
C
            IF (ERR_PROC.EQ.0) THEN
C
C              Inquire if user wants to create INF file
C              ----------------------------------------
               STAT = -1
               DO WHILE(STAT.NE.0)
                  CALL T_WRITE(18,1,QUESTION(1),38)
                  CALL T_READ(0,LINE,LLINE,STAT)
               ENDDO
C
               IF(LINE(1:LLINE).EQ.ANSWER(1:LLINE).OR.LLINE.EQ.0) THEN
C
C                 Create *.INF file
C                 -----------------
                  CALL FIL_OPEN(3,1,IERR)
                  IF (IERR.EQ.0) THEN
                     CALL FILE_INF(ERR_INF)
                     IF(ERR_INF.EQ.0) THEN
                        CALL MESSAGE(1)
                        WRITE(6,500) ESCAPE,POSUP,INFFILNAM
                     ELSE
                        ERRSEVR = .TRUE.
                     ENDIF
                  ELSE
                     ERRSEVR=.TRUE.
                  ENDIF
               ENDIF
C
C              Inquire if user wants to create DLD file
C              ----------------------------------------
               STAT = -1
               DO WHILE(STAT.NE.0)
CC                  CALL TERM_READ(QUESTION(2),QLEN,WORD_NM2,QPOS,STUPFLG)  !File TSSDLIB.FOR
                  CALL T_WRITE(19,1,QUESTION(2),38)
                  CALL T_READ(0,LINE,LLINE,STAT)
               ENDDO
C
               IF(LINE(1:LLINE).EQ.ANSWER(1:LLINE).OR.LLINE.EQ.0) THEN
C
C                   Create the download file
C                   ------------------------
                    CALL FIL_OPEN(4,1,IERR)
                    IF(IERR.EQ.0) THEN
                      CALL FILE_DLD(*1001)
                      CALL MESSAGE(1)
C
                      IF (.NOT.STDLONE) THEN
C
C                       Set update flag TRUE for FORMGEN
C                       --------------------------------
                        IF (ComFlag(1)) THEN
                           ComFlag(2) = .TRUE.
                           Enable(1) = 0
                           Enable(2) = 1
                           Enable(3) = 0
                           CALL XLINK_WRITE(Enable,ComFlag,IERR)
                           IF (IERR.NE.0) THEN
C
C                             Error opening LOG file: quit TSDGEN
C                             -----------------------------------
                              CALL MESSAGE(2)
                              WRITE(6,10) ESCAPE,POSDOWN
                           ENDIF
                        ENDIF
                      ENDIF
C
                      WRITE(6,501) ESCAPE,POSUP,DLDFILNAM
                      IF (OPTION) THEN
                         CALL MESSAGE(1)
                         WRITE(6,501) ESCAPE,POSUP,DLXFILNAM
                      ENDIF
                    ELSE
                      ERRSEVR = .TRUE.
                    END IF
               ENDIF
C
            ELSE
               ERRSEVR=.TRUE.
            ENDIF
C
         ELSE
            ERRSEVR=.TRUE.
         ENDIF
C
C        Close DATA ,INF and Temporary storage files
C        -------------------------------------------
         CALL FIL_OPEN(1,2,IERR)
         CALL FIL_OPEN(2,2,IERR)
         CALL FIL_OPEN(3,2,IERR)
         CALL FIL_OPEN(4,2,IERR)
         IF (OPTION) CALL FIL_OPEN(6,2,IERR)
C
      ELSE
C
C        Open DATA file error : Fatal error
C        ----------------------------------
         ERRSEVR=.TRUE.
         CALL MESSAGE(2)
         WRITE(6,1000) ESCAPE,POSDOWN,DATFILNAM
C
      ENDIF
C
C     Write exit message on the screen according to error condition
C     -------------------------------------------------------------
      CALL MESSAGE(1)
      IF(ERRSEVR) THEN
        WRITE(6,300) ESCAPE,POSUP
        GOTO 1002
      ELSE
        WRITE(6,301) ESCAPE,POSUP
        GOTO 1002
      ENDIF
 1001 CALL MESSAGE(1)
      WRITE(6,300) ESCAPE,POSUP
 1002 WRITE(6,305) ESCAPE
      CALL Wait_Time(3.0)
      CALL EXIT                  !Return to DCL/TSM level
C
    5 FORMAT('$','    %ERR - ** Error when reading LOG name file')
   10 FORMAT('$',A2,'1',A1,';1H',2X,'%ERR - ** Error when writing to LO'
     &       ,'G name file')
   50 FORMAT('$',A2,'1',A1,';1H',2X,'Enter DATA filename [TSDSNC]?')
   51 FORMAT('$',A2,'1',A1,';1H',75X)
   52 FORMAT('$',A2,'1',A1,';1H',2X,'Enter DATA filename [TSDRFC]?')  
  100 FORMAT(' ',A2,'1',A1,';1H',2X,'** TSDGEN using data file '
     &       ,A46,' **')
  200 FORMAT(' ',A2,'1',A1,';1H',2X,'%READ- **               ',I5,
     &       ' Lines read successfully                     **')
  300 FORMAT(' ',A2,'1',A1,';1H',2X,'%EXIT- **        END OF PROCESSING'
     &       ,' WITH ERROR    -       TSDGEN           **')
  301 FORMAT(' ',A2,'1',A1,';1H',2X,'%EXIT- **            END OF PROCES'
     &       ,'SING - TSDGEN                           **')
  305 FORMAT(' ',A2,'25;1H')
  500 FORMAT(' ',A2,'1',A1,';1H',2X,'** Information file ',A46,
     &       'created **')
  501 FORMAT(' ',A2,'1',A1,';1H',2X,'** Download file ',A46,
     &       'created   **')
 1000 FORMAT(' ',A2,'2',A1,';1H',2X,'%ERR - ** Error when opening DATA '
     &       ,'FILE ',A40)
C
      END
