C
C     >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                    FILE     ** TSDLIB.FOR **
C
C                LIBRARY SUBROUTINES OF TSDGEN UTILITY
C
C     Contains:
C                     LOW_UPR   ;
C                     TERM_READ1 ;
C                     READQ     ;
C                     HEADER    ;
C                     CAR_CMP   ;
C                     MAT_OPER  ;
C                     CAR_CHG   ;
C                     MESSAGE   ;
C     >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C =================================================
C                 LOW_UPR
C =================================================
C
C    This subroutine transforms all lower case caracters
C to upper case caracters.
C
C     INPUT : LINE with upper case and lower case character
C
C     OUTPUT : LINE with only upper case characters
C
C ............................................................
C
      SUBROUTINE LOW_UPR
C     ====================================
      IMPLICIT NONE
C
      INCLUDE 'tsdata.inc'
C
       INTEGER*2
     &  DELTA           ,!Difference between upper case and lower case
     &  I
C
      LOGICAL
     &  READ_F/.TRUE./  !First read pass flag
C
C     First pass
C     ----------
C     If necessary, get the difference between 'a' and 'A'
C

      IF (READ_F) THEN
        READ_F = .FALSE.
        DELTA = ICHAR('a') - ICHAR('A')
      ENDIF
C
C     If no character in the line, leave subroutine
C     ---------------------------------------------
      IF (LLINE.EQ.0) RETURN
C
C     Translate to UPPER CASE in place
C     --------------------------------
      DO I = 1,LLINE
        IF (('a'.LE.LINE(I:I)).AND.(LINE(I:I).LE.'z')) THEN
          LINE(I:I) = CHAR(ICHAR(LINE(I:I))-DELTA)
        ENDIF
      ENDDO
      RETURN
      END
C
C ===========================================================
C                     TERM_READ1
C ===========================================================
C
C    This subroutine write a question (CTL_NAME), read the answer
C  and performs some modification like translation in upper case
C  caracter via LOW_UPR subroutine.
C
      SUBROUTINE TERM_READ1(CTL_NAME,CTL_LEN,K,MPOS,RTN)
      IMPLICIT NONE
C
       INTEGER*4
     &  IERR         ,!Error logger
     &  K            ,!Loop counter
     &  I             !Loop counter
C
       LOGICAL*1
     &  F_SPACE      ,!First space found flag
     &  FLAG         ,!
     &  RTN          ,!Return flag
     &  COMMA         !Parenthesis IN/OUT flag
C
       CHARACTER
     &  CTL_NAME*34  ,!Character variable to be  display to the terminal
     &  CTL_LEN*2    ,!Length of character variable
     &  FMAT*25      ,!Format character
     &  MPOS*2        !Screen position(Vertical) to display line
C
      INCLUDE 'tsdata.inc'
C
C     Set the format for the write
C     ----------------------------
      FMAT = '(1H$,A2,A2,'';12H'',A'//CTL_LEN//',1X)'
C
 5    CONTINUE
C
C     Write the message
C     -----------------
C
      WRITE(6,FMAT)ESCAPE,MPOS,CTL_NAME
C
C     Read the answer
C     ---------------
      CALL READQ(1,IERR)
C
      WRITE(6,155) ESCAPE,MPOS
 155  FORMAT(' ',A2,A2,';1H',80(' '))
      IF (LINE(1:1).EQ.'!'.OR.LINE(1:1).EQ.'$'
     -    .OR.LINE(1:1).EQ.'*') GOTO 5
C
C     Transform in all upper case caracters
C     -------------------------------------
      CALL LOW_UPR
C
C     If nothing in the answer, try again or leave depending on RTN status
C     --------------------------------------------------------------------
      IF (LLINE.EQ.0) THEN
        IF (RTN) RETURN
        GOTO 5
      ENDIF
C
      K = 1
      F_SPACE = .FALSE.
      COMMA   = .TRUE.
C
C     Find each word in the answer : K= # of words, STRG(i)= words
C     ------------------------------------------------------------
      DO I = 1,LLINE
        IF (LINE(I:I).EQ.'(') COMMA = .FALSE.
        IF (LINE(I:I).EQ.')') COMMA = .TRUE.
        IF((LINE(I:I).EQ.','.AND.COMMA).OR.
     -     (LINE(I:I).EQ.'=').OR.
     -     (LINE(I:I).EQ.' '.AND.F_SPACE) ) THEN
          K = K + 1
          F_SPACE = .FALSE.
          IF (K.GT.4) RETURN
        ELSE
            L_STRG(K) = L_STRG(K) + 1
            STRG(K)(L_STRG(K):L_STRG(K)) = LINE(I:I)
            F_SPACE = .TRUE.
        ENDIF
      END DO
      RETURN
 180  FORMAT(' ',A2,'2',A1,';1H',2X,'%WAR - ** TOO MANY INPUT SPECIFIED'
     &        ,T67,'* WARNING **')
C
      END
C
C =========================================================
C                         READQ
C =========================================================
C
C    This subroutine read a line from the terminal or the data file
C  and give the answer without any blanks at the end
C
      SUBROUTINE READQ(BANK,ERR)
      IMPLICIT NONE
C
       INTEGER*4
     &  BANK          ,!Which unit to read
     &  ERR            !Error counter
C
      INCLUDE 'tsdata.inc'
C
      IF (BANK.EQ.1) THEN
         READ(5,1)LINE  !Read a line from terminal
      ELSE
         CALL IO_READ1(ERR) !Read a line from data file  !File TSDIO.FOR
      ENDIF
C
      LLINE = 132
 10   IF(LINE(LLINE:LLINE).EQ.' '.AND.LLINE.NE.0) THEN
        LLINE = LLINE - 1
        GOTO 10
      ENDIF
      RETURN
 1    FORMAT(A)
      END
C
C ===============================================================
C                            HEADER
C ===============================================================
C
      SUBROUTINE HEADER
      IMPLICIT NONE
C
       CHARACTER
     &  DDATE*9         ,!Date in an array
     &  DTIME*11         !Time in an array
C
      INCLUDE 'tsdata.inc'
C
      CALL CDATE(DDATE,DTIME)
C
C     Write header to terminal
C     ------------------------
      WRITE(6,1)ESCAPE,ESCAPE,ESCAPE,DDATE
      WRITE(6,2)ESCAPE,ESCAPE
 1    FORMAT(' ',A2,'2J',A2,'4;27H',29('*')/26X,'*',T35,
     &       'T S D B U S ',T55,'*',/26X,'* C O N F I G U R A T I O N *'
     &       ,/26X,'*',T34,'U T I L I T Y',T55,'*',/26X,29('*'),A2,
     &       '10;35H','VERSION 1.1',/10X,/27X,'DATE OF THIS RUN: ',A)
 2    FORMAT(1X,A2,'15;1H',35('='),'<STATUS>',36('='),/1H0,A2,'20;1H'
     &       ,80('-'))
C
C
      RETURN
      END
C
C
C ====================================================================
C                            CAR_CMP
C ====================================================================
C
C   This subroutine transform a 60 characters string into a 8 characters
C   string with no blank at the beginning and at the end. It also set
C   all characters into UPPERCASE.
C
      SUBROUTINE CAR_CMP(CAR_INP,CAR_OUT)
C
      IMPLICIT NONE
C
       INTEGER*4
     &  I        ,!Loop counter
     &  V_LONG   ,!Variable length of character string
     &  LONG     ,!Length of character string
     &  DELTA     !Difference between upper case and lower case
C
       CHARACTER*(*)
     &  CAR_INP  ,!Character string on input
     &  CAR_OUT   !Character string on output
C
       LOGICAL
     &  READ_F/.TRUE./  !First read pass flag
C
      LONG = LEN(CAR_INP)
C
      IF (LONG.NE.0) THEN
C
C        Remove all blank character at the beginning
C        -------------------------------------------
         V_LONG = 1
         DO WHILE (CAR_INP(V_LONG:V_LONG).EQ.' '.AND.V_LONG.NE.LONG)
           V_LONG = V_LONG + 1
         ENDDO
C
C        Create new string array without blank at the beginning
C        ------------------------------------------------------
         CAR_INP(1:LONG+1-V_LONG)=CAR_INP(V_LONG:LONG)
C
C        Remove all blank character at the end
C        -------------------------------------
         V_LONG = LONG+1-V_LONG
         LONG = LEN(CAR_INP)
         DO WHILE (CAR_INP(V_LONG:V_LONG).EQ.' '.AND.V_LONG.NE.1)
            V_LONG = V_LONG - 1
         ENDDO
C
         CAR_OUT = CAR_INP(1:V_LONG)
C
      ELSE
         CAR_OUT(1:1) = '        '
      ENDIF
C
C     Translate to UPPER CASE in place
C     --------------------------------
      IF (READ_F) THEN
         READ_F=.FALSE.
         DELTA = ICHAR('a')-ICHAR('A')
      ENDIF
C
      DO I = 1,V_LONG
        IF (('a'.LE.CAR_OUT(I:I)).AND.(CAR_OUT(I:I).LE.'z')) THEN
          CAR_OUT(I:I) = CHAR(ICHAR(CAR_OUT(I:I))-DELTA)
        ENDIF
      ENDDO
C
      RETURN
      END
C
C
C ========================================================
C                    MAT_OPER
C ========================================================
C
C   This subroutine do all the operation on the input/output
C   to the temporary storage file.
C
C   TMP file contains for each record:
C       CODE : code for the bus 2 first digit is the bus id
C                               (00-49 BUS A , 50-99 BUS B)
C                               3rd digit is the chassis number
C       BUSNAME : Bus name
C       MSLOT : Master slot number
C       ASLOT : Task number of all the bus slot
C       ASSIGN : Task name
C       MATRIX : Matrice of input/output schedule
C       ACHAS : Chassis number of the assign tasks
C       MAT_DIM : Dimension of teh matrix
C
      SUBROUTINE MAT_OPER(RECN,SAV_TYP,CODE,MSLOT,BUSNAME,NERR)
      IMPLICIT NONE
C
       INTEGER*2
     &  MSLOT          ,!Master slot number
     &  CODE            !Code number for this bus
C
       INTEGER*4
     &  RECN           ,!Record number
     &  J              ,!Loop counter
     &  SAV_TYP        ,!Save type on the storage file
     &  I              ,!Loop counter
     &  ERR            ,!Error counter from read/write operation
     &  NERR            !Error counter-main
C
       CHARACTER*8
     &  BUSNAME         !Bus name
C
       LOGICAL
     &  F_TIME/.TRUE./  !First pass flag
C
      INCLUDE 'tsdata.inc'
C
      IF (F_TIME) THEN
C
C        Open array temporary storage file
C        ---------------------------------
         CALL FIL_OPEN(2,1,ERR)  !File TSDIO.FOR
         F_TIME = .FALSE.
      ENDIF
C
      IF (SAV_TYP.EQ.1) THEN
C
C       Save the array into TMP file
C       ----------------------------
        CALL IO_WRIT1(RECN,CODE,MSLOT,BUSNAME,ERR)        !File TSDIO.FOR
C
      ELSE IF (SAV_TYP.EQ.2) THEN
C
C       Get the array in TMP file
C       -------------------------
        CALL IO_READ2(RECN,CODE,MSLOT,BUSNAME,ERR)      !File TSDIO.FOR
C
      ENDIF
C
      IF (ERR.NE.0) NERR = ERR+500
C
      RETURN
C
      END
C
C
C =====================================================================
C                               CAR_CHG
C =====================================================================
C
C      These subroutine transforms number stored in character array in
C     ASCII format to decimal or hexadecimal number and vice-versa.
C
C           1-From decimal to ASCII character
C           2-ASCII character to decimal
C           3-Decimal to ASCII hexadecimal
C           4-ASCII character to hexadecimal
C
      SUBROUTINE CAR_CHG(BANK,DECIM,CARAC1,CARAC2)
C
      IMPLICIT NONE
C
      CHARACTER
     &  CARAC2*2         ,!Character value for input(CHA to DEC)
     &  CARAC1(4)*1       !Character value for output(DEC to CHA)
C
       INTEGER*2
     &  DECIM            ,!Decimal number
     &  CODE             ,!Decimal digit
     &  NDECIM           ,!Stored decimal value
     &  ASCII            ,!ASCII number
     &  BUFF(4)           !Buffer
C
       INTEGER*4
     &  BANK             ,!Type of operation
     &  II                !Loop counter
C
       LOGICAL*1
     &  ZERO              !Zero before flag
C
      IF (BANK.EQ.1) THEN
C
C        Change from decimal to ASCII
C        ----------------------------
         NDECIM = DECIM
         IF (NDECIM.GE.9999) THEN
            NDECIM = NDECIM-((DECIM/10000)*10000)
         ENDIF
C
         ZERO = .FALSE.
         DO II=1,4
             CODE = NDECIM/(10**(4-II))
             NDECIM = NDECIM -CODE*(10**(4-II))
             CARAC1(II) = CHAR(CODE+48)
             IF(CODE.NE.0)THEN
                ZERO=.TRUE.
             ELSEIF(.NOT.ZERO)THEN
                CARAC1(II)=' '
             ENDIF
         ENDDO
C
      ELSE IF (BANK.EQ.2) THEN
C
C        Change from ASCII to decimal
C        ----------------------------
         DECIM=0
         DO II=1,2
            ASCII = ICHAR(CARAC2(II:II))
            IF(ASCII.GE.48.AND.ASCII.LE.57) THEN
              CODE = ASCII-48
              DECIM = DECIM + CODE*10**(2-II)
            ENDIF
         ENDDO
C
      ELSE IF (BANK.EQ.3) THEN
C
C       Change from decimal to ASCII hex
C       --------------------------------
        IF (DECIM.LT.0) THEN                 !negatif value
          DECIM = (255 + DECIM) + 1
        ENDIF
C
        IF (DECIM.GT.15) THEN
          BUFF(1) = DECIM / 16
          BUFF(3) = BUFF(1) * 16
          BUFF(2) = DECIM - BUFF(3)
        ELSE
          BUFF(1) = 0
          BUFF(2) = DECIM
        ENDIF
        DO II=1,2
          IF(BUFF(II).GE.10)THEN
             CARAC2(II:II)= CHAR(BUFF(II)+55)
          ELSE
             CARAC2(II:II)= CHAR(BUFF(II)+48)
          ENDIF
        ENDDO
C
      ELSE IF (BANK.EQ.4) THEN
C
C        Change from ASCII to hexadecimal
C        --------------------------------
         DECIM=0
         DO II=1,2
            ASCII = ICHAR(CARAC2(II:II))
            IF(ASCII.GE.48.AND.ASCII.LE.57) THEN
              CODE = ASCII-48
              DECIM = DECIM + CODE*16**(2-II)
            ELSE IF(ASCII.GE.65.AND.ASCII.LE.70) THEN
              CODE = ASCII-55
              DECIM = DECIM + CODE*16**(2-II)
            ENDIF
         ENDDO
      ENDIF
C
      RETURN
      END
C
C
C =====================================================================
C                            MESSAGE
C =====================================================================
C
C    This subroutine calculate the position of the display to the
C    screen. It also erase the previous line and ask for a continue
C    character.
C
C       1- UP SCREEN (16 to 19)   : Status message
C       2- DOWN SCREEN (21 to 24) : Error message
C
      SUBROUTINE MESSAGE(TYPE)
C
      IMPLICIT NONE
C
       CHARACTER*50
     &  QUESTION      !Continue question to be printed
C
       INTEGER*2
     &  CPOSDOWN/48/ ,!Screen line position in ASCII for DOWN
     &  CPOSUP/53/   ,!Screen line position in ASCII for UP
     &  DUMK          !Dummy
C
       INTEGER*4
     &  TYPE          !Position (1-UP, 2-DOWN)
C
      INCLUDE 'tsdata.inc'
C
      DATA QUESTION /'    < ... Press return to continue ... >'/
C
      IF(TYPE.EQ.1) THEN
C
C        Message to the screen
C        ---------------------
         CPOSUP = CPOSUP + 1
         IF(CPOSUP.EQ.58) THEN
            CPOSUP=54
            WRITE(6,200) ESCAPE,'6'
            WRITE(6,200) ESCAPE,'7'
         ELSEIF(CPOSUP.EQ.55) THEN
            WRITE(6,200) ESCAPE,'8'
         ENDIF
         POSUP = CHAR(CPOSUP)
C
      ELSEIF(TYPE.EQ.2)THEN
C
C        Error message to the screen
C        ---------------------------
         CPOSDOWN = CPOSDOWN + 1
         IF(CPOSDOWN.EQ.52) THEN
            CALL TERM_READ1(QUESTION,'40',DUMK,'19',.TRUE.)  !File TSSDLIB.FOR
            WRITE(6,100) ESCAPE,'1'      !Erase all the previous line
            WRITE(6,100) ESCAPE,'2'
            WRITE(6,100) ESCAPE,'3'
            CPOSDOWN=49
         ENDIF
         POSDOWN = CHAR(CPOSDOWN)
      ENDIF
C
      RETURN
 100  FORMAT (' ',A2,'2',A1,';1H',79(' '))
 200  FORMAT (' ',A2,'1',A1,';1H',79(' '))
      END
C
C
C     ==============================================
      SUBROUTINE T_WRITE(CLINE,COL,SENTENCE,LENGTH)
C     ==============================================
C
C     This routine writes a string to the terminal at the specified
C     position.
C     If you specified at CLINE -1, the string is written at the cursor
C     position.
C     If you specified at CLINE -2, the routine will return the last cursor
C     position in CLINE,COL.
C
      IMPLICIT NONE
C
      INCLUDE 'tsdata.inc'
C
      INTEGER*4 CLINE,COL,LENGTH,PRLINE,PRCOL
C
      CHARACTER*(*) SENTENCE
C
      CHARACTER*50 INTERR
      INTEGER*4 IERR
      CHARACTER*2 S_COL,S_LINE
      CHARACTER*3 S_LENGTH
      CHARACTER*28 FMAT
      CHARACTER*1 NULL,ESC
C
C     Set previous cursor position and leave
C     --------------------------------------
      NULL = CHAR(0)
      ESC = CHAR(27)
C
      IF(CLINE.EQ.-2) THEN
         CLINE = PRLINE
         COL = PRCOL
         RETURN
      ENDIF
C
      IF(LENGTH.GT.0)THEN
C
C      Get the character equivalence of the string length
C      --------------------------------------------------
       WRITE(S_LENGTH,'(I3.3)',ERR=701,IOSTAT=IERR ) LENGTH
C
       IF(CLINE.NE.-1) THEN
C
C       Write string to the specified position
C       --------------------------------------
C       Get the character equivalence of the CLINE,COL positions
C       -------------------------------------------------------
        WRITE(S_LINE,'(I2.2)',ERR=701,IOSTAT=IERR ) CLINE
        WRITE(S_COL,'(I2.2)',ERR=701,IOSTAT=IERR ) COL
C
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'',A'
     &        //S_LENGTH(1:3)//',$)'
        ELSE
         FMAT = '(''+'',A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//
     &        'H'',A'//S_LENGTH(1:3)//')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NULL,ESC,SENTENCE(1:LENGTH)
       ELSE
C
C       Write the string at the cursor position
C       ---------------------------------------
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A'//S_LENGTH(1:3)//',$)'
        ELSE
         FMAT = '(''+'',A1,A'//S_LENGTH(1:3)//')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NULL,SENTENCE(1:LENGTH)
       ENDIF
      ELSE
C
C       When length is zero, just position the cursor
C       ---------------------------------------------
C       Get the character equivalence of the CLINE,COL positions
C       -------------------------------------------------------
        WRITE(S_LINE,'(I2.2)',ERR=701,IOSTAT=IERR ) CLINE
        WRITE(S_COL,'(I2.2)',ERR=701,IOSTAT=IERR ) COL
C
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'',$)'
        ELSE
         FMAT = '(''+'',A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NULL,ESC
C
      ENDIF
C
      PRLINE = CLINE
      PRCOL = MIN(COL+LENGTH,80)
C
      GOTO 700
 701  WRITE(INTERR(27:31),'(I5)',ERR=702) IERR
 702  CONTINUE
 700  CONTINUE

      RETURN
      END
C
C     ============================================
      SUBROUTINE T_READ(MODE,STRING,STR_LEN,IERR)
C     ============================================
C
C     This routine reads input from terminal.
C     MODE select the action for CR processing and upper cases conversion:
C          MODE <= 0 is convert all characters to upper cases.
C          MODE <  0 is refuse CR as input (beep an error).
C
      IMPLICIT NONE
      CHARACTER STRING*132,ERR_AMB*40,DUMMY*10
      INTEGER*4 IERR,STR_LEN,TYPE,MODE,LINE,COLUM
      LOGICAL*1 READ_CONT
C
C     Get cursor position
C     -------------------
      LINE = -2
      CALL T_WRITE(LINE,COLUM,DUMMY,10)
C
C     Read input from terminal until valid one entered
C     ------------------------------------------------
      READ_CONT=.TRUE.
      DO WHILE(READ_CONT)
        CALL TREADQ(1,STRING,STR_LEN,IERR)
C
C       Check for empty line
C       --------------------
        IF(STR_LEN.GT.0) THEN
          IF(MODE.LE.0)THEN
             CALL LOW_UPPER(STRING,STR_LEN)
          ENDIF
          READ_CONT=.FALSE.
C
        ELSEIF(MODE.LT.0)THEN
C
C         If mode is CR invalid, print error message and read again
C         ---------------------------------------------------------
          CALL T_WRITE(LINE,COLUM,DUMMY,0)
C
        ELSE
C
C         If mode is CR valid, QUIT
C         -------------------------
          READ_CONT=.FALSE.
        ENDIF
      ENDDO
C
      RETURN
      END
C
C
C =========================================================
C                         TREADQ
C =========================================================
C
C    This subroutine read a line from the terminal or the data file
C  and give the answer without any blanks at the end
C
      SUBROUTINE TREADQ(BANK,STRING,LEN_STR,RERR)
      IMPLICIT NONE
C
       INTEGER*4
     &  BANK           !Which unit to read
C
       INTEGER*4
     &  LEN_STR       ,!Length of string
     &  RERR            !Error counter
C
       CHARACTER*132 STRING
C
      IF (BANK.EQ.1) THEN
         READ(5,1,ERR=701,IOSTAT=RERR)STRING  !Read a line from terminal
 701     CONTINUE
      ENDIF
C
      LEN_STR = 60
      CALL STRING_LEN(STRING,LEN_STR)
      RETURN
 1    FORMAT(A)
      END
C
C     ======================================
      SUBROUTINE LOW_UPPER(INPLINE,LEN_LINE)
C     ======================================
C
      IMPLICIT NONE
C
C     INPUT : INPLINE -- contains the input character string to be translated
C             LEN_LINE -- contains the length of the character string
C
C     OUTPUT : INPLINE -- contains the upper case equivalent of the strin
C
       INTEGER*2
     &  DELTA           ,!Difference between upper case and lower case
     &  LEN_LINE,
     &  I
C
      LOGICAL
     &  READ_F/.TRUE./  !First read pass flag

      CHARACTER INPLINE*(*)
C
C
C     First pass
C     ----------
C     If necessary, get the difference between 'a' and 'A'
C

      IF (READ_F) THEN
        READ_F = .FALSE.
        DELTA = ICHAR('a') - ICHAR('A')
      ENDIF
C
C     If no character in the line, leave subroutine
C     ---------------------------------------------
      IF (LEN_LINE.EQ.0) RETURN
C
C     Translate to UPPER CASE in place
C     --------------------------------
      DO I = 1,LEN_LINE
        IF (('a'.LE.INPLINE(I:I)).AND.(INPLINE(I:I).LE.'z')) THEN
          INPLINE(I:I) = CHAR(ICHAR(INPLINE(I:I))-DELTA)
        ENDIF
      ENDDO
      RETURN
      END
C
C
C     ====================================
      SUBROUTINE STRING_LEN(STRING,LENGTH)
C     ====================================
C
      IMPLICIT NONE
C
      INTEGER*4 LENGTH
      CHARACTER*(*) STRING
C
      DO WHILE(LENGTH.GE.1.AND.STRING(LENGTH:LENGTH).EQ.' ')
           LENGTH=LENGTH-1
      ENDDO
C
      RETURN
      END
