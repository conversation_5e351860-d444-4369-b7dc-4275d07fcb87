C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                           TSDWRITE.FOR
C
C      THIS FILE CONTAINS ALL SUBROUTINES RELATED TO THE CREATION
C
C      OF THE .INF FILE AND THE DOWNLOAD FILE.
C
C                     FILE_INF
C                     FIL_HEADER
C                     CH_HEADER
C                     BUS_HEADER
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C ===================================================================
C                           FILE_INF
C ===================================================================
C
C   This subroutine create the information file TSDBUS.INF which
C   contains the TSDBUS output/input slot configuration.
C
      SUBROUTINE FILE_INF(ERR_INF)
C
      IMPLICIT NONE
C
        CHARACTER
     & P_LINE*80          ,!Print line
     & HYPHEN*80          ,!Hyphen header line
     & EQUAL*80           ,!Equal header line
     & NTCHA*80           ,!Not the same chassis header line
     & BUS_ID*5           ,!Identification of the BUS (A or B)
     & BNAME*8            ,!Bus name
     & EMPTY*80           ,!Empty line
     & CARAC(4)*1         ,!Character variable for transformation ASCII to dec.
     & FILENAME*40        ,!Information file name
     & SPECAR*1           ,!Special character
     & CARSLASH*2          !Character variable for slash input-output information
C
        INTEGER*2
     & II                 ,!Loop counter
     & JJ                 ,!Loop counter
     & CH_COD             ,!Chassis identificationn number
     & BUS_COD            ,!Bus identification number
     & ABSNUMB            ,!Positive chassis number
     & PRACHAS            ,!Previous chassis number
     & CODE               ,!Temporary file information code
     & MSLOT              ,!Master slot number
     & PCH_COD            ,!Previous chassis id. number
     & DECIM              ,!Decimal number
     & ELEMINX            ,!X-array element
     & PGLINE              !Page line counter
C
        INTEGER*4
     & RECN               ,!Record number
     & END_FILE           ,!End of file error counter
     & ERR                ,!I/O error counter
     & ERR_INF             !Information file error counter
C
        LOGICAL*1
     & PRINTBUS            !Print another bus flag
C
      INCLUDE 'tsdata.inc'
C
      DATA HYPHEN/' +---------+-----+-----+-----+-----+-----+-----+-----
     &+-----+-----+-----+-----+-+'/
      DATA EQUAL /' +===================================================
     &==========================+'/
      DATA NTCHA/' |CHAS.#   |     |     |     |     |     |     |     |
     &     |     |     |     | |'/
      DATA EMPTY /'
     &                           '/
C
C     Print file header
C     -----------------
      CALL FIL_HEADER
      PGLINE = 8
C
      END_FILE=0
C
C     Read first data line
C     --------------------
      RECN = 1
      CALL MAT_OPER(RECN,2,CODE,MSLOT,BNAME,END_FILE)   !File TSDLIB.FOR
C
      DO WHILE(END_FILE.EQ.0.AND.ERR.EQ.0)
C
         CH_COD = CODE/100
         BUS_COD = CODE-CH_COD*100
C
         IF(CH_COD.NE.PCH_COD)THEN
C
C           Print chassis header
C           --------------------
            CALL CH_HEADER(CH_COD)
            PGLINE = PGLINE + 3
C
         ENDIF
         PCH_COD = CH_COD
C
         IF((BUS_COD/50).EQ.0)THEN
             BUS_ID = 'BUS A'
         ELSE
             BUS_ID = 'BUS B'
         ENDIF
C
C        Print the BUS header
C        --------------------
         CALL BUS_HEADER(BUS_ID,BNAME)
         CALL COMMENT_TIT
         PGLINE = PGLINE + 4
C
         ELEMINX = -10
         PRINTBUS = .TRUE.
         DO WHILE(PRINTBUS)
C
C           Print all the array
C           -------------------
            ELEMINX = ELEMINX + 10
            IF(MAT_DIM(2).GT.10)THEN
              MAT_DIM(2) = MAT_DIM(2)-10
            ELSE
              PRINTBUS = .FALSE.
            ENDIF
C
C           Print all the array with max column to 10
C           -----------------------------------------
            PRACHAS=ACHAS(1)
            DO II=1,MAT_DIM(1)
               IF(ACHAS(II).NE.PRACHAS)THEN
C
                 P_LINE(1:80) = EQUAL(1:80)
                 CALL IO_WRIT2(P_LINE,ERR)       !File TSDIO.FOR
                 P_LINE(1:80) = NTCHA(1:80)
                 ABSNUMB=-ACHAS(II)
                 CALL CAR_CHG(1,ABSNUMB,CARAC,'   ')   !File TSDLIB.FOR
                 P_LINE(9:10) = CARAC(3)//CARAC(4)
                 CALL IO_WRIT2(P_LINE,ERR)       !File TSDIO.FOR
                 P_LINE(1:80) = HYPHEN(1:80)
                 CALL IO_WRIT2(P_LINE,ERR)       !File TSDIO.FOR
                 PGLINE=PGLINE+3
               ELSE
                 P_LINE(1:80) = HYPHEN(1:80)
                 CALL IO_WRIT2(P_LINE,ERR)       !File TSDIO.FOR
               ENDIF
               PRACHAS = ACHAS(II)
C
               P_LINE(1:80) = EMPTY(1:80)
               IF (ASLOT(II).GT.0)THEN
                  P_LINE(1:12) = ' |'//ASSIGN(II)//' |'
               ELSE
                  P_LINE(1:12) = ' |<'//ASSIGN(II)(1:7)//'>|'
               ENDIF
C
C              Print all the datas for this line
C              ---------------------------------
               DO JJ=1,10
                  IF(MATRIX(II,JJ+ELEMINX).NE.0)THEN
                     IF(MATRIX(II,JJ+ELEMINX).LT.0) THEN
                        DECIM = -MATRIX(II,JJ+ELEMINX)
                        CARSLASH = '/I'
                     ELSE
                        DECIM = MATRIX(II,JJ+ELEMINX)
                        CARSLASH = '/O'
                     ENDIF
                     IF(IABS(MATRIX(II,JJ+ELEMINX)).GE.400) THEN
                        DECIM = DECIM-400
                        SPECAR= '*'
                     ELSE
                        SPECAR= ' '
                     ENDIF
                     CALL CAR_CHG(1,DECIM,CARAC,'   ')   !File TSDLIB.FOR
                     P_LINE(13+(JJ-1)*6:12+JJ*6) = SPECAR//
     &                    CARAC(3)//CARAC(4)//CARSLASH//'|'
                  ELSE
                     P_LINE(13+(JJ-1)*6:12+JJ*6)='     |'
                  ENDIF
C
               ENDDO
C
C              Print the slot number and the master symbol
C              -------------------------------------------
               P_LINE(73:74) ='XA'
               CALL CAR_CHG(1,IABS(ASLOT(II)),CARAC,'     ')  !File TSDLIB.FOR
               IF(CARAC(3).EQ.' ') CARAC(3)='0' !Put 0 instead of a blank for XA
               P_LINE(75:76) = CARAC(3)//CARAC(4)
               P_LINE(78:78) = '|'
               IF(MSLOT.EQ.IABS(ASLOT(II)))THEN
                  P_LINE(79:80) = 'Y|'
               ELSE
                  P_LINE(79:80) = ' |'
               ENDIF
               CALL IO_WRIT2(P_LINE,ERR)    !File TSDIO.FOR
C
               PGLINE = PGLINE + 2
C
            ENDDO
C
C           Print the last hyphen and an empty lines after each bus block
C           -------------------------------------------------------------
            P_LINE(1:80) = HYPHEN(1:80)
            CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
            P_LINE(1:80) = EMPTY(1:80)
            CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
            PGLINE = PGLINE +1
C
            IF(PRINTBUS)THEN
C
C              The next bus print is the continuation of this bus
C              --------------------------------------------------
               P_LINE(1:80) = EMPTY(1:80)
               CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
               PGLINE = PGLINE +1
C
C              Leave blank line until the 60th line on the file if not enough
C              space to print the next bus (continued or new line)
C              --------------------------------------------------------------
               IF ((PGLINE+MAT_DIM(1)+8).GT.60)THEN
                   DO II=PGLINE,60
                      P_LINE(1:80)=EMPTY(1:80)
                      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
                   ENDDO
               ENDIF
C
C              Print the CONTINUED symbol
C              --------------------------
               P_LINE(34:46)= '< CONTINUED >'
               CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
               P_LINE(1:80) = EMPTY(1:80)
               CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
               CALL COMMENT_TIT
               PGLINE = PGLINE +3
            ELSE
C
C              The next print bus is a new bus
C              -------------------------------
               P_LINE(1:80) = EMPTY(1:80)
               CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
               PGLINE = PGLINE +1
            ENDIF
C
         ENDDO
C
C        Read another bus data line
C        --------------------------
         RECN = RECN+1
         IF (RECN .LE. NB_REC) THEN
           CALL MAT_OPER(RECN,2,CODE,MSLOT,BNAME,END_FILE)   !File TSDLIB.FOR
         ELSE
           END_FILE = 99
         ENDIF
C
      ENDDO
C
C     Send error message to the master program
C     ----------------------------------------
      IF(ERR.NE.0)THEN
         ERR_INF = 1
      ENDIF
C
      RETURN
      END
C
C
C =====================================================================
C                              FIL_HEADER
C =====================================================================
C
C   This routine print the header message on the file.
C
      SUBROUTINE FIL_HEADER
C
      IMPLICIT NONE
C
        INTEGER*4
     & II                 ,!Loop counter
     & ERR                 !Error counter
C
        CHARACTER
     & P_LINE*80          ,!Print line
     & EMPTY*80           ,!Empty line
     & HEAD_TIT(10)*23     !Title header array
C
      INCLUDE 'tsdata.inc'
C
      DATA HEAD_TIT /'    ','    ',' **********************'
     &             ,' **  TSDGEN UTILITY  **',' **    INFORMATION   **',
     &             ' **       FILE       **',' **********************',
     &             '      ','      ','      ' /
      DATA EMPTY /'                                                    
     &                           '/
C
C     Print all the header line by line
C     ---------------------------------
      DO II=1,10
         P_LINE(1:80)=EMPTY(1:80)
         P_LINE(29:51)=HEAD_TIT(II)
         CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
      ENDDO
C
      RETURN
      END
C
C
C ==================================================================
C                       CH_HEADER
C ==================================================================
C
C    This routine print the chassis header in the .INF file.
C
      SUBROUTINE CH_HEADER(CHASSIS)
C
      IMPLICIT NONE
C
        INTEGER*4
     & ERR                 !Error counter
C
        INTEGER*2
     & CHASSIS            ,!Chassis number
     & HALF                !Half length of the title
C
        CHARACTER
     & P_LINE*80          ,!Print line
     & CARAC(4)*1         ,!Character equivalent of chassis number
     & STARE*80           ,!STAR line
     & EMPTY*80            !Empty line
C
      INCLUDE 'tsdata.inc'
C
      DATA EMPTY /'
     &                           '/
      DATA STARE /' ****************************************************
     &***************************'/
C
C     Print the chassis title
C     -----------------------
      P_LINE(1:80) = STARE(1:80)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
      P_LINE(1:80) = EMPTY(1:80)
      CALL CAR_CHG(1,CHASSIS,CARAC,'     ')  !File TSDLIB.FOR
      P_LINE(2:13) = 'CHASSIS # '//CARAC(3)//CARAC(4)
      P_LINE(20:LTITLE(CHASSIS)+20) = TITLE(CHASSIS)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
      P_LINE(1:80) = STARE(1:80)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
C
      RETURN
      END
C
C
C =================================================================
C                           BUS_HEADER
C =================================================================
C
C   This routine print the header of the new bus.
C
      SUBROUTINE MATRIX_HEAD
C
      IMPLICIT NONE
C
        INTEGER*4
     & ERR                !Error counter
C
        CHARACTER
     & MESSAGE*80        ,!Message line
     & P_LINE*80         ,!Print line
     & HYPHEN*80         ,!Hyphen line
     & IDENT*5           ,!Bus identification name
     & BUSNAME*8         ,!Busname
     & EMPTY*80           !Empty line
C
      INCLUDE 'tsdata.inc'
C
      DATA EMPTY /'
     &                           '/
      DATA MESSAGE /'    TASK               TSDBUS   INPUT OUTPUT    SEQ
     &UENCE                SLOT  M '/
      DATA HYPHEN/' +---------+-----+-----+-----+-----+-----+-----+-----
     &+-----+-----+-----+-----+-+'/
C
      ENTRY BUS_HEADER(IDENT,BUSNAME) !Print all the bus header
C
      P_LINE(1:80) = EMPTY(1:80)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
C
      P_LINE(1:80)  = EMPTY(1:80)
      P_LINE(30:32) = 'TSD'
      P_LINE(34:38) = IDENT
      P_LINE(40:40) = '='
      P_LINE(42:49) = BUSNAME
C
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
C
      P_LINE(1:80)  = EMPTY(1:80)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
      RETURN
C
      ENTRY COMMENT_TIT  !Print only a blank line and the "hyphen" line
C
      P_LINE(1:80) = MESSAGE(1:80)
      CALL IO_WRIT2(P_LINE,ERR)     !File TSDIO.FOR
C
      RETURN
      END
C
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                     FILE    FILE_DLD
C
C     THIS SUBROUTINE WILL GENERATE TSD DATA BIT FILE
C
C                   ASCII_INP_OUT
C                   CAR_CHG
C                   CHECK_SUM
C                   IO_WRIT3
C
C
C
C    HEADER RECORD
C    =============
C    (1:1)(2:2)(3:3)(4:4)(5:6)
C     &    m    f    d     cc
C
C     &    = record mark      1  byte   -> 1 ASCII
C     m    = DMC #            1  byte   -> 1 ASCII
C     f    = file type        1  byte   -> 1 ASCII   H=86 Hex
C     d    = destination      1  byte   -> 1 ASCII   S=Tsd
C     cc   = card slot        2  bytes  -> 2 ASCII   1 to 27
C                           ------
C                            6 Bytes
C
C
C    DATA RECORD
C    ============
C    (1:1)(2:3)(4:7)(8:9)(10:41)(42:43)
C     :    ll   aaaa tt   d...d  ss
C
C     :    = record mark      1  byte   -> 1  ASCII
C     ll   = record lenght    2  bytes  -> 2  ASCII
C     aaaa = load address     4  bytes  -> 4  ASCII
C     tt   = record type      2  bytes  -> 2  ASCII
C     d..d = 16 data          32 bytes  -> 32 ASCII -> 16 data bytes in hex
C     ss   = check sum        2  bytes  -> 2  ASCII
C                           ------
C                            43  Bytes
C
C    END RECORD
C    ===========
C
C    (1:1)(2:3)(4:7)(8:9)(10:11)
C     :    ll   zzzz tt   ss
C
C     :    = record mark      1  byte   -> 1  ASCII      always :
C     ll   = record lenght    2  bytes  -> 2  ASCII      always 00
C     zzzz = zeroes           4  bytes  -> 4  ASCII      always 0000
C     tt   = record type      2  bytes  -> 2  ASCII      always 01
C     ss   = check sum        2  bytes  -> 2  ASCII      always FF
C                           ------
C                            11  Bytes
C
C
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
      SUBROUTINE FILE_DLD(*)
C
      IMPLICIT NONE
C
      INTEGER*2
C
     &  POISTA                                ,!Pointer start
     &  POIEND                                ,!Pointer end
     &  NUMBDASLOT                            ,!POSITION OF D/A slot begins
     &  PRACHAS                               ,!Previous ACHAS(?)
     &  RECNUM                                ,!Record number
     &  SLOT                                  ,!Slot number
     &  RLEN                                   ,!Lenght of record
     &  DACSLOT(27)                           ,!Dacbus slot flag
     &  PROC                                  ,!Procces output or input
     &  CODE                                  ,!
     &  MSLOT                                 ,!
     &  I,XX,X                                 !DO loop counter
C
C
      INTEGER*4
C
     &  RECN                                 ,!Record number
     &  II                                   ,!Loop counter
     &  END_FILE                             ,!
     &  ERR_DLD                              ,!Download file error
     &  RECERR                                !Record error
C
      LOGICAL*1
C
     &  ANOTHER                             ,!Another group of slot found flag
     &  SLOTAVAI(27)                          ,!Slot availability
     &  NEWCHAS                                !New chassis
C
      CHARACTER*4
C
     &  DACOFF      /'0010'/               ,!DAC slot offset address
     &  DACSEG      /'2000'/                !DAC slot segment address
C
      CHARACTER
     &  FILEDLD*40                            ,!Download filename
     &  CARAC1*4                              ,!Firts character
     &  CARAC2*2                              ,!Second character
     &  BUSN*8                                ,!Bus name
     &  CHECKSUM*2                             !Check sum
C
      CHARACTER*19
C
     &  DISPLAYP(4)                         !Display presentation message
     &                 /'                   ' ,
     &                  ' TSD BUS SCHEDULER ' ,
     &                  '  DOWNLOAD   FILE  ' ,
     &                  '                   '/
C
      CHARACTER*19
C
     &  DISPLAYE(4)                         !Display end message
     &                 /'                   ' ,
     &                  ' TSD BUS SCHEDULER ' ,
     &                  '    END OF FILE    ' ,
     &                  '                   '/
C
      CHARACTER*43
C
     &  RECORD                                ,!1 DATA Record
     &  COMMENT                               ,!Comment record
     &  CLEANREC                              ,!Clean Record
     &  CLEANCOM                               !Clean commnet Record
C
C
      CHARACTER*512
C
     &  ASCOUT(27)                            ,!ASCII TSD out matrix
     &  ASCINP(27)                             !ASCII TSD inp matrix
C
C
      INCLUDE 'tsdata.inc'
C
      DATA  CLEANREC  /'0000000000000000000000000000000000000000000'/
      DATA  CLEANCOM  /'0000000000000000000000000000000000000000000'/
C
      IF(ERR_DLD.NE.0) RETURN 1
C
      IF (OPTION) THEN
         CALL FIL_OPEN(6,1,ERR_DLD)
         IF(ERR_DLD.EQ.0) CALL FXILINX(ERR_DLD)
      ENDIF
C
      IF(ERR_DLD.NE.0) THEN
C
C        Write an error message for second sequence of D/A slot
C        ------------------------------------------------------
         CALL MESSAGE(2)
         WRITE(6,130) ESCAPE,POSDOWN,ERR_DLD
         RETURN 1
      ENDIF
C
      RECN = 1
      CALL MAT_OPER(RECN,2,CODE,MSLOT,BUSN,END_FILE)
C
C
      DO I = 1,4
C
C       Comment Presentation section
C       ----------------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = DISPLAYP(I)
        COMMENT(32:43) = '            '
C
        RLEN = 43
        CALL IO_WRIT3 (RLEN,COMMENT,RECERR)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C
      DO WHILE (END_FILE.EQ.0)
        PRACHAS = ACHAS(1)
        NEWCHAS = .TRUE.
C
        DO WHILE (END_FILE.EQ.0.AND.ACHAS(1).EQ.PRACHAS)   !Procces up-to 27 slot
          PRACHAS = ACHAS(1)
          CALL ASCII_INP_OUT(NEWCHAS,SLOTAVAI,DACSLOT,ASCINP,ASCOUT)
          RECN = RECN+1
          IF (RECN .LE. NB_REC) THEN
            CALL MAT_OPER(RECN,2,CODE,MSLOT,BUSN,END_FILE)
          ELSE
            END_FILE = 99
          ENDIF
        ENDDO                                     !One chassis finish
C
C       Comment DMC # section
C       ---------------------
        COMMENT(1:1)   = '$'                        !Comment mark
        COMMENT(2:16) = '===== CHASSIS #'           !
        CALL CAR_CHG (1,PRACHAS,CARAC1,CARAC2)
        COMMENT(17:18) = CARAC1(3:3)//CARAC1(4:4)       !
        COMMENT(19:24) = ' ==== '
        COMMENT(25:31) = ' DMC # '                !
        COMMENT(32:33) = DMC_ADD(PRACHAS)(1:2)      !
        COMMENT(34:43)  = '  ========'              !
C
        RLEN = 43
        CALL IO_WRIT3 (RLEN,COMMENT,RECERR)
        COMMENT = CLEANCOM                        !Clean the comment record
C
C
        DO SLOT = 1,27                            !27 slot = 1 chassis
C
          IF (SLOTAVAI(SLOT)) THEN
C
C           Comment slot section
C           --------------------
            COMMENT(1:1) = '$'                      !Comment mark
            COMMENT(2:15)  = '<<<<<<<<<<<<<<'       !
            COMMENT(16:26) = '  SLOT #   '          !
            CALL CAR_CHG (1,SLOT,CARAC1,CARAC2)
            IF (CARAC1(3:3).EQ.' ') CARAC1(3:3) = '0'
            COMMENT(27:28) = CARAC1(3:4)
            COMMENT(29:43)  = '  >>>>>>>>>>>>>'     !
C
            RLEN = 43
            CALL IO_WRIT3 (RLEN,COMMENT,RECERR)
            COMMENT = CLEANCOM                      !Clean the comment record
C
C           Header section
C           --------------
            RECORD(1:1) = '&'                       !Rec mark
            RECORD(2:3) =  DMC_ADD(PRACHAS)(1:2)    !dmc #
            IF(DACSLOT(SLOT).GT.0)THEN
               RECORD(4:5) = 'HD'       !file type H = 86 hex,Dest.D = D/A bus
            ELSE
               RECORD(4:5) = 'HS'       !file type H = 86 hex,Dest.S = tsd bus
            ENDIF
            WRITE(RECORD(6:7),'(Z2.2)') SLOT        !Card slot
C
            RLEN = 7
            CALL IO_WRIT3 (RLEN,RECORD,RECERR)
            RECORD = CLEANREC                       !Clean the record
C
            IF(DACSLOT(SLOT).EQ.0)THEN
C
C              Data section
C              ------------
               RLEN = 43
C
               DO PROC = 1,2                           !Input & output
C
                 POISTA = 1
                 POIEND = 32
C
                 DO RECNUM = 1,16                      !16 data bytes per card
C
                   RECORD(1:1) = ':'                   !Rec mark
                   RECORD(2:3) = '10'                  !Rec lenght
C
                   IF (PROC.EQ.1) THEN
                     RECORD(4:7) = '0000'              !Load address input
                     RECORD(10:41) = ASCINP(SLOT)(POISTA:POIEND)
                   ELSE
                     RECORD(4:7) = '0100'              !Load address output
                     RECORD(10:41) = ASCOUT(SLOT)(POISTA:POIEND)
                   ENDIF
C
                   RECORD(8:9) = '00'                  !Rec type
C
                   CALL CHECK_SUM(RECORD,CHECKSUM,41)
                   RECORD(42:43) = CHECKSUM(1:2)
C
                   CALL IO_WRIT3 (RLEN,RECORD,RECERR)
                   RECORD = CLEANREC                   !Clean the record
C
                   POISTA = 32 * RECNUM + 1
                   POIEND = 32 * (RECNUM + 1)
C
                 ENDDO
               ENDDO
C
            ELSE    !D/A SECTION
C
C              Write extended record
C              ---------------------
               RECORD(1:13) =':020000022000'
               RECORD(10:13)= DACSEG
C
               CALL CHECK_SUM(RECORD,CHECKSUM,12)
               RECORD(14:15) = CHECKSUM(1:2)
C
               RLEN = 15
               CALL IO_WRIT3 (RLEN,RECORD,RECERR)
               RECORD = CLEANREC                   !Clean the record
C
C              Special D/A section record
C              --------------------------
               RLEN = 15
C
               RECORD(1:1) = ':'                   !Rec mark
               RECORD(2:3) = '02'                  !Rec lenght
C
               RECORD(4:7) = DACOFF              !Load address output
C
               POISTA=1
               DO WHILE(ASCINP(SLOT)(POISTA:POISTA+1).NE.'01'.AND.
     &                  ASCINP(SLOT)(POISTA:POISTA+1).NE.'03'.AND.
     &                  POISTA.LT.512 )
                  POISTA=POISTA+2
               ENDDO
C
               RECORD(8:9) = '00'                  !Rec type
               IF(POISTA.LT.512)THEN
                  NUMBDASLOT= POISTA/2
C
C                 Write number of D/A slot: allways a word with LSB first
C                 -------------------------------------------------------
                  WRITE(RECORD(10:11),'(Z2.2)') NUMBDASLOT !LSB DATA
                  RECORD(12:13) = '00'                     !MSB DATA
C
C                 Check if there is more than 12 words for D/A
C                 --------------------------------------------
                  POISTA = POISTA+2
                  POIEND=1
                  DO WHILE((ASCINP(SLOT)(POISTA:POISTA+1).EQ.'01'.OR.
     &                     ASCINP(SLOT)(POISTA:POISTA+1).EQ.'03').AND.
     &                     POISTA.LT.512 )
                     POIEND = POIEND+1
                     POISTA = POISTA+2
                  ENDDO
C
                  IF(POIEND.GT.12) THEN
C
                    IF((POIEND-12).GT.(DACSLOT(SLOT)-1))THEN
C
C                     Write an error message for D/A number of slot
C                     ---------------------------------------------
                      CALL MESSAGE(2)
                      WRITE(6,100) ESCAPE,POSDOWN,SLOT,PRACHAS
                      RETURN 1
                    ENDIF
                  ENDIF
C
C                 Check for another group of slot for the D/A
C                 -------------------------------------------
                  ANOTHER=.FALSE.
                  DO II=POISTA,512,2
                     IF(ASCINP(SLOT)(II:II+1).EQ.'01'.OR.
     &                  ASCINP(SLOT)(II:II+1).EQ.'03' ) THEN
                        ANOTHER=.TRUE.
                     ENDIF
                  ENDDO
                  IF (ANOTHER) THEN
C
C                   Write an error message for second sequence of D/A slot
C                   ------------------------------------------------------
                    CALL MESSAGE(2)
                    WRITE(6,120) ESCAPE,POSDOWN,SLOT,PRACHAS
                    RETURN 1
                  ENDIF
C
               ELSE
C
C                 Write a warning message for no D/A slot found
C                 ---------------------------------------------
                  CALL MESSAGE(2)
                  WRITE(6,110) ESCAPE,POSDOWN,SLOT,PRACHAS
               ENDIF
C
C              Check for illegal output on D/A card
C              ------------------------------------
               POISTA = 1
               DO WHILE(ASCOUT(SLOT)(POISTA:POISTA+1).NE.'01'.AND.
     &                  ASCOUT(SLOT)(POISTA:POISTA+1).NE.'03'.AND.
     &                   POISTA.LT.512 )
                  POISTA = POISTA+2
               ENDDO
C
C              Write a warning message if there is words on output
C              ---------------------------------------------------
               IF (POISTA.LT.513)THEN
                  CALL MESSAGE(2)
                  WRITE(6,111) ESCAPE,POSDOWN,SLOT,PRACHAS
               ENDIF
C
               CALL CHECK_SUM(RECORD,CHECKSUM,12)
               RECORD(14:15) = CHECKSUM(1:2)
C
               CALL IO_WRIT3 (RLEN,RECORD,RECERR)
               RECORD = CLEANREC                   !Clean the record
C
            ENDIF
C
C           End section
C           -----------
            RECORD(1:11) = ':00000001FF'
            RLEN = 11
            CALL IO_WRIT3 (RLEN,RECORD,RECERR)
            RECORD = CLEANREC                       !Clean the record
C
          ELSE
C
C           Comment empty slot section
C           --------------------------
            COMMENT(1:1) = '$'                       !Comment mark
            COMMENT(2:12)   = '<<<<<<<<<<<'          !
            COMMENT(13:29)  = ' NO TSD ON SLOT #'             !
            CALL CAR_CHG (1,SLOT,CARAC1,CARAC2)      !
            IF (CARAC1(3:3).EQ.' ') CARAC1(3:3) = '0'
            COMMENT(30:31)  = CARAC1(3:4)            !Card slot
            COMMENT(32:43)  = '  >>>>>>>>>>'         !
C
            RLEN = 43
            CALL IO_WRIT3 (RLEN,COMMENT,RECERR)
            COMMENT = CLEANCOM                      !Clean the comment record
C
          ENDIF
C
        ENDDO
C
      ENDDO
C
C
      DO I = 1,4
C
C       Comment end section
C       -------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = DISPLAYE(I)
        COMMENT(32:43) = '            '
C
        RLEN = 43
        CALL IO_WRIT3 (RLEN,COMMENT,RECERR)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C
      RETURN
C
 100  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Too many slot for D/A'
     &        ,' card: slot# ',I2,', chassis # ',I2,'    * SEVERE **')
 110  FORMAT (' ',A2,'2',A1,';1H',2X,'%WAR - ** No input words for D/'
     &        ,'A card : slot# ',I2,', chassis # ',I2,'           **')
 111  FORMAT (' ',A2,'2',A1,';1H',2X,'%WAR - ** Output words for D/A '
     &        ,'card : slot# ',I2,', chassis # ',I2,' * IGNORED   **')
 120  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** A 2nd D/A group of in'
     &        ,'put on the same card:ch#',I2,',sl#',I2,'* SEVERE **')
 130  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** CANNOT CREATE XILINX '
     &        ,'DOWNLOAD FILE: ERROR # ',I3,'   **')
C
      END
C
C
C
C ==============================================================
C
C                  SUBROUTINE ASCII_INP_OUT
C
C     THIS SUBROUTINE WILL PACK 2 ASCII MATRIX (27,512) OF DATA BIT
C                     FOR TSD ; OUTPUT & INPUT.
C
C
C ==============================================================
C
C
      SUBROUTINE ASCII_INP_OUT(NEWCHAS,SLOTAVAI,DACSLOT,ASCINP,ASCOUT)
      IMPLICIT NONE
C
      INTEGER*2
C
     &   MAXTASK
C
      PARAMETER    (MAXTASK = 20)          !Max task
C
      INTEGER*2
C
     &   HOWLONG(MAXTASK)               ,!Each sequence How long
     &   HOWMATAS                       ,!How many task
     &   INP_OUT(MAXTASK)               ,!Output or Input
     &   DACSLOT(27)                    ,!Dacbus slot flags
     &   POINTER                         !Pointer for output/input
C
      INTEGER*4
C
     &   X                              ,!Vertical axis for matrix
     &   Y                              ,!Horizontal axis for matrix
     &   K                              ,!Counter for loop
     &   KK                              !Counter for loop
C
C
      LOGICAL*1
C
     &   NEWCHAS                        ,!A new chassis
     &   SPEINP                         ,!Special input
     &   SPEOUT                         ,!Special output
     &   SLOTAVAI(27)                   ,!Slot availability
     &   OUTPUT                         ,!Output for matrix
     &   INPUT                           !Input for matrix
C
      CHARACTER*2
C
     &   ZERO  /'00'/                   ,!Code ascii for zero   NO in / out
     &   ONE   /'01'/                   ,!Code ascii for one       in / out
     &   TWO   /'02'/                   ,!Code ascii for two       end
     &   THREE /'03'/                   ,!Code ascii for three   end + in / out
     &   IASCII                         ,!Input ascii
     &   OASCII                          !Output ascii
C
      CHARACTER*512
C
     &   ASCOUT(27)                     ,!ascii tsd output matrix
     &   ASCINP(27)                      !ascii tsd input matrix
C
C
      INCLUDE 'tsdata.inc'
C
C     Look for how many task
C     ---------------------
      HOWMATAS = 0
C
      DO WHILE( (ACHAS(HOWMATAS+1).GT.0).AND.(HOWMATAS.LT.MAT_DIM(1)))
         HOWMATAS = HOWMATAS + 1
      ENDDO
C
C
C     Each sequence it is input or output
C     -----------------------------------
      X = 1
      Y = 1
      DO WHILE (Y.LE.HOWMATAS)
        DO WHILE (MATRIX(Y,X).EQ.0.AND.X.LE.MAT_DIM(2))
          X = X + 1
        ENDDO
        IF (X.LE.MAT_DIM(2)) THEN
          INP_OUT(Y) = MATRIX(Y,X)
        ELSE
          INP_OUT(Y) = 0
        ENDIF
        Y = Y + 1
        X = 1
      ENDDO
C
C
C     Clean tsd: input & output matrix + set end character
C     ----------------------------------------------------
      IF (NEWCHAS) THEN
        NEWCHAS = .FALSE.
        DO Y =1,27
          DO X = 3,511
            ASCINP(Y)(X:X+1) = ZERO
            ASCOUT(Y)(X:X+1) = ZERO
          ENDDO
            ASCINP(Y)(1:2) = TWO                     !Set all slot to
            ASCOUT(Y)(1:2) = TWO                     !TSD end character
C
            SLOTAVAI(Y) = .FALSE.
C
        ENDDO
      ENDIF
C
C     Remove END character when the task is used
C     ------------------------------------------
      DO K = 1,HOWMATAS
C
C       Special process if DAC task
C       ---------------------------
        IF(ASLOT(K).LT.0)THEN
           ASLOT(K)=-ASLOT(K)
           DACSLOT(ASLOT(K))=1 + MAX((IABS(MATRIX(K,MAT_DIM(2)))-400),0)
        ELSE
           DACSLOT(ASLOT(K))=0
        ENDIF
C
        IF (INP_OUT(K).LT.0) THEN
           ASCINP(ASLOT(K))(1:2) = ZERO
        ELSE IF (INP_OUT(K).GT.0) THEN
           ASCOUT(ASLOT(K))(1:2) = ZERO
        ENDIF
      ENDDO
C
C
C     Which slot is available
C     ----------------------
      DO K = 1,HOWMATAS
        SLOTAVAI(ASLOT(K)) = .TRUE.
      ENDDO
C
C
C     Prepare How long vector for each sequence
C     -----------------------------------------
      X = 1
      Y = 1
C
      DO WHILE (X.LE.MAT_DIM(2))
        DO WHILE ((Y.LT.MAT_DIM(1)).AND.(MATRIX(Y,X).EQ.0))
          Y = Y + 1
        ENDDO
        IF (Y.LE.MAT_DIM(1)) THEN
          HOWLONG(X) = MATRIX(Y,X)
          HOWLONG(X) = IABS (HOWLONG(X))
          IF(HOWLONG(X).GE.400) THEN
            HOWLONG(X) = HOWLONG(X)-400
          ENDIF
        ENDIF
        X = X + 1
        Y = 1
      ENDDO
C
C
C     Pack input & output matrix with ascii code
C     ------------------------------------------
      X = 1
      POINTER  = 1
C
      DO WHILE (X.LE.MAT_DIM(2))
        Y = 1
        DO WHILE (Y.LE.HOWMATAS)
C
          SPEINP = .TRUE.
          SPEOUT = .TRUE.
C
C         Choose the right ascii code for each sequence
C         ---------------------------------------------
          IF (X.EQ.MAT_DIM(2)) THEN
            IF (MATRIX(Y,X).LT.0) THEN
              IASCII = THREE
              INPUT = .TRUE.
            ELSE IF (MATRIX(Y,X).GT.0) THEN
              OASCII = THREE
              OUTPUT = .TRUE.
            ELSE
              IF (INP_OUT(Y).LT.0) THEN
                IASCII = TWO
                INPUT = .TRUE.
                SPEINP = .FALSE.
              ELSEIF (INP_OUT(Y).GT.0) THEN
                OASCII = TWO
                OUTPUT = .TRUE.
                SPEOUT = .FALSE.
              ENDIF
            ENDIF
          ELSE
            IF (MATRIX(Y,X).LT.0) THEN
              IASCII = ONE
              INPUT = .TRUE.
            ELSE IF (MATRIX(Y,X).GT.0) THEN
              OASCII = ONE
              OUTPUT = .TRUE.
            ENDIF
          ENDIF
C
C
C         Fill up input & output matrix at  right slot position
C         -----------------------------------------------------
          IF (INPUT) THEN
            INPUT = .FALSE.
            K = 0
            DO WHILE ((K/2).LT.(HOWLONG(X)-1))
              KK = K + POINTER
                IF (SPEINP) ASCINP(ASLOT(Y))(KK:KK+1) = ONE
              K = K + 2
            ENDDO
            KK = K + POINTER
            ASCINP(ASLOT(Y))(KK:KK+1) = IASCII !Store the last one
          ENDIF
C
          IF (OUTPUT) THEN
            OUTPUT = .FALSE.
            K = 0
            DO WHILE ((K/2).LT.(HOWLONG(X)-1))
              KK = K + POINTER
                IF (SPEOUT) ASCOUT(ASLOT(Y))(KK:KK+1) = ONE
              K = K + 2
            ENDDO
            KK = K + POINTER
            ASCOUT(ASLOT(Y))(KK:KK+1) = OASCII  !Store the last one
          ENDIF
C
          Y = Y + 1
        ENDDO
        POINTER = POINTER + HOWLONG(X)*2
        X = X + 1
      ENDDO
      RETURN
C
      END
C
C
C =============================================================
C
C     THIS SUBROUTINE WILL RETURN THE CHECKSUM FOR A RECORD
C
C =============================================================
C
      SUBROUTINE CHECK_SUM(VECTOR,CHECKSUM,RECLEN)
      IMPLICIT NONE
C
      INTEGER*2
C
     &  CHEKSTAR     /2/                    ,!Check sum  start at
     &  I                                   ,!Counter for loop
     &  SUM                                 ,!Sum of all bytes in the record
     &  HEX_SUM                             ,!Check sum in Hex
     &  DECIM                                !Decimal Value from subroutine
C
      INTEGER*4
     &  RECLEN                               !Length of the record
C
      CHARACTER*43
C
     &   VECTOR                              !Record file
C
      CHARACTER*2
C
     &   CARAC1*2                            ,!First caracter
     &   CARAC2                              ,!Second caracter
     &   CHECKSUM                             !Check sum for the record
C
      INCLUDE 'tsdata.inc'
C
C
C
      SUM = 0
      CARAC2(1:1) = '0'
C
      DO I = CHEKSTAR, RECLEN,2
        CARAC2(1:2) = VECTOR(I:I+1)
        CALL CAR_CHG(4,DECIM,CARAC1,CARAC2)
        SUM = SUM + DECIM
        IF (SUM.GT.255) SUM = SUM - 255
      ENDDO
C
      HEX_SUM = NOT(SUM) + 1
C
      CALL CAR_CHG(3,HEX_SUM,CARAC1,CHECKSUM)
C
      RETURN
C
      END
C
C
C
C ======================================================================
C                                FXILINX
C ======================================================================
C
C   These routine create the XILINX download file
C
      SUBROUTINE FXILINX(IERR)
C
      IMPLICIT NONE
C
      LOGICAL*1
     & READON          !Read ON flag
C
      CHARACTER
     & SLOT*2         ,!Slot number in character
     & RDLINE*60      ,!Read line from XILINX data file
     & COPCODE*1      ,!Opcode in character
     & COM(6)*20       !Comment for XILINX code
C
      INTEGER*2
     & PRSLOT         ,!Previous slot number
     & PRACHAS        ,!Previous chassis number
     & MODNUM1        ,!Modulo number for decoding
     & MODNUM2        ,!Modulo number for decoding
     & NBLINE         ,!Number of line
     & CHAS_NUM       ,!Chassis number
     & SLOT_NUM        !Slot number
C
      INTEGER*4
     & KK             ,!Loop counter
     & II             ,!Loop counter
     & IERR            !Error logger
C
      INCLUDE 'tsdata.inc'
      DATA COM /'OUTPUT=A , INPUT=B  ','OUTPUT=B , INPUT=A  ',
     &          'MASTER=A , OUTPUT=A ','MASTER=A , OUTPUT=B ',
     &          'MASTER=B , OUTPUT=A ','MASTER=B , OUTPUT=B '/
C
C - Print header of the file
C
      WRITE(12,500,IOSTAT=IERR,ERR=901 ) '$  '
      WRITE(12,500,IOSTAT=IERR,ERR=901 ) '$            XILINX FORMATTER'
      WRITE(12,500,IOSTAT=IERR,ERR=901 ) '$              DOWNLOAD FILE '
      WRITE(12,500,IOSTAT=IERR,ERR=901 ) '$  '

      PRACHAS = 0
      PRSLOT = 27
      DO II = 1,SLTASK_MAX
C
         IF (TASK_ID(II).LT.10000) THEN
C
C  - Not a D/A slot, process the option
C
            MODNUM1 = MOD(TASK_ID(II)+0,1000)
            SLOT_NUM =  MOD(MODNUM1+0,100)
            CHAS_NUM = ((MODNUM1-SLOT_NUM)/100)+1
            WRITE(SLOT,'(Z2.2)') SLOT_NUM
C
C  - If a new chassis, print consequent line header
C
            IF(CHAS_NUM.NE.PRACHAS) THEN
C
C  - Set the last slot from previous chassis empty
C
               DO WHILE((PRSLOT+1).LT.28)
                  WRITE(12,504,IOSTAT=IERR,ERR=901 ) PRSLOT+1
                  PRSLOT = PRSLOT+1
               ENDDO
               WRITE(12,501,IOSTAT=IERR,ERR=901 ) CHAS_NUM,
     &                                      DMC_ADD(CHAS_NUM)
               PRACHAS = CHAS_NUM
               PRSLOT = 0
            ENDIF
            DO WHILE((PRSLOT+1).LT.SLOT_NUM)
               WRITE(12,504,IOSTAT=IERR,ERR=901 ) PRSLOT+1
               PRSLOT = PRSLOT+1
            ENDDO
            PRSLOT = SLOT_NUM
            WRITE(12,502,IOSTAT=IERR,ERR=901 ) SLOT_NUM,COM(OPCODE(II))
            WRITE(12,100,IOSTAT=IERR,ERR=901 ) DMC_ADD(CHAS_NUM),SLOT
C
C  - Open XILINX data file
C
            CALL FIL_OPEN(5,1,IERR)
C
            WRITE (COPCODE,'(I1)') OPCODE(II)
            READON = .TRUE.
            DO WHILE(READON)
C
               READ(11,200,ERR=901) RDLINE
               IF(RDLINE(1:1).EQ.COPCODE) THEN
                 READ(RDLINE(3:5),'(I3)',ERR=901) NBLINE
                 DO KK = 1,NBLINE
                    READ(11,200,ERR=901) RDLINE
                    WRITE(12,200,ERR=901) RDLINE
                 ENDDO
                 READON = .FALSE.
               ENDIF
            ENDDO
C
C  - Close XILINX data file
C
            CALL FIL_OPEN(5,2,IERR)
         ENDIF
      ENDDO
C
C  - Set the last slot from previous chassis empty
C
      DO WHILE((PRSLOT+1).LT.28)
         WRITE(12,504,IOSTAT=IERR,ERR=901 ) PRSLOT+1
         PRSLOT = PRSLOT+1
      ENDDO
C
      RETURN
 901  IERR = -20
      RETURN
 100  FORMAT('&',A2,'HX',A2)
 200  FORMAT(A50)
 500  FORMAT(A)
 501  FORMAT('$===== CHASSIS #',I2,' ====  DMC # ',A2,' ========')
 502  FORMAT('$<<<< SLOT # ',I2,' -  ',A20, '>>>>')
 504  FORMAT('$<<<<<<<< NO XILINX ON SLOT # ',I2,' >>>>>>>>>')
      END
C

