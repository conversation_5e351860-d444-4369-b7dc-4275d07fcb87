C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C     This is the COMMON declaration of TSDGEN utility
C
C       Version 0.1       14 September 1987      By: Gaetan De Serre
C               1.0       13 April 1988
C               1.1       13 December 1988
C               1.2       31 July 1990           By: <PERSON>
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
       INTEGER*4    INDEX,IABS,ICHAR,NOT,LEN
       CHARACTER*1  CHAR
       INTRINSIC    IABS,INDEX,CHAR,ICHAR,NOT,LEN
C
       INTEGER*2
     &   MAXCHAS,
     &   MXIOTK,
     &   MXSLOTK,
     &   MXIOTKIN,
     &   MXIOINIT,
     &   Config_L(12),
     &   File_Len(5),
     &   Comp_Id
C
       PARAMETER  (MAXCHAS=10)               !Maximum number of chassis
       PARAMETER  (MXIOTK=100)               !Maximum number of task in IO section
       PARAMETER  (MXSLOTK=270)              !Maximum number of task in SLOT section
       PARAMETER  (MXIOTKIN=30)              !Maximum number of reference task in
       PARAMETER  (MXIOINIT=MXIOTKIN*2*MXIOTK) !Maximum of init words
C                                           IO section
C
       INTEGER*4
     &   NB_REC                           ,!Number of records in .TMP file
     &   TT_SCR                           ,!
     &   FILE_Q(3)                        ,!Data file status
     &   LLINE                            ,!Input line length
     &   INPLEN                           ,!
     &   L_STRG(8)                        ,!Length of input string
     &   PARA_L(8)                         !Input parameters length
C
       INTEGER*2
     &   L_DATA_DIR,
     &   L_INT_DIR,
     &   IOTKCK(MXIOTK)                   ,!IO task check logger
     &   FILE_L(8)                        ,!Data file name length
     &   CH_MAX                           ,!Maximum number of chassis in DATA file
     &   TASK_ID(MXSLOTK)                 ,!Slot task identification number
     &   IO_NUMB(2,MXIOTK,MXIOTKIN)     ,!IO task number of words for transfert
     &   ASLOT(20)                        ,!Assign slot task number
     &   ACHAS(20)                        ,!Assign chassis number
     &   ASSWAY(20)             ,!Assign way of communication via Option XILINX
     &   POSASS(20)                       ,!Assign array slot task position
     &   OPCODE(MXSLOTK)                  ,!Option XILINX code
     &   IOPOS(20)                        ,!Assign task position in IO section
     &   LTITLE(MAXCHAS)                  ,!Title length
     &   IOTASK_MAX                       ,!Maximum number of task-IO section
C                                                 in DATA file
     &   SLTASK_MAX                       ,!Maximum number of task-SLOT section
C                                                 in DATA file
     &   AS_CNT_MAX                        !Maximum number of assign task
C
       CHARACTER
     &   DATA_DIR*40,
     &   INT_DIR*40,
     &   INPDATFIL*80                     ,!Data entered filename
     &   DATFILNAM*80                     ,!Data file name
     &   INFFILNAM*80                     ,!Information file name
     &   DLDFILNAM*80                     ,!Download file name
     &   DLXFILNAM*80                     ,!Download file name
     &   LINE*132                         ,!Input line
     &   FILE_S(8)*52                     ,!Data file name string
     &   FILE_ASS(MAXCHAS)*3             ,!Chassis download file assignment code
     &   TITLE(MAXCHAS)*60                ,!Chassis title
     &   DMC_ADD(MAXCHAS)*2               ,!Chassis DMC address
     &   PARA_S(8)*40                     ,!Parameter string
     &   POSUP*1                          ,!Up window cursor position
     &   POSDOWN*1                        ,!Down window cursor position
     &   ESCAPE*2                         ,!Escape character ASCII definition
     &   IO_TASK(MXIOTK)*8                ,!IO section task name
     &   IO_NAME(2,MXIOTK,MXIOTKIN)*8     ,!IO section X-reference task name
     &   TASK_NAME(MXSLOTK)*8             ,!Slot section task name
     &   TASK_PORT(MXSLOTK,2)*8           ,!Slot section port name
     &   STRG(8)*132                      ,!Input string
     &   ASSIGN(20)*8                     ,!Assign tasks for the X-Ref array
     &   DMC_Num*2                        ,!Parameter from XLINK file...
     &   Page_Num*2                       ,!Parameter from XLINK file...
     &   Filetters*3                      ,!SN or RF from XLINK file...
     &   File_N(5)*40                     ,!Name of input data files
     &   Config_S(12)*80                   !Logical name definitions
C
       LOGICAL*1
     &   VAXSEL                           ,!Computer index (T=VAX)
     &   STDLONE                          ,!Standalone flag
     &   OPTION                           ,!Option XILINX flag
     &   SLOTASK_M(MXSLOTK)               ,!Slot task match with IO task flags
     &   T_ECHO                           ,!Terminal echo flag
     &   ComFlag(6)                       ,!Communication flags from XLINK
     &   LinkFlag(4)                       !On Site Flag...
C
       INTEGER*2
     &   MAT_DIM(2)                       ,!X-Ref array dimension
     &   IOCHECK(2,MXIOTK,MXIOTKIN)       ,!X-Ref checkpoint array
     &   TASK_OPT(MXSLOTK,2)              ,!Slot option number
     &   MATRIX(20,20)                     !X-Ref array
C
C     COMMON BLOCK DECLARATION
C     ------------------------
C
      COMMON /INTEGER2/ IOTASK_MAX,SLTASK_MAX,AS_CNT_MAX,
     &                  CH_MAX,FILE_L,ASSWAY,OPCODE,
     &                  POSASS,TASK_ID,MAT_DIM,MATRIX,LTITLE,
     &                  IO_NUMB,IOPOS,ACHAS,ASLOT,IOCHECK,TASK_OPT,
     &                  IOTKCK,L_DATA_DIR,L_INT_DIR,Config_L,
     &                  Comp_Id,File_len
C
      COMMON /INTEGER4/ INPLEN,LLINE,TT_SCR,L_STRG,PARA_L,FILE_Q,
     &                  NB_REC
C
      COMMON /CHARAC1/ POSUP,POSDOWN
C
      COMMON /CHARAC2/ DMC_ADD,ESCAPE,DMC_Num,Page_Num
C
      COMMON /CHARAC3/ FILE_ASS,Filetters
C
      COMMON /CHARAC8/ TASK_NAME,TASK_PORT,ASSIGN,IO_TASK,IO_NAME
C
      COMMON /CHARAC40/ PARA_S,DATA_DIR,INT_DIR,File_N
C
      COMMON /CHARAC52/ FILE_S
C
      COMMON /CHARAC60/ TITLE
C
      COMMON /CHARAC80/ DLXFILNAM,DLDFILNAM,DATFILNAM,INFFILNAM,
     &                  INPDATFIL,Config_S
C
      COMMON /CHARAC132/ LINE,STRG
C
      COMMON /LOGICAL1/ STDLONE,OPTION,T_ECHO,SLOTASK_M,VAXSEL,
     &                  LinkFlag,ComFlag
C
