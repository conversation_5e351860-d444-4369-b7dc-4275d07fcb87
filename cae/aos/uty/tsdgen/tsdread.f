C
C
C  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                          FILE TSDREAD.FOR
C
C               CONTAINS SUBROUTINES TO READ THE DATA FILE:
C
C                        READ_DATA
C                        READ_IO
C                        READ_SLOT
C                        OPTXIL
C
C  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C ====================================================================
C                              READ_DATA
C ====================================================================
C
C    This routine read a line without comment character or blank,
C    identify if section logic is OK and call processing subroutines
C    to read the fields in SLOT and IO section.
C
C
      SUBROUTINE READ_DATA(ERR_READ,LINE_CNT)
C
      IMPLICIT NONE
C
        LOGICAL*1
     & COMMENT               ,!Comment or blank line flag
     & TITLE_OK              ,!Title found with right SLOT section header flag
     & SLOT_OK               ,!Slot section header OK flag
     & DMC_OK                ,!DMC line found with right title flag
     & XA_OK                 ,!Slot task input line OK flag
     & END_OK                ,!IO section can be closed flag
     & X_REF_OK              ,!IO section X-Reference can be opened flag
     & IO_SLOT_OK            ,!slot section close properly flag
     & READ_ON               ,!Data file read flag
     & EXIT_OK                !Exit OK flag
C
        INTEGER*4
     & ERR_READ              ,!Error read counter
     & SLTASK_CNT            ,!Slot task counter
     & IOTASK_CNT            ,!IO task counter
     & IOLINE_CNT            ,!IO line counter
     & LOC_EQ                ,!Position of the equal sign in the line
     & SLOT_NB               ,!Number of slot section
     & LINE_CNT              ,!Data file line counter
     & SL_CNT                ,!Slot counter
     & CH_CNT                ,!Chassis counter
     & II                    ,!Loop pointer
     & LIN_COD_L(8)          ,!Length of the data array for TSDGEN header
     & CODE                  ,!TSDGEN line code
     & POSI(8)               ,!Position of the TSDGEN header in the line
     & ERR                   ,!Error identifier
     & FOUND                  !IO input or output line counter
C
        INTEGER*2
     & POSXIL                ,!Position of the XILINX option qualifier
     & IO_COMMA              ,!Continuation line identifier in IO section
     & NO_SLASH               !No slash counter in IO section
C
        CHARACTER
     & COMPRESS*8            ,!Compressed character string
     & TABCAR*1              ,!ASCII tabulation character
     & CARAC*60              ,!Non-compressed character srting
     & LIN_COD(8)*10          !Data array of TSDGEN header
C
       INCLUDE 'tsdata.inc'
C
      DATA LIN_COD /'SLOT_START','SLOT_END','IO_START','IO_END',
     &              'TITLE','DMC =','XA','OPTION'/
      DATA LIN_COD_L / 10, 8, 8 ,6 , 5 , 5, 2 , 6 /
C
      TABCAR = CHAR(9)
      READ_ON = .TRUE.
      OPTION = .FALSE.
      ERR_READ=0
      DO WHILE(READ_ON)
C
         ERR = 0
         COMMENT = .TRUE.
         DO WHILE(COMMENT.AND.(ERR.EQ.0))
C
C           Read a line from data file: OUTPUT is LINE
C           ------------------------------------------
            CALL READQ(2,ERR)                         ! File TSDLIB.FOR
C
C           Continue to read until no comment or blank line found
C           -----------------------------------------------------
            COMMENT = (INDEX(LINE,'*').EQ.1).OR.(LLINE.EQ.0)
C
         ENDDO
C
C        Check for TAB character in the line
C        -----------------------------------
         IF (INDEX(LINE,TABCAR).NE.0) THEN
             CALL MESSAGE(2)
             WRITE (6,15) ESCAPE,POSDOWN,LINE
             ERR = 1555
             IF(CH_CNT.EQ.0) CH_CNT=1
         ENDIF
C
         IF (ERR.EQ. 0 ) THEN
C
            LINE_CNT = LINE_CNT + 1
C
C           Find the code for the line :    1-Slot header : SLOT_START
C           --------------------------      2-Slot header : SLOT_END
C               	                    3-I/O header : IO_START
C				            4-I/O header : IO_END
C				            5-Title
C				            6-DMC code
C				            7-Slot identifier
C     				            8-X-Reference line
C
            CODE = 9  !If 8 first code not found, code is 9 by default
            DO II=1,8
               POSI(II) = INDEX(LINE,LIN_COD(II)(1:LIN_COD_L(II)))
               IF (POSI(II).NE.0) THEN
C
C                 Valid code 7 only if on position 1
C                 ----------------------------------
                  IF(II.NE.7.OR.POSI(7).EQ.1) THEN
                    CODE = II
                  ENDIF
               ENDIF
            ENDDO
C
C           ========= START OF CODE CHECK SECTION ==============
C
            IF (CODE.EQ.7)THEN
C
               IF(TITLE_OK) THEN
C
C                 Add one slot read
C                 -----------------
                  SL_CNT = SL_CNT + 1
C
C                 Set slot section close possible
C                 -------------------------------
                  IF (SL_CNT.EQ.27)THEN
                     XA_OK=.TRUE.
                  ELSE
                     XA_OK=.FALSE.
                  ENDIF
C
C                 Read and store datas in slot section
C                 ------------------------------------
                  CALL READ_SLOT(SLTASK_CNT,SL_CNT,CH_CNT,ERR_READ)
               ELSE
                  ERR_READ=71 !Error in procedure order:No header before
                  CALL MESSAGE(2)
                  WRITE(6,710) ESCAPE,POSDOWN,LINE_CNT
C
               ENDIF
C
            ELSEIF(CODE.EQ.1) THEN    !SLOT_START
C
               IF(SLOT_NB.EQ.0) THEN
C
C                 Indicate that SLOT section is open
C                 ----------------------------------
                  SLOT_OK = .TRUE.
                  SLOT_NB = SLOT_NB + 1
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,100) ESCAPE,POSDOWN,LINE_CNT !TOO many slots in slot section
                  ERR_READ = 1
               ENDIF
C
            ELSE IF(CODE.EQ.2) THEN      !SLOT_END
C
               IF(XA_OK) THEN
C
C                 Close slot section and indicate ready to IO section
C                 ---------------------------------------------------
                  IO_SLOT_OK = .TRUE.
                  XA_OK = .FALSE.
                  TITLE_OK = .FALSE.
                  SLTASK_MAX=SLTASK_CNT
C
                  CH_MAX = CH_CNT  !Set the maximum number of chassis
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,200) ESCAPE,POSDOWN,LINE_CNT   !Slot section can not be closed
                  ERR_READ = 2
C
               ENDIF
C
            ELSE IF(CODE.EQ.3) THEN      !IO_START
C
               IF (IO_SLOT_OK) THEN
                  X_REF_OK = .TRUE.
                  IO_SLOT_OK = .FALSE.
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,300) ESCAPE,POSDOWN,LINE_CNT  !IO section in wrong order
                  ERR_READ = 3
               ENDIF
C
            ELSE IF(CODE.EQ.4) THEN      !IO_END
C
               IF (END_OK) THEN
                  EXIT_OK = .TRUE. !Exit OK: everything found
                  READ_ON = .FALSE.
C
C                 Set last IO task maximum number of X-Ref tasks
C                 ----------------------------------------------
                  IO_NUMB(1,IOTASK_CNT,IOLINE_CNT+1) = 999
                  IO_NUMB(2,IOTASK_CNT,IOLINE_CNT+1) = 999
                  IOTASK_MAX = IOTASK_CNT  !Set maximum number of task in IO section
C
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,400) ESCAPE,POSDOWN,LINE_CNT !Close IO section without finish
                  ERR_READ = 4
               ENDIF
C
            ELSE IF(CODE.EQ.5) THEN      !TITLE
C
C              Add one new chassis
C              -------------------
               CH_CNT = CH_CNT + 1
               IF (SLOT_OK.OR.XA_OK) THEN
                  XA_OK = .FALSE.
                  SLOT_OK = .FALSE.
                  DMC_OK  = .TRUE.
C
                  IF ( CH_CNT.LE.MAXCHAS) THEN
                      LOC_EQ = INDEX(LINE,'=')
                      TITLE(CH_CNT)  = LINE(LOC_EQ+2:LLINE)
                      LTITLE(CH_CNT) = LLINE-(LOC_EQ+1)
                  ELSE
                      CALL MESSAGE(2)
                      WRITE (6,510) ESCAPE,POSDOWN,LINE_CNT !Too many chassis in the data file
                      ERR_READ = 51
                  ENDIF
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,500) ESCAPE,POSDOWN,CH_CNT,LINE_CNT  !New chassis error
                  ERR_READ = 5
               ENDIF
C
            ELSE IF(CODE.EQ.6) THEN      !DMC
C
               IF (DMC_OK) THEN
C
                  SL_CNT = 0
                  TITLE_OK = .TRUE.
                  DMC_OK = .FALSE.
C
                  LOC_EQ = INDEX (LINE,'=')
C
C                 IF first chassis and not in standalone mode, used
C                 DMC number from parameter file
C                 -------------------------------------------------
                  IF(CH_CNT.EQ.1.AND.(.NOT.STDLONE)) THEN
                     IF(LINE(LOC_EQ+2:LOC_EQ+4).NE.
     &                  DMC_Num(1:2)           )THEN
                        CALL MESSAGE(2)
                        WRITE (6,580) ESCAPE,POSDOWN,CH_CNT !DMC address mismatch between data file and utility parameter
                        ERR_READ =58
                     ENDIF
                     DMC_ADD(1)=DMC_Num(1:2)
                  ELSE
                     DMC_ADD(CH_CNT) = LINE(LOC_EQ+2:LOC_EQ+4)
                  ENDIF
C
               ELSE
                  CALL MESSAGE(2)
                  WRITE (6,610) ESCAPE,POSDOWN,CH_CNT,LINE_CNT  !DMC address without proper header
                  ERR_READ =61
               ENDIF
C
            ELSE IF(CODE.EQ.8) THEN      !OPTION
C
               IF(SLOT_OK) THEN
C
                  POSXIL = INDEX(LINE,'= XILINX')
                  IF(POSXIL.GT.0) THEN
                     OPTION=.TRUE.
                  ENDIF
C
               ELSE
                  CALL MESSAGE(2)
                  WRITE (6,920) ESCAPE,POSDOWN,CH_CNT !Option misplace
                  ERR_READ =91
               ENDIF
C
            ELSE     !X-REFERENCE (CODE=8)
C
               IF (X_REF_OK) THEN
C
                  FOUND = 0
C
                  IF (IO_COMMA.NE.0)THEN
C
C                    Check if there is no TASK for that cont. line
C                    ---------------------------------------------
                     IF(LINE(2:14).EQ. '             ') THEN
C
                       IF(IO_COMMA.EQ.1) THEN
C
C                        The line is the continuation of an output statement
C                        ---------------------------------------------------
                         NO_SLASH = 1
                         CALL READ_IO(IO_COMMA,15,34,1,IOTASK_CNT,
     &                                IOLINE_CNT,FOUND,NO_SLASH,END_OK)
C
                       ELSEIF(IO_COMMA.EQ.2) THEN
C
C                        The line is the continuation of an input statement
C                        --------------------------------------------------
                         NO_SLASH = 1
                         CALL READ_IO(IO_COMMA,35,54,2,IOTASK_CNT,
     &                                IOLINE_CNT,FOUND,NO_SLASH,END_OK)
C
                       ENDIF
                     ELSE
C
C                      Print wrong continuation line
C                      -----------------------------
                       CALL MESSAGE(2)
                       WRITE(6,830) ESCAPE,POSDOWN,LINE_CNT
                       ERR_READ=83
                     ENDIF
                  ELSE
C
C                    The line is without any continuation mark
C                    -----------------------------------------
                     CARAC = LINE(2:14)
                     IF (CARAC.NE.'             ')THEN
                        IF(IOTASK_CNT.NE.0)THEN
                           IO_NUMB(1,IOTASK_CNT,IOLINE_CNT+1) = 999
                           IO_NUMB(2,IOTASK_CNT,IOLINE_CNT+1) = 999
                        ENDIF
                        IOTASK_CNT = IOTASK_CNT+1
                        CALL CAR_CMP(CARAC,COMPRESS)
                        IO_TASK(IOTASK_CNT) = COMPRESS  !Store task name
                        IOLINE_CNT = 0
                     ENDIF
C
                     NO_SLASH = 0
                     CALL READ_IO(IO_COMMA,15,34,1,IOTASK_CNT,
     &                            IOLINE_CNT,FOUND,NO_SLASH,END_OK)
                     CALL READ_IO(IO_COMMA,35,54,2,IOTASK_CNT,
     &                            IOLINE_CNT,FOUND,NO_SLASH,END_OK)
C
                  ENDIF
C
C                 Write wrong line with no slash in IO section(Not SEVERE)
C                 --------------------------------------------------------
                  IF (NO_SLASH.GE.2) THEN
                     CALL MESSAGE(2)
                     WRITE(6,810) ESCAPE,POSDOWN,IO_TASK(IOTASK_CNT)
     &                                   ,LINE_CNT
                  ENDIF
C
C                 Write IO section with output/input on the same line
C                 ---------------------------------------------------
                  IF (FOUND.GE.2)THEN
                     CALL MESSAGE(2)
                     WRITE(6,820) ESCAPE,POSDOWN,LINE_CNT
                     ERR_READ=82
                  ENDIF
C
               ELSE
                  CALL MESSAGE(2)
                  WRITE(6,800) ESCAPE,POSDOWN,LINE_CNT !IO section without proper header
                  ERR_READ = 8
               ENDIF
C
            ENDIF
C
            IF(ERR_READ.GT.0) READ_ON=.FALSE. !Exit reading loop
C
C           ========= END OF CODE CHECK SECTION ==============
C
         ELSEIF(ERR.LT.0)THEN
C
C           End of file found
C           -----------------
            READ_ON = .FALSE. !Exit reading loop
C
            IF (.NOT.EXIT_OK)THEN
C
C              End of file without all datas found
C              -----------------------------------
               CALL MESSAGE(2)
               ERR_READ = 91
               WRITE (6,910) ESCAPE,POSDOWN,LINE_CNT
            ENDIF
C
         ELSE
C
C           Error when reading a line in data file
C           --------------------------------------
            READ_ON = .FALSE. !Exit reading loop
            ERR_READ = 90
            CALL MESSAGE(2)
            WRITE(6,900) ESCAPE,POSDOWN,ERR,SL_CNT,CH_CNT,LINE_CNT
         ENDIF
      ENDDO
C
      RETURN
C
  15  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** "TAB" CHARACTER FOUND: '
     &        ,A45,' *')
 100  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** MORE THAN ONE SLOT SECT'
     &        ,'ION FOUND                        * LINE ',I3,'*')
 200  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** SLOT SECTION CLOSE WITH'
     &        ,' LESS THAN 27 SLOTS              * LINE ',I3,'*')
 300  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** IO SECTION BEGINS BEFOR'
     &        ,'E SLOT SECTION IS CLOSED         * LINE ',I3,'*')
 400  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** IO SECTION CLOSED IN A '
     &        ,'WRONG SEQUENCE                   * LINE ',I3,'*')
 500  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** NEW CHASSIS (#',I2,
     &        ') IN SLOT SECTION WITHOUT PROPER HEADER * LINE ',I3,'*')
 510  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** TOO MANY CHASSIS IN THE'
     &        ,' DATA FILE                       * LINE ',I3,'*')
 580  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** MISMATCH OF DMC ADDRESS'
     &        ,' DATA FILE-PARAMETER IN CHASSIS #',I2,'    *')
 610  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** DMC ADDRESS FOUND WITHO'
     &        ,'UT PROPER HEADER IN CHASSIS #',I2,'  * LINE ',I3,'*')
 710  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** SLOT HEADER INCORRECTLY'
     &        ,' POSITIONED OR MISSING           * LINE ',I3,'*')
 800  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** IO HEADER INCORRECTLY P'
     &        ,'OSITIONED OR MISSING             * LINE ',I3,'*')
 810  FORMAT (' ',A2,'2',A1,';1H',2X,'%WAR - ** LINE WITH NO SLASH FOUN'
     &        ,'D IN IO SECTION ',A8,'         * LINE ',I3,'*')
 820  FORMAT (' ',A2,'2',A1,';1H',2X,'%WAR - ** IN/OUT ON THE SAME LINE'
     &        ,' IN IO SECTION-INPUT IGNORED     * LINE ',I3,'*')
 830  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** NEW TASK LINE IN IO SEC'
     &        ,'TION AFTER A CONT. CHARACTER     * LINE ',I3,'*')
 900  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** READ ERROR #',I5,' AT S'
     &        ,'LOT XA',I2,' IN CHASSIS #',I3,'         * LINE ',I3,'*')
 910  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** END OF FILE FOUND WITH '
     &        ,'DATAS NOT COMPLETE               * LINE ',I3,'*')
 920  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** OPTION STATEMENT MISPLA'
     &        ,'CED IN CHASSIS # ',I2,'              *')
      END
C
C
C ============================================================
C                         READ_IO
C ============================================================
C
C   This subroutine reads all the lines in IO section and
C   generate specific IO arrays according to the character
C   position on the line.
C
C   INPUT: LINE and LLINE
C
C   OUTPUT: IO_NUMB(x,y,z) : X-Reference number of words to transfert with
C                             special code
C                              - :negative number represent a task using the
C                                 same slot as the previous one
C                            400-500: Special D/A
C                              >500 : No X-check have to be done
C           IO_NAME(x,y,z) : X-Reference task name
C           IO_TASK(y)     : IO task (Set in routine READ_DATA)
C
C            Where: x is 1 for output X-Ref and 2 for input X-Ref
C                   y is the IO task number
C                   z is the X-Ref task count
C
       SUBROUTINE READ_IO(IO_COMMA,POS_MIN,POS_MAX,BANK,IOTASK_CNT,
     &                   IOLINE_CNT,FOUND,NO_SLASH,END_OK)
C
      IMPLICIT NONE
C
        CHARACTER
     & PROLINE*20             ,!Regenerated line without unusable character
     & CARAC*60               ,!Non-compressed character
     & COMPRESS*8             ,!Compressed character
     & CARR(4)*1              ,!Dummy character variable
     & NUMCAR*2                !Slot number in ASCII character
C
        INTEGER*2
     & HYPHEN                 ,!Hyphen pointer
     & IO_COMMA               ,!Continuation line identifier
     & NO_SLASH               ,!No slash found counter
     & NUMBER                 ,!Slot number in decimal
     & P_SLASH1               ,!Position of the first slash
     & P_SLASHN               ,!Position of the Nth slash
     & P_SLASHP               ,!Position of the previous slash
     & DACAR                  ,!Special control character (*) position
     & SPECAR                  !Special control character (#) position
C
        INTEGER*4
     & POS_MIN                ,!Minimum position character
     & POS_MAX                ,!Maximum position character
     & BANK                   ,!Input or output section pointer
     & FOUND                  ,!Found slash counter
     & IOLINE_CNT             ,!IO section line counter
     & IOTASK_CNT             ,!IO section task counter
     & P_COMMA                 !Position of the comma
C
        LOGICAL*1
     & FINISH                 ,!Look for a slash loop flag
     & END_OK                 ,!IO section can be closed flag
     & FIRSLASH                !First slash found flag
C
      INCLUDE 'tsdata.inc'
C
      FINISH = .TRUE.
C
C     Create a new line related to the position of the analysed character
C     -------------------------------------------------------------------
      PROLINE(1:20) = LINE(POS_MIN:POS_MAX)
C
C     Look for first slash
C     --------------------
      P_SLASH1 = INDEX(PROLINE(1:20),'/')
      P_SLASHP = P_SLASH1 !Set update previous slash position
C
C     Skip next section if a continued line
C     -------------------------------------
      IF(IO_COMMA.NE.BANK)THEN
C
         IF(P_SLASH1.GT.0)THEN
C
            NUMBER = 0
            FIRSLASH = .TRUE.
            FOUND = FOUND +1
C
C           Set number for the IO and check special character #(no check)
C           -------------------------------------------------------------
            SPECAR = INDEX(PROLINE(1:20),'#')
            IF(SPECAR.GT.0)THEN
C
C              Special character found: increase code number by 500
C              ----------------------------------------------------
               NUMCAR(1:2) = '  '
               NUMCAR(4+SPECAR-P_SLASH1:2)=PROLINE(SPECAR+1:P_SLASH1-1)
               CALL CAR_CHG(2,NUMBER,CARR,NUMCAR)            !File TSDLIB.FOR
               NUMBER = NUMBER + 500
            ELSE
C
C              Check for special D/A character
C              -------------------------------
               DACAR = INDEX(PROLINE(1:20),'*')
               IF(DACAR.GT.0) THEN
                 NUMCAR(1:2) = '  '
                 NUMCAR(4+DACAR-P_SLASH1:2)=PROLINE(DACAR+1:P_SLASH1-1)
                 CALL CAR_CHG(2,NUMBER,CARR,NUMCAR)            !File TSDLIB.FOR
                 NUMBER = NUMBER + 400
               ELSE
C
C                No special character: just get number
C                -------------------------------------
                 NUMCAR = PROLINE(P_SLASH1-2:P_SLASH1-1)
                 CALL CAR_CHG(2,NUMBER,CARR,NUMCAR)            !File TSDLIB.FOR
               ENDIF
            ENDIF
C
         ELSE
            FINISH = .FALSE.  !Skip all new look for slash
            NO_SLASH = NO_SLASH+1  !Add error counter to ind. no slash found
         ENDIF
      ELSE
         IO_COMMA=0
      ENDIF
C
      DO WHILE(FINISH)
C
C        Look for a new slash or comma
C        -----------------------------
         P_SLASHN = (INDEX(PROLINE(P_SLASHP+1:20),'/'))+P_SLASHP
         P_COMMA  = (INDEX(PROLINE(P_SLASHP+1:20),','))+P_SLASHP
C
         IOLINE_CNT=IOLINE_CNT + 1  !Add one line in this task
C
         IF (P_SLASHN.GT.P_SLASHP) THEN
C
C           Task between 2 slashes: get task
C           --------------------------------
            CARAC = PROLINE(P_SLASHP+1:P_SLASHN-1)
C
            P_SLASHP = P_SLASHN  !Update previous slash position
C
         ELSEIF(P_COMMA.GT.P_SLASHP)THEN
C
C           Task between 1 slash and 1 comma: get task and set continuation
C           identifier. Inhibit successful exit and don't look for a new slash
C           ------------------------------------------------------------------
            CARAC = PROLINE(P_SLASHP+1:P_COMMA-1)
C
            IO_COMMA = BANK
            END_OK = .FALSE.
            FINISH = .FALSE. !Leave subroutine
        ELSE
C
C           Task between 1 slash and 1 space: get task and enable correct exit.
C           -------------------------------------------------------------------
            CARAC = PROLINE(P_SLASHP+1:20)
C
            END_OK = .TRUE.
            FINISH = .FALSE. !Leave subroutine
         ENDIF
C
C        Store task number of words
C        --------------------------
         IF(.NOT.FIRSLASH)THEN
            IO_NUMB(BANK,IOTASK_CNT,IOLINE_CNT) = -NUMBER
         ELSE
            FIRSLASH = .FALSE.
            IO_NUMB(BANK,IOTASK_CNT,IOLINE_CNT) =  NUMBER
         ENDIF
C
C        Store compressed task name: max 8 characters
C        --------------------------------------------
         CALL CAR_CMP(CARAC,COMPRESS)
         IO_NAME(BANK,IOTASK_CNT,IOLINE_CNT) = COMPRESS
C
      ENDDO
C
C
      RETURN
      END
C
C
C ================================================================
C                           READ_SLOT
C ================================================================
C
C    This routine reads the SLOT section of the data file.
C
C     INPUT :  LINE , LLINE
C
C     OUTPUT:  TASK_ID(y) : Contains number to identify this task
C                           5 digits number:
C                            2   LSDigits is the slot number
C                            3rd digit is the chassis number
C                            4th digit is the port identification
C                                0000-1000 Only PORT A
C                                1000-2000 PORT A and PORT B
C                                2000-3000 Only PORT B
C                            5th digit is the special D/A task identifier
C                                1-Special D/A , 0-Nothing special for D/A
C
C              TASK_NAME(y) : Contains the slot task name
C
C              TASK_PORT(y,b) : Contains the slot port name
C
C              TASK_OPT(y,b) : Option number for processing
C                              0 no option
C                              + number is output
C                              - number is input
C                              |number|>1 is MASTER
C
C              OPCODE(y) : Option number for XILINX output file
C                            0- No option
C                            1- Output in A , input in B
C                            2- Output in B , input in A
C                            3- Output in A , input in B , Master A
C                            4- Output in B , input in A , Master A
C                            5- Output in A , input in B , Master B
C                            6- Output in B , input in A , Master B
C
C              DAXIL     : D/A port assognation for XILINX file
C                            1- Port A
C                            2- Port B
C
C              OPTA      : Option on port A flag
C              OPTB      : Option on port B flag
C
C        Where:
C               y : Slot section task counter
C               b : 1-PORT A , 2-PORT B
C
C
      SUBROUTINE READ_SLOT(SLTASK_CNT,SL_CNT,CH_CNT,ERR_READ)
C
      IMPLICIT NONE
C
        LOGICAL*1
     & OPTA                    ,!Port A exist for option XILINX
     & OPTB                     !Port B exist for option XILINX
C
        INTEGER*4
     & IERR                    ,!Option XILINX error logger
     & ERR_READ                ,!Read error logger
     & STAR                    ,!Star position
     & P_SLASH1                ,!First slash position
     & P_SLASH2                ,!Second slash position
     & P_SLASH3                ,!Third slash position
     & SL_CNT                  ,!Slot counter
     & CH_CNT                  ,!Chassis counter
     & SLTASK_CNT              ,!Slot task counter
     & PLINE                    !Length of regenerated line
C
        CHARACTER
     & COMPRESS*8              ,!Compressed character
     & CARAC*60                ,!Non-compressed character
     & PROLINE*126              !Regenrated line without unusable character
C
        INTEGER*2
     & DAXIL                   ,!D/A port for XILINX option
     & INCNUMB                 ,!Number increment
     & NUMBER                   !Slot task identification number
C
      INCLUDE 'tsdata.inc'
C
      DAXIL = 0
C
C     Skip slash check section if line length<8
C     -----------------------------------------
      IF (LLINE.GE.7) THEN
C
C        Create a new line without 'XAii ='
C        ----------------------------------
         PROLINE(1:126) = LINE(7:132)
         PLINE = LLINE -6
C
C        Find first slash position in the line
C        -------------------------------------
         P_SLASH1 = INDEX(PROLINE(1:PLINE),'/')
C
         IF (P_SLASH1.GT.0) THEN
C
C           Reset option port flags
C           -----------------------
            OPTA = .FALSE.
            OPTB = .FALSE.
C
            SLTASK_CNT = SLTASK_CNT+1
            CALL CAR_CHG(2,NUMBER,' ',LINE(3:4))
            NUMBER= NUMBER+100*(CH_CNT-1)
            IF (P_SLASH1.GE.24) THEN
C
C              XA line without PORT A: store task name
C              ---------------------------------------
               NUMBER = NUMBER+2000
               CARAC = PROLINE(1:P_SLASH1-1)
               CALL CAR_CMP(CARAC,COMPRESS)
               STAR = INDEX(COMPRESS,'*')
C
C              Check if special D/A task: special character *
C              ----------------------------------------------
               IF(STAR.EQ.1) THEN
                  DAXIL = 2  !Port B D/A for XILINX
                  NUMBER = NUMBER+10000
                  COMPRESS(1:8)=COMPRESS(2:8)//' '
               ENDIF
               TASK_NAME(SLTASK_CNT) = COMPRESS  !Set task name
C
C              P_SLASH2 is the option character ':'
C              ------------------------------------
               P_SLASH2=(INDEX(PROLINE(P_SLASH1+1:PLINE),':'))+P_SLASH1
C
               IF (P_SLASH2.GT.P_SLASH1) THEN
C
C                 Option character found: store PORT B
C                 ------------------------------------
                  CARAC = PROLINE(P_SLASH1+1:P_SLASH2-1)
                  CALL CAR_CMP(CARAC,COMPRESS)
                  TASK_PORT(SLTASK_CNT,2) = COMPRESS !Set PORT B name
C
               ELSE
C
C                 Option character not found : set PORT B name
C                 --------------------------------------------
                  CARAC = PROLINE(P_SLASH1+1:PLINE)
                  CALL CAR_CMP(CARAC,COMPRESS)
                  TASK_PORT(SLTASK_CNT,2) = COMPRESS  !Set PORT B name
C
               ENDIF
               OPTB = .TRUE.  !Set port B exist for option process
C
            ELSE
C
C              XA line with PORT A: store it
C              -----------------------------
               CARAC = PROLINE(1:P_SLASH1-1)
               IF(CARAC.EQ.'  ' )THEN
                 INCNUMB=2000  !No port A
               ELSE
                  INCNUMB=1000  !With Port A
               ENDIF
               CALL CAR_CMP(CARAC,COMPRESS)
               TASK_PORT(SLTASK_CNT,1) = COMPRESS  !Set PORT A name
               OPTA = .TRUE.  !Set port A exist for option process
C
               P_SLASH2=(INDEX(PROLINE(P_SLASH1+1:PLINE),'/'))+P_SLASH1
C
               IF (P_SLASH2.GT.P_SLASH1) THEN
C
C                 Store the task name
C                 -------------------
                  CARAC = PROLINE(P_SLASH1+1:P_SLASH2-1)
                  CALL CAR_CMP(CARAC,COMPRESS)
                  STAR = INDEX(COMPRESS,'*')
C
C                 Check if special D/A task: special character *
C                 ----------------------------------------------
                  IF(STAR.EQ.1) THEN
                     ERR_READ = -998
                     CALL MESSAGE(2)
                     WRITE(6,998) ESCAPE,POSDOWN,CH_CNT,SL_CNT
                  ENDIF
                  TASK_NAME(SLTASK_CNT) = COMPRESS
C
C                 P_SLASH3 is the option character
C                 --------------------------------
                  P_SLASH3 = (INDEX(PROLINE(P_SLASH2+1:PLINE),':'))+
     &                         P_SLASH2
                  IF(P_SLASH3.GT.P_SLASH2)THEN
C
C                    Option character found: set PORT B
C                    ----------------------------------
                     CARAC = PROLINE(P_SLASH2+1:P_SLASH3-1)
                     CALL CAR_CMP(CARAC,COMPRESS)
                     TASK_PORT(SLTASK_CNT,2)= COMPRESS
                     OPTB = .TRUE.
C
                  ELSE
C
C                    Option character not found : set PORT B
C                    ---------------------------------------
                     CARAC = PROLINE(P_SLASH2+1:PLINE)
                     CALL CAR_CMP(CARAC,COMPRESS)
                     TASK_PORT(SLTASK_CNT,2)= COMPRESS
C
                  ENDIF
C
C                 Set ID number for PORT A and PORT B
C                 -----------------------------------
                  NUMBER = NUMBER + INCNUMB
               ELSE
C
C                 Only 1 slash found: set TASK array
C                 ----------------------------------
                  CARAC = PROLINE(P_SLASH1+1:PLINE)
                  CALL CAR_CMP(CARAC,COMPRESS)
                  STAR = INDEX(COMPRESS,'*')
C
C                 Check if special D/A task: special character *
C                 ----------------------------------------------
                  IF(STAR.EQ.1) THEN
                     DAXIL = 1  !Port A D/A for XILINX
                     NUMBER = NUMBER+10000
                     COMPRESS(1:8)=COMPRESS(2:8)//' '
                  ENDIF
                  TASK_NAME(SLTASK_CNT)= COMPRESS
               ENDIF
            ENDIF
C
C           Store number ID for that task
C           -----------------------------
            TASK_ID(SLTASK_CNT) = NUMBER
C
C           Process the option XILINX: check for master and
C           port association.
C           Check only if OPTION = XILINX exist in the chassis
C           OPTION is TRUE
C           --------------------------------------------------
            IF(OPTION) THEN
               CALL OPTXIL(OPTA,OPTB,DAXIL,SLTASK_CNT,IERR)
               IF (IERR.NE.0) THEN
                 ERR_READ = -IERR
               ENDIF
            ENDIF
         ENDIF
C
      ENDIF
 998  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Two ports associated wi'
     &        ,'th D/A task in ch#',I2,', sl#',I2)
C
      RETURN
      END
C
C
C ================================================================
C                         OPTXIL
C ================================================================
C
C    This routine process the Option XILINX qualifier which
C    specify the MAster slot and the input/output BUS
C
C
      SUBROUTINE OPTXIL(OPTA,OPTB,DAXIL,SL_CNT,IERR)
C
      IMPLICIT NONE
C
      CHARACTER
     & PARAM(6)*20              !Option parameters
C
      INTEGER*2
     & DAXIL                   ,!D/A port for XILINX option
     & POSCAR                  ,!Position of option delimeter
     & NOUT                    ,!Number of input/output field
     & L_PARAM(6)              ,!Length of option parameters
     & MASTER                  ,!Master field position
     & OUTPUT                  ,!Output field position
     & INPUT                   ,!Input field position
     & K                        !Parameters counter
C
      INTEGER*4
     & SL_CNT                  ,!Slot task count
     & IERR                    ,!Error logger
     & II                       !Loop counter
C
      LOGICAL*1
     & OPTA                    ,!Option port A flag
     & OPTB                     !Option port A flag
C
      INCLUDE 'tsdata.inc'
C
C - Decode all parameters after the character ':'
C
      NOUT = 0
C
      DO II = 1 , 6
         L_PARAM(II) = 0
      ENDDO
      POSCAR = INDEX(LINE,':')
C
C - Look for number of parameters only if option character exist
C
      IF (POSCAR.GT.0) THEN
        K = 1
        DO II=POSCAR+1 , LLINE
           IF(LINE(II:II).NE.',')THEN
              L_PARAM(K) = L_PARAM(K) + 1
              PARAM(K)(L_PARAM(K):L_PARAM(K)) = LINE(II:II)
           ELSE
              K = K + 1
           ENDIF
        ENDDO
      ENDIF
C
C  - Decode first parameters
C
      IF (L_PARAM(1).GT..0) THEN
C
C  - Check for output bus
C
          OUTPUT = INDEX(PARAM(1)(1:L_PARAM(1)),'O')
          IF (OUTPUT.GT.0) THEN
              NOUT = NOUT + 1
              IF(PARAM(1)(OUTPUT+1:OUTPUT+1).EQ.'A'
     &                                          .AND.OPTA) THEN
                 TASK_OPT(SL_CNT,1) = 1
                 OPCODE(SL_CNT) = 1
                 IF (OPTB) TASK_OPT(SL_CNT,2) = - 1
              ELSEIF(PARAM(1)(OUTPUT+1:OUTPUT+1).EQ.'B'
     &                                          .AND.OPTB)THEN
                 TASK_OPT(SL_CNT,2) = 1
                 OPCODE(SL_CNT) = 2
                 IF (OPTA) TASK_OPT(SL_CNT,1) = - 1
              ELSE
                 IERR = 102
                 CALL MESSAGE(2)
                 WRITE(6,102) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
              ENDIF
          ENDIF
C
C  - Check for input bus
C
          INPUT = INDEX(PARAM(1)(1:L_PARAM(1)),'I')
          IF (INPUT .GT.0) THEN
              NOUT = NOUT + 1
              IF(PARAM(1)(INPUT+1:INPUT+1).EQ.'A'
     &                                          .AND.OPTA) THEN
                 TASK_OPT(SL_CNT,1) = -1
                 OPCODE(SL_CNT) = 2
                 IF (OPTB) TASK_OPT(SL_CNT,2) = 1
              ELSEIF(PARAM(1)(INPUT+1:INPUT+1).EQ.'B'
     &                                          .AND.OPTB)THEN
                 TASK_OPT(SL_CNT,2) = -1
                 OPCODE(SL_CNT) = 1
                 IF (OPTA) TASK_OPT(SL_CNT,1) = 1
              ELSE
                 IERR = 102
                 CALL MESSAGE(2)
                 WRITE(6,102) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
              ENDIF
          ENDIF
C
C - Check for error: too many O and I option
C
          IF(NOUT.NE.1) THEN
             IERR = 109
             CALL MESSAGE(2)
             WRITE(6,109) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
          ENDIF
C
C  - Check for second parameter : MASTER
C
          MASTER = INDEX(PARAM(2)(1:L_PARAM(2)),'M')
          IF (MASTER.GT.0)THEN
           IF(DAXIL.EQ.0) THEN
             IF(PARAM(2)(MASTER+1:MASTER+1).EQ.'A'
     &                                      .AND.OPTA) THEN
                TASK_OPT(SL_CNT,1) = TASK_OPT(SL_CNT,1)*2
                OPCODE(SL_CNT) = OPCODE(SL_CNT)+ 2   !Master A (3 or 4)
             ELSEIF(PARAM(2)(MASTER+1:MASTER+1).EQ.'B'
     &                                      .AND.OPTB) THEN
                TASK_OPT(SL_CNT,2) = TASK_OPT(SL_CNT,2)*2
                OPCODE(SL_CNT) = OPCODE(SL_CNT) + 4  !Master B (5 or 6)
             ELSE
                IERR = 102
                CALL MESSAGE(2)
                WRITE(6,102) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
             ENDIF
           ELSE
C
C - Master specified for D/A task: generate error message
C
             IERR = 100
             CALL MESSAGE(2)
             WRITE(6,100) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
           ENDIF
          ENDIF
C
C - Check if more than 2  option parameters
C
          IF (K.GT.2) THEN
            CALL MESSAGE(2)
            WRITE(6,125) ESCAPE,POSDOWN,TASK_NAME(SL_CNT)
          ENDIF
      ELSE
C
C - No option parameters specified: check for default values
C
         IF(DAXIL.EQ.0) THEN
C
C - No D/A task: set default priority to output in A
C
           IF(OPTA) THEN
C
C - Output in A, input in B if there is a bus in B
C
              TASK_OPT(SL_CNT,1) = 1
              IF(OPTB) TASK_OPT(SL_CNT,2) = -1
              OPCODE(SL_CNT) = 1
           ELSEIF(OPTB) THEN
C
C - Output in B, input in A if there is a bus in A
C
              TASK_OPT(SL_CNT,2) = 1
              IF(OPTA) TASK_OPT(SL_CNT,1) = -1
              OPCODE(SL_CNT) = 2
           ENDIF
         ELSEIF(DAXIL.EQ.1) THEN
C
C - D/A task exist in A: set input in A and nothing in B
C
           OPCODE(SL_CNT) = 2
           TASK_OPT(SL_CNT,1) = -1
         ELSEIF(DAXIL.EQ.2) THEN
C
C - D/A task exist in B: set input in B and nothing in A
C
           OPCODE(SL_CNT) = 1
           TASK_OPT(SL_CNT,2) = -1
         ENDIF
C
      ENDIF
      RETURN
C
 100  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** A master option has bee'
     &        ,'n specified for D/A: task ',A8)
 102  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** Invalid bus specificati'
     &        ,'on for option XILINX, task:',A8)
 109  FORMAT (' ',A2,'2',A1,';1H',2X,'%ERR - ** More than one I or O fi'
     &        ,'eld in OPTION XILINX, task:',A8)
 125  FORMAT (' ',A2,'2',A1,';1H',2X,'%WAR - ** Only 2 field allow in O'
     &        ,'PTION XILINX: other ignored, task:',A8)
      END
C
