C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : FIRGEN                                                   **
C   **                                                                      **
C   **  Program  : FIRLIB.F                                                 **
C   **  Function : Misc. subroutines                                        **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  See firgen.f file                                                   **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  MAIN_MENU                                                           **
C   **  MODE_SET                                                            **
C   **  MES23                                                               **
C   **  CREATE_DWNLD                                                        **
C   **  CHECK_SUM                                                           **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C       ==================================
        SUBROUTINE MAIN_MENU(NITEM,HQXFLG)
C       ==================================
        IMPLICIT NONE
C
C -- This routine sets up the main menu
C
C +--------------------    DATA DECLARATIONS    ----------------------------+
C
        INCLUDE 'firdata.inc'
C
        LOGICAL*4 HQXFLG,FIRST23/.TRUE./
        INTEGER*4 NITEM,ITLEN(6),IPOSI(6),POSI(6),START
        INTEGER*2 PR_IT /0/
C
        CHARACTER ITEM1*8,
     &            ITEM2*8,
     &            ITEM3*8,
     &            ITEM4*10,
     &            ITEM5*8,
     &            ITEM6*8,
     &            ITEM7*8,
     &            ITEM8*8,
     &            ITSTR(6)*21,
     &            TEXT*100
C
        DATA ITEM1/'H : HELP'/,
     &       ITEM2/'        '/,
     &       ITEM3/'Q : QUIT'/,
     &       ITEM4/'X,E : EXIT'/,
     &       ITEM5/'        '/,
     &       ITEM6/'S : SAVE'/,
     &       ITEM7/'Q : EXIT'/,
     &       ITEM8/'        '/
       DATA ITSTR/'    EDIT     ','   DELETE    ','   ASSIGN    ',
     &            '     LIST    ','     PLOT    ','     LOAD    '/
       DATA ITLEN/13,13,13,13,13,13/
       DATA IPOSI/2,15,28,41,54,67/
C
C +--------------------      PROGRAM START  ----------------------------+
C
C -- Clear the screen, set the output mode to either PRINTER or SCREEN in
C    the header line of the main menu
C
       IF(NITEM.EQ.-1)THEN
          TEMPIX=PR_IT
          FIRST23=.TRUE.
       ELSE
          TEMPIX=NITEM
       ENDIF
C
       IF(HQXFLG) THEN
          CALL Clear_Screen
       ENDIF
C
       CALL MODE_SET(-1)
C
       TEXT(1:100) = BLANK//BLANK(1:20)
       DO JJ=1,6
          POSI(JJ)=IPOSI(JJ)
       ENDDO
       DO JJ=1,6
          START = POSI(JJ)
          IF(JJ.EQ.TEMPIX)THEN
             TEXT(START:START+7)=BRT_STRT//REV_STRT
             START=START+8
             TEXT(START:START+ITLEN(JJ))=ITSTR(JJ)(1:ITLEN(JJ))
             START=START+ITLEN(JJ)
             TEXT(START:START+9)=BRT_END//REV_END
             DO II = JJ+1,6
                POSI(II)=IPOSI(II)+18
             ENDDO
          ELSE
             TEXT(START:START+ITLEN(JJ))=ITSTR(JJ)(1:ITLEN(JJ))
          ENDIF
       ENDDO
       CALL Term_Write(3,1,TEXT,100)

C -- Display the menu options

       IF(HQXFLG.OR.((TEMPIX.NE.PR_IT).AND.(TEMPIX.EQ.0.OR.PR_IT.EQ.0)
     &                         .AND.(TEMPIX.LE.7)) )THEN

C -- Display the first reverse video line, which contains the help , quit
C    and exit options.

          CALL Term_Write(-1,1,REV_STRT,4)

          IF(TEMPIX.NE.0.) THEN
            IF(TEMPIX.EQ.4.OR.TEMPIX.EQ.6) THEN
               TEXT = '   '//ITEM1//BLANK(1:8)//ITEM2//BLANK(1:8)
     &                //ITEM8//BLANK(1:8)//ITEM7//BLANK(1:8)//
     &                  ITEM4//'   '
            ELSE
               TEXT = '  '//ITEM1//BLANK(1:8)//BLANK(1:8)//
     &            BLANK(1:8)//ITEM8//BLANK(1:10)//ITEM7//BLANK(1:8)//
     &                  ITEM4//'  '
            ENDIF
          ELSE
            TEXT = '   '//ITEM1//BLANK(1:5)//
     &                ITEM5//'     '//ITEM6//'  '
     &           //BLANK(1:4)//ITEM8//'    '//ITEM3//'    '//
     &           ITEM4//'   '
          ENDIF
          CALL Term_Write(5,1,TEXT,80)
          CALL Term_Write(-1,1,REV_END,5)

C -- Display the second reverse video line , which is used to display
C    user input errors

          IF(FIRST23) THEN
             FIRST23=.FALSE.
             CALL MES23(1,BLANK)
          ENDIF
        ENDIF
        PR_IT = TEMPIX
C
        RETURN
        END
C
C     =========================
      SUBROUTINE MODE_SET(MODE)
C     =========================
      IMPLICIT NONE
C
C -- This subroutine sets the mode to either PRINTER or SCREEN depending
C    on the value of MODE.
C
C    INPUT : MODE -- if mode >= 1, then display mode on header
C                    if mode = 1, toggle mode setting
C                    if mode < 1, then don't display mode on header
C
      INCLUDE 'firdata.inc'
C
      CHARACTER OMODE*23,            ! String for PRINTER or SCREEN mode
     &          TEXT*100,
     &          MOVE*7,              ! Moves the curser to given line and col
     &          REV_CAR*4            ! Revision level character
C
      LOGICAL*1 FIRST_PASS /.TRUE./  ! First pass flag
C
      INTEGER*2 MODE,PMODE           ! Mode input value
C
C     Set the revision level number into string
C     -----------------------------------------
      WRITE(REV_CAR,'(F4.1)',ERR=999) REV_LEVEL
      GOTO 998
 999  REV_CAR = 'inval'
 998  CONTINUE
C
C -- If MODE is greater than or equal to one, then print the MODE
C    on the header line.
C
      IF (MODE .GE. 1) THEN

C -- If mode is = 1 then toggle the mode setting

        IF (MODE.EQ.1) THEN
          OUTMODE = .NOT. OUTMODE
        ENDIF
        IF (OUTMODE) THEN
          OMODE = ' OUTPUT MODE : PRINTER '
        ELSE
          OMODE = ' OUTPUT MODE : SCREEN  '
        ENDIF
        TEXT =  NUL//REV_STRT
     &  //'     Version '//REV_CAR//'             **     FIRGEN     ** '
     &  //'      '//BRT_STRT//OMODE//
     &            REV_END//BRT_END
        CALL Term_Write(1,1,TEXT,99)
      ELSEIF(MODE.GE.-1) THEN
        OMODE = '                       '
        TEXT =  NUL//REV_STRT
     &  //'     Version '//REV_CAR//'             **     FIRGEN     ** '
     &  //'      '//OMODE//
     &            REV_END
        CALL Term_Write(1,1,TEXT,90)
      ENDIF
C
      RETURN
      END
C
C ================================================================
C                         MES23
C ================================================================
C
C   This routine write a message (or erase) on line 23 in
C   reverse video mode.
C
      SUBROUTINE MES23(MODE,MESSAGE)
C
      IMPLICIT NONE
C
      INCLUDE 'firdata.inc'
C
      CHARACTER*80 MESSAGE,mess
      INTEGER*4 mode
C
C     Set reverse video mode
C     ----------------------
      CALL Term_Write(-1,1,REV_STRT,4)
C
      IF(MODE.EQ.1)THEN
C
C       Erase any message on line 23
C       ----------------------------
        CALL Term_Write(24,1,BLANK,79)
      ELSE
C
C       Print the message on line 23
C       ----------------------------
        CALL Term_Write(24,1,MESSAGE,79)
      ENDIF
C
C     Return to normal mode
C     ---------------------
      CALL Term_Write(-1,1,REV_END,5)
CC      CALL Term_Write(21,1,SEND,0)
C
      RETURN
      END
C
C =============================================================
C                       CREATE_DWNLD
C =============================================================
C
      SUBROUTINE CREATE_DWNLD(IERR)
C
C  -- This subroutine produces the intermediate download file
C     containing the values of the filter coefficients.
C
      IMPLICIT NONE
C
      INCLUDE 'firdata.inc'
C
      INTEGER*2
     &          DSP_NB,
     &          TMP_SLOT,
     &          LENGTH1,                     !Lenght of character filename
     &          LEN                          !Length of record
C
      INTEGER*4
     &          ADDRESS,                     !Address of data
     &          LIN
C
      CHARACTER
     &  DSPSTR*2                              ,!DSP number in character
     &  FILTSTR*2                             ,!Filter number in character
     &  WORKING*80                            ,!Submit file to printer message
     &  HEX_SLOT*2                            ,!Slot number in HEX
     &  ASCADD*4                              ,!Data address in ASCII
     &  SUBMIT1*80                            ,!Submit file to printer message
     &  ASCBTMP*2                             ,!ASCII buffer temporary storage
     &  ASCBUFF*4                             ,!Buffer datas in ASCII
     &  FILEDLD*80                            ,!Download filename
     &  FMAT*6                                ,!Format specifier
     &  CHECKSUM*2                            ,!Check sum
     &  COMMENT*43,
     &  CLEANCOM*43,
     &  CLEANREC*43,
     &  RECORD*43                             ,!Data record
     &  HEADER(4)*19  /'                   ',
     &                 '   FIRGEN  DATA    ',
     &                 '  DOWNLOAD  FILE   ',
     &                 '                   '/ ,!
     &  FOOTER(4)*19  /'                   ',
     &                 '   FIRGEN DATA     ',
     &                 '   END OF FILE     ',
     &                 '                   '/
C
      DATA   WORKING  /'PROCESS    of    - Filter          '/
      DATA  CLEANREC  /'0000000000000000000000000000000000000000000'/
      DATA  CLEANCOM  /'                                           '/
C
      CALL FIL_OPEN(2,1,IERR)
C
      IF(IERR.NE.0) THEN
         RETURN
      ENDIF
C
      CALL Clear_Disp(6)
C
C     Set the filename for submit message
C     -----------------------------------
      FILEDLD = INT_DIR(1:L_INT_DIR)
     &          //OUT_FILE(1:L_OUT_FILE)
      LENGTH1 = L_INT_DIR+L_OUT_FILE
      SUBMIT1 = 'Creating the Download file '//FILEDLD(1:LENGTH1)
      CALL Term_Write(14,1,SUBMIT1,LENGTH1+27)
C
      DO I = 1,4
C
C       Comment Presentation section
C       ----------------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = HEADER(I)
        COMMENT(32:43) = '            '
C
        LEN = 43
        WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001)
     &                                             COMMENT(1:LEN)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C     Repeat for each DSP card
C     ------------------------
      DO DSP_NB = 1,NUM_DSP

C        Comment DMC # section
C        ---------------------
         COMMENT(1:1)   = '$'                    !Comment mark
         COMMENT(2:16) = '===== SLOT #   '       !
         COMMENT(17:18) = SL_NBC(DSP_NB)         !
         COMMENT(19:24) = ' ==== '               !
         COMMENT(25:31) = ' DMC # '              !
         COMMENT(32:33) = DMC(1:2)               !
         COMMENT(34:43)  = '  ========'          !
         LIN = 15 + DSP_NB
         WRITE(DSPSTR,'(I1)',ERR=1001) DSP_NB
         CALL Term_Write(LIN,5,DSPSTR,1)
         CALL Term_Write(LIN,6,': Slot XA',9)
         CALL Term_Write(LIN,15,SL_NBC(DSP_NB),2)
         CALL Term_Write(LIN,17,' - ',3)
C
         LEN = 43
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) COMMENT(1:LEN)
         COMMENT = CLEANCOM                    !Clean the comment record
C
C        Header section
C        --------------
         RECORD(1:1) = '&'                            !Rec mark
         RECORD(2:3) =  DMC(1:2)                      !dmc #
         RECORD(4:4) = 'H'                            !file type H = 86 hex
         RECORD(5:5) = 'F'                            !Destination F = firgen
         READ(SL_NBC(DSP_NB),'(I2)',ERR=1001) TMP_SLOT !Slot number in HEX
         WRITE(HEX_SLOT,'(Z2.2)',ERR=1001) TMP_SLOT
         RECORD(6:7) =  HEX_SLOT                   !Card slot
C
         LEN = 7
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
         RECORD = CLEANREC                       !Clean the record
C
C        Extended record
C        ---------------
         RECORD(1:15) = ':020000020000FC'    !Rec mark
C
         LEN = 15
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
         RECORD = CLEANREC                       !Clean the record
C
         LEN = 43
         FMAT = '(Z4.4)'
         ADDRESS = 0
         WRITE(FILTSTR,'(I2)',ERR=1001) NUMFILT(DSP_NB)
         WORKING(15:16) = FILTSTR
         DO I = 1,NUMFILT(DSP_NB)
C
C           Data section
C           ------------
            WRITE(FILTSTR,'(I2)',ERR=1001) I
            WORKING(9:10) = FILTSTR
            WRITE(FILTSTR,'(I2)',ERR=1001) SAV_FILT(DSP_NB,I)
            WORKING(26:27) = FILTSTR
            CALL Term_Write(LIN,20,WORKING,27)
CCCC            TABCALC=SAV_FILT(DSP_NB,I)
CCCC            CALL CREATE_TABLE(TABCALC)
C
            J = 0
            DO WHILE(J.LT.FILTSIZE(DSP_NB,I))
C
               RECORD(1:1) = ':'                   !Rec mark
               RECORD(2:3) = '10'                  !Rec lenght
C
               WRITE(ASCADD,FMAT,ERR=1001) ADDRESS
               RECORD(4:7) = ASCADD                !Load address input
               RECORD(8:9) = '00'                  !Rec type
               DO M=1,8                            !Data
                  WRITE(ASCBUFF,FMAT,ERR=1001) COEFF(DSP_NB,I,M+J)
C
C                 Reverse the 2 bytes for INTEL format (1234-->3412)
C                 --------------------------------------------------
                  ASCBTMP = ASCBUFF(1:2)
                  ASCBUFF(1:2) = ASCBUFF(3:4)
                  ASCBUFF(3:4) = ASCBTMP
                  RECORD(6+M*4:9+M*4) = ASCBUFF
               ENDDO
               J = J + 8
               ADDRESS = ADDRESS+16
C
               CALL CHECK_SUM(RECORD,CHECKSUM)
               RECORD(42:43) = CHECKSUM(1:2)
C
               WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
               RECORD = CLEANREC                   !Clean the record
C
            ENDDO
C
         ENDDO
C
C        End section
C        -----------
         RECORD(1:11) = ':00000001FF'
         LEN = 11
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
C
      ENDDO              ! (DO DSP_NB = 1,NUM_DSP)
C
      DO I = 1,4
C
C       Comment end section
C       -------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = FOOTER(I)
        COMMENT(32:43) = '            '
C
        LEN = 43
        WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) COMMENT(1:LEN)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C
C     Close download file
C     -------------------
      CALL FIL_OPEN(2,2,IERR)
      IF (IERR.EQ.0) THEN
C
C        Print download file created message
C        -----------------------------------
         CALL Term_Write(19,1,'Download file created successfully',34)
      ELSE
        CALL Term_Write(19,1,'An error has been generated, no file creat
     &ed',44)
      ENDIF
      CALL Wait_Time(1.0)
C
 1001 CONTINUE
C
      RETURN
C
  10  FORMAT(A)
 130  FORMAT(' ',Z4,':    ',4(I6,'(',Z4,')',5X),A11)
 131  FORMAT(' ',20X,A,/)
      END
C
C
C =============================================================
C                        CHECK_SUM 
C =============================================================
C
      SUBROUTINE CHECK_SUM(VECTOR,CHECKSUM)
C
C  -- This subroutine returns the checksum of the record specified
C     in the VECTOR argument...
C
      IMPLICIT NONE
      INCLUDE 'firdata.inc'
C
      INTEGER*2
C
     &  HEX_SUM                             ,!Check sum in Hex
     &  CHEKSTAR     /2/                    ,!Check sum  start at
     &  SUM                                 ,!Sum of all bytes in the record
     &  RECLEN      /41/                    ,!Length of the record
     &  DECIM                                !Decimal Value from subroutine
C
      CHARACTER*43
C
     &   VECTOR                              !Record file
C
      CHARACTER
C
     &   FMAT*6                              ,!Format
     &   I2CHKSUM*4                          ,!Check sum for the record
     &   CHECKSUM*2                           !Check sum for the record
C
      FMAT ='(Z4.4)'
      SUM = 0
C
      DO KK = CHEKSTAR, RECLEN,2
        READ(VECTOR(KK:KK+1),'(Z2)',ERR=702)DECIM
 702    CONTINUE
        SUM = SUM + DECIM
        IF (SUM.GT.255) SUM = SUM - 256
      ENDDO
C
      HEX_SUM = NOT(SUM) + 1
C
      WRITE(I2CHKSUM,FMAT,ERR=701) HEX_SUM
 701  CONTINUE
C
      CHECKSUM(1:2)=I2CHKSUM(3:4)
C
      RETURN
C
      END
C
