C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : FIRGEN                                                   **
C   **                                                                      **
C   **  Program  : FIRUNIX.FOR                                              **
C   **  Function : Input/output functions routine on UNIX computer          **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  See firgen.f file                                                   **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  FIL_OPEN                                                            **
C   **  CHECK_FILE                                                          **
C   **  READ_DATA                                                           **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C   =====================================================
C                       FIL_OPEN
C   =====================================================
C
C   This subroutine open and close all the files used
C   in FIRGEN utility.
C
      SUBROUTINE FIL_OPEN(FILE,OPER,IERR)
      IMPLICIT NONE
C
      INCLUDE 'firdata.inc'
C
       INTEGER*4
     &  RevStat          ,!Status of revision handling routine
     &  FILE             ,!File identification number
     &  OPER              !Type of operation (1-Open, 2-Close)
C
       INTEGER*2
     &  PU
C
       CHARACTER
     &  ERROR*5            ,!Error number in ASCII
     &  MESSA(12)*27      ,!
     &  N_FILENAME*80     ,!File name with revision number
     &  FILENAME*255       !File name
C
      DATA MESSA/'HARTAB.LIS for list table  ',
     &           'HARDLD.LIS for download    ',
     &           'Harmony download .INT      ',
     &           'wavegen transfert .SIZ     ',
     &           'plot list HARPLOT_xxx.LIS  ',
     &           'plot submit HARPLOT_xxx.COM',
     &           'for help HARHELP.TXT       ',
     &           'for data storage .DAT      ',
     &           'for FFT plot HARFFT.LIS    ',
     &           'for summary list HARSUM.LIS',
     &           'for configure HARMONY.CNF  ',
     &           'for external data          '/
C
C
      IERR = 0
C
      IF (FILE.EQ.1) THEN
C
         IF(OPER.EQ.1) THEN
C
C           Open DATA file
C           --------------
            FILENAME=DATA_DIR(1:L_DATA_DIR)
     &               //DATA_FILE(1:L_DATFIL)
            CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DAT_UNIT,FILE=N_FILENAME,STATUS='OLD',ERR=1001,
     &           IOSTAT = IERR)
C
         ELSEIF(OPER.EQ.2.OR.OPER.EQ.9)THEN
C
C           Close DATA file
C           ---------------
            CLOSE(UNIT=DAT_UNIT,IOSTAT=IERR,ERR=1001)
C
         ENDIF
C
C     Intermediate download file operations
C     -------------------------------------
      ELSEIF(FILE.EQ.2)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open .INT file
C           --------------
            FILENAME=INT_DIR(1:L_INT_DIR)
     &               //OUT_FILE(1:L_OUT_FILE)
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DLD_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,ERR=1001)
         ELSE
C
C           Close .INT file
C           ---------------
            CLOSE(UNIT=DLD_UNIT,IOSTAT=IERR,ERR=1001)
         ENDIF
      ENDIF
C
      IF (IERR.NE.0) THEN
C
CC 1001    CONTINUE
C
C        Write error message during IO operation
C        ---------------------------------------
CCC         SEND(1:37) ='%FIL_OPEN: Error during OPEN on file '
CCC         SEND(38:64)=MESSA(FILE)
CCC         SEND(65:75)=' : Error # '
CCC         CALL GET_ERR_STR(IERR,ERROR)
CCC         SEND(76:80)=ERROR
CCC         CALL ERR_MESS(SEND,80,-1,)
C
      ENDIF
C
 1001 CONTINUE
      RETURN
      END
C
C
C ===================================================================
C                           CHECK_FILE
C ===================================================================
C
      SUBROUTINE CHECK_FILE(PRESENT)
C
      LOGICAL*4 PRESENT
C
      INCLUDE 'firdata.inc'
C
      INTEGER*4
     &         revstat,
     &         len
C
      CHARACTER
     &         FILENAME*80,     !Filename to check
     &         N_FILENAME*80    !Filename with version number
C
      FILENAME = DATA_DIR(1:L_DATA_DIR)//DATA_FILE(1:L_DATFIL)
      CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
      INQUIRE(FILE=N_FILENAME,EXIST=PRESENT)
      IF (PRESENT) THEN
         CALL String_Length(N_FILENAME,len)
         FULL_NAME(1:len) = N_FILENAME
         L_FULL = len
      ENDIF
      RETURN
      END
C
C =====================================================================
C                             READ_DATA
C =====================================================================
C
      SUBROUTINE READ_DATA(IO_ERROR)
C
      IMPLICIT NONE
C
C     ---------------------------------------------------------------
C
      INTEGER*2 START,
     &          STOP
C
      INTEGER*2
     &          DSP_NB,                 ! DSP number for download
     &          TMP,                    ! Temporary variable
     &          CNT,                    ! 
     &          TOTTBL                  ! Total number of tables
C
      INTEGER*4
     &         IO_ERROR,
     &         status
C
      INTEGER*2 DUMMY,MIN_ONE/-1/       ! Marker to indicate end of data type
C
      CHARACTER
     & SAVDATE*11,SAVTIME*11           ,! Date and time fields
     & EMPTY_READ*40                   ,! Dummy read of data line
     & MESSA(6)*80                     ,! Data save error messages
     & ERRORD*80                        ! Error on data file message
C
      CHARACTER
     &          DATRESTOR*80           ,! Message:RESTORE DATA FILE
     &          DATEMPTY*80            ,! Message:DATA FILE EMPTY
     &          CREATMES*80             ! Message:CREATE NEW DATA TABLE
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
C
      INTEGER*2 HAFSRC,                 ! Half the number of sources
     &          SAVERR,                 ! IO routines error
     &          IOERR,                  ! IO routines error
     &          NUM_LINE                ! Number of lines to read
C
      INCLUDE 'firdata.inc'
C
      INTEGER*4
     &         TMP_SL_NBI(MAX_DSP)
C
C
      DATA   ERRORD /'            ***   ERROR ON DATA FILE: #        ***
     &                             '/
      DATA  CREATMES/'%REST_DATA: Creating the new DATA file
     &                             '/
      DATA MESSA    /'                              *** CANNOT SAVE DATA
     & ***                         ',
     &               '
     &                             ',
     &               '       An error as ocurred during save DATA operat
     &ion with HARMONY.            ',
     &               '            No data will be saved if you quit the
     & utility NOW.                ',
     &               '    You can return to HARMONY main menu and use ot
     &her terminal to correct      ',
     &               '    the problem if possible (Answer NO ) or quit w
     &ith unsaved session (YES).   ' /
      DATA ERROR1   /'%REST_DATA: Error when reading DATA file - Some da
     &ta invalid'/
      DATA ERROR2   /'%REST_DATA: Data reinitialize to zero: no DATA res
     &tore'/
C
C     Get filename with version number
C     --------------------------------
      DATRESTOR = '%REST_DATA: Reading DATA from file '//
     &             FULL_NAME(1:L_FULL)
C
C     Read the number of tables
C     -------------------------
      READ(DAT_UNIT,199,ERR=101,IOSTAT=status) EMPTY_READ
      READ(DAT_UNIT,'(A30)',ERR=101) EMPTY_READ
      READ(DAT_UNIT,'(A30)',ERR=101) EMPTY_READ
C
      DO DSP_NB = 1,NUM_DSP
        READ(DAT_UNIT,199,ERR=101,END=102) EMPTY_READ
        READ(DAT_UNIT,200,ERR=101,END=102) TMP_SL_NBI(DSP_NB)
        if (TMP_SL_NBI(DSP_NB) .ne. SL_NBI(DSP_NB)) then
           goto 103
        endif
        READ(DAT_UNIT,'(A30)',ERR=101,END=102) EMPTY_READ
        READ(DAT_UNIT,205,ERR=101,END=102) NUMFILT(DSP_NB)
C
C       Restore the coefficients for each filter
C       ----------------------------------------
        IF(NUMFILT(DSP_NB).GT.0)THEN
        DO MM = 1,NUMFILT(DSP_NB)
          READ(DAT_UNIT,210,ERR=101,END=102 ) SAV_FILT(DSP_NB,MM)
          K = SAV_FILT(DSP_NB,MM)
          READ(DAT_UNIT,220,ERR=101 ) TITLE(DSP_NB,K)
          READ(DAT_UNIT,230,ERR=101 ) FILTSIZE(DSP_NB,K)
C
          NUM_LINE = FILTSIZE(DSP_NB,K)/8
          STOP = 0
          DO CNT = 1,NUM_LINE
            START = STOP + 1
            STOP = START + 7
            READ(DAT_UNIT,240,ERR=101 ) (COEFF(DSP_NB,K,J),J=START,STOP)
          ENDDO
        ENDDO
      ELSE                      ! DATA file is empty
        CALL MES23(0,DATEMPTY)
        CALL Wait_Time(5.0)
        CALL MES23(1,BLANK)
      ENDIF
C
      ENDDO
C
      CALL MES23(0,DATRESTOR)
      CALL Wait_Time(5.0)
      CALL MES23(1,BLANK)
      CALL FIL_OPEN(1,2,IOERR)
      RETURN
C
C -- If there is an error in read, then print error message, and close the
C    file
C
101   CALL Beep(1)
      CALL MES23(0,ERROR1)
      CALL Wait_Time(4.0)
      CALL Beep(1)
      CALL MES23(0,ERROR2)
      CALL Wait_Time(4.0)
      CALL FIL_OPEN(1,2,IOERR)
      CALL INITIALIZE  !Reinitialize with nothing, better than garbage
      RETURN
C
 102  CALL Beep(1)
      CALL MES23(0,'%ERROR: End of File reached... Check number of DSP.
     &                            ')
      CALL Wait_Time(4.0)
      CALL Beep(1)
      IO_ERROR = 1
      CALL FIL_OPEN(1,2,IOERR)
      RETURN
C
 103  CALL Beep(1)
      CALL MES23(0,'%ERROR: Slot numbers in DATA file and Configuration 
     &do not match...              ')
      CALL Wait_Time(4.0)
      CALL Beep(1)
      IO_ERROR = 1
      CALL FIL_OPEN(1,2,IOERR)
      RETURN
C
 202  CONTINUE
      RETURN
C
199   FORMAT(A)
200   FORMAT(T22,I4)
205   FORMAT(T21,I4)
210   FORMAT(T14,I4)
220   FORMAT(A30)
230   FORMAT(T2,I4)
240   FORMAT(T2,8(Z4,1X))
C
      END
C
