C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : FIRGEN                                                   **
C   **                                                                      **
C   **  Program  : FIRGEN.FOR                                               **
C   **  Function : Main calling program                                     **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1        Debug on VAX             P.Daigle      13 Jun 1991   **
C   **  Rev 1.0        First release on IBM     P.Daigle      17 Dec 1991   **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  FIRGEN                                                              **
C   **  initialize                                                          **
C   **  show_config                                                         **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     ==============
      PROGRAM FIRGEN
C     ==============
C
      IMPLICIT NONE
C
      INCLUDE 'firdata.inc'
C
      LOGICAL*2 QUIT
C
      LOGICAL*4 EXIST, quit_ask
C
      CHARACTER*80 PROMPT,MENU(13)*15,ANSWER*60,PROMPT2,default_name
C
      CHARACTER char*1,File_N(5)*40
C
      INTEGER*2 stat2, dummy, File_Len(5)
C
      INTEGER*4 L_ANSWER, STATUS, ERROR
C
      DATA PROMPT /'ENTER DATA FILE NAME (or QUIT): '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6) /'EDIT'/
      DATA MENU(7) /'DELETE'/
      DATA MENU(8) /'ASSIGN'/
      DATA MENU(9) /'LIST'/
      DATA MENU(10) /'PLOT'/
      DATA MENU(11) /'LOAD'/
      DATA MENU(12) /'CONF'/
      DATA MENU(13) /'SAVE'/
C
C
C     CONFIGURATION INFORMATION:
C       These values should be modified to reflect the hardware
C       configuration of the Digital Audio System chassis. 
C     ---------------------------------------------------------
C
      NUM_DSP = 1        !Specify the number of DSP boards to receive
                         !...the downloaded F.I.R. coefficients
      SL_NBI(1) =  7     !Specify the slot number for each DSP.
      SL_NBC(1) = '07'   !Specify the slot number in character type
C
C      SL_NBI(2) = 13
C      SL_NBC(2) = '13'
C     ---------------------------------------------------------
C
C
      CALL Init_Libgd(1,3)
      CALL INITIALIZE
C
C     Read all directories, flags and names from SNDUTY transfert file
C     ----------------------------------------------------------------
      CALL XLINK_READ(Config_String,Config_length,DMC,Page_Num,
     &                Filetters,Comp_Id,LinkFlag,Com,File_N,File_Len,
     &                ERROR)
C
      DATA_DIR = Config_String(1)(1:Config_Length(1))//'audio/data/'
      INT_DIR  = Config_String(1)(1:Config_Length(1))//'audio/inter/'
      L_DATA_DIR = Config_Length(1) + 11
      L_INT_DIR = Config_Length(1) + 12
C
      CALL MAIN_MENU(0,.TRUE.)
C
      CALL Show_Config
C
      QUIT = .FALSE.
      quit_ask = .false.
      DO WHILE (.NOT. QUIT)
        CALL Term_Write(21,1,PROMPT,32)
        CALL Term_Read(1,ANSWER,L_ANSWER,STATUS)
        IF (L_ANSWER .NE. 0) THEN
           if (ANSWER(1:4).eq.'quit' .or. ANSWER(1:4).eq.'QUIT') then
              QUIT = .true.
              quit_ask = .true.
           else
              DATA_FILE = ANSWER
              L_DATFIL = L_ANSWER
              CALL CHECK_FILE(EXIST)
              IF (EXIST) THEN
               QUIT = .TRUE.
              ELSE
                QUIT = .FALSE.
                CALL Beep(2)
                CALL mes23(0,'%ERROR: File not found in DATA directory..
     &.Try again...                         ')
              ENDIF
           endif
        ENDIF
      ENDDO
C
      if (.not. quit_ask) then
       CALL FIL_OPEN(1,1,IERR)
       CALL READ_DATA(IERR)
C
       IF (IERR .EQ. 0) THEN
         QUIT = .FALSE.
         DO WHILE (.NOT. QUIT)
           CALL Term_Write(21,30,'                    ',20)
           default_name(1:12) = Config_String(9)(1:4)//Filetters(1:3)//
     &                          'f.int'
           PROMPT2 = 'OUTPUT FILE NAME (or QUIT): ['//default_name(1:12)
     &               //']'
           CALL Term_Write(21,1,PROMPT2,42)
           CALL Term_Read(1,ANSWER,L_ANSWER,STATUS)
           IF (L_ANSWER .NE. 0) THEN
             if (ANSWER(1:4).eq.'quit' .or. ANSWER(1:4).eq.'QUIT') then
               QUIT = .true.
               quit_ask = .true.
             else 
               OUT_FILE = ANSWER
               L_OUT_FILE = L_ANSWER
               QUIT = .TRUE.
             endif
           ELSE                         !Use default file name
              OUT_FILE = default_name(1:12)
              L_OUT_FILE = 12
              QUIT = .TRUE.
           ENDIF
         ENDDO
C
         if (.not.quit_ask) then
           CALL CREATE_DWNLD(IERR)
         endif
       ENDIF
C
       CALL Start_Highlite
       CALL Term_Write(23,19,'<< Press any key to Exit the utility >>'
     &               ,39)
       CALL Stop_Highlite
       CALL ReadKey(dummy,char,stat2)
C
      endif
C
      CALL Clear_Screen
      END
C  
C     =====================
      SUBROUTINE INITIALIZE
C     =====================
      IMPLICIT NONE
C
C -- This subroutine initializes the amplitudes, types, phases, titles
C    and sizes required by HARMONY.  It also sets certain commonly
C    used escape sequences.
C
      INCLUDE 'firdata.inc'
C
C -- Initialize the phase to zero phase (phase=-1), the amplitudes to 0,
C    the sizes to 512 and blank out the titles.
C
      BLANK(1:52)='                                                    '
      BLANK(53:80)='                                      '
C
C -- Set up the commonly used escape sequences.
C
      NUL = CHAR(0)                          ! Escape
      ESC = CHAR(27)                         ! Escape
      BEL = CHAR(7)                          ! Bell
      BRT_STRT = ESC//'[1m'                  ! Start bright intensity
      BRT_END = ESC//'[22m'                  ! End bright intensity
      REV_STRT = ESC//'[7m'                  ! Start reverse video
      REV_END = ESC//'[27m'                  ! End reverse video
      CLS = ESC//'[2J'                       ! Clear entire screen
      CLS_STR = ESC//'[?J'                       ! Clear entire screen
      CLEAR = ESC//'[0J'                     ! Clear from curser to screen end
C
C     Set output mode on SCREEN
C     -------------------------
      OUTMODE = .FALSE.
C
      RETURN
      END
C
C
C     ======================
      SUBROUTINE Show_Config
C     ======================
C
      IMPLICIT NONE
C
      Include 'firdata.inc'
C
      Integer*4
     &         begin,temp1,temp2
C
      Character
     &         slots*60,
     &         num_dsp_char*2
C
      write(num_dsp_char(1:2),'(i2)') num_dsp
      temp1 = l_data_dir
      temp2 = l_int_dir
C
      slots(1:60) = blank(1:60) 
      begin = 1
      do i=1,num_dsp
         slots(begin:begin+3) = 'XA'//sl_nbc(i)(1:2) 
         begin = begin + 5
      enddo
C
      call Term_Write(7,19,'***  Hardware Configuration  ***',32)
      call term_write(8,1,'DMC number: ',12)
      call term_write(9,1,'Number of DSP in chassis: ',26)
      call term_write(10,1,'DSP in slot: ',13)
      call start_highlite
      call term_write(8,13,dmc(1:2),2)
      call term_write(9,27,num_dsp_char(1:2),2)
      call term_write(10,14,slots(1:begin),begin)
      call stop_highlite
C
      call Term_Write(13,19,'***  Utility Configuration  ***',31)
      call term_write(14,1,'SHIP mnemonic : ',16)
      call term_write(15,1,'DATA directory: ',16)
      call term_write(16,1,'INTER directory: ',17)
      call start_highlite
      call term_write(14,18,Config_String(9)(1:4),4)
      call term_write(15,18,DATA_DIR(1:L_DATA_DIR),temp1)
      call term_write(16,18,INT_DIR(1:L_INT_DIR),temp2)
      call stop_highlite
C
      RETURN
      END
