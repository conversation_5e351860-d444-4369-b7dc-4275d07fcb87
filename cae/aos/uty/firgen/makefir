INCLUDE = firdata.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
firgen: firgen.o firlib.o firunix.o $(CAELIB)/libcae.a \
$(LIBDIR)/libaos.a $(INCLUDE)
	xlf -C -g -qcharlen=1024 firgen.o firlib.o firunix.o \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o firgen
#
firgen.o: firgen.f $(INCLUDE)
	xlf -C -qcharlen=1024 -g -c firgen.f
#
firlib.o: firlib.f $(INCLUDE)
	xlf -C -qcharlen=1024 -g -c firlib.f
#
firunix.o: firunix.f $(INCLUDE)
	xlf -C -qcharlen=1024 -g -c firunix.f
