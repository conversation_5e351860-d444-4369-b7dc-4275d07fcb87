C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : FIRGEN                                                   **
C   **                                                                      **
C   **  Program  : FIRDATA.INC                                              **
C   **  Function : Declaration common variables                             **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1          Author: P. Daigle          Date: 13 June 1991      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
        LOGICAL*2
     &            OUTMODE
C
        CHARACTER BRT_STRT*4,
     &            BRT_END*5,
     &            REV_STRT*4,
     &            REV_END*5,
     &            CLEAR*4,
     &            CLS*4,
     &            CLS_STR*4,
     &            BEL*1,
     &            NUL*1,
     &            ESC*1
C
      INTEGER*2 I,J,K,M,II,JJ,KK,MM     !Different loop counter
C
      INTEGER*4
     &            IERR          ,!
     &            DAT_UNIT      ,!HARMONY DATA file unit
     &            DLD_UNIT       !Intermediate Download unit
C
      INTEGER*2
     &            L_DATFIL      ,!Length of data file name
     &            L_DATA_DIR    ,
     &            L_INT_DIR     ,
     &            L_FULL        ,
     &            MAX_FILT      ,!Maximum number of filters
     &            MAX_SIZE      ,!Maximum number of coefficients for filter
     &            MAX_DSP        !Maximum number of DSP (slots) to download to.
C
      CHARACTER
     &            BLANK*80,                 !Blank line
     &            DATA_DIR*80,              !Data directory name
     &            INT_DIR*80,               !Intermediate directory
     &            DATA_FILE*80,             !Data filename
     &            FULL_NAME*80              !Complete pathname of data file
C
      REAL*4
     &            REV_LEVEL                 !FIRGEN revision level
C
      PARAMETER (DAT_UNIT=80)
      PARAMETER (DLD_UNIT=20)
      PARAMETER (MAX_FILT=512)
      PARAMETER (MAX_SIZE=128)
      PARAMETER (MAX_DSP =20)
      PARAMETER (REV_LEVEL = 1.0)
C
      CHARACTER
     &          SL_NBC(MAX_DSP)*2,            !Slot number for each DSP
     &          DMC*2,                        !DMC number 
     &          TITLE(MAX_DSP,MAX_FILT)*30,   !Title of Filter
     &          OUT_FILE*40,                  !Output filename
     &          Page_Num*2,                   !Page number
     &          Filetters*3,                  !SN or RF + config letter
     &          Config_String(12)*80          !Logical names in XLINK file 
C
      LOGICAL*1
     &           LinkFlag(4),                !On site flag & spares...
     &           Com(6)                      !Communication flags for XLINK
C
      INTEGER*2
     &          Config_Length(12),
     &          Comp_Id,
     &          L_OUT_FILE,
     &          TEMPI,
     &          TEMPIX
C
      INTEGER*4
     &          SL_NBI(MAX_DSP)                 ,!Slot number for each DSP
     &          NUM_DSP                         ,!Number of filter DSPs (slots)
     &          NUMFILT(MAX_DSP)                ,!Number of filters for each
     &          SAV_FILT(MAX_DSP,MAX_FILT)      ,!Saved filter number
     &          FILTSIZE(MAX_DSP,MAX_FILT)      ,!Size of filter (# of coeff.)
     &          COEFF(MAX_DSP,MAX_FILT,MAX_SIZE) !Values of filter coefficients
C
      COMMON /SCREEN/ CLS,BRT_STRT,BRT_END,REV_STRT,
     &                REV_END,ESC,BEL,CLEAR,NUL,CLS_STR
C
      COMMON /LOG2/   OUTMODE
      COMMON /LOG1/   LinkFlag,Com
      COMMON /CHAR2/  DMC, SL_NBC
      COMMON /CHAR40/ OUT_FILE
      COMMON /CHAR80/ DATA_FILE,INT_DIR,DATA_DIR,TITLE,BLANK,FULL_NAME,
     &                Config_String,Page_Num,Filetters
      COMMON /INT2/   L_DATFIL, L_OUT_FILE, L_INT_DIR, L_DATA_DIR,
     &                TEMPI, TEMPIX, L_FULL,Config_Length,Comp_Id
      COMMON /INT4/   NUMFILT, SAV_FILT, FILTSIZE, COEFF, SL_NBI,
     &                NUM_DSP
C
