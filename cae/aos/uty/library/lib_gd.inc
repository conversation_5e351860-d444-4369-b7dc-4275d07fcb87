      INTEGER*2
     &        ED_NUL, ED_PF1, ED_PF2, ED_PF3, ED_PF4, ED_CUP, ED_CDN,
     &        ED_CRT, ED_CLF, ED_FTB, ED_RTB, ED_TPO, ED_INS, ED_RET,
     &        ED_EOL, ED_NWD, ED_PWD, ED_DCH, ED_LWD, ED_DPW, ED_BKSP,
     &        ED_KP0, ED_KP1, ED_KP2, ED_KP3, ED_KP4, ED_KP5, ED_KP6,
     &        ED_KP7, ED_KP8, ED_KP9, ED_KPM, ED_KPC, ED_KPD, ED_ENT,
     &        ED_FND, ED_INH, ED_RMV, ED_SEL, ED_PVS, ED_NXS, ED_F06,
     &        ED_F07, ED_F08, ED_F09, ED_F10, ED_F11, ED_F12, ED_F13,
     &        ED_F14, ED_F17, ED_F18, ED_F19, ED_F20, ED_HLP, ED_DO,
     &        ED_CTW,
     &        ED_TYIN,ED_RBCK,ED_RESV,ED_EXIT,
     &        ED_GPF2,ED_GPF3,ED_GPF4,
     &        ED_GKP0,ED_GKP1,ED_GKP2,ED_GKP3,ED_GKP4,ED_GKP5,ED_GKP6,
     &        ED_GKP7,ED_GKP8,ED_GKP9,ED_GKPM,ED_GKPC,ED_GKPD,ED_GENT
C
      LOGICAL*4 GOLD_KEY
      CHARACTER
     &   Dummy_String*1,       ! Dummy string
     &   Blank*80,             ! Blank line string 
     &   Bell*1,               ! Bell character
     &   Nul*1,                ! Null character
     &   Escape*1              ! Escape character
C
      INTEGER*2 
     &   Max_List_Item,        ! Maximum number of item in the Parse_Command
                               !  ...list
     &   Max_Word,             ! Maximum number of word in Parse_Word
     &   Max_Char_Per_Item,    ! Maximum number of characters in each Item
     &   Max_Char_Per_Word,    ! Maximum number of characters in each Word
     &   Max_Char_Length,      ! Maximum character on a line
     &   Loop,                 ! Loop counter
     &   Computer_Id,          ! Computer Identification (1-VAX,2-Gould)
     &   Term_type             ! Terminal type (1-VT100/VT220/VT320)
C
      INTEGER*4
     &   Loop_Index
C
      PARAMETER (Max_Word = 10)
      PARAMETER (Max_Char_Per_Word = 15)
      PARAMETER (Max_Char_Per_Item = 15)
      PARAMETER (Max_List_Item = 13)
      PARAMETER (Max_Char_Length = 80)
C
      PARAMETER (
     &          ED_NUL = 0,       ! indicates no escape sequence
     &          ED_CUP = 5,       ! Cursor up
     &          ED_CDN = 6,       ! Cursor down
     &          ED_CRT = 7,       ! Cursor right
     &          ED_CLF = 8,       ! Cursor left
     &          ED_FTB = 9,       ! Forward Tab (TAB key)
     &          ED_RTB = 10,      ! Reverse Tab (BACKSPACE key)
     &          ED_TPO = 11,      ! Typeover Mode
     &          ED_INS = 12,      ! Insert Mode
     &          ED_RET = 13,      ! RETURN key
     &          ED_EOL = 14,      ! End of line
     &          ED_NWD = 15,      ! Next word
     &          ED_PWD = 16,      ! Previous word
     &          ED_DCH = 17,      ! Delete character at cursor position
     &          ED_LWD = 18,      ! End of Last word
     &          ED_DPW = 19,      ! Delete previous word
     &          ED_BKSP= 127      ! Bacspace
     &          )
C
C ---- The following parameters define the 14 keypad and GOLD keypad keys ----
C
      PARAMETER (
     &          ED_PF1 = 1,                     ! Keypad PF1
     &          ED_PF2 = 2,  ED_GPF2 = 62,      ! Keypad PF2
     &          ED_PF3 = 3,  ED_GPF3 = 63,      ! Keypad PF3
     &          ED_PF4 = 4,  ED_GPF4 = 64,      ! Keypad PF4
     &          ED_KP0 = 20, ED_GKP0 = 65,      ! Keypad 0 (zero)
     &          ED_KP1 = 21, ED_GKP1 = 66,      ! Keypad 1
     &          ED_KP2 = 22, ED_GKP2 = 67,      ! Keypad 2
     &          ED_KP3 = 23, ED_GKP3 = 68,      ! Keypad 3
     &          ED_KP4 = 24, ED_GKP4 = 69,      ! Keypad 4
     &          ED_KP5 = 25, ED_GKP5 = 70,      ! Keypad 5
     &          ED_KP6 = 26, ED_GKP6 = 71,      ! Keypad 6
     &          ED_KP7 = 27, ED_GKP7 = 72,      ! Keypad 7
     &          ED_KP8 = 28, ED_GKP8 = 73,      ! Keypad 8
     &          ED_KP9 = 29, ED_GKP9 = 74,      ! Keypad 9
     &          ED_KPM = 30, ED_GKPM = 75,      ! Keypad "-" (minus sign)
     &          ED_KPC = 31, ED_GKPC = 76,      ! Keypad "," (comma)
     &          ED_KPD = 32, ED_GKPD = 77,      ! Keypad "." (decimal point)
     &          ED_ENT = 33, ED_GENT = 78       ! Keypad ENTER key
     &          )
C
C ---- The following parameters define the VT220 function keys -------------
C
      PARAMETER (
     &          ED_FND = 40,      ! FIND Editing key
     &          ED_INH = 41,      ! INSERT HERE Editing key
     &          ED_RMV = 42,      ! REMOVE Editing key
     &          ED_SEL = 43,      ! SELECT Editing key
     &          ED_PVS = 44,      ! PREV SCREEN Editing key
     &          ED_NXS = 45,      ! NEXT SCREEN Editing key
     &          ED_F06 = 46,      ! F6  Function key
     &          ED_F07 = 47,      ! F7  Function key
     &          ED_F08 = 48,      ! F8  Function key
     &          ED_F09 = 49,      ! F9  Function key
     &          ED_F10 = 50,      ! F10 Function key
     &          ED_F11 = 51,      ! F11 Function key
     &          ED_F12 = 52,      ! F12 Function key
     &          ED_F13 = 53,      ! F13 Function key
     &          ED_F14 = 54,      ! F14 Function key
     &          ED_HLP = 55,      ! HELP Function key
     &          ED_DO  = 56,      ! DO  Function key
     &          ED_F17 = 57,      ! F17 Function key
     &          ED_F18 = 58,      ! F18 Function key
     &          ED_F19 = 59,      ! F19 Function key
     &          ED_F20 = 60,      ! F20 Function key
     &          ED_CTW = 61       ! Control W (refresh screen)
     &          )
C
C ---- The following parameters define the controls keys -------------
C
      PARAMETER (
     &          ED_TYIN = 79,     ! CTRL A key: TYPEOVERT/INSERT
     &          ED_RBCK = 80,     ! CTRL H key: Reverse backspace
     &          ED_RESV = 81,     ! CTRL U key: Reserve/Unreserved
     &          ED_EXIT = 82      ! CTRL Z key: Exit
     &          )
C
      COMMON /Equipment/  Term_Type,Computer_Id
      COMMON /Com_String/ Nul,Escape,Bell,Blank
C
