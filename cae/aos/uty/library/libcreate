#! /bin/csh -f
#
set AOSDISK = "`printenv aos_disk`"
set LIBDIR = "${AOSDISK}/aos/uty/library/"
set CMP = 'xlf -c -w -qcharlen=1024'
if (-e libaos.a) then
   echo " Removing old version of libaos.a ..."
   rm ${LIBDIR}libaos.a
endif
#
if (!(-e lib_gd.o)) then
   set CMD = "$CMP lib_gd.f"
   echo $CMD
   $CMD
endif
#
if (!(-e lib_io.o)) then
   set CMD = "$CMP -g lib_io.f"
   echo $CMD
   $CMD
endif
#
if (!(-e get_date.o)) then
   set CMD = "cc -O -c get_date.c"
   echo $CMD
   $CMD
endif
#
if (!(-e getkey.o)) then
   set CMD = "cc -O -c getkey.c"
   echo $CMD
   $CMD
endif
#
echo ' '
echo "Creating ARCHIVE library -> ${LIBDIR}libaos.a"
echo ' '
set CMD = "ar vq ${LIBDIR}libaos.a"
set CMD = "$CMD lib_gd.o lib_io.o getkey.o get_date.o"
$CMD
exit
