C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : Sound & Audio                                            **
C   **                                                                      **
C   **  File     : AOSUTY.FOR                                               **
C   **  Function : Common Library routines in Fortran for IBM computer      **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0             G. De <PERSON>re                    7 June 1990      **
C   **  Rev 2.0             P. Daigle                     13 Sept 1990      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  Beep                                                                **
C   **  Clear_Disp                                                          **
C   **  Clear_Screen                                                        **
C   **  Convert_Upper                                                       **
C   **  Disp_Box                                                            **
C   **  Erase_Screen                                                        **
C   **  Get_Number                                                          **
C   **  Get_String                                                          **
C   **  Init_Libgd                                                          **
C   **  Input_Buffer                                                        **
C   **  Parse_Command                                                       **
C   **  Parse_Number1                                                       **
C   **  Parse_Word                                                          **
C   **  Read_Command                                                        **
C   **  Screen_Reset                                                        **
C   **  Start_Blinking                                                      **
C   **  Start_Highlite                                                      **
C   **  Start_Reverse                                                       **
C   **  Start_Underline                                                     **
C   **  Stop_Blinkking                                                      **
C   **  Stop_Highlite                                                       **
C   **  Stop_Reverse                                                        **
C   **  Stop_Underline                                                      **
C   **  String_Length                                                       **
C   **  Term_Read                                                           **
C   **  Term_Write                                                          **
C   **  Wait_For_A_Key                                                      **
C   **  Wait_For_Cont                                                       **
C   **  Wait_Time                                                           **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     ====================
      SUBROUTINE Beep(Num)
C     ====================
C
C     This subroutine beeps the number of times indicated in NUM
C     ----------------------------------------------------------
C     INPUT : Num -- number of times to beep
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*2                 ! Output string
C
      INTEGER*4 
     &          Num                   ! Number of times to beep
C
      INCLUDE 'lib_gd.inc'
C
C     Generate the escape sequence for the BELL and beep the required
C     number of times
C     ---------------------------------------------------------------
      IF (First) THEN
        Out = Nul//Bell
        First = .FALSE.
      ENDIF
C
      IF (Num.GT.0) THEN
        IF (Term_Type.EQ.1) THEN
C
C          Send a Beep only one time if VT220 type
C          ---------------------------------------  
           CALL Term_Write(-1,0,Out,2)
        ELSE
           DO Loop_Index=1,Num
              CALL Term_Write(-1,0,Out,2)
           ENDDO
        ENDIF
      ENDIF  
C
      RETURN
      END
C
C
C
C     ===========================
      SUBROUTINE Clear_Disp(Line)
C     ===========================
C
C     This routine clear the screen from 'Line' to bottom of screen
C     -------------------------------------------------------------
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'
C
      INTEGER*4
     &  Line
C
      CHARACTER
     &  Out*4
C
      LOGICAL*1 First /.TRUE./
C
      IF(First) THEN
        Out = Escape//'[0J'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(Line,1,Out,4)
      RETURN
      END
C
C     =======================
      SUBROUTINE Clear_Screen
C     =======================
C
C     This routine clears the entire screen
C     -------------------------------------
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*5                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
         IF (Term_Type.EQ.1) THEN 
            Out = '[2J'
         ENDIF
         First = .FALSE.
      ENDIF
C
      Out = '[2J'     
      WRITE(6,'(A6)',ERR=99) out
CCCC      CALL Term_Write(-1,0,Out,4)
C
 99   CONTINUE
      RETURN
      END
C
C
C
C     =======================================
      SUBROUTINE Convert_Upper(String,Length)
C     =======================================
C
C     This routine convert the characters in String to Upper cases
C     ------------------------------------------------------------
C     INPUT : String -- contains the input character string to be translated
C             Length -- contains the length of the character string
C
C     OUTPUT : String -- contains the upper case equivalent of the string
C
      IMPLICIT NONE
C
      INTEGER*2
     &  Delta           ! Difference between upper case and lower case

C
      INTEGER*4
     &  Length           ! Length of string to be converted (Maybe # of char)
C
      LOGICAL*1
     &  First /.TRUE./   ! First read pass flag
C
      CHARACTER 
     &  String*(*)       ! Input string to be converted  
C
      INCLUDE 'lib_gd.inc'
C
C     First pass
C     ----------
      IF (First) THEN
         First = .FALSE.
         Delta = ICHAR('a') - ICHAR('A')
      ENDIF
C
C     If no character in the line, leave subroutine
C     ---------------------------------------------
      IF (Length.GT.0) THEN
C
C        Translate to UPPER CASE in place
C        --------------------------------
         DO Loop_Index = 1,Length
            IF ( ('a'.LE.String(Loop_Index:Loop_Index))  .AND.
     &           (String(Loop_Index:Loop_Index).LE.'z')         ) THEN
               String(Loop_Index:Loop_Index) = 
     &                  CHAR(ICHAR(String(Loop_Index:Loop_Index))-Delta)
            ENDIF
         ENDDO
      ENDIF
C
      RETURN
C
      END
C
C
C
C     ================================================================
      SUBROUTINE Disp_Box(X1,Y1,X2,Y2,Corner_LU,Corner_RU,Corner_RL,
     &                                Corner_LL,
     &                     Line_Upper,Line_Right,Line_Lower,Line_Left,
     &                     Reset_Set_Graphics)
C     ================================================================
C
C     Display a graphic box on screen
C     -------------------------------
C     INPUT: Coordinates X1,X2 are the rows of the box
C                        Y1,Y2 are the columns of the box.
C            Flags Corner_LU,Corner_RU,Corner_RL and Corner_LL are the corner 
C                  print flags (0=Hidden,x=Print)
C               x     Corner_LU    Corner_RU    Corner_RL    Corner_LL
C              ---    ---------    ---------    ---------    ---------
C               1                    (Straight corner)
C               2                   (Vertical T corner)
C               3                  (Horizontal T corner)
C               4                      (Cross corner)
C            Flags Line_Upper,Line_Right,Line_Lower and Line_Left are the 
C                  straight line print flags (0=Hidden,1=Print)
C
C            Corner_LU    Line_Upper   Corner_RU
C          (Y1,X1)+----------------------+
C                 |                      |
C        Line_Left|                      |Line_Right
C                 |                      |
C                 +----------------------+(Y2,X2)
C            Corner_LL    Line_Lower   Corner_RL
C
C          Reset_Set_Graphics is a special flag for setting graphic screen mode
C               -1 : Reset graphic mode at the end of the box
C                0 : No graphic mode set or reset
C                1 : Set graphic mode at the beginning of the box
C                2 : Set graphic mode at the beginning of the box and reset
C                    at the end
C
      IMPLICIT NONE
C
      INTEGER*4 
     &  X1,                    ! Upper Left corner row
     &  X2,                    ! Lower Right corner row
     &  Y1,                    ! Upper left corner column
     &  Y2,                    ! Lower Right corner column
     &  Corner_LU,             ! Print flag for Left-Upper corner
     &  Corner_RU,             ! Print flag for Right-Upper corner
     &  Corner_RL,             ! Print flag for Right-Lower corner
     &  Corner_LL,             ! Print flag for Left-Lower corner
     &  Line_Upper,            ! Print flag for Upper line
     &  Line_Right,            ! Print flag for Right side line
     &  Line_Lower,            ! Print flag for Lower line
     &  Line_Left,             ! Print flag for Left side line
     &  Reset_Set_Graphics,    ! Graphic mode (Terminal) set/reset flag
     &  Length_Vertical_Line   ! Number of vertical line character
C
      LOGICAL*1 
     &  Valid_Coordinates      ! Coordinates check flag
C
      CHARACTER 
     &  Print_String*3,        ! Print a string buffer
     &  Line_Characters(80)*1, ! Graphic code character for a line
     &  Vertical_Line*80,      ! Vertical line string 
     &  Print_Line*80,         ! Print a line buffer
     &  Draw*1                 ! Print a character buffer
C
      INCLUDE 'lib_gd.inc'
C
C      EQUIVALENCE 
C     &  (Line_Characters,Print_Line)
C
      DATA Line_Characters /80*'q'/
      DATA Print_Line(1:40)
     &       /'qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq'/
      DATA Print_Line(41:80)
     &       /'qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq'/
C
C     Check for valid coordinates
C     ---------------------------
      Valid_Coordinates = (X2.GT.X1+1) .AND. (Y2.GT.Y1+1)
C
      IF(Valid_Coordinates) THEN
C
C       Set graphic mode if requested
C       -----------------------------
        IF(Reset_Set_Graphics.GE.1) THEN
          Print_String = Escape//'(0'
          CALL Term_Write (-1,0,Print_String,3)
        ENDIF
C
C       Print left/top corner
C       ---------------------
        IF (Corner_LU.EQ.1) THEN
          Draw = 'l'                      !      __
          CALL Term_Write (X1,Y1,Draw,1)  !     |
        ELSEIF (Corner_LU.EQ.2) THEN
          Draw = 't'                      !     |__
          CALL Term_Write(X1,Y1,Draw,1)   !     |
        ELSEIF (Corner_LU.EQ.3) THEN
          Draw = 'w'                      !   _____
          CALL Term_Write(X1,Y1,Draw,1)   !     |
        ELSEIF (Corner_LU.EQ.4) THEN
          Draw = 'n'                      !   __|__
          CALL Term_Write(X1,Y1,Draw,1)   !     |
        ENDIF
C
C       Print the top horizontal line
C       -----------------------------
        IF (Line_Upper.EQ.1) THEN
           Length_Vertical_line = Y2-Y1-1
           Vertical_Line = Print_Line(1:Length_Vertical_line)
           CALL Term_Write(X1,Y1+1,Vertical_Line,Length_Vertical_line)
        ENDIF
C
C       Print right/top corner
C       ----------------------
        IF (Corner_RU.EQ.1) THEN
          Draw = 'k'                     !   --
          CALL Term_Write(X1,Y2,Draw,1)  !     |
        ELSEIF (Corner_RU.EQ.2) THEN
          Draw = 'u'                     !   __|
          CALL Term_Write(X1,Y2,Draw,1)  !     |
        ELSEIF (Corner_RU.EQ.3) THEN
          Draw = 'w'                     !   _____
          CALL Term_Write(X1,Y2,Draw,1)  !     |
        ELSEIF (Corner_RU.EQ.4) THEN
          Draw = 'n'                     !   __|__
          CALL Term_Write(X1,Y2,Draw,1)  !     |
        ENDIF
C
C       Print the right vertical line
C       -----------------------------
        IF(Line_Right.EQ.1) THEN
          Draw = 'x'
          DO Loop_Index=X1+1,X2-1
             CALL Term_Write(Loop_Index,Y2,Draw,1)
          ENDDO
        ENDIF
C
C       Print right/bottom corner
C       -------------------------
        IF (Corner_RL.EQ.1) THEN
          Draw = 'j'                     !     |
          CALL Term_Write(X2,Y2,Draw,1)  !   __|
        ELSEIF (Corner_RL.EQ.2) THEN
          Draw = 'u'                     !   __|
          CALL Term_Write(X2,Y2,Draw,1)  !     |
        ELSEIF (Corner_RL.EQ.3) THEN
          Draw = 'v'                     !     |
          CALL Term_Write(X2,Y2,Draw,1)  !   __|__
        ELSEIF (Corner_RL.EQ.4) THEN
          Draw = 'n'                     !   __|__
          CALL Term_Write(X2,Y2,Draw,1)  !     |
        ENDIF
C
C       Print the bottom horizontal line
C       --------------------------------
        IF (Line_Lower.EQ.1) THEN
          Length_Vertical_line = Y2-Y1-1
          Vertical_Line = Print_Line(1:Length_Vertical_line)
          CALL Term_Write(X2,Y1+1,Vertical_Line,Length_Vertical_line)
        ENDIF
C
C       Print left/bottom corner
C       ------------------------
        IF (Corner_LL.EQ.1) THEN
          Draw = 'm'                     !    |
          CALL Term_Write(X2,Y1,Draw,1)  !    |__
        ELSEIF (Corner_LL.EQ.2) THEN
          Draw = 't'                     !     |__
          CALL Term_Write(X2,Y1,Draw,1)  !     |
        ELSEIF (Corner_LL.EQ.3) THEN
          Draw = 'v'                     !     |
          CALL Term_Write(X2,Y1,Draw,1)  !   __|__
        ELSEIF (Corner_LL.EQ.4) THEN
          Draw = 'n'                     !   __|__
          CALL Term_Write(X2,Y1,Draw,1)  !     |
        ENDIF
C
C       Print the left vertical line
C       ----------------------------
        IF (Line_Left.EQ.1) THEN
          Draw = 'x'
          DO Loop_Index=X1+1,X2-1
             CALL Term_Write(Loop_Index,Y1,Draw,1)
          ENDDO
        ENDIF
C
C       Reset graphic mode if requested
C       -------------------------------
        IF (Reset_Set_Graphics.EQ.-1.OR.Reset_Set_Graphics.EQ.2) THEN
          Print_string = Escape//'(B'
          CALL Term_Write(-1,0,Print_String,3)
        ENDIF
C
      ENDIF
C
      RETURN
C
      END
C
C
C
C     ========================================
      SUBROUTINE Erase_Screen(Xp1,Yp1,Xp2,Yp2)
C     ========================================
C
C     This routine erase the screen from Xp1,Yp1 to Xp2,Yp2
C
      IMPLICIT NONE
C
      INTEGER*4
     &  Xp1,
     &  Xp2,
     &  Yp1,
     &  Yp2,
     &  Index,
     &  Length 
C
      INCLUDE 'lib_gd.inc'
C
      Length = Yp2-Yp1+1
      DO Index=Xp1,Xp2
         Call Term_Write(Index,Yp1,Blank,Length)
      ENDDO
C
      RETURN
      END
C
C
C
C     ==================================================
      SUBROUTINE Get_Number(String,Length,Number,Status)
C     ==================================================
C
C     This routine translates a string into a real number
C     ---------------------------------------------------
C     INPUT: String contains the number string to be translated
C            Length is the length of the string (Only number length)
C     OUTPUT: Number is the translated number (Real)
C             Status is the translation validity
C                  0: Valid
C                999: Invalid character in the string 
C              Other: Fortran error during translation
C
      IMPLICIT NONE
C
      CHARACTER 
     &  String*(*)
      INTEGER*2 
     &  CAR_ASCII,
     &  Status, 
     &  Length,
     &  Counter,
     &  Pointer
      INTEGER*4
     &  Stat
      REAL*4  
     &  Number
      LOGICAL*1 
     &  Space
C
      INCLUDE 'lib_gd.inc'
C
      Pointer = 0
      Space   = .FALSE.
      Counter = 1
      DO WHILE (Counter.LE.Length)
         CAR_ASCII = ICHAR(String(Counter:Counter))
C
         IF(((CAR_ASCII.GE.48.AND.CAR_ASCII.LE.57).OR.(CAR_ASCII.EQ.46))
     &                    .AND..NOT.Space) THEN
            Pointer = Pointer + 1
         ELSE
            IF(CAR_ASCII.EQ.32)THEN
               Pointer = Pointer + 1
               Space = .TRUE.
            ENDIF
         ENDIF
         Counter = Counter + 1
      ENDDO
C
      IF (Pointer.EQ.Length) THEN
         READ (String(1:Length),*,ERR=99,IOSTAT=Stat) Number
 99      CONTINUE
      ELSE
         Stat = 9999
      ENDIF
C
      Status = Stat
      RETURN
      END
C
C
C     ===========================================
      SUBROUTINE Get_String(Format,Number,String)
C     ===========================================
C
C     This routine translate a number into a string
C     ---------------------------------------------
C     INPUT: Format is the format type for the number
C               1 or -1 is Integer
C               2 or -2 is Hexadecimal
C               <0 is leading zeros in the string
C            Number is the number to be translated
C     OUTPUT: String is the translated number        
C
      IMPLICIT NONE
C
      INTEGER*2 
     &  Format,          ! Format of translation (|1|-Integer,|2|-Hex)
                         !                        <0 is print leading zeros
     &  Dimension,
     &  Length,
     &  Number           ! Number to be translated
      CHARACTER
     &  String_Format*7, ! String in a format
     &  String*(*)       ! String receiver
C
      INCLUDE 'lib_gd.inc'
C
      DATA String_Format /'(.....)'/  
C
      Dimension = MIN (LEN(String),5) 
      IF (ABS(Format).EQ.1) THEN
          String_Format(2:2) = 'I'
      ELSE
          String_Format(2:2) = 'Z'
      ENDIF 
C
      WRITE (String_Format(3:3),'(I1)') Dimension
C 
      IF (Format.LT.0) THEN
          String_Format(4:4) = '.'
          WRITE (String_Format(5:5),'(I1)') Dimension
      ELSE
          String_Format(4:5) = '  '
      ENDIF   
C
      WRITE (String,String_Format,ERR=1001) Number
 1001 CONTINUE
C
      RETURN
      END
C
C
C
C     ========================================
      SUBROUTINE Init_Libgd(Terminal,Computer)
C     ========================================
C
C     This routine initializes these library routine parameters
C     --------------------------------------------------------- 
C     << It should be called as first pass before calling any other routine >>
C
C     INPUT: Terminal is the terminal identifier 1-VT220/VT100 Family
C             (No other terminal are supported yet!)
C            Computer is the computer Id      1: Digital - VAX
C                                             2: Gould - MPX
C                                             3: Silicon Graphics - UNIX
C                                             4: IBM - UVX [UNIX]
C
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'  
C
      INTEGER*4
     &  Computer,
     &  Terminal 
C
      Term_Type = Terminal
      Computer_Id = Computer 
C
      Blank(1:40)  = '                                        '
      Blank(41:80) = '                                        '
C
      Nul    = ' '
      Bell   = ''
      Escape = '' 
C
      RETURN
C
      END
C
C
C
C     ============================================================
      SUBROUTINE Input_Buffer(String,S_Yp,Position,Code,Term_Echo)
C     ============================================================
C
C     This routine manages the input buffer from a terminal.  It automatically
C     takes care of the wrap around recall type buffer.
C     ------------------------------------------------------------------------
C     INPUT: String is the input buffer string from user
C            Position is the column where the input should start
C     OUTPUT: Code is the string code return from terminal
C             Term_Echo is the terminal Echo flag.  It will print the input
C                       character only if this flag is true
C
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'  
C
      CHARACTER 
     &  String*80,
     &  Character*1
C
      INTEGER*2 
     &  Code,
     &  Position,
     &  Right_Limit,
     &  Dummy1
C
      INTEGER*4
     &  Xp,
     &  Yp,
     &  S_Yp,
     &  Length,
     &  Pointer
C    
      LOGICAL*1 
     &  Buffer_Full,
     &  Overstrike/.TRUE./,
     &  Term_Echo
C
      Xp = -2
      CALL Term_Write (Xp,Yp,Dummy_String,0)
      Buffer_Full = .FALSE.
      Right_Limit = Max_Char_Length - S_Yp
      Pointer  = MIN (Position+1,Right_Limit+0)
      Length = Position
      DO WHILE (.NOT.Buffer_Full)
         Character = Nul
         CALL READKEY (Dummy1,Character,Code)
         IF (.NOT.Term_Echo) THEN
            Buffer_Full = .TRUE.
            IF (Code.EQ.ED_RET) THEN
               Code = 0
            ELSE IF (Code.EQ.ED_NUL) THEN
               String(1:1) = Character
            ENDIF
         ELSE
            IF (Code.EQ.ED_NUL) THEN
               IF (ICHAR(Character).NE.0) THEN
                  IF (Overstrike) THEN
                     String(Pointer:Pointer) = Character
                     CALL Term_Write (Xp,Pointer+S_Yp,Character,1)
                     Pointer = Pointer+1
                     IF (Pointer.GT.Right_Limit) THEN
                        Buffer_Full=.TRUE.
                     ELSE IF (Length.LT.Pointer-1) THEN
                        Length=Length+1
                     ENDIF
                  ELSE
                     String(Pointer+1:Length+1) = String(Pointer:Length)
                     String(Pointer:Pointer) = Character
                     Length=Length+1
                     IF (Length.GT.Right_Limit) THEN
                        Buffer_Full = .TRUE.
                        Length = Right_Limit
                     ENDIF
                     CALL Term_Write(Xp,Pointer+S_Yp,
     &                               String(Pointer:Length),
     &                               (Length-Pointer+1))
                     Pointer = Pointer+1
                     IF (Pointer.LE.Right_Limit) THEN
                        CALL Term_Write(Xp,Pointer+S_Yp,Dummy_String,0)
                     ENDIF
                  ENDIF
               ENDIF
            ELSE IF (Code.EQ.ED_CRT) THEN
               IF (Pointer.LT.(Length+1)) THEN
                  Pointer=Pointer+1
                  CALL Term_Write(Xp,Pointer+S_Yp,Dummy_String,0)
               ENDIF
            ELSE IF (Code.EQ.ED_CLF) THEN
               IF (Pointer.GT.1) THEN
                  Pointer = Pointer - 1
                  CALL Term_Write (Xp,Pointer+S_Yp,Dummy_String,0)
               ENDIF
            ELSE IF (Code.EQ.ED_RET) THEN
               Buffer_Full = .TRUE.
               Code = 0
            ELSE IF (Code.EQ.ED_TPO) THEN
               Overstrike = .TRUE.
            ELSE IF (Code.EQ.ED_INS) THEN
               Overstrike = .FALSE.
            ELSE IF (Code.EQ.ED_TYIN) THEN
               Overstrike = .NOT.Overstrike
            ELSE IF (Code.EQ.ED_BKSP) THEN
               IF (Pointer.GT.1) THEN
                  IF (Length.GE.Pointer) THEN
                     String(Pointer-1:Length) = 
     &                             String(Pointer:Length)//' '
                  ELSE
                     String(Pointer-1:Length) = ' '
                  ENDIF
                  Pointer = Pointer - 1
                  CALL Term_Write (Xp,Pointer+S_Yp,
     &                             String(Pointer:Length),
     &                             (Length-Pointer+1))
                  Length = Length - 1
                  IF (Length.LT.1) THEN
                     Length=1
                  ENDIF
                  CALL Term_Write(Xp,Pointer+S_Yp,Dummy_String,0)
               ENDIF
            ELSE
               Buffer_Full = .TRUE.
            ENDIF
         ENDIF
      ENDDO
C
      RETURN
C
      END
C
C
C     ============================================================
      SUBROUTINE Parse_Command(String,Length,NMax_Item,A_List_Item,
     &                         Found,Status)
C     ============================================================
C
C     This routine parses a string into item, letter, qualifier and
C     parameter fields.
C     -------------------------------------------------------------
C     INPUT: String is the command fields
C            Length is the length of command fields
C            Max_Item is the maximum number of valid item fields
C            A_List_Item is an array for the item fields (MAX=15)
C     OUTPUT: Found is the List_Item position for the parsed String
C             Status is the status of the parse command routine
C                     0: Successful completion
C                     1: No item 
C                     2: Ambiguous items
C                   999: Invalid character in the string
C
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'
C
      CHARACTER
     &  String*(*),                 ! String to be parsed
     &  Word(Max_Word)*15,          ! Parsed word from string
     &  A_List_Item(15)*15          ! List of items
C
      INTEGER*2 
     &  N_Item,                     ! Number of items found in the String

     &  Length_Word(Max_Word),      ! Length of each word in the string
     &  Code_Word(Max_Word),      !Code for each word(0-Parameter,1-Qualifiers)
     &  Number_Word                 ! Number of words in the string
C
      INTEGER*4
     &  Found,                  ! Item position in the list of the parsed string
     &  Status,                 ! Status
     &  Length,                 ! Length of line to be parsed
     &  NMax_Item,              ! Number of items in the list 
     &  Max_Item
C
      Status = 0
      IF(NMax_Item.LT.0) THEN
         Max_Item = -NMax_Item
      ELSE
         Max_Item = NMax_Item
      ENDIF
C
C     Parse all the words in the string
C     ---------------------------------
      CALL Parse_Word (String,Length,Word,Length_Word,
     &                           Code_Word,Number_Word)
C
      N_Item = 0
C
      IF (Code_Word(1).EQ.0)THEN
         Loop_Index = 1
         DO WHILE (Loop_Index.LE.Max_Item)
            IF ( A_List_Item(Loop_Index)(1:Length_Word(1)) .EQ.
     &          Word(1)(1:Length_Word(1))                ) THEN
               N_Item = N_Item + 1
               Found = Loop_Index
            ENDIF
            Loop_Index = Loop_Index + 1
         ENDDO
      ENDIF
C
C     Reflect Status of the Parse operation
C     -------------------------------------
      IF (N_Item.NE.1) THEN
C
         IF (N_Item.LT.1) THEN
            Status = 1
         ELSE
            Status = 2
        ENDIF
      ELSE
        Status = 0
      ENDIF
C
      RETURN
C
      END
C
C
C
C     ==========================================================
      SUBROUTINE Parse_Number1(String,Length,Max_For_List,Count,
     &                            A_Numb_List,Status)
C     ==========================================================
C
      IMPLICIT NONE
C
      CHARACTER 
     &  String*(*)            ! String to be parsed
C
      INTEGER*2
     &  A_Numb_List(1),       ! Number list array 
     &  Length,               ! Length of the parsed string
     &  Prev_Comma,           ! Previous position of a Comma in the string
     &  Comma_Position,       ! Actual Position of a Comma in the string
     &  Hyphen_Position,      ! Actual Position of an Hyphen in the string
     &  Pos_Min_Hyphen,       ! Minimum position to look for an Hyphen
     &  Pos_Max_Hyphen,       ! Maximum position to look for an Hyphen 
     &  Max_For_List,         ! Dimension of the number list
     &  Start_Number,         ! Hyphen type numbering start number
     &  Stop_Number,          ! Hyphen type numbering stop number
     &  Status,               ! Status return
     &  Count                 ! Count for number list 
C
      REAL*4
     &  Number                ! Real number parsed
C
      LOGICAL*1 
     &  Stop_Loop,            ! Loop stopper
     &  Comma_Loop,           ! Loop for a comma
     &  Invalid               ! Invalid number flag
C
      INCLUDE 'lib_gd.inc'
C
      Count     = 0
      Prev_Comma = 1
      Invalid    = .FALSE.
      Comma_Loop = .TRUE.
C
      DO WHILE (Comma_Loop)
C
C        Look for all commas in the input line
C        -------------------------------------
         Comma_Position = INDEX(String(Prev_Comma:Length),',')
C
         Pos_Min_Hyphen = Prev_Comma
         IF (Comma_Position.GT.0) THEN
            Comma_Position = Comma_Position + Prev_Comma -1
            Pos_Max_Hyphen = Comma_Position - 1
            Prev_Comma = Comma_Position + 1
         ELSE
            Pos_Max_Hyphen = Length
            Comma_Loop = .FALSE.
         ENDIF
C
C        Check for hyphen character between two numbers
C        ----------------------------------------------
         Hyphen_Position = INDEX(String(Pos_Min_Hyphen:Pos_Max_Hyphen),
     &                                         '-')
         IF (Hyphen_Position.EQ.0) THEN
C
C           No hyphen found: store number
C           -----------------------------
            CALL Get_Number(String(Pos_Min_Hyphen:Pos_Max_Hyphen),
     &                      Pos_Max_Hyphen-Pos_Min_Hyphen+1,
     &                      Number,Status)
            IF (Status.EQ.0) THEN
               Count = Count + 1
               IF (Count.LE.Max_For_list) THEN
                  Start_Number = Number 
                  A_Numb_List(Count) = Start_Number
               ELSE
                  Status = -1
                  Comma_Loop = .FALSE.
               ENDIF
            ELSE
               Comma_Loop = .FALSE.
               Invalid = .TRUE.
            ENDIF
         ELSE
C
C           Hyphen found: store all number between them
C           -------------------------------------------
            Hyphen_Position = Hyphen_Position + Pos_Min_Hyphen - 1
            CALL Get_Number(String(Pos_Min_Hyphen:Hyphen_Position-1),
     &                      Hyphen_Position - Pos_Min_Hyphen,
     &                      Number,Status)
            IF (Status.EQ.0) THEN
C
               Start_Number = Number 
               CALL Get_Number(String(Hyphen_Position+1:Pos_Max_Hyphen),
     &                         Pos_Max_Hyphen - Hyphen_Position,
     &                         Number,Status)
               IF (Status.EQ.0) THEN
C
                  Stop_Number = Number
                  Loop_Index = Start_Number
                  Stop_Loop = .FALSE.
                  DO WHILE ((Loop_Index.LE.Stop_Number).AND.
     &                       .NOT.Stop_Loop)
                     Count = Count + 1
                     IF (Count.LE.Max_For_List) THEN
                        A_Numb_List(Count) = Loop_Index
                     ELSE
                        Status = -1
                        Comma_Loop = .FALSE.
                        Stop_Loop = .TRUE.
                     ENDIF
                  ENDDO
C
               ELSE
                  Comma_Loop = .FALSE.
                  Invalid = .TRUE.
               ENDIF
            ELSE
               Comma_Loop = .FALSE.
               Invalid = .TRUE.
            ENDIF
         ENDIF
      ENDDO
C
      IF (Status.EQ.0) Then
         IF (Invalid) THEN
            Status = 1
         ELSE IF (Number.EQ.0) THEN
            Status = 9999
         ENDIF
      ENDIF
C
      RETURN
      END
C
C
C     ===============================================================
      SUBROUTINE Parse_Word(String,Length,Word,Length_Word,Type_Word,
     &                                                      Number)
C     ===============================================================
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'
C
      CHARACTER 
     &  String*(*),             ! String to be parsed
     &  Word(Max_Word)*15       ! Each parsed word 
C
      INTEGER*2 
     &  Position,               ! Position of the current character
     &  Length_Word(Max_Word),  ! Length of each parsed word
     &  Type_Word(Max_Word),    ! Type of each parsed word (0-Parameters,
                                !                           1-Qualifiers)  
     &  Number                  ! Number of word in the string
C
      INTEGER*4
     &  Length                  ! Number of characters in the string
C
      LOGICAL*1          
     &  Delimiter,             ! A word delimiter in the string (Space or '/') 
     &  First_Letter           ! First letter of a word flag 
C
      Position       = 1
      Delimiter      = .FALSE.
      First_Letter   = .TRUE.
      Loop_Index          = 0
      Type_Word(1)   = 0
      Length_Word(1) = 0
      DO WHILE (Position.LE.Length)
         IF (String(Position:Position).EQ.' '.OR.
     &       String(Position:Position).EQ.'/'     ) THEN
            IF (.NOT.Delimiter) THEN
               IF (String(Position:Position).EQ.' ') THEN
                  Type_Word(Loop_Index+1) = 0
               ELSE
                  Type_Word(Loop_Index+1) = 1
               ENDIF
               Delimiter = .TRUE.
               IF(Length_Word(Loop_Index).GE.15) THEN
                  Length_Word(Loop_Index) = 
     &                          MIN(Length_Word(Loop_Index)+0,15)
C
C                 Fill the word with blanks 
C                 -------------------------
                  Loop = Max_Char_Per_Word-Length_Word(Loop_Index)
                  DO WHILE (Loop.NE.0)
                     Word(Loop_Index)(Length_Word(Loop_Index)+Loop:) 
     &                                      = ' '
                     Loop = Loop-1   
                  ENDDO 
               ENDIF
C
               IF(Loop_Index.EQ.10) THEN
                   Position = Length+1
               ELSE
                   Length_Word(Loop_Index+1) = 0
               ENDIF
               First_Letter = .TRUE.
            ENDIF
C
         ELSE
            Delimiter = .FALSE.
C
C           Increase # of words each time a new word start 
C           ----------------------------------------------
            IF (First_Letter) THEN
               Loop_Index = Loop_Index+1
               First_Letter = .FALSE.
            ENDIF
C
C           Store length and data
C           ---------------------
            Length_Word(Loop_Index) = Length_Word(Loop_Index) + 1
            Word(Loop_Index)(Length_Word(Loop_Index):
     &           Length_word(Loop_Index)) = String(Position:Position)
         ENDIF
         Position = Position+1
      ENDDO
C
      RETURN
C
      END
C
C
C     ==============================================================
      SUBROUTINE Read_Command(Mode,Xp,Yp,Prompt,Length,Input_String,
     &                        Input_Length,Status)
C     ==============================================================
C
C     This routine prompts the Prompt string at the specified location
C     (LINE[Xp],COLUMN[Yp]) and reads the string answered by the user.
C     ----------------------------------------------------------------
C
C     INPUT:
C
C     OUTPUT:
C
C
      IMPLICIT NONE
C
      CHARACTER 
     &  Input_String*80,
     &  Prompt*(*)
C
      INTEGER*4 
     &  Status,
     &  Input_length,
     &  Length,
     &  Xp,
     &  Yp,
     &  Yp1,
     &  EOLL,
     &  Mode
C
      LOGICAL*1
     &  Cont_Read         
C
      INCLUDE 'lib_gd.inc'
C
      Cont_Read = .TRUE.
      DO WHILE(Cont_Read)
C
         CALL Term_Write(Xp,Yp,Prompt,Length)
C
C        Clear end of line & position cursor at end of prompt
C        ----------------------------------------------------
         Yp1 = Yp+Length
         EOLL = 79-Length
         CALL Term_Write(Xp,Yp1,Blank(1:EOLL),EOLL)
         CALL Term_Write(Xp,Yp1,Dummy_String,0)
C
C        Check MODE > 100 Special function
C               Normal MODE = MODE-1000
C               The prompt represent the input buffer to be transform
C               The prompt length is the real prompt not to be modified
C        ---------------------------------------------------------------
         IF (Mode.GT.1000)THEN
            Input_String(1:80-Length) = Prompt(Length+1:80)
C
C           Put blank in the remaining character(s)
C           ---------------------------------------
            Input_String(80-Length+1:80) = Blank(1:Input_Length)
            Input_Length = 80-Length                         !Set length
            CALL String_Length(Input_String,Input_Length)
        ENDIF
C
        CALL Term_Read(Mode,Input_String,Input_Length,Status)
        Cont_Read = .FALSE.
C
      ENDDO
C
      RETURN
      END
C
C
C
C
C     =======================
      SUBROUTINE Screen_Reset
C     =======================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*4                 ! Output string
C
      INCLUDE 'lib_gd.inc' 
C
      IF (First) THEN
        Out = Escape//'[0m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,4)
C
      RETURN
      END
C
C
C
C
C     =========================
      SUBROUTINE Start_Blinking
C     =========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*4                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[5m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,4)
C
      RETURN
      END
C
C
C
C
C     =========================
      SUBROUTINE Start_Highlite
C     =========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*4                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[1m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,4)
C
      RETURN
      END
C
C
C
C
C     ========================
      SUBROUTINE Start_Reverse
C     ========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*4                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[7m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,4)
C
      RETURN
      END
C
C
C
C
C     ==========================
      SUBROUTINE Start_Underline
C     ==========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*4                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[4m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,4)
C
      RETURN
      END
C
C
C
C     ========================
      SUBROUTINE Stop_Blinking
C     ========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*5                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[25m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,5)
C
      RETURN
      END
C
C
C
C
C     ========================
      SUBROUTINE Stop_Highlite
C     ========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*5                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[22m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,5)
C
      RETURN
      END
C
C
C
C
C     =======================
      SUBROUTINE Stop_Reverse
C     =======================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*5                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[27m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,5)
C
      RETURN
      END
C
C
C
C
C     =========================
      SUBROUTINE Stop_Underline
C     =========================
C
      IMPLICIT NONE
C
      LOGICAL*1 First /.TRUE./
      CHARACTER 
     &          Out*5                 ! Output string
C
      INCLUDE 'lib_gd.inc'
C
      IF (First) THEN
        Out = Escape//'[24m'
        First = .FALSE.
      ENDIF
C
      CALL Term_Write(-1,0,Out,5)
C
      RETURN
      END
C
C
C
C
C     =======================================
      SUBROUTINE String_Length(String,Length)
C     =======================================
C
      IMPLICIT NONE
C
      INTEGER*4
     &  Length,
     &  LENG,
     &  Len
C
      CHARACTER*(*)
     &  String
C
      Len = LENG(String)
      Length = Len
C
      RETURN
      END
C
C
C
C     ===============================================
      SUBROUTINE Term_Read(Mode,String,Length,Status)
C     ===============================================
C
C     This routine reads input from terminal.
C     ---------------------------------------
C     INPUT: MODE select the action for CR processing and upper cases 
C                 conversion
C
C             MODE <= 0 is convert all characters to upper cases.
C             MODE >= 1 is keep characters as they are (Upper or lower).
C             MODE = 0 or 1 is accept CR as input.
C             MODE <> 0 or 1 is refuse CR as input (print error message).
C
C            |MODE|= 99 is no echo of character entered and return immediately
C                    after one character has been entered
C
C             MODE > 100 Special function
C                        Normal MODE = MODE-1000
C                        The prompt represent the input buffer to be transform
C                        The prompt length is the real prompt not to be modified
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'
C 
      CHARACTER 
     &  String*80,
     &  Input*80
C
      INTEGER*4
     &  Length,
     &  Mode,
     &  Xp,
     &  Yp,
     &  Status,
     &  Input_Mode
C
      INTEGER*2 
     &  Type
C
      LOGICAL*1
     &  Cont_Read,
     &  First/.TRUE./,
     &  Term_Echo
C
C
      Status = 0
C     Get current cursor position 
C     --------------------------- 
      Xp = -2
      CALL Term_Write(Xp,Yp,Dummy_String,0)
C
C     Position cursor at end of prompt
C     --------------------------------
      CALL Term_Write(Xp,Yp,Dummy_String,0)
C
C     Read input from terminal until valid one entered
C     ------------------------------------------------
      READ(5,'(A40)',ERR=91) Input
C
C     Get length of string
C     --------------------
      CALL String_Length(Input,Length)
      String = Input(1:Length)
C
C     Convert to uppercase if mode <=0
C     --------------------------------
      IF (Mode.LE.0) THEN
         CALL Convert_Upper(String,Length)
      ENDIF
C
 91   CONTINUE
      RETURN
C
      END
C
C
C
C     ==========================================
      SUBROUTINE Term_Write(Xp,Yp,String,Length)
C     ==========================================
C
C     This routine writes a string to the terminal at the specified
C     position.
C     ------------------------------------------------------------- 
C     INPUT: Xp is the line where the string should be written.  If you 
C               specified at Xp -1, the string is written at the cursor
C               position.    If you specified at Xp -2, the routine will
C               return the last cursor position in Xp,Yp.
C            Yp is the column where the string is written.
C            String is the string to be written
C            Length is the length of the string to be written.  Only length
C                   character will be written whatever the declared length
C                   of the string.
C
      IMPLICIT NONE
C
      INTEGER*4 
     &  Xp,
     &  Yp,
     &  Length,
     &  Prev_Xp,
     &  Prev_Yp
C
      INTEGER*4
     &  Status
C
      CHARACTER
     &  String*(*),
     &  Str_Xp*2,
     &  Str_Yp*2,
     &  Str_Length*3,
     &  Str_Format*28
C
      INCLUDE 'lib_gd.inc'
C
C     Set previous cursor position and leave
C     --------------------------------------
      IF (Xp.EQ.-2) THEN
          Xp = Prev_Xp
          Yp = Prev_Yp
          RETURN
      ENDIF
C
      IF (Length.GT.0) THEN
C
C        Get the character equivalence of the string length
C        --------------------------------------------------
         WRITE(Str_Length,'(I3.3)',ERR=99,IOSTAT=Status ) Length
C
         IF (Xp.NE.-1) THEN
C
C           Write string to the specified position
C           --------------------------------------
C           Get the character equivalence of the Xp,Yp positions
C           -------------------------------------------------------
            WRITE(Str_Xp,'(I2.2)',ERR=99,IOSTAT=Status ) Xp
            WRITE(Str_Yp,'(I2.2)',ERR=99,IOSTAT=Status ) Yp
C
C           Set the format string according to computer index (VAX-SEL)
C           -----------------------------------------------------------
            IF (Computer_Id.EQ.1.OR.Computer_Id.EQ.3) THEN
               Str_Format ='(A1,''['//Str_Xp(1:2)//';'//Str_Yp(1:2)//
     &                      'H'',A'//Str_Length(1:3)//',$)'
            ELSE IF (Computer_Id.EQ.2) THEN
               Str_Format = '(''+'',A1,A1,''['//Str_Xp(1:2)//';'//
     &                       Str_Yp(1:2)//'H'',A'//Str_Length(1:3)//')'
            ENDIF
C
C           Write the string on the screen
C           ------------------------------
CCC            WRITE (6, '(A1,''[24;1H'',A1,$)') Escape,Nul
            WRITE(6,Str_Format,ERR=99,IOSTAT=Status ) Escape,
     &                                         String(1:Length)
         ELSE
C
C           Write the string at the cursor position
C           ---------------------------------------
C           Set the format string according to computer index (VAX-SEL)
C           -----------------------------------------------------------
            IF (Computer_Id.EQ.1.OR.Computer_Id.EQ.3) THEN
               Str_Format = '(A'//Str_Length(1:3)//',$)'
            ELSE IF (Computer_Id.EQ.2) THEN
               Str_Format = '(''+'',A1,A'//Str_Length(1:3)//')'
            ENDIF
C
C
C           Write the string on the screen
C           ------------------------------
CCC            WRITE (6, '(A1,''[24;1H'',A1,$)') Escape,Nul
            WRITE(6,Str_Format,ERR=99,IOSTAT=Status ) 
     &                                 String(1:Length)
         ENDIF
      ELSE
C
C        When length is zero, just position the cursor
C        ---------------------------------------------
C        Get the character equivalence of the Xp,Yp positions
C        -------------------------------------------------------
         WRITE(Str_Xp,'(I2.2)',ERR=99,IOSTAT=Status ) Xp
         WRITE(Str_Yp,'(I2.2)',ERR=99,IOSTAT=Status ) Yp
C
C        Set the format string according to computer index (VAX-SEL)
C        -----------------------------------------------------------
         IF (Computer_Id.EQ.1.OR.Computer_Id.EQ.3) THEN
            Str_Format = '(A1,''['//Str_Xp(1:2)//';'//Str_Yp(1:2)//
     &                                  'H'',$)'
         ELSE IF (Computer_Id.EQ.2) THEN
            Str_Format = '(''+'',A1,A1,''['//Str_Xp(1:2)//';'//
     &                               Str_Yp(1:2)//'H'')'
         ENDIF
C
C        Write the string on the screen
C        ------------------------------
CCC         WRITE (6, '(A1,''[24;1H'',A1,$)') Escape,Nul
         WRITE(6,Str_Format,ERR=99,IOSTAT=Status ) Escape
C
      ENDIF
C
      Prev_Xp = Xp
      Prev_Yp = MIN(Yp+Length,Max_Char_Length+0)
C
  99  CONTINUE
C
      RETURN
C
      END
C
C
C     =========================================================
      SUBROUTINE Wait_For_A_Key(Xp,Yp,Text,Length,Quit_Pressed)
C     =========================================================
C
      IMPLICIT NONE
C
      LOGICAL*1 
     &  Quit_Pressed
C
      CHARACTER
     &  Text*(*),
     &  Input*80
C
      INTEGER*4
     &  Xp,
     &  Yp,
     &  Read_Length,
     &  Length
C
      INTEGER*2
     &  Status
C
      CALL Term_Write(Xp,Yp,Text,Length)
C
      CALL Term_Read(0,Input,Read_Length,Status)
C
      IF (Input(1:1).EQ.'Q') THEN
         Quit_Pressed = .TRUE.
      ELSE
         Quit_Pressed = .FALSE.
      ENDIF
C
      RETURN
C
      END
C
C
C
C     ===========================================
      SUBROUTINE Wait_For_Cont(Mode,Xp,Quit_Type)
C     ===========================================
C
      IMPLICIT NONE
C
      INTEGER*2 
     &  Mode,
     &  Quit_Type,
     &  Length,
     &  Yp,
     &  Xp
C
      INTEGER*4
     &  Status,
     &  Selected,
     &  L_Answer,
     &  Max_Menu
C
      LOGICAL*1 
     &  Cont_Read
C
      CHARACTER*15
     &  Menu(5),
     &  Answer*80, 
     &  Prompt*80
C
      INCLUDE 'lib_gd.inc'
C
      DATA Menu(1)/'EXIT'/
      DATA Menu(2)/'X'/
      DATA Menu(3)/'QUIT'/
      DATA Menu(4)/'NEXT'/
      DATA Menu(5)/'PREV'/
C
      IF (Mode.EQ.0) THEN
         Prompt = ' [ Press <CR>, P[REV] or N[EXT] to continue or Q, E o
     &r X to leave ] '
         Length = 68
         Max_Menu = 5
         Yp = 7
      ELSE IF (Mode.EQ.1) THEN
         Prompt =' [ Press <CR> to continue, N[EXT] to skip section or Q
     &, E or X to leave ] '
         Length = 74
         Max_Menu = 4
         Yp = 3
      ELSE
         Prompt =' [ Press <CR> to continue or Q, E or X to leave ] '
         Length = 50
         Max_menu = 3
         Yp = 15
      ENDIF
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      Cont_Read = .TRUE.
      DO WHILE (Cont_Read)
         CALL Read_Command(-99,Xp,Yp,Prompt,Length,Answer,L_Answer)
         IF (L_Answer.GT.0) THEN
            CALL Parse_Command(Answer,L_Answer,Max_Menu,Menu,
     &                                Selected,Status)
C
            IF (Status.EQ.0) THEN
C
               IF (Selected.LE.3) THEN        !QUIT,EXIT or X command
                  Quit_Type = 0
               ELSE IF(Selected.EQ.5) THEN    !PREV command
                  Quit_Type = -1
               ELSE IF (Selected.EQ.4) THEN    !NEXT command
                  IF (Mode.EQ.1) THEN
                     Quit_Type = 99
                  ELSE
                     Quit_Type = 1
                  ENDIF
               ENDIF
               Cont_Read =.FALSE.
            ELSE                      !Invalid command
               Cont_Read =.FALSE.
               Quit_Type = 0
            ENDIF
         ELSE
            Cont_Read =.FALSE.
            Quit_Type = 1
         ENDIF
      ENDDO
      CALL Term_Write(Xp,Yp,Blank,Length)
C
      RETURN
C
      END
C
C
C
C     ====================================
      SUBROUTINE Wait_Time(Period_Of_Time)
C     ====================================
C
C     This routine waits the amount of time Period_Of_time in seconds
C     ---------------------------------------------------------------
C
C     INPUT: Period_Of_Time is the waste time period in seconds
C
      IMPLICIT NONE
C
      REAL*4 
     &  Total_Seconds,
     &  Prev_Total,
     &  Period_Of_Time
      REAL*4 
     &  Minute,
     &  Second,
     &  Hour,
     &  Hundred_Of_Second
      CHARACTER 
     &  Time*11,
     &  Date*11
C
      INCLUDE 'lib_gd.inc'
C
      WRITE(6,'(A1,A6)') Escape,'[23;1H'
C
C     Get starting time
C     -----------------
      CALL Cdate(Date,Time)
C
C     Transfert it to decimal format
C     ------------------------------
      READ (Time(1:2),*) Hour
      READ (Time(4:5),*) Minute
      READ (Time(7:8),*) Second
      READ (Time(10:11),*) Hundred_Of_Second
C
C     Get starting number of seconds
C     ------------------------------
      Total_Seconds = Hour*3600.0 + Minute*60.0 + Second + 
     &                (Hundred_Of_Second/100.0)
      Prev_Total = Total_Seconds
C
C     Wait the number of second HOW_LONG
C     ----------------------------------
      DO WHILE ((Total_Seconds-Prev_Total).LT.Period_Of_Time)
C
C        Get actual time
C        ---------------
         CALL Cdate(Date,Time)
C
C        Transfert it to decimal format
C        ------------------------------
         READ (Time(1:2),*) Hour
         READ (Time(4:5),*) Minute
         READ (Time(7:8),*) Second
         READ (Time(10:11),*) Hundred_Of_Second
C
C        Get actual number of seconds
C        ----------------------------
         Total_Seconds = Hour*3600.0 + Minute*60.0 + Second + 
     &                   (Hundred_Of_Second/100.0)
      ENDDO
C
      RETURN
C
      END
C
