C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : Sound & Audio                                            **
C   **                                                                      **
C   **  File     : AOSLIB.FOR                                               **
C   **  Function : Common Library routines in Fortran for UNIX systems      **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0             G. De Serre                    7 June 1990      **
C   **  Rev 2.0             P. Daigle                     20 aug. 1990      **
C   **                                                                      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  AOS_Link_IO                                                         **
C   **  Cdate                                                               **
C   **  RANDOM                                                              **
C   **  READKEY                                                             ** 
C   **  Translate                                                           **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C     ======================
      SUBROUTINE AOS_Link_IO
C     ======================
C
C
C    This subroutine read the sound utility logical name file and
C    store them in parameter variables.
C
C   ---------------------------------------------------------------
C    STRUCTURE OF THE DATA FILE
C
C    Line:      Content:
C    ----       -------
C     1 -       Comments
C     2 -       --------
C     3 -       Comments
C     4 -       --------
C     5 -       Comments
C     6 -       --------
C     7 -       DIRECTORY: AOSLIB
C     8 -       DIRECTORY: Simex
C     9 -       DIRECTORY: Ship
C    10 -       DIRECTORY: Executable
C    11 -       DIRECTORY: CDB
C    12 -       DIRECTORY: Help
C    13 -       DIRECTORY: Spare #1
C    14 -       DIRECTORY: Spare #2
C    15 -       LOGICAL: Ship identification
C    16 -       LOGICAL: Ship name description
C    17 -       LOGICAL: Spare #1
C    18 -       LOGICAL: Spare #2
C    19 -       Comments
C    20 -       --------
C    21 -       AOS: DMC number
C    22 -       AOS: DMC page
C    23 -       AOS: Filename Letters
C    24 -       AOS: Computer Id
C    25 -       Comments
C    26 -       --------
C    27 -       FLAG: On site
C    28 -       FLAG: Spare #1
C    29 -       FLAG: Spare #2
C    30 -       FLAG: Spare #3
C    31 -       Comments
C    32 -       --------
C    33 -       WAVEGEN: Data File name
C    34 -       TSDGEN : Data File name
C    35 -       TMSGEN : Data File name
C    36 -       XILINK : Load File name
C    37 -       TMS CODE: Load File name
C    38 -       Comments
C    39 -       -------- 
C    40 -       COM: UPDATE flag
C    41 -       COM: DOWNLOAD flag
C    42 -       COM: WGSIZE flag
C    43 -       COM: Spare #1
C    44 -       COM: Spare #2
C    45 -       COM: Spare #3
C   ------------------------------------------------------------------
C
      IMPLICIT NONE
C
      INTEGER*2
     &  Index,
     &  Config_Len(12),
     &  MODE,            !Write mode (1-First,2-Second,3-Both)
     &  File_L(5),
     &  Comp_Id          !1=Vax, 2=Gould
C
      INTEGER*4
     &  Write_Enable(6), !Write mode for each flag
     &  Status,
     &  revstat,
     &  stat1
C
      CHARACTER
     &  xfile*80,
     &  n_xfile*80,
     &  String*80,
     &  DMC_Number*2,
     &  Page_Number*2,
     &  Filetters*3,
     &  Read_Line(45)*80, 
     &  Config_String(12)*80,
     &  FLAG_DATA(6)*19  ,!String for flag definition
     &  VAL*1            ,!
     &  Value*1          ,!Character value of the flags
     &  File_N(5)*40     ,!Input file names
     &  Tempo*255         
C
      LOGICAL*1
     &  Com_Value(6)   ,!Updated value for each flag
     &  Com(6)         ,!Communication flags(UPDATE,DOWNLOAD,WGSIZE)    
     &  Flag(4)         !OnSite flag ...
C
C
      INCLUDE 'lib_gd.inc'
C
      DATA FLAG_DATA    /' AOS    : UPDATE  =',
     &                   ' AOS    : DWNLOAD =',
     &                   ' AOS    : WGSIZE  =',
     &                   ' AOS    : Spare    ',
     &                   ' AOS    : Spare    ',
     &                   ' AOS    : Spare    '/
C
C     ******************
C         XLINK_READ
C     ******************
C
      ENTRY Xlink_Read(Config_String,Config_Len,DMC_Number,
     &     Page_Number,Filetters,Comp_Id,Flag,Com,File_N,File_L,Status)
C
C     Open AOSXLINK.INF file
C     ----------------------
      xfile = 'aosxlink.inf'
      CALL rev_curr(xfile,n_xfile,' ',.FALSE.,1,revstat)
      OPEN(UNIT=99,FILE=n_xfile,STATUS='OLD',IOSTAT=status,
     &     ERR=1000)
C
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(1)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(2)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(3)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(4)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(5)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(6)
      DO Index=1,10
         READ(99,100,IOSTAT=Status,ERR=1000) String,
     &                         Config_Len(Index),Tempo
         Config_String(Index)(1:Config_Len(Index))
     &               =Tempo(5:5+Config_Len(Index))
      ENDDO
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(17)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(18)
C
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(19)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(20)
C
      READ(99,110,IOSTAT=Status,ERR=1000) String,DMC_Number
      READ(99,111,IOSTAT=Status,ERR=1000) String,Page_Number
      READ(99,112,IOSTAT=Status,ERR=1000) String,Filetters
      READ(99,113,IOSTAT=Status,ERR=1000) String,Comp_Id
      Computer_Id = Comp_Id
C
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(25)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(26)
C
      DO Index=1,4
         READ(99,120,IOSTAT=Status,ERR=1000) String,Value
         IF (Value.EQ.'T') THEN
            Flag(Index) = .TRUE.
         ELSE
            Flag(Index) = .FALSE.
         ENDIF
      ENDDO 
C
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(31)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(32)
C
      DO Index=1,5
         READ(99,105,IOSTAT=Status,ERR=1000) String,
     &        File_L(Index),Tempo
         File_N(Index)(1:File_L(Index))=Tempo(5:5+File_L(Index))
      ENDDO
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(38)
      READ(99,'(A80)',IOSTAT=Status,ERR=1000) Read_Line(39)
C
      DO Index=1,3
         READ(99,120,IOSTAT=Status,ERR=1000) String,Value
         IF (Value.EQ.'T') THEN
            Com(Index) = .TRUE.
         ELSE
            Com(Index) = .FALSE.
         ENDIF
      ENDDO 
C
 1000 CLOSE (UNIT=99,IOSTAT=Status,ERR=1005)
 1005 CONTINUE     
      RETURN
C
 100  FORMAT(A14,I2,A)
 105  FORMAT(A24,I2,A)
 110  FORMAT(A14,A2)
 111  FORMAT(A14,A2)
 112  FORMAT(A14,A3)
 113  FORMAT(A15,I2)
 120  FORMAT(A20,A1)
C
C     *******************
C         XLINK_WRITE
C     *******************
C
C     Write flags routine
C     -------------------
      ENTRY Xlink_Write(Write_Enable,Com_Value,Status)
C
C     Open aosxlink.inf file
C     ----------------------
      xfile = 'aosxlink.inf'
      CALL rev_curr(xfile,n_xfile,' ',.FALSE.,1,revstat)
      OPEN(UNIT=99,FILE=n_xfile,STATUS='OLD',IOSTAT=Status,ERR=1003)
C
C     Read all the 45 lines and store them
C     ------------------------------------
      DO Index=1,45
        READ(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(Index)
      ENDDO
C
      REWIND (UNIT=99)
C
      DO Index=1,39
         WRITE(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(Index)
      ENDDO
C
      DO Index=1,3
         IF (Write_Enable(Index).EQ.1) THEN
            IF (Com_Value(Index))THEN
               VAL='T'
            ELSE
               VAL='F'
            ENDIF
C
            WRITE(99,500,IOSTAT=Status,ERR=1003) FLAG_DATA(Index),
     &                   VAL
         ELSE
            WRITE(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(39+Index)
         ENDIF
      ENDDO
      WRITE(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(43)
      WRITE(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(44)
      WRITE(99,'(A80)',IOSTAT=Status,ERR=1003) Read_Line(45)
C
 1003 CLOSE(UNIT=99,IOSTAT=Status,ERR=1004)
 1004 CONTINUE
C
      RETURN
C
 500  FORMAT(1X,A19,A1)
C
      END
C
C
C
C     ===========================
      SUBROUTINE CDATE(DATE,TIME)
C     ===========================
C
C    This routine get time and date for the day
C
      IMPLICIT NONE
C
      INCLUDE 'lib_gd.inc'
C
       CHARACTER
     & DATE*11        ,!Date
     & TIME*11        ,!Time
     & DAT_TIM*24
C
       CHARACTER*24
     & fdate
C
CXXX       CALL GET_DATE(DATE,TIME)
       DAT_TIM = fdate()
       TIME(1:8) = DAT_TIM(12:19)
       TIME(9:11)= ':00'
       DATE(1:11)= '  -   -    '
       DATE(1:2) = DAT_TIM(9:10)
       DATE(4:6) = DAT_TIM(5:7)
       DATE(8:9)= DAT_TIM(23:24)
C
       RETURN
       END
C =======================================================================
C                             RANDOM
C =======================================================================
C
      SUBROUTINE RANDOM(SEED,OUT)
C
      INTEGER*4 SEED
      REAL*4 OUT
C
      CALL SRAND(OUT)
      OUT = RAND(SEED)
C
      RETURN
      END
C
C
C
C     =====================================
      SUBROUTINE ReadKey (Dummy,Chr,Status)
C     =====================================
C
      IMPLICIT NONE
C
C'Purpose
C --- The READKEY module reads a character from the terminal,
C     converts it to upper case and returns it to the calling
C     calling program.
C
C
      INTEGER*2
     &          Status,                    !Return status
     &          Dummy
C
      CHARACTER
     &           GETKEY,            !C routine...
     &           Chr,               !Equiv. to 1st element of BUFFER
     &           NESC,
     &           RET
C
C
      INCLUDE 'lib_gd.inc'
C
C --- Initial values
C
      DATA NESC /'1B'X/,
     &     RET  /'0D'X/
C
C --- Read one character
C
      Chr = GETKEY()
C
C --- If the 1st character is 'ESCAPE'
C
      IF (Chr.EQ.RET) THEN
         Status = 1
      ELSE IF (Chr.EQ.NESC) THEN
         Status = 2
      ELSE
         CALL Convert_Upper(Chr,1)
         Status = 0
      ENDIF     
C
      CALL Restore()
      RETURN
      END
C
C ==========================================================================
C                              TRANS
C ==========================================================================
C
C   This subroutine translate logical names
C
      SUBROUTINE Translate(INP_PAR,INP_LEN,LOGNAME,L_LOG,STATUS)

      IMPLICIT NONE
C
      INTEGER*4
     & STATUS,         !Status of CAE routine logical name translation
     & cae_trnl        !CAE routine for translation
C
      CHARACTER
     & LOGNAME*40 ,    !String for AOSUTY.CNF parameters
     & LOGNAME2*40,
     & INP_PAR*(*)     !Logical name
C
      INTEGER*4
     & L_LOG,          !Length of translated logical name
     & INP_LEN         !Logical name length
C
C     Look for the LOGICAL table name translation with CAE routine
C     ------------------------------------------------------------
      STATUS = cae_trnl (INP_PAR,L_LOG,LOGNAME2,0)
C
      IF (LOGNAME2(1:1).EQ.'/') THEN
       LOGNAME = LOGNAME2(1:L_LOG)//'/'
         L_LOG = L_LOG + 1
      ELSE
         LOGNAME = LOGNAME2(1:L_LOG)
      ENDIF
C
 99   CONTINUE
C
C     Get environment variable value
C     ------------------------------
CCC      CALL GETENV(INP_PAR(1:INP_LEN),LOGNAME2)
CCC      CALL String_Length(LOGNAME2,L_LOG)
CCC      IF (L_LOG .LE. 0) THEN
CCC         STATUS = 99
CCC      ELSE
CCC         STATUS = 1
CCC         IF (LOGNAME2(1:1).EQ.'/') THEN
CCC            LOGNAME = LOGNAME2(1:L_LOG)//'/'
CCC            L_LOG = L_LOG + 1
CCC         ELSE
CCC            LOGNAME = LOGNAME2(1:L_LOG)
CCC         ENDIF
CCC      ENDIF
C
C
      RETURN
      END
C
