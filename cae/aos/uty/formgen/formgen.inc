C======================================================================
C             FORMGEN COMMON BLOCK DATA DECLARATION
C======================================================================
C
C
      INTEGER*2
C
     &  MAXFILE                ,!Number of data files to be processed
     &  NUMFILE                ,!Number of sound utilities data files
     &  REC_MAXS               ,!Maximum block size (Bytes of data)
     &  Config_L(12)           ,!XLink_Read parameter...
     &  File_Len(5)            ,!
     &  Comp_Id                 !XLink_Read parameter...
C
C
      INTEGER*4
C
     & DAC_FNUM               ,!DAC Temporary file number
     & TAG_NUMB                 !Number of TMS Tag Characters
C
C
      CHARACTER
C
     &  SND_ID*2,               !New Sound system download identification
     &  Config_S(12)*80,        !Xlink_Read string parameter...
     &  DMC_Num*2,              !Xlink_Read passed parameters ...
     &  Page_Num*2,             !    "         "       "
     &  File_N(5),              !
     &  Filetters*3             !    "         "       "
C
C
      PARAMETER   (MAXFILE = 8)
      PARAMETER   (NUMFILE = 8)
      PARAMETER   (DAC_FNUM = 9)
      PARAMETER   (TAG_NUMB = 5)
      PARAMETER   (SND_ID = 'NS')
      PARAMETER   (REC_MAXS = 512)
C
C
      INTEGER*2
C
     & L_DATA_DIR,
     & L_INT_DIR,
     & DMC_NUMB                ,!DMC Number
     & DMC_PAGE                ,!Sound DMC page number
     & SLT_NUMB                ,!Slot number
     & TOT_SLOT                ,!Total number of slots used by the data file
     & TOT_RECS                ,!Total number of records for output DLD file
     & REC_SIZA                ,!Data file record ASCII size in Bytes
     & REC_SIZB                ,!Data file record BINARY size in Bytes
     & SLT_RECS                ,!Slot total number of records
     & FILE_L(11)              ,!File name string length
     & TASK_LEN(8)             ,!Task name string length
     & FIL_SLOT(27)            ,!The slots used by data file
     & BLCK_INF(400,8)          !Block information array
C
C
      INTEGER*4
C
     & REC_NUMB                ,!Output download file record number
     & LINE_L                  ,!File name string length
     & MLT_SLOT                ,!Number of slots for Multi download
     & PRV_FREC                ,!Input file previous total number of records
     & FIL_RECS                ,!Data file record number
     & DAC_SREC                ,!DAC Data starting record
     & OUT_FILL                ,!Output file name length
     & INF_FILL                ,!Information file name string length
     & TMP_FILL                ,!Temporary file name string length
     & DAC_FILL                ,!DAC Temporary file name string length
     & NUM_BYTE                ,!Data file size in Bytes
     & TOT_BYTE                ,!Total number of Bytes for all data files
     & PARA_L(20)              ,!Input parameter string length
     & STRG_LEN(4)             ,!Input parameter corresponding string length
C
     & BLK_SIZE(400)           ,!Size of each block of the Output download file
C
     & INP_FILL(MAXFILE)        !Input data file name string length
C
C
      CHARACTER
C
     & ESC*2                   ,!Escape sequence
     & LINE*40                 ,!File name string temporary buffer
     & POSUP*1                 ,!Up window cursor position
     & POSDN*1                 ,!Down window cursor position
     & DATA_ID*2               ,!Input data identification
     & DATA_DIR*40,
     & INT_DIR*40,
     & INF_FILE*80             ,!Information file name string
     & OUT_FILE*80             ,!Output download file name string
     & TMP_FILE*80             ,!Temporary file name string
     & DAC_FILE*80             ,!DAC Temporary file name string
     & ID_STRG*132             ,!Data identification line string
     & STRG(4)*132             ,!Input parameter corresponding name string
     & SLT_LINE*132            ,!Comment line containing slots for multidownload
     & FILE_ID(9)*2            ,!Data file identification
     & PARA_S(20)*255          ,!Input parameter string
     & DATA_BUF*1024           ,!Output data buffer
     & FILE_S(11)*52           ,!File name string
     & TASK_NAM(8)*9           ,!Task name string
     & HEAD_STR(9)*26          ,!Input data file start header
     & HEAD_END(9)*24          ,!Input data file end header
     & BLOCK_ID(400)*2         ,!Input data identification associated with block
     & INP_FILE(MAXFILE)*80    ,!Input data file name string
     & TAG_CHAR(TAG_NUMB)*1     !TMS Tag Characters
C
C
       LOGICAL*2
C
     & ID_PRS                 ,!Identifier is present flag
     & MLT_DNLD               ,!MULTI download flag
     & MLT_DATA               ,!MULTI download data mode flag
     & STD_DATA               ,!STANDARD download data mode flag
     & TMS_DATA               ,!TMS data type flag
     & DAC_DATA               ,!DAC data flag
     & FST_PASS               ,!First pass
     & DAC_PROC               ,!DAC data is being processed
     & DAC_PRST               ,!Data of DAC has been stored
     & DAC_INIT               ,!Flag for the DAC starting record
     & DATA_PRS               ,!Data is present flag
     & COM_FLAG(6)            ,!Sound utilities communication flags
     & FILE_Q(MAXFILE)        ,!Input data file status
     & F_COMPLT(NUMFILE+1)     !Input data file completion (+1 is for DAC data)
C
      LOGICAL*1
     & LinkFlag(4)            ,!XLink_Read parameter...  
     & Com(6)                  !XLink_Read parameter...
C
C
C      LOGICAL type common data
C
       COMMON / CBLCK11 / F_COMPLT,TMS_DATA,FST_PASS,FILE_Q,DAC_DATA
       COMMON / CBLCK12 / DAC_PROC,DAC_INIT,DAC_PRST,MLT_DNLD,MLT_DATA
       COMMON / CBLCK13 / STD_DATA,COM_FLAG,DATA_PRS,ID_PRS
       COMMON / CBLCK14 / LinkFlag,Com
C
C
C      CHARACTER type common data
C
       COMMON / CBLCK1  / POSUP,POSDN
       COMMON / CBLCK2  / DATA_ID,ESC,BLOCK_ID,DMC_Num,Page_Num
       COMMON / CBLCK3  / Filetters
       COMMON / CBLCK52 / FILE_S
       COMMON / CBLCK40 / LINE,DATA_DIR,INT_DIR
       COMMON / CBLCK80 / OUT_FILE,INP_FILE,TMP_FILE,INF_FILE,
     &                    DAC_FILE,Config_S
       COMMON / CBLCK132/ STRG,ID_STRG,SLT_LINE
       COMMON / CBLCK255/ PARA_S
       COMMON / CBLCK1024/ DATA_BUF
C
C
C      INTEGER*2 type common data
C
       COMMON / CBLCK31 / REC_SIZA,REC_SIZB,TOT_RECS,BLCK_INF
       COMMON / CBLCK32 / TOT_SLOT,FIL_SLOT,FILE_L,L_DATA_DIR,L_INT_DIR
       COMMON / CBLCK33 / DMC_NUMB,SLT_NUMB,SLT_RECS,DMC_PAGE,Config_L,
     &                    Comp_Id
C
C
C      INTEGER*4 type common data
C
       COMMON / CBLCK41 / STRG_LEN,PARA_L,OUT_FILL,INF_FILL,DAC_FILL,
     &                    REC_NUMB
       COMMON / CBLCK42 / FIL_RECS,NUM_BYTE,TOT_BYTE,DAC_SREC,MLT_SLOT
       COMMON / CBLCK43 / TMP_FILL,PRV_FREC,INP_FILL,BLK_SIZE,LINE_L
C
C
C
      DATA FILE_ID / 'HP'      ,!SPC data file identification
     &               'TI'      ,!XILINX INT data file identification
     &               'HX'      ,!XILINX data file identification
     &               'TT'      ,!TMS data file identification
     &               'HS'      ,!TSD data file identification
     &               'HF'      ,!FIRGEN data file identification
     &               'HH'      ,!HARMONY data file identification
     &               'HW'      ,!WAVEGEN data file identification
     &               'HD'      /!DAC data identification
C
C
C
       DATA HEAD_STR       /'$ SPC PAGE REGISTER  START',
     &                      '$ INTXILINX   CODE   START',
     &                      '$ XILINX  CONTROLLER START',
     &                      '$ TMS         CODE   START',
     &                      '$ TSD   CONTROLLER   START',
     &                      '$ FIRGEN    DATA     START',
     &                      '$ HARMONY   DATA     START',
     &                      '$ WAVEGEN CONTROLLER START',
     &                      '$ TSD DAC  REGISTER  START'/
C
C
C
       DATA HEAD_END       /'$ SPC PAGE REGISTER  END',
     &                      '$ INTXILINX   CODE   END',
     &                      '$ XILINX  CONTROLLER END',
     &                      '$ TMS          CODE  END',
     &                      '$ TSD   CONTROLLER   END',
     &                      '$ FIRGEN    DATA     END',
     &                      '$ HARMONY   DATA     END',
     &                      '$ WAVEGEN CONTROLLER END',
     &                      '$ TSD DAC  REGISTER  END'/
C
C
C
      DATA TASK_NAM /'SPC','INTXILINX','XILINX','TMS','TSD','FIRGEN',
     &               'HARMONY','WAVEGEN'/
C
      DATA TASK_LEN / 3, 9, 6, 3, 3, 6, 7, 7 /
C
      DATA TAG_CHAR / '9', 'B', '7', 'F', ':' /
C
C
