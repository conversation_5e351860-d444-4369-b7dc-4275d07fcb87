INCLUDE = formgen.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
formgen: formgen.o formlib.o forioibm.o formprc.o $(CAELIB)/libcae.a \
$(LIBDIR)/libaos.a
	xlf -C -qcharlen=1024 formgen.o formprc.o formlib.o forioibm.o \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o $(EXEDIR)/formgen
#
formgen.o: formgen.f $(INCLUDE)
	xlf -C -qcharlen=1024 -c formgen.f
#
formlib.o: formlib.f $(INCLUDE)
	xlf -C -qcharlen=1024 -c formlib.f
#
formprc.o: formprc.f $(INCLUDE)
	xlf -C -qcharlen=1024 -c formprc.f
#
forioibm.o: forioibm.f $(INCLUDE)
	xlf -C -qcharlen=1024 -c forioibm.f
