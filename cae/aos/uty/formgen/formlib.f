C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                            HEADER
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine displays the utility header on the terminal screen.
C
C
C
C
      SUBROUTINE HEADER
C
C
      IMPLICIT NONE
C
C
       CHARACTER
     &  DDATE*9         ,!Date in an array
     &  DTIME*11         !Time in an array
C
C
      INCLUDE 'formgen.inc'
C
C
      CALL CDATE(DDATE,DTIME)
C
C     Write the utility header to terminal
C     ------------------------------------
C
      WRITE(6, 100) ESC,ESC,ESC,DDATE
      WRITE(6, 200) ESC,ESC,ESC
 100  FORMAT(' ',A2,'2J',A2,'1;25H',33('*')/24X,'*',T27,
     &       'E T H E R N E T   F O R M A T ',T57,'*',/24X,
     &       '*      G E N E R A T I O N      *'
     &       ,/24X,'*',T35,'U T I L I T Y',T57,'*',/24X,33('*'),A2,
     &       '7;35H','VERSION 2.1',/10X,/27X,'DATE OF THIS RUN: ',A)
C
 200  FORMAT(1X,A2,'11;1H',34('='),'< STATUS >',36('='),/1H0,A2,'13;1H'
     &       ,80('-'),/1H0,A2,'21;1H',34('-'),'< ERRORS >',36('-'))
C
C
      RETURN
      END
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                            INF_HEAD
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine prints the utility information file header in the
C information file 'Shipname'SND.INF.xxx
C
C
C
      SUBROUTINE INF_HEAD
C
C
      IMPLICIT NONE
C
C
       CHARACTER
     &  DDATE*9         ,!Date in an array
     &  DTIME*11         !Time in a bottle
C
C
      INCLUDE 'formgen.inc'
C
C
      CALL CDATE(DDATE,DTIME)
C
C     Write the utility information file header
C     -----------------------------------------
C
      WRITE(30, 100) DDATE
 100  FORMAT(1X///,27X,27('*')/27X,'*',T31,'FORMGEN UTILITY  V2.1',T54
     &       ,'*',/27X,'*',T36,'INFORMATION',T54,'*',/27X,'*',T40
     &       ,'FILE',T54,'*',/27X,27('*')//27X,'Date of This Info: ',A,
     &       //,5X,66('*')//)
C
      RETURN
C
      END
C
C
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                            CUR_POS
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C    This subroutine calculates the position of the display to the
C    screen. It also erases the previous lines
C
C       1- UP SCREEN (11 to 13)       : Processing Status message
C       2- MIDDLE SCREEN (14 to 20)   : Processing completion information
C       3- DOWN SCREEN (21 to 24)     : Error message
C
      SUBROUTINE CUR_POS(TYPE)
C
C
      IMPLICIT NONE
C
C
       CHARACTER
     &  NUL*1         !
C
C
       INTEGER*2
     &  CPOSUP       ,!Screen line position in ASCII for UP
     &  CPOSDOWN      !Screen line position in ASCII for DOWN
C
       INTEGER*4
     &  TYPE          !Position (1-UP, 2-MIDDLE, 3-DOWN)
C
      INCLUDE 'formgen.inc'
C
C
      DATA CPOSDOWN  / 49 /
C
C
      NUL = CHAR(0)
C
      IF(TYPE .EQ. 1) THEN
C
C       Message to the upper portion of the screen
C       ------------------------------------------
C
        CPOSUP = 50
        WRITE(6,200) ESC,'2'
        POSUP = CHAR(CPOSUP)
C
      ELSE IF(TYPE .EQ. 2) THEN
C
C       Message to the middle portion of the screen
C       -------------------------------------------
C
        CPOSUP = 52
C
        WRITE(6,200) ESC,'4'
        WRITE(6,200) ESC,'5'
        WRITE(6,200) ESC,'6'
        WRITE(6,200) ESC,'7'
        WRITE(6,200) ESC,'8'
        WRITE(6,200) ESC,'9'
        WRITE(6,100) ESC,'0'
C
        POSUP = CHAR(CPOSUP)
C
      ELSE IF(TYPE .EQ. 3)THEN
C
C       Error message to the screen
C       ---------------------------
C
        CPOSDOWN = CPOSDOWN + 1
        IF(CPOSDOWN.EQ.52) THEN
          CPOSDOWN = 50
          WRITE(6,100) ESC,'2'
          WRITE(6,100) ESC,'3'
        ENDIF
        POSDN = CHAR(CPOSDOWN)
C
      ELSE IF(TYPE .EQ. 4)THEN
C
C       Clear the error message portion of the screen
C       ---------------------------------------------
C
          WRITE(6,100) ESC,'2'
          WRITE(6,100) ESC,'3'
      ENDIF
C
      RETURN
 100  FORMAT (' ',A2,'2',A1,';1H',80(' '))
 200  FORMAT (' ',A2,'1',A1,';1H',80(' '))
C
      END
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                             WAIT_KEY                                 <
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine waits for the user to press the <CR> key...
C
C
      SUBROUTINE WAIT_KEY
C
      IMPLICIT NONE
C
      INCLUDE 'formgen.inc'
C
      CHARACTER
     &  KEY*1
C
      INTEGER*2
     &  dum, stat
C
      WRITE(6,'(A1,A2,A5,A2,A2,A78,A2,A3,$)') ' ',ESC,'23;1H',ESC,'7m',
     &      '                    ...Press any key to EXIT utility...    
     &                   ',ESC,'27m'
      CALL ReadKey(dum,key,stat)
CC      READ(5,'(A1)') KEY
C
      RETURN
C
      END
