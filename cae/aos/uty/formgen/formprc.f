C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           FIL_PROC
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  This subroutine handles the processing of the input data one file at
C  time.
C
C
C
      SUBROUTINE FIL_PROC(*)
C
C
      IMPLICIT NONE
C
C
      CHARACTER
     & SLOT*2      ,!Slot number
     & DTIME*11    ,!Output download file creation time
     & DDATE*9     ,!Output download file creation date
     & BLNK_L*1024 ,!Line to clear data buffer
     & L_STRG*132   !Input line from data file
C
C
      INTEGER*2
     & L_TYPE       !Data line type
C
C
      INTEGER*4
     & I           ,!Loop index
     & J           ,!Loop index
     & FILE        ,!File identification number
     & IERR         !IO Error number
C
C
      LOGICAL*4
     & F_START      !Indicates the beginning of a input data file
C
C
      INCLUDE 'formgen.inc'
C
C
C     Initialization of some Common Blocks variables (For SEL Computer)
C     -----------------------------------------------------------------
C
      SLT_RECS = 0
      FIL_RECS = 0
      NUM_BYTE = 0
      TOT_BYTE = 0
      TOT_RECS = 0
      TOT_SLOT = 0
      MLT_SLOT = 0
C
C     DAC Variables initialization
C     ----------------------------
C
      DAC_SREC = 0
      DAC_DATA = .FALSE.
      DAC_PROC = .FALSE.
      DAC_INIT = .FALSE.
      DAC_PRST = .FALSE.
C
      TMS_DATA = .FALSE.
      FST_PASS = .TRUE.
C
      DO I = 1 , 400
        BLK_SIZE(I) = 0
        BLOCK_ID(I) = '  '
        DO J = 1 , 7
          BLCK_INF(I,J) = 0
        ENDDO
      ENDDO
C
C
C
      REC_NUMB = 1
      DATA_BUF = BLNK_L
C
C     Initialize the first record of the output download file
C     -------------------------------------------------------
C
      CALL FIL_WRT1(IERR)
      IF( IERR .NE. 0 ) RETURN 1
C
      REC_NUMB = 1
C
C     Initialize the first record of the temporary file
C     -------------------------------------------------
C
      CALL FIL_WRT2(IERR)
      IF( IERR .NE. 0 ) RETURN 1
C
C     Start processing the input data on file at time
C     -----------------------------------------------
C
      DO I = 1, MAXFILE
C
        PRV_FREC = 1
C
        ID_PRS   = .FALSE.
        DATA_PRS = .FALSE.
C
C
C       STANDARD & MULTI download data mode flags
C       -----------------------------------------
C
        STD_DATA = .FALSE.
        MLT_DATA = .FALSE.
        MLT_DNLD = .FALSE.
C
C       Display the name of the input data file in process
C       --------------------------------------------------
C
        CALL INF_REP1(I,1,IERR)
        IF( IERR .NE. 0 ) RETURN 1
C
C       Message for unavailability of a data file in STANDALONE mode
C       ------------------------------------------------------------
C
        IF( FILE_Q(I) ) THEN
          IF (I.NE.1 .AND. I.NE.6) THEN
            CALL INF_REP1(I,2,IERR)
            CALL Beep(1)
            CALL Wait_Time(2)
          ENDIF
          CALL INF_REP4(I,IERR)
          IF( IERR .NE. 0 ) RETURN 1
          GOTO 200
        ENDIF
C
 400    CONTINUE
C
        IF(DAC_PROC) THEN
C
C         processing the DAC data
C         -----------------------
C
          FILE = DAC_FNUM
C
        ELSE
C
C         processing an input data file
C         -----------------------------
C
          FILE = I
C
        ENDIF
C
C
C
C       Reserve eight words for the block header
C       ----------------------------------------
C
        REC_SIZA = 16
        REC_SIZB = 16
C
        F_START = .TRUE.
        F_COMPLT(FILE) = .FALSE.
C
C       Read a line from data file
C       --------------------------
C
        CALL I_F_READ(FILE, L_STRG, IERR)
        IF( IERR .EQ. -1 ) THEN
          CALL CUR_POS(3)
          IF(FILE .GT. 7) THEN
            WRITE(6,800) ESC,POSDN,DAC_FILE(1:DAC_FILL)
          ELSE
            WRITE(6,800) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
          ENDIF
          CALL Beep(1)
          CALL Wait_Time(5)
          RETURN 1
        ELSE IF( IERR .NE. 0 ) THEN
          RETURN 1
        ENDIF
C
        DO WHILE (.NOT. F_COMPLT(FILE))
C
C         Determine the type of the line
C         ------------------------------
C
          CALL LIN_CHCK(FILE, L_STRG, L_TYPE, IERR)
          IF( IERR .NE. 0 ) RETURN 1
C
C         DAC data routine to store DAC data
C         ----------------------------------
C
          IF(DAC_DATA) THEN
            DAC_DATA = .FALSE.
            DAC_PRST = .TRUE.
            CALL DAC_STOR(FILE,L_STRG,IERR)
            IF( IERR .EQ. -1 ) THEN
              GO TO 900
            ELSE IF( IERR .NE. 0 ) THEN
              RETURN 1
            ENDIF
            CALL LIN_CHCK(FILE, L_STRG, L_TYPE, IERR)
            IF(IERR .NE. 0) RETURN 1
          ENDIF
C
          IF(L_TYPE .EQ. 1 .OR. L_TYPE .EQ. 2) THEN
C
C           Process the comment & identifier lines
C           --------------------------------------
C
            DO WHILE( L_STRG(1:1) .NE. '&' )
              SLT_LINE = L_STRG
              CALL I_F_READ(FILE, L_STRG, IERR)
              IF( IERR .EQ. -1 ) THEN
                IF( DATA_PRS ) THEN
C
C                End of valid data file is reached
C                ---------------------------------
C
                 F_COMPLT(FILE) = .TRUE.
                 GO TO 100
C
                ELSE IF( ID_PRS ) THEN
C
C                End of invalid data file (Identifier without data) is reached
C                -------------------------------------------------------------
C
                 CALL CUR_POS(3)
                 IF(FILE .GT. 7) THEN
                   WRITE(6,700) ESC,POSDN,DAC_FILE(1:DAC_FILL),ID_STRG
                 ELSE
                   WRITE(6,700) ESC,POSDN,
     &                   INP_FILE(FILE)(1:INP_FILL(FILE)),ID_STRG
                 ENDIF
                 CALL Beep(1)
                 CALL Wait_Time(3)
                 RETURN 1
                ELSE
C
C                End of invalid data file (Without Identifier) is reached
C                --------------------------------------------------------
C
                 CALL CUR_POS(3)
                 WRITE(6,600) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
                 CALL Beep(1)
                 CALL Wait_Time(5)
                 RETURN 1
                ENDIF
              ELSE IF( IERR .NE. 0 ) THEN
                RETURN 1
              ENDIF
            ENDDO
C
C           DAC data routine to store DAC data
C           ----------------------------------
C
            IF(L_STRG(4:5) .EQ. FILE_ID(9) .AND..NOT. DAC_PROC) THEN
              DAC_PRST = .TRUE.
              CALL DAC_STOR(FILE,L_STRG,IERR)
              IF( IERR .EQ. -1 ) THEN
                GO TO 900
              ELSE IF( IERR .NE. 0 ) THEN
                RETURN 1
              ENDIF
            ENDIF
C
C           identifier line
C           ---------------
C
            CALL ID_LINE(FILE, L_STRG, SLOT, IERR)
            IF( IERR .NE. 0 ) RETURN 1
C
C           comment line
C           ------------
C
            CALL COM_LINE(FILE, SLOT, F_START, .FALSE., IERR)
            IF( IERR .NE. 0 ) RETURN 1
C
C           Read a new line from input data file
C           ------------------------------------
C
            CALL I_F_READ(FILE, L_STRG, IERR)
            IF( IERR .NE. 0 ) RETURN 1
C
          ELSE IF(L_TYPE .EQ. 3) THEN
C
C           Data line
C           ---------
C
            IF( .NOT. ID_PRS) THEN
              CALL CUR_POS(3)
              IF(FILE .GT. 7) THEN
               WRITE(6,600) ESC,POSDN,DAC_FILE(1:DAC_FILL)
              ELSE
               WRITE(6,600) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
              ENDIF
              CALL Beep(1)
              CALL Wait_Time(5)
              RETURN 1
            ENDIF
            CALL DAT_LINE(FILE, L_STRG, IERR)
            IF( IERR .NE. 0 .AND. IERR .NE. -1 ) RETURN 1
C
C
          ENDIF
C
 100      CONTINUE
C
        ENDDO
C
C
C       Process DAC data after TSD data has been processed
C       --------------------------------------------------
C
 900    CONTINUE
C
        IF( DAC_PRST ) THEN
          ID_PRS   = .FALSE.
          DATA_PRS = .FALSE.
          DAC_PRST = .FALSE.
          DAC_PROC = .TRUE.
          GO TO 400
        ENDIF

C
C       Write the block header to each data block
C       -----------------------------------------
C
        CALL R_HDRWRT(IERR)
        IF(IERR .NE. 0) RETURN 1
C
C       Display information report after each data file completion
C       ----------------------------------------------------------
C
        CALL INF_REP2(I,IERR)
        IF(IERR .NE. 0) RETURN 1
C
        TMS_DATA = .FALSE.
C
 200    CONTINUE
C
      ENDDO
C
C
C
C
C++++++++++++++++++++++++  Header Block Section   ++++++++++++++++++++++++
C
C
C     Clear the header block
C     ----------------------
C
      DATA_BUF = BLNK_L
C
C
C     Total number of records of the output downlocd file
C     ---------------------------------------------------
C
      TOT_RECS = REC_NUMB - 2       !Blocks of processed data only Excluding Header Block
C
C
C     Select the header block record in the output download file
C     ----------------------------------------------------------
C
      REC_NUMB = 1
C
C
C     Read the header block to be updated
C     -----------------------------------
C
      CALL T_F_READ(IERR)
      IF( IERR .NE. 0 ) RETURN 1
C
C
C     Select the header block record in the output download file
C     ----------------------------------------------------------
C
      REC_NUMB = 1
C
C
C     Date & Time of this run for the header block
C     --------------------------------------------
C
      CALL Cdate(DDATE,DTIME)
C
C
C     Append date, time, and output file name in the first record
C     -----------------------------------------------------------
C
      DATA_BUF(1:1)   = ' '
      DATA_BUF(2:10)  = DDATE
      DATA_BUF(11:11) = ' '
      DATA_BUF(12:19) = DTIME(1:8)
      DATA_BUF(20:20) = ' '
C
      DATA_BUF(21:21+OUT_FILL) = OUT_FILE
C
C
C     Write the updated header block
C     ------------------------------
C
      CALL FIL_WRT4(IERR)
      IF( IERR .NE. 0 ) RETURN 1
C
C     Report the information FORMGEN successful completion
C     ----------------------------------------------------
C
      CALL INF_REP3(IERR)
      IF(IERR .NE. 0) RETURN 1
C
      RETURN
C
 600  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - ',A,' DOES NOT CONTAIN A
     & DATA IDENTIFIER')
C
C
 700  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ',A,' DOES NOT CONTAIN DA
     &TA FOR ',A7)
C
C
 800  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - FILE ',A,' IS EMPTY')
C
C
      END
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           LIN_CHCK
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  This subroutine determines the type of a data line.
C
C   Type = 1    : Comment Line
C   Type = 2    : Identifier Line
C   Type = 3    : Data Line
C
C
C
      SUBROUTINE LIN_CHCK(FILE,L_STRG,L_TYPE,IERR)
C
C
      IMPLICIT NONE
C
C
      CHARACTER
     & L_STRG*132  !Input line string
C
C
      INTEGER*4
     & FILE       ,!Input data file identification number
     & IERR        !I/O Error number
C
C
      INTEGER*2
     & L_TYPE      !Type of a data line
C
C
      INCLUDE 'formgen.inc'
C
C
      IERR = 0
C
      IF(L_STRG(1:1) .EQ. '$') THEN
C
C        Input comment line
C        ------------------
C
         L_TYPE = 1
C
      ELSE IF(L_STRG(1:1) .EQ. '&') THEN
C
C        Input identifier line
C        ---------------------
C
         L_TYPE = 2
C
C        Check if this is a DAC identifier
C        ---------------------------------
C
         IF(L_STRG(4:5).EQ.FILE_ID(9).AND..NOT.DAC_PROC) DAC_DATA=.TRUE.
C
      ELSE IF(L_STRG(1:1) .EQ. ':' .OR. L_STRG(1:1) .EQ. 'K') THEN
C
C        Input data line
C        ---------------
C
         L_TYPE = 3
C
         DATA_PRS = .TRUE.
C
C        Check if this is TMS data
C        -------------------------
C
         IF(L_STRG(1:1) .EQ. 'K') TMS_DATA = .TRUE.
      ELSE
C
C        A message for invalid input line
C        --------------------------------
C
         CALL CUR_POS(3)
         WRITE(6,100) ESC,POSDN,INP_FILE(FILE)
         CALL Beep(1)
         CALL Wait_Time(5)
         IERR = 100
      ENDIF
C
      RETURN
C
 100  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - INVALID LINE IN FILE ',
     &          A40)
C
      END
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           COM_LINE
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine processes the comment data line written to the output
C download file.
C
C
C
      SUBROUTINE COM_LINE(FILE,S_NUMB,F_START,F_END,IERR)
C
C
      IMPLICIT NONE
C
C
      INTEGER*2
     & SLOT_DEC           !Slot number in Decimal
C
C
      INTEGER*4
     & I                 ,!DO Loop counter
     & FILE              ,!Iput data file identification number
     & IERR              ,!I/O Error number
     & SLT_POS           ,!Character position on Slot comment line
     & SLT_STRT           !Starting position of Slot number in the comment line
C
C
      CHARACTER*6
     & IFMT1   /'(Z2.2)'/,!Conversion Integer format
     & IFMT2   /'(I2.2)'/ !Conversion Integer format
      CHARACTER
     & S_NUMB*2          ,!Slot number
     & S_STRG*9          ,!Slot comment line string
     & S_LINE*14          !Slot comment line string
C
C
      LOGICAL*4
     & F_END             ,!Indicates an end of a data file
     & F_START            !Indicates a start of a data file
C
      LOGICAL*1
     & MLT_HDR            !Indicates a multi download start & end header
C
C
       INCLUDE 'formgen.inc'
C
C
       DATA S_STRG         /'$ SLOT # '/
C
C
C
C     Initialize error indicator
C     --------------------------
C
      IERR = 0
C
C     Data file end header comment line
C     -----------------------------------
C
      IF(F_END) THEN
        IF(MLT_HDR) THEN
C
C         MULTI download data end header
C         ------------------------------
C
          MLT_HDR = .FALSE.
          DATA_BUF(REC_SIZA+1:REC_SIZA+24) =
     &     HEAD_END(FILE)(1:TASK_LEN(FILE)+2)
     &     //' MULTI '//HEAD_END(FILE)(TASK_LEN(FILE)+10:24)
          REC_SIZA = REC_SIZA + 24
          REC_SIZB = REC_SIZB + 24
        ELSE
C
C         STANDARD download data end header
C         ---------------------------------
C
          DATA_BUF(REC_SIZA+1:REC_SIZA+24) = HEAD_END(FILE)
          REC_SIZA = REC_SIZA + 24
          REC_SIZB = REC_SIZB + 24
        ENDIF
C
        RETURN
C
C     Data file start header comment line
C     -----------------------------------
C
      ELSE IF(F_START) THEN
        F_START = .FALSE.
        IF(MLT_DNLD) THEN
C
C         MULTI download data start header
C         --------------------------------
C
          MLT_HDR = .TRUE.
          DATA_BUF(REC_SIZA+1:REC_SIZA+26) =
     &     HEAD_STR(FILE)(1:TASK_LEN(FILE)+2)
     &     //' MULTI '//HEAD_STR(FILE)(TASK_LEN(FILE)+10:26)
          REC_SIZA = REC_SIZA + 26
          REC_SIZB = REC_SIZB + 26
        ELSE
C
C         STANDARD download data start header
C         -----------------------------------
C
          DATA_BUF(REC_SIZA+1:REC_SIZA+26) = HEAD_STR(FILE)
          REC_SIZA = REC_SIZA + 26
          REC_SIZB = REC_SIZB + 26
        ENDIF
      ENDIF
C
C
C     Convert Slot Number from Character Hexadecimal to Decimal Numeric
C     -----------------------------------------------------------------
C
      READ(S_NUMB, IFMT1, IOSTAT=IERR, ERR=111) SLOT_DEC
C
      IF( SLOT_DEC .GT. 0 .AND. SLOT_DEC .LE. 27) THEN
        SLT_NUMB = SLOT_DEC
C
        IF( MLT_DNLD ) THEN
C
C         Extraction of slots #'s for MULTI download data
C         -----------------------------------------------
C
          SLT_POS = 132
          DO WHILE(SLT_LINE(SLT_POS:SLT_POS).NE.'#'.AND.SLT_POS.NE.0)
              SLT_POS = SLT_POS - 1
          ENDDO
          IF(SLT_POS .GT. 0) THEN
            SLT_STRT = SLT_POS + 2
            DATA_BUF(REC_SIZA+1:REC_SIZA+8) = S_STRG(1:8)
            REC_SIZA = REC_SIZA + 8
            REC_SIZB = REC_SIZB + 8
            DO I = 1, SLT_NUMB
              READ(SLT_LINE(SLT_STRT:SLT_STRT+1),IFMT2,IOSTAT=IERR,
     &             ERR=222) SLOT_DEC
              IF(SLOT_DEC .GT. 0 .AND. SLOT_DEC .LE. 27) THEN
                FIL_SLOT(TOT_SLOT+I) = SLOT_DEC
                WRITE(S_NUMB, 100) SLOT_DEC
                DATA_BUF(REC_SIZA+1:REC_SIZA+3) = ' '//S_NUMB
                REC_SIZA = REC_SIZA + 3
                REC_SIZB = REC_SIZB + 3
                SLT_STRT = SLT_STRT + 3
              ELSE
C
C               Slot number is out of range
C               ---------------------------
C
                CALL CUR_POS(3)
                WRITE(6,400) ESC,POSDN,SLOT_DEC
                CALL Beep(1)
                CALL Wait_Time(5)
                IERR = 500
                RETURN
              ENDIF
            ENDDO
            TOT_SLOT = TOT_SLOT + SLT_NUMB
          ELSE
            CALL CUR_POS(3)
            WRITE(6,200) ESC,POSDN
            CALL Beep(1)
            CALL CUR_POS(3)
            WRITE(6,300) ESC,POSDN,SLT_LINE
            CALL Wait_Time(5)
            IERR = 300
            RETURN
          ENDIF
        ELSE
C
          TOT_SLOT = TOT_SLOT + 1
          FIL_SLOT(TOT_SLOT) = SLOT_DEC
C
C         Convert Slot Number from Decimal Numeric to Decimal Character
C         -------------------------------------------------------------
C
          WRITE(S_NUMB, 100) SLOT_DEC
C
          S_LINE = S_STRG//S_NUMB
C
C         Slot number comment line
C         ------------------------
C
          DATA_BUF(REC_SIZA+1:REC_SIZA+11) = S_LINE
          REC_SIZA = REC_SIZA + 11
          REC_SIZB = REC_SIZB + 11
        ENDIF
      ELSE
C
C       Slot number is out of range
C       ---------------------------
C
        CALL CUR_POS(3)
        WRITE(6,400) ESC,POSDN,SLOT_DEC
        CALL Beep(1)
        CALL Wait_Time(5)
        IERR = 200
        RETURN
      ENDIF
C
C
C
C     Data identification line
C     ------------------------
C
      DATA_BUF(REC_SIZA+1:REC_SIZA+7) = ID_STRG
      REC_SIZA = REC_SIZA + 7
      REC_SIZB = REC_SIZB + 7
C
      RETURN
C
C     Invalid slot number
C     -------------------
C
 111  CONTINUE
      CALL CUR_POS(3)
      WRITE(6,500) ESC,POSDN,S_NUMB
      CALL Beep(1)
      CALL Wait_Time(5)
      RETURN
C
C     Invalid slot number in a MULTI code
C     -----------------------------------
C
 222  CONTINUE
      CALL CUR_POS(3)
      WRITE(6,500) ESC,POSDN,SLT_LINE(SLT_STRT:SLT_STRT+1)
      CALL Beep(1)
      CALL Wait_Time(5)
      RETURN
C
 100  FORMAT(I2.2)
C
 200  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - FILE CONTAINS INVALID MUL
     &TI SLOT COMMENT LINE :')
C
 300  FORMAT(' ',A2,'2',A1,';H',5X,A74)
C
 400  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - FILE CONTAINS SLOT NUMBER
     & ',I2.2,' WHICH IS OUT OF CHASSIS RANGE')
C
 500  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - FILE CONTAINS INVALID SLO
     &T NUMBER : ',A)
C
C
      END
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           ID_LINE
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine processes the identifier line of a data file.
C
C
C
      SUBROUTINE ID_LINE(FILE,L_STRG,SLOT,IERR)
C
C
      IMPLICIT NONE
C
C
      CHARACTER*6
     & IFMT   /'(Z2.2)'/  !Conversion Integer format
C
      CHARACTER
     & SLOT*2            ,!Slot number
     & L_STRG*132         !Input identifier line
C
      INTEGER*4
     & FILE           ,!Input data file identification number
     & IERR            !I/O Error number
C
C
      INCLUDE 'formgen.inc'
C
C
      IERR = 0
C
      IF( ID_PRS )THEN
        CALL CUR_POS(3)
        WRITE(6,500) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
        CALL Beep(1)
        CALL Wait_Time(5)
        IERR = 500
        RETURN
      ENDIF
C
C
      ID_STRG = L_STRG(1:7)
C
      IF(FST_PASS) THEN
        FST_PASS = .FALSE.
C
C
C       Get DMC number and convert it into decimal
C       ------------------------------------------
C
        READ(DMC_Num(1:2),IFMT,IOSTAT=IERR,ERR=111) DMC_NUMB
C
C
C       Get sound DMC page number and convert it into decimal
C       -----------------------------------------------------
C
        READ(Page_Num(1:2),IFMT,IOSTAT=IERR,ERR=222) DMC_PAGE
C
      ENDIF
C
C     Check if DMC number in identifier line is valid
C     -----------------------------------------------
C
      IF( L_STRG(2:3) .NE. DMC_Num(1:2) ) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,L_STRG(2:3)
        CALL Beep(1)
        CALL Wait_Time(5)
        IERR = 100
        RETURN
      ENDIF
C
C
C     Get the data identifier and check if it is valid
C     ------------------------------------------------
C
      IF( L_STRG(4:5) .EQ. FILE_ID(FILE) ) THEN
C
C        STANDARD download data identifier
C        ---------------------------------
C
         STD_DATA = .TRUE.
         DATA_ID = L_STRG(4:5)
      ELSE IF( L_STRG(4:5) .EQ. 'M'//FILE_ID(FILE)(2:2) ) THEN
C
C        MULTI download data identifier
C        ------------------------------
C
         MLT_DATA = .TRUE.
         MLT_DNLD = .TRUE.
         DATA_ID = L_STRG(4:5)
      ELSE
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,L_STRG(4:5)
        CALL Beep(1)
        CALL Wait_Time(5)
        IERR = 200
        RETURN
      ENDIF
C
C
C     Get the slot number
C     -------------------
C
      SLOT = L_STRG(6:7)
C
      ID_PRS = .TRUE.
C
      RETURN
C
 111  CONTINUE
      CALL CUR_POS(3)
      WRITE(6,300) ESC,POSDN,DMC_Num(1:2)
      CALL Beep(1)
      CALL Wait_Time(5)
      RETURN
C
 222  CONTINUE
      CALL CUR_POS(3)
      WRITE(6,400) ESC,POSDN,Page_Num(1:2)
      CALL Beep(1)
      CALL Wait_Time(5)
      RETURN
C
 100  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ',A2,
     &' IS INVALID DMC NUMBER')
C
C
 200  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ',A2,
     &' IS INVALID FILE IDENTIFICATION')
C
 300  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN -  FILE AOSXLINK.INF CONTAI
     &NS INVALID DMC PARAMETER ',A)
C
C
 400  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN -  FILE AOSXLINK.INF CONTAI
     &NS INVALID DMC PAGE PARAMETER ',A)
C
C
 500  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ',A,' CONTAINS IDENTIFIER
     & WITHOUT DATA')
C
C
      END
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           DAT_LINE
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine processes the data line from the input data file.
C
C
C
      SUBROUTINE DAT_LINE(FILE,L_STRG,IERR)
C
C
      IMPLICIT NONE
C
C
      LOGICAL*1
     & END_DATA     ,!End of data of a file is reached
     & REC_FULL      !A record of data is full
C
C
      INTEGER*2
     & ASC_LEN      ,!Input line ASCII length
     & BIN_LEN      ,!Input line BINARY length
     & L_TYPE        !Data line type
C
C
      INTEGER*4
     & I            ,!DO Loop counter
     & J            ,!DO Loop counter
     & FILE         ,!Input data file identification number
     & IERR          !Error counter
C
C
      CHARACTER
     & L_STRG*132     ,!Input line string
     & T_LINE*132      !Temporary storage for Input line string
C
C
      INCLUDE 'formgen.inc'
C
C
C
      REC_FULL = .FALSE.
      END_DATA = .FALSE.
      DATA_PRS = .FALSE.
C
C
      DO WHILE ( .NOT. END_DATA )
       ASC_LEN = 132
C
C      Extract only the data from the input line
C      -----------------------------------------
C
       DO WHILE ((L_STRG(ASC_LEN:ASC_LEN).EQ.' ').AND.ASC_LEN.GT.0)
         ASC_LEN = ASC_LEN - 1
       ENDDO
C
       IF(ASC_LEN .EQ. 0) THEN
         CALL CUR_POS(3)
         WRITE(6,200) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
         CALL Beep(1)
         CALL Wait_Time(2)
       ELSE IF( L_STRG(1:1) .NE. '$' ) THEN
C
C
         I = 1
         BIN_LEN = 0
         DO WHILE (I .LE. ASC_LEN)
           IF( TMS_DATA ) THEN
             IF(L_STRG(I:I) .EQ. 'F' .OR. L_STRG(I:I) .EQ. ':' ) THEN
               I = I + 1
               BIN_LEN = BIN_LEN + 1
             ELSE IF(L_STRG(I:I) .EQ. 'K' ) THEN
               I = I + 13
               BIN_LEN = BIN_LEN + 13
             ELSE
               I = I + 5
               BIN_LEN = BIN_LEN + 3
             ENDIF
           ELSE
             BIN_LEN = BIN_LEN + 1
             IF( L_STRG(I:I) .EQ. ':' ) THEN
               I = I + 1
             ELSE
               I = I + 2
             ENDIF
           ENDIF
         ENDDO
C
C
C        End of data is reached
C        ----------------------
C
         IF((L_STRG(1:11) .EQ. ':00000001FF') .OR.
     &     (L_STRG(1:1)   .EQ. ':' .AND. TMS_DATA) ) THEN
C
C
           IF( TMS_DATA ) ASC_LEN = 1
C
C
C          Read the next line from input data file
C          ---------------------------------------
C
           CALL I_F_READ(FILE, T_LINE, IERR)
           SLT_LINE = T_LINE
           IF( IERR .EQ. -1 ) THEN
C
C            End of file is reached
C            ----------------------
C
             IF((REC_SIZB+BIN_LEN+24) .GT. REC_MAXS) THEN
               REC_FULL = .TRUE.
               GO TO 100
             ELSE
               END_DATA = .TRUE.
               F_COMPLT(FILE) = .TRUE.
C
C              Store data in data buffer
C              -------------------------
C
               DATA_BUF(REC_SIZA+1:REC_SIZA+ASC_LEN) = L_STRG(1:ASC_LEN)
               REC_SIZA = REC_SIZA + ASC_LEN
               REC_SIZB = REC_SIZB + BIN_LEN
               CALL COM_LINE(FILE,' ',.FALSE.,.TRUE.,IERR)
               GO TO 100
             ENDIF
           ELSE IF( IERR .NE. 0 ) THEN
             RETURN
           ELSE
C
C            Skip all comment lines
C            ----------------------
C
             DO WHILE( T_LINE(1:1) .NE. '&' )
               SLT_LINE = T_LINE
               CALL I_F_READ(FILE, T_LINE, IERR)
               IF( IERR .EQ. -1 ) THEN
C
C                 End of file is reached
C                 ----------------------
C
                 IF((REC_SIZB+BIN_LEN+24) .GT. REC_MAXS) THEN
                   REC_FULL = .TRUE.
                   GO TO 100
                 ELSE
                   END_DATA = .TRUE.
                   F_COMPLT(FILE) = .TRUE.
C
C                  Store data in data buffer
C                  -------------------------
C
                   DATA_BUF(REC_SIZA+1:REC_SIZA+ASC_LEN) =
     &                    L_STRG(1:ASC_LEN)
                   REC_SIZA = REC_SIZA + ASC_LEN
                   REC_SIZB = REC_SIZB + BIN_LEN
                   CALL COM_LINE(FILE,' ',.FALSE.,.TRUE.,IERR)
                   GO TO 100
                 ENDIF
               ELSE IF( IERR .NE. 0 ) THEN
                 RETURN
               ENDIF
             ENDDO
C
C
C            DAC data routine to store DAC data
C            ----------------------------------
C
             IF(T_LINE(4:5) .EQ. FILE_ID(9) .AND..NOT. DAC_PROC) THEN
               DAC_PRST = .TRUE.
               CALL DAC_STOR(FILE,T_LINE,IERR)
C
               IF(IERR .EQ. -1) THEN
C
C                End of file is reached
C                ----------------------
C
                 IF((REC_SIZB+BIN_LEN+24) .GT. REC_MAXS) THEN
                   REC_FULL = .TRUE.
                   GO TO 100
                 ELSE
                   END_DATA = .TRUE.
                   F_COMPLT(FILE) = .TRUE.
C
C                  Store data in data buffer
C                  -------------------------
C
                   DATA_BUF(REC_SIZA+1:REC_SIZA+ASC_LEN) =
     &                  L_STRG(1:ASC_LEN)
                   REC_SIZA = REC_SIZA + ASC_LEN
                   REC_SIZB = REC_SIZB + BIN_LEN
                   CALL COM_LINE(FILE,' ',.FALSE.,.TRUE.,IERR)
                   GO TO 100
                 ENDIF
               ELSE IF( IERR .NE. 0 ) THEN
                 RETURN
               ENDIF
C
             ENDIF
C
C
             END_DATA = .TRUE.
C
C            Store data in data buffer
C            -------------------------
C
             DATA_BUF(REC_SIZA+1:REC_SIZA+ASC_LEN) = L_STRG(1:ASC_LEN)
             REC_SIZA = REC_SIZA + ASC_LEN
             REC_SIZB = REC_SIZB + BIN_LEN
             L_STRG = T_LINE
           ENDIF
         ENDIF
C
 100     CONTINUE
C
C
C
         IF((REC_SIZB+BIN_LEN).GT.REC_MAXS.OR.END_DATA.OR.REC_FULL) THEN
C
C          Increment input data file record number
C          ---------------------------------------
C
           FIL_RECS = FIL_RECS + 1
C
C          Increment input data file slot record number
C          --------------------------------------------
C
           SLT_RECS = SLT_RECS + 1
C
C          Check if processing DAC data to mark its starting record number
C          ---------------------------------------------------------------
C
           IF(DAC_PROC .AND. DAC_SREC .EQ. 0) DAC_INIT = .TRUE.
C
C
C          Store the block header information
C          ----------------------------------
C
           CALL R_HDRSTR
C
C          Write the data into the temporary file
C          --------------------------------------
C
           CALL FIL_WRT2(IERR)
           IF(IERR .NE. 0) RETURN
           BLK_SIZE(FIL_RECS) = REC_SIZA
           REC_SIZA = 16
           REC_SIZB = 16
         ENDIF
C
         IF(.NOT. (END_DATA .OR. REC_FULL)) THEN
C
C          Store data in data buffer and read a new line
C          ---------------------------------------------
C
           DATA_BUF(REC_SIZA+1:REC_SIZA+ASC_LEN) = L_STRG(1:ASC_LEN)
           REC_SIZA = REC_SIZA + ASC_LEN
           REC_SIZB = REC_SIZB + BIN_LEN
           CALL I_F_READ(FILE,L_STRG,IERR)
C
           IF( IERR .EQ. -1 ) THEN
             CALL CUR_POS(3)
             IF(FILE .GT. 7) THEN
               WRITE(6,300) ESC,POSDN,DAC_FILE(1:DAC_FILL)
             ELSE
               WRITE(6,300) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
             ENDIF
             CALL Beep(1)
             CALL Wait_Time(5)
             IERR = 100
             RETURN
           ELSE IF(IERR .NE. 0) THEN
             RETURN
           ENDIF
C
         ENDIF
C
       ELSE
         CALL CUR_POS(3)
         WRITE(6,300) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE))
         CALL Beep(1)
         CALL Wait_Time(5)
         IERR = 100
         RETURN
       ENDIF
      ENDDO
C
C
C     Total slot record number
C     ------------------------
C
      DO J = PRV_FREC, FIL_RECS
        BLCK_INF(J,3) = SLT_RECS
      ENDDO
      PRV_FREC = FIL_RECS + 1
      SLT_RECS = 0
C
      MLT_DNLD = .FALSE.

C
      ID_PRS   = .FALSE.
      DATA_PRS = .TRUE.
C
C
      RETURN
C
 200  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ',A,' CONTAINS EMPTY DATA
     & LINE')
C
 300  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - END_OF_DATA RECORD IS EXP
     &ECTED IN ',A)
C
C
      END
C
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           REC_HDR
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  This subroutine stores the block header information (8 Words) during
C  data process and then writes these words to the block header when
C  the end of the input data file is reached.
C
C
C
      SUBROUTINE REC_HDR
C
C
      IMPLICIT NONE
C
C
      INTEGER*2
     & J               ,!DO loop counter
     & SIXTEEN /16/     !An integer*2 constant
C
C
      INTEGER*4
     & I               ,!DO loop counter
     & IERR             !IO Error number
C
C
      INCLUDE 'formgen.inc'
C
C
C
C=======================================================================
      ENTRY R_HDRSTR
C=======================================================================
C
C
C     This routine stores the block header information (Block header
C     8 Words) associated with each data block in order to update each
C     block when end of the file is reached.
C
C
C     Check if processing DAC data to mark its starting record number
C     ---------------------------------------------------------------
C
      IF(DAC_INIT) THEN
        DAC_INIT = .FALSE.
        DAC_SREC = FIL_RECS
      ENDIF
C
C
C     Store the block header information
C     ----------------------------------
C
      BLCK_INF(FIL_RECS,1) =(REC_SIZB-16) !Word # 1 Block size (Bytes of data)
      BLOCK_ID(FIL_RECS)   = DATA_ID      !Word # 2 data identification
      BLCK_INF(FIL_RECS,2) = SLT_RECS     !Word # 3 Data file record number
C     BLCK_INF(FIL_RECS,3)                !Word # 4 total # of records per slot
C                                         !computed in DAT_LINE
      BLCK_INF(FIL_RECS,4) = REC_NUMB     !Output file record number storage
C
C     Bit manipulation of Words 6 & 7 for MULTI download data
C     -------------------------------------------------------
C
      IF( MLT_DNLD ) THEN
        MLT_SLOT = TOT_SLOT - SLT_NUMB
        BLCK_INF(FIL_RECS,5) = 0          !Reset Word # 6
        BLCK_INF(FIL_RECS,6) = 0          !Reset Word # 7
        DO I = 1, SLT_NUMB
         IF(FIL_SLOT(MLT_SLOT+I) .LE. 16) THEN
          J = FIL_SLOT(MLT_SLOT+I)
          BLCK_INF(FIL_RECS,5)=IBSET(BLCK_INF(FIL_RECS,5),SIXTEEN-J) !Word # 6
         ELSE
          J = FIL_SLOT(MLT_SLOT+I) - 16
          BLCK_INF(FIL_RECS,6)=IBSET(BLCK_INF(FIL_RECS,6),SIXTEEN-J) !Word # 7
         ENDIF
        ENDDO
        BLCK_INF(FIL_RECS,6) = IOR(BLCK_INF(FIL_RECS,6), DMC_PAGE) !Word # 7
      ELSE
        BLCK_INF(FIL_RECS,5) = SLT_NUMB     !Word # 6 Slot number
        BLCK_INF(FIL_RECS,6) = DMC_PAGE     !Word # 7 DMC page number
      ENDIF
C
      BLCK_INF(FIL_RECS,7) = 0              !Word # 8 - Spare Word
C
C     Number of bytes for each data block
C     ----------------------------------/
C
      NUM_BYTE = NUM_BYTE + REC_SIZB
C
C     Total number of bytes of the output download file
C     -------------------------------------------------
C
      TOT_BYTE = TOT_BYTE + REC_SIZB
C
      RETURN
C
C
C
C
C
C=======================================================================
      ENTRY R_HDRWRT(IERR)
C=======================================================================
C
C
C     This routine restores the block header information
C     associated with eack data block & writes it to the
C     output download file.
C
C
      DO I = 1 , FIL_RECS
C
C       Restore the record number
C       -------------------------
C
        REC_NUMB = BLCK_INF(I,4)
C
C       Read the record from the temporary file
C       ---------------------------------------
C
        CALL T_F_READ(IERR)
        IF(IERR .NE. 0 .AND. IERR .NE. -1) RETURN
C
C       Restore the same record number just read (For VAX)
C       --------------------------------------------------
C
        REC_NUMB = BLCK_INF(I,4)
C
C
C       Check if processing DAC data
C       ----------------------------
C
        IF(DAC_PROC) THEN
          IF(I .LT. DAC_SREC) THEN
C
C           TSD data identification
C           -----------------------
C
            DATA_ID = FILE_ID(5)
C
          ELSE
C
C           DAC data identification
C           -----------------------
C
            DATA_ID = FILE_ID(9)
C
          ENDIF
        ENDIF
C
C
C       Write the updated record to the output download file
C       ----------------------------------------------------
C
        CALL FIL_WRT3(I, IERR)
        IF(IERR .NE. 0 ) RETURN
C
      ENDDO
C
C
C     Finished processing DAC data
C     ----------------------------
C
      DAC_PROC = .FALSE.
C
C
      RETURN
C
      END
C
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                           DAC_STOR
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C   This routine stores the DAC data during processing TSD data. This
C data is stored in a temporary file 'Shipname'SNDD.TMP which is
C deleted after the DAC data is processed.
C
C
      SUBROUTINE DAC_STOR(FILE,L_STRG,IERR)
C
C
      IMPLICIT NONE
C
C
      CHARACTER
     & L_STRG*132       !Input data line
C
C
      INTEGER*4
     & FILE            ,!File identification number
     & IERR             !IO Error number
C
C
      INCLUDE 'formgen.inc'
C
C
C     Store all DAC data in DAC data temporary file
C     ---------------------------------------------
C
      DO WHILE ( L_STRG(1:11) .NE. ':00000001FF')
        CALL FIL_WRT5(L_STRG,IERR)
        IF( IERR .NE. 0 ) RETURN
        CALL I_F_READ(FILE,L_STRG,IERR)
        IF( IERR .EQ. -1 ) THEN
          CALL CUR_POS(3)
          WRITE(6,200) ESC,POSDN
          CALL Beep(1)
          CALL Wait_Time(5)
          IERR = 100
          RETURN
        ELSE IF( IERR .NE. 0 ) THEN
          RETURN
        ENDIF
      ENDDO
C
C
C     Store the end_of_data line of DAC data
C     --------------------------------------
C
      CALL FIL_WRT5(L_STRG,IERR)
      IF( IERR .NE. 0 ) RETURN
C
C     Read the next data line from TSD data file
C     ------------------------------------------
C
      CALL I_F_READ(FILE,L_STRG,IERR)
      IF( IERR .EQ. -1 ) THEN
        GO TO 100
      ELSE IF( IERR .NE. 0 ) THEN
        RETURN
      ENDIF
C
C
C     Skip all comment lines
C     ----------------------
C
      DO WHILE (L_STRG(1:1) .NE. '&')
        CALL I_F_READ(FILE, L_STRG, IERR)
        IF( IERR .EQ. -1 ) THEN
          GO TO 100
        ELSE IF( IERR .NE. 0 ) THEN
          RETURN
        ENDIF
      ENDDO
C
C
 100  CONTINUE
C
      REWIND(UNIT=19)
C
      RETURN
C
C
 200  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - END_OF_DATA RECORD IS EXP
     &ECTED FOR DAC TASK (HD)')
C
C
      END
C
