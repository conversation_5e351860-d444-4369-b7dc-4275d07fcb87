C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                              FIL_OPEN
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C       This subroutine opens of the different units handled by FORMGEN
C  utility. It opens the following files :
C
C  Input Files :
C
C                     AOSXLINK.INF         Input parameters & flags
C
C                  Files that are standard for any configuration :
C
C                  1. 'Shipname'SN/RF CP.INT   SPC
C                  2. 'Shipname'SN/RF CI.INT   INTXILINX
C                  3. 'Shipname'SN/RF CX.INT   XILINX
C                  4. 'Shipname'SN/RF CT.INT   TMS
C                  5. 'Shipname'SN/RF CS.INT   TSD
C                  6. 'Shipname'SN/RF CF.INT   FIRGEN
C
C                  Files that are configuration dependant :
C
C                  7. 'Shipname'SN/RFcH.INT   HARMONY
C                  8. 'Shipname'SN/RFcW.INT   WAVEGEN
C
C  Output Files :
C
C                  1. 'Shipname'SN/RFDcL.DLD  Sound download formatted file
C                  2. 'Shipname'SN/RFDc.INF   Sound download information file
C
C                  Where C identifies one configuration file
C                  Where c identifies a specific configuration file
C
C------------------------------------------------------------------------------
C
C
C
      SUBROUTINE FIL_OPEN(*)
C
C
      IMPLICIT NONE
C
C
      INTEGER*4
     & revstat       ,!Status of revision handling routine
     & I             ,!Input data file identification number
     & IERR           !IO Error counter
C
      CHARACTER
     & FILE_ID2(9)*2,
     & FILENAME*80,
     & IN_FILE(8)*80
C
      DATA FILE_ID2 /'hp','ti','hx','tt','hs','hf','hh','hw','hd'/
C
      INCLUDE 'formgen.inc'
C
C
C     A message to inform the user of the opening process
C     ---------------------------------------------------
C
      CALL CUR_POS(1)
      WRITE(6,600) ESC,POSUP,ESC,ESC
C
C
C     Units 11 - 18 are assigned to the input data files
C     --------------------------------------------------
C
C
      DO I = 1 , MAXFILE
C
C       Input data file existence status for STANDALONE
C       -----------------------------------------------
C
        FILE_Q(I) = .FALSE.
C
C       Define the input data files
C       ---------------------------
C
        IF( I .LT. 7 ) THEN      !Standard Input Files
          IN_FILE(I) = INT_DIR(1:L_INT_DIR)//Config_S(9)
     &                  (1:Config_L(9))//Filetters(1:2)//'c'//
     &                  FILE_ID2(I)(2:2)//'.int'
          INP_FILL(I) = L_INT_DIR + Config_L(9) + 11
C
        ELSE                   !Configuration Dependant Files
          IN_FILE(I) = INT_DIR(1:L_INT_DIR)//Config_S(9)
     &                  (1:Config_L(9))//Filetters(1:3)
     &                  //FILE_ID2(I)(2:2)//'.int'
          INP_FILL(I) = L_INT_DIR + Config_L(9) + 11
C
        ENDIF
C
C       Open the input data files
C       -------------------------
C
        CALL rev_curr(IN_FILE(I),INP_FILE(I),' ',.FALSE.,1,revstat)
        OPEN(UNIT=I+10,FILE=INP_FILE(I),STATUS='OLD',IOSTAT=IERR)
C
        IF((revstat .EQ. 5) .AND. .NOT. Com(1)) THEN
C
C         Send a special message when data file does't exist in STANDALONE mode
C         ---------------------------------------------------------------------
C
          FILE_Q(I) = .TRUE.
          IF (I.NE.1 .AND. I.NE.6) THEN
            CALL CUR_POS(3)
            WRITE(6,300) ESC,POSDN,INP_FILE(I)(1:INP_FILL(I))
C            CALL CUR_POS(3)
            CALL Beep(1)
C            WRITE(6,400) ESC,POSDN
            CALL Wait_Time(2)
          ENDIF
C
        ELSE IF(revstat.EQ.5 .AND. (I.EQ.1 .OR. I.EQ.6)) THEN
          FILE_Q(I) = .TRUE.
        ELSE IF( IERR.NE.0 ) THEN
          CALL CUR_POS(3)
          WRITE(6,200) ESC,POSDN,INP_FILE(I)(1:INP_FILL(I)),IERR
          CALL Beep(1)
          CALL Wait_Time(5)
          RETURN 1
        ENDIF
C
      ENDDO
C
C
C
C     Define the DAC temporary storage file
C     -------------------------------------
C
      DAC_FILE = INT_DIR(1:L_INT_DIR)//Config_S(9)(1:Config_L(9))
     &           //Filetters(1:2)//'c'//FILE_ID2(9)(2:2)//'.tmp'
      DAC_FILL = L_INT_DIR + Config_L(9) + 8
C
C
C     Open the DAC temporary storage file
C     -----------------------------------
C
      OPEN(UNIT=19,FILE=DAC_FILE,STATUS='NEW',FORM='FORMATTED',
     &     ACCESS='SEQUENTIAL',IOSTAT=IERR)
C
C
      IF( IERR .NE. 0 ) THEN
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,DAC_FILE(1:DAC_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
        RETURN 1
      ENDIF
C
C
C
C     Define the output download file
C     -------------------------------
C
      FILENAME = Config_S(3)(1:Config_L(3))//Config_S(9)
     &           (1:Config_L(9))//Filetters(1:3)//'l.dld'
      CALL rev_next(FILENAME,OUT_FILE,' ',.FALSE.,1,revstat)
      OUT_FILL = Config_L(3) + 1 + Config_L(9) + 11
C
C
C     Open Output download file
C     -------------------------
C
      OPEN(UNIT=25,FILE=OUT_FILE,STATUS='NEW',FORM='UNFORMATTED',
     &     ACCESS='DIRECT',IOSTAT=IERR,RECL=512)
C
      IF( IERR .NE. 0 ) THEN
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,OUT_FILE(1:OUT_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
        RETURN 1
      ENDIF
C
C
C
C     Define the output information file
C     ----------------------------------
C
C
      FILENAME = INT_DIR(1:L_INT_DIR)//Config_S(9)(1:Config_L(9))
     &           //Filetters(1:3)//'.inf'
      CALL rev_next(FILENAME,INF_FILE,' ',.FALSE.,1,revstat)
      INF_FILL = L_INT_DIR + Config_L(9) + 10
C
C
C     Open Output Information File
C     ----------------------------
C
      OPEN(UNIT=30,FILE=INF_FILE,STATUS='NEW',FORM='FORMATTED',
     &     ACCESS='SEQUENTIAL',IOSTAT=IERR)
C
      IF( IERR .NE. 0 ) THEN
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,INF_FILE(1:INF_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
        RETURN 1
      ENDIF
C
C
C
C     Define the temporary storage file
C     ---------------------------------
C
      TMP_FILE = INT_DIR(1:L_INT_DIR)//Config_S(9)(1:Config_L(9))
     &           //Filetters(1:3)//'.tmp'
      TMP_FILL = L_INT_DIR + Config_L(9) + 7
C
C
C     Open the temporary storage file
C     -------------------------------
C
      OPEN(UNIT=35,FILE=TMP_FILE,STATUS='NEW',FORM='UNFORMATTED',
     &     ACCESS='DIRECT',IOSTAT=IERR,RECL=1024)
C
C
      IF( IERR .NE. 0 ) THEN
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,TMP_FILE(1:TMP_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
        RETURN 1
      ENDIF
C
      CALL CUR_POS(1)
C
      RETURN
C
C
 200  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - ERROR DURING OPENING : ',
     &A,'; IOSTAT = ',I3)
C
 300  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - ',A,' DOESN''T EXIST, PRO
     &CEED IN STANDALONE')
C
 400  FORMAT(' ',A2,'2',A1,';H',25X,'PROCEEDS IN STANDALONE MODE')
C
 600  FORMAT(' ',A2,'1',A1,';H',20X,A2,'1m','FORMGEN IS OPENING INPUT DA
     &TA FILES' ,A2,'0m')
C
      END
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                               FIL_READ
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C  This subroutine performs the I/O reading of the input
C  data files and the temporary file.
C
C  Entry Points : I_F_READ
C                 T_F_READ
C
C
C
C
      SUBROUTINE FIL_READ
C
C
      IMPLICIT NONE
C
C
      CHARACTER
     & L_STRG*132   !Input line string
C
C
      INTEGER*4
     & IERR        ,!IO Error counter
     & FILE        ,!Input data file identification number
     & F_UNIT       !Input data file associated unit number
C
C
      INCLUDE 'formgen.inc'
C
C
C=======================================================================
      ENTRY I_F_READ(FILE,L_STRG,IERR)
C=======================================================================
C
C  This routine reads a data string from the input data file.
C
C
      F_UNIT = FILE + 10
C
      READ(F_UNIT, 100, END=10, IOSTAT=IERR) L_STRG
C
      IF( IERR .NE. 0 .AND. IERR .NE. -1 ) THEN
        CALL CUR_POS(3)
        IF(FILE .GT. 6) THEN          !DAC Temporary File
          WRITE(6,200) ESC,POSDN,DAC_FILE(1:DAC_FILL),IERR
        ELSE                          !Input Data Files
          WRITE(6,200) ESC,POSDN,INP_FILE(FILE)(1:INP_FILL(FILE)),IERR
        ENDIF
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
 10   IERR = -1
      RETURN
C
C
C
C=======================================================================
      ENTRY T_F_READ(IERR)
C=======================================================================
C
C This routine reads a record of processed data from the temporary
C storage download file.
C
C
      READ(35,REC=REC_NUMB,IOSTAT=IERR) DATA_BUF(1:1024)
C
      IF( IERR .NE. 0 .AND. IERR .NE. -1 ) THEN
        CALL CUR_POS(3)
        WRITE(6,200) ESC,POSDN,TMP_FILE(1:TMP_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
 100  FORMAT(A)
C
C
 200  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - ERROR DURING READING ',
     &A,'; IOSTAT = ',I3)
C
      END
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                               FIL_WRIT
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  This subroutine performs the I/O writing to the temporary file and
C  the output download file.
C
C  Entry Points : FIL_WRT1
C                 FIL_WRT2
C                 FIL_WRT3
C                 FIL_WRT4
C                 FIL_WRT5
C
C
      SUBROUTINE FIL_WRIT
C
C
      IMPLICIT NONE
C
C
      INTEGER*1        !BYTE type = 8 bits...
     & SHFT_AR1       ,!Binary conversion shift arguement
     & DATA_INT(512)   !Binary data buffer
C
C
      INTEGER*2
     & TMP1,TMP2,TMP3 ,!
     & SHFT_AR2       ,!Binary conversion shift arguement
     & DLD_ADDR        !Download address
C
      PARAMETER ( DLD_ADDR = '0700'X )
C
C
      INTEGER*4
     & I              ,!DO Loop counter
     & J              ,!DO Loop counter
     & K              ,!DO Loop counter
     & M              ,!DO Loop counter
     & N              ,!DO Loop counter
     & IERR           ,!IO Error number
C
     & END_POS        ,!Binary conversion end position
     & EMP_BYTE       ,!Number of empty (Unused) Bytes in each block
     & STRT_POS       ,!Binary conversion starting position
     & BT_COUNT       ,!Byte count
C
     & FL_RECN         !Input file record number
C
C
      CHARACTER*6
     & IFMT     /'(Z1.1)'/  !Binary conversion format
C
      CHARACTER
     & L_STRG*132            ,!Input data line
     & BLNK_L*1024            !Line to clear the data buffer
C
C
      LOGICAL*1
C
     & TAG_CONV               ,!TMS Tag Character conversion
     & TAG_PRES               ,!TMS Tag Character is present
     & STR_COMM               ,!Data file starting comment is present
     & END_COMM                !Data file end comment is present
C
C
      INCLUDE 'formgen.inc'
C
C
C
C=======================================================================
      ENTRY FIL_WRT1(IERR)
C=======================================================================
C
C  This routine initializes the first record of the output download file.
C
C
      WRITE(25, REC=REC_NUMB, IOSTAT=IERR) DATA_BUF(1:512)
C
      DATA_BUF = BLNK_L
C
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,OUT_FILE(1:OUT_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
C
C
C
C=======================================================================
      ENTRY FIL_WRT2(IERR)
C=======================================================================
C
C        This routine writes a record of processed data to the temporary
C file. This processed data is in  ASCII  format  as it is read from the
C input data files.
C
C
      WRITE(35, REC=REC_NUMB, IOSTAT=IERR) DATA_BUF(1:1024)
C
      DATA_BUF = BLNK_L
      REC_NUMB = REC_NUMB + 1
C
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,TMP_FILE(1:TMP_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
C
C
C=======================================================================
      ENTRY FIL_WRT3(FL_RECN, IERR)
C=======================================================================
C
C      This routine writes a complete data block to the  output download
C file. The processed data  that is restored from the temporary download
C file is in  ASCII format. In this routine, this data is converted into
C BINARY format before it is written to the output download file.
C
C
C
C     DATA CONVERSION PROCESS FROM ASCII TO BINARY
C     --------------------------------------------
C
C     Initialize binary data buffer
C     -----------------------------
      DO I = 1, 512
        DATA_INT(I) = 0
      ENDDO
C
C     Select the starting position of the conversion
C     ----------------------------------------------
      IF(DATA_BUF(17:17) .EQ. '$') THEN
        STR_COMM = .TRUE.
        I = 18
        DO WHILE (DATA_BUF(I:I) .NE. '&' .AND. I .LE. 512)
         I = I + 1
        ENDDO
        IF(TMS_DATA) THEN
          STRT_POS = I + 20
        ELSE
          STRT_POS = I + 7
        ENDIF
      ELSE
        STRT_POS = 17
      ENDIF
C
      IF( TMS_DATA ) THEN
        BT_COUNT = 2
      ELSE
        BT_COUNT = 1
      ENDIF
C
      K = 0
      I = STRT_POS
C
      DO WHILE( I .LE. BLK_SIZE(FL_RECN) )
C
C
C       Check if the character is a TMS Tag Character
C       ---------------------------------------------
        IF( TMS_DATA ) THEN
          IF( .NOT. TAG_PRES ) THEN
            DO J = 1, TAG_NUMB
              IF( DATA_BUF(I:I) .EQ. TAG_CHAR(J) ) TAG_CONV = .TRUE.
            ENDDO
          ENDIF
        ENDIF
C
        IF( TAG_CONV ) THEN
          K = K + 1
          TAG_CONV = .FALSE.
C
C         TMS Tag Character conversion
C         ----------------------------
          IF( DATA_BUF(I:I) .EQ. ':' ) THEN
            DATA_INT(K) = ICHAR(DATA_BUF(I:I))
          ELSE
            READ(DATA_BUF(I:I),IFMT,IOSTAT=IERR,ERR=1000) DATA_INT(K)
            IF( DATA_BUF(I:I) .NE. 'F' ) TAG_PRES = .TRUE.
          ENDIF
          I = I + 1
C
        ELSE IF( .NOT. TMS_DATA .AND. DATA_BUF(I:I) .EQ. ':' ) THEN
C
C
C         INTEL Tag Character conversion
C         ------------------------------
          K = K + 1
          DATA_INT(K) = ICHAR(DATA_BUF(I:I))
          I = I + 1
C
        ELSE IF( DATA_BUF(I:I) .EQ. '$' ) THEN
C
C         End of data comment line is reached
C         -----------------------------------
          END_COMM = .TRUE.
          END_POS = I
          GO TO 2000
        ELSE
          TAG_PRES = .FALSE.
C
C         Data BINARY Conversion Process
C         ------------------------------
          DO J = 1, BT_COUNT
            K = K + 1
            READ(DATA_BUF(I:I),IFMT,IOSTAT=IERR,ERR=1000) SHFT_AR2
            I = I + 1
            SHFT_AR2 = ISHFT(SHFT_AR2+0, 4)
            IF(SHFT_AR2 .GT. 127) SHFT_AR2 = SHFT_AR2 - 256
            SHFT_AR1 = SHFT_AR2
            READ(DATA_BUF(I:I),IFMT,IOSTAT=IERR,ERR=1000) DATA_INT(K)
            I = I + 1
C
C           Do the following: DATA_INT(K) = DATA_INT(K) .OR. SHFT_AR1
C           ---------------------------------------------------------
            TMP1 = DATA_INT(K)
            TMP2 = SHFT_AR1
            TMP3 = OR(TMP1,TMP2)
            DATA_INT(K) = TMP3
C
          ENDDO
        ENDIF
      ENDDO
C
 2000 CONTINUE
C
C     Write the BINARY data to the output download file
C     -------------------------------------------------
      N = K + 1
      IF( STR_COMM ) THEN
        STR_COMM = .FALSE.
        IF( END_COMM ) THEN
          END_COMM = .FALSE.
          EMP_BYTE = 512 - (K + STRT_POS + (BLK_SIZE(FL_RECN)-END_POS))
          IF( EMP_BYTE .GT. 0 ) THEN
            M = K + EMP_BYTE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      DATA_BUF(17:STRT_POS-1), (DATA_INT(I), I=1, K),
     &      DATA_BUF(END_POS:BLK_SIZE(FL_RECN)), (DATA_INT(I), I=N, M)
          ELSE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      DATA_BUF(17:STRT_POS-1), (DATA_INT(I), I=1, K),
     &      DATA_BUF(END_POS:BLK_SIZE(FL_RECN))
          ENDIF
        ELSE
          EMP_BYTE = 512 - (K + STRT_POS - 1)
          IF( EMP_BYTE .GT. 0 ) THEN
            M = K + EMP_BYTE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      DATA_BUF(17:STRT_POS-1), (DATA_INT(I), I=1, M)
          ELSE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      DATA_BUF(17:STRT_POS-1), (DATA_INT(I), I=1, K)
          ENDIF
        ENDIF
C
      ELSE
        IF( END_COMM ) THEN
          END_COMM = .FALSE.
          EMP_BYTE = 512 - (K + STRT_POS + (BLK_SIZE(FL_RECN)-END_POS))
          IF( EMP_BYTE .GT. 0 ) THEN
            M = K + EMP_BYTE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      (DATA_INT(I), I=1 , K), DATA_BUF(END_POS:BLK_SIZE(FL_RECN)),
     &      (DATA_INT(I), I=N, M)
          ELSE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),
     &      (DATA_INT(I), I=1 , K), DATA_BUF(END_POS:BLK_SIZE(FL_RECN))
          ENDIF
        ELSE
          EMP_BYTE = 512 - (K + STRT_POS - 1)
          IF( EMP_BYTE .GT. 0 ) THEN
            M = K + EMP_BYTE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),(DATA_INT(I),I=1,M)
          ELSE
            WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &      BLCK_INF(FL_RECN,1), BLOCK_ID(FL_RECN),
     &      (BLCK_INF(FL_RECN,I), I=2, 3), DMC_NUMB,BLCK_INF(FL_RECN,5),
     &      BLCK_INF(FL_RECN,6), BLCK_INF(FL_RECN,7),(DATA_INT(I),I=1,K)
          ENDIF
        ENDIF
      ENDIF
C
      REC_NUMB = REC_NUMB + 1
      DATA_BUF = BLNK_L
C
 1000 CONTINUE
C
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,OUT_FILE(1:OUT_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
C
C
C
C=======================================================================
      ENTRY FIL_WRT4(IERR)
C=======================================================================
C
C  This routine writes the header block to the output download file.
C  This block contains the following :
C
C           Word # 1 : DMC Number
C           Word # 2 : Total Number of blocks of download file
C           Word # 3 : Download address
C           Word # 4 : New Sound system identification
C
C
C
      WRITE(25, REC=REC_NUMB, IOSTAT=IERR)
     &     DMC_NUMB, TOT_RECS, DLD_ADDR, SND_ID, DATA_BUF(1:504)
C
      REC_NUMB = REC_NUMB + 1
      DATA_BUF = BLNK_L
C
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,OUT_FILE(1:OUT_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
C
C
C=======================================================================
      ENTRY FIL_WRT5(L_STRG,IERR)
C=======================================================================
C
C   This routine stores the data of the DAC in the DAC temporary storage
C file. This data is latter restored, processed, and then written to the
C output download file when the TSD BUS data is completely processed.
C
C
      WRITE(19, 200, IOSTAT=IERR) L_STRG
C
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6,100) ESC,POSDN,DAC_FILE(1:DAC_FILL),IERR
        CALL Beep(1)
        CALL Wait_Time(5)
      ENDIF
C
      RETURN
C
C
 100  FORMAT(' ',A2,'2',A1,';H',1X,'%FORMGEN - ERROR DURING WRITING ',
     &A,'; IOSTAT = ',I3)
C
 200  FORMAT(A)
C
C
      END
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                              FIL_CLOS
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  This routine performs the I/O closing of the different units handled
C  by FORMGEN utility.
C
C
C
      SUBROUTINE FIL_CLOS
C
C
      IMPLICIT NONE
C
C
      INTEGER*4
     & I                  !DO Loop counter
C
C
      INCLUDE 'formgen.inc'
C
C
C     Close input data files
C     ----------------------
C
      DO I = 1 , MAXFILE
         CLOSE(UNIT = I+10 )
      ENDDO
C
C
C     Close DAC temporary data file
C     -----------------------------
C
      CLOSE(UNIT = 19,STATUS='DELETE')
C
C
C     Close output download files
C     ---------------------------
C
      CLOSE(UNIT = 25)
C
C
C     Close output information file
C     -----------------------------
C
      CLOSE(UNIT = 30)
C
C
C     Close temporary scratch file
C     -----------------------------
C
      CLOSE(UNIT = 35,STATUS='DELETE')
C
      RETURN
C
      END
C
C
C
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                               INF_REP
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C This subroutine reports information associated with each input data
C file. This information contains the following :
C
C       -Number of blocks
C       -DMC Number
C       -The slots used
C       -Number of bytes
C
C Entry Points : INF_REP1
C                INF_REP2
C                INF_REP3
C                INF_REP4
C
C
      SUBROUTINE INF_REP
C
C
      IMPLICIT NONE
C
C
      INTEGER*2
     & I                    ,!DO loop counter
     & TOT_SLTB              !Total number of slots temporary buffer
C
C
      INTEGER*4
     & FILE                 ,!Input data file identification number
     & SCREEN               ,!Screen display portion
     & IERR                  !I/O Error number
C
C
      CHARACTER
     & FMT1*2               ,!Format string
     & FMT2*1               ,!Format string
     & FMT3*2               ,!Format string
     & FMT4*2               ,!Format string
     & FMT5*110             ,!Format string
     & FMT6*250             ,!Format string
     & FMT7*250             ,!Format string
     & FMT8*18              ,!Format string
     & FMT9*2               ,!Format string
     & SLT_STRG*13          ,!Slot number comment line string
     & DMC_STRG*10          ,!DMC number comment line string
     & BYT_STRG*15          ,!Bytes number comment line string
     & BLK_STRG*16           !Blocks number comment line string
C
C
      INCLUDE 'formgen.inc'
C
C
      DATA DMC_STRG / 'DMC Number' /
      DATA SLT_STRG / 'Used on Slots' /
      DATA BYT_STRG / 'Number of Bytes' /
      DATA BLK_STRG / 'Number of Blocks' /
C
C
C
C=======================================================================
      ENTRY INF_REP1(FILE,SCREEN,IERR)
C=======================================================================
C
C This routine reports the name of the input data file that is in process
C or not available.
C
C SCREEN = 1 -> The portion of the screen that displays file in process
C SCREEN = 2 -> The portion of the screen that displays file data
C               statistics
C
C
C
C
      IF(SCREEN .EQ. 1) THEN
         WRITE(FMT1, '(I2.2)') (40-(TASK_LEN(FILE)/2))
         WRITE(FMT2, '(I1.1)') TASK_LEN(FILE)
         FMT5 = '('' '',A2,''1'',A1,'';1H'',T'//FMT1//',A2,''1;5m'',
     &          ''Processing '',A'//FMT2//','' File '',A2,''0m'')'
C
         CALL CUR_POS(1)
C
         WRITE(6, FMT5) ESC,POSUP,ESC,TASK_NAM(FILE)(1:TASK_LEN(FILE)),
     &                  ESC
C
      ELSE
         WRITE(FMT1, '(I2.2)') (35-(TASK_LEN(FILE)/2))
         WRITE(FMT2, '(I1.1)') TASK_LEN(FILE)
         FMT5 = '('' '',A2,''16'','';H'',T'//FMT1//',A2,''1;5m'',
     &          A'//FMT2//','' file is not available'',A2,''0m'')'
C
C
         CALL CUR_POS(2)
C
         WRITE(6, FMT5) ESC,ESC,TASK_NAM(FILE)(1:TASK_LEN(FILE)),ESC
      ENDIF
C
      RETURN
C
C
C
C
C=======================================================================
      ENTRY INF_REP2(FILE,IERR)
C=======================================================================
C
C This routine reports the following information when the input file is
C successfully processed :
C
C       -Number of blocks
C       -DMC Number
C       -The slots used
C       -Number of bytes
C
C
C
      WRITE(FMT2, '(I1.1)') TASK_LEN(FILE)
      WRITE(FMT3, '(I2.2)') TASK_LEN(FILE)+INP_FILL(FILE)+18
      WRITE(FMT9, '(I2.2)') INP_FILL(FILE)
C
C     Select mode format of download data
C     -----------------------------------
C
      IF(MLT_DATA .AND. STD_DATA) THEN
        WRITE(FMT8,'(A)') '   (STD and MULTI)'  !STANDARD & MULTI download data
      ELSE IF(MLT_DATA) THEN
        WRITE(FMT8,'(A)') '   (MULTI)        '  !MULTI download data only
      ELSE
        WRITE(FMT8,'(A)') '                  '  !STANDARD download data only (Default)
      ENDIF
C
      IF(TOT_SLOT .LE. 12) THEN
C
        WRITE(FMT4, '(I2.2)') TOT_SLOT
C
        FMT6='('' '',A2,''1'',A1,'';1H'',A2,''1m'',A'//FMT2//',
     &'' File Completed'',A2,''0m'',
     &//,1X,A10,''       :  '',Z4.4/,1X,A16,'' : '',I5,'''//FMT8//'''/,
     &1X,A15,''  :'',I6/,1X,A13,''    :  '','//FMT4//'(''XA'',I2.2,
    "&'' ''))'
C
C
        FMT7 = '(5X,''('',I1,'') '',A'//FMT2//','' File'',2X,A'//FMT9//'
     &,2X,''Processed'',/,9X,'//FMT3//'(''-'')//,9X,A10,''       :   '',
     &Z4.4/,9X,A16,'' :  '',I5,'''//FMT8//'''/,9X,A15,''  : '',I6/,9X,
     &A13,''    :   '','//FMT4//'(''XA'',I2.2,'' '')///)'
C
C
C
      ELSE
C

        TOT_SLTB = TOT_SLOT
        WRITE(FMT1, '(I2.2)') ( 21 - (MAX0((TOT_SLTB-24),0)*5) )
C
        TOT_SLTB = TOT_SLTB - 12
        WRITE(FMT4, '(I2.2)') TOT_SLTB
C
        FMT6='('' '',A2,''1'',A1,'';1H'',A2,''1m'',A'//FMT2//',
     &'' File Completed'',A2,''0m'',
     &//,1X,A10,''       :  '',Z4.4/,1X,A16,'' : '',I5,'''//FMT8//'''/,
     &1X,A15,''  :'',I6/,1X,A13,''    :  '',12(''XA'',I2.2,'' ''),/,
     &'//FMT1//'X,'//FMT4//'(''XA'',I2.2,'' ''))'
C
C
        TOT_SLTB = TOT_SLOT
        WRITE(FMT1, '(I2.2)') ( 30 - (MAX0((TOT_SLTB-24),0)*5) )
C
C
        FMT7 = '(5X,''('',I1,'') '',A'//FMT2//','' File'',2X,A'//FMT9//'
     &,2X,''Processed'',/,9X,'//FMT3//'(''-'')//,9X,A10,''       :   '',
     &Z4.4/,9X,A16,'' :  '',I5,'''//FMT8//'''/,9X,A15,''  : '',I6/,9X,
     &A13,''    :   '',12(''XA'',I2.2,'' ''),/,'//FMT1//'X,'//FMT4//
     &'(''XA'',I2.2,'' '')///)'
C
C
      ENDIF
C
C
      CALL CUR_POS(2)
C
      WRITE(6, FMT6, ERR=200, IOSTAT=IERR)
     &                ESC,POSUP,ESC,
     &                TASK_NAM(FILE)(1:TASK_LEN(FILE)),ESC,DMC_STRG,
     &                DMC_NUMB,BLK_STRG,FIL_RECS,BYT_STRG,NUM_BYTE,
     &                SLT_STRG,(FIL_SLOT(I), I=1, TOT_SLOT)
C
C
      WRITE(30, FMT7, ERR=200, IOSTAT=IERR)
     &                FILE,TASK_NAM(FILE)(1:TASK_LEN(FILE)),
     &                INP_FILE(FILE)(1:INP_FILL(FILE)),DMC_STRG,
     &                DMC_NUMB,BLK_STRG,FIL_RECS,BYT_STRG,NUM_BYTE,
     &                SLT_STRG,(FIL_SLOT(I), I=1, TOT_SLOT)
C
C
      FIL_RECS = 0
      NUM_BYTE = 0
      TOT_SLOT = 0
C
 200  CONTINUE
C
      RETURN
C
C
C
C
C=======================================================================
      ENTRY INF_REP3(IERR)
C=======================================================================
C
C This routine reports the following information after a successful
C completion of FORMGEN :
C
C       - Total Number of blocks of all processed data
C       - Total Number of bytes of all processed data
C
C
C
       CALL Wait_Time(2)
C      CALL WAITIME(2)
C
C
      CALL CUR_POS(1)
C
      CALL CUR_POS(2)
C
      WRITE(6,600,ERR=500,IOSTAT=IERR) ESC,POSUP,ESC,ESC,BLK_STRG,
     &                                 TOT_RECS,BYT_STRG,TOT_BYTE,
     &                                 OUT_FILE(1:OUT_FILL),
     &                                 INF_FILE(1:INF_FILL)
C
C
      WRITE(FMT3, '(I2.2)') OUT_FILL
      WRITE(FMT4, '(I2.2)') OUT_FILL+32
C
      FMT6 = '(9X,''FORMGEN output file'',2X,A'//FMT3//',2X,''Completed'
     &',/,9X,'//FMT4//'(''-'')//,9X,''Total '',A16,'' :  '',I5,/,9X,''To
     &tal '',A15,''  : '',I6)'
C
C
      WRITE(30,FMT6,ERR=500,IOSTAT=IERR) OUT_FILE(1:OUT_FILL),BLK_STRG,
     &                                   TOT_RECS,BYT_STRG,TOT_BYTE
C
 500  CONTINUE
C
      RETURN
C
 600  FORMAT(' ',A2,'1',A1,';1H',30X,A2,'1m','FORMGEN Completed',A2,
     &       '0m',//,25X,'Total ',A16,' :  ',I5/,25X,'Total ',A15,
     &       '  : ',I6,//5X,'Download File : ',A,' Created',
     &       /5X,'Information File : ',A,' Created'/)
C
C
C
C
C
C=======================================================================
      ENTRY INF_REP4(FILE,IERR)
C=======================================================================
C
C This routine reports a message to the information file when the input
C file does not exist.
C
C
C
C
      WRITE(FMT2, '(I1.1)') TASK_LEN(FILE)
      WRITE(FMT3, '(I2.2)') TASK_LEN(FILE)+39
C
        FMT7 = '(5X,''('',I1,'') '',A'//FMT2//','' File Not Processed'',
     &'' : It Does Not Exist''/9X,'//FMT3//'(''-'')///)'
C
C
      WRITE(30, FMT7, ERR=800, IOSTAT=IERR)
     &                FILE,TASK_NAM(FILE)(1:TASK_LEN(FILE))
C
C
 800  CONTINUE
C
      RETURN
C
C
C
      END
C
C
