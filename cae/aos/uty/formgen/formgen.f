C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C                          FORMGEN  UTILITY
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  Revision V2.0                H. Ramadhan            8 Nov 1988
C  Revision V2.1                P. Daigle             30 Jul 1990
C
C----------------------------------------------------------------------
C
C     This utility is designed to format the input data (ASCII) to the
C  DMC of the Digital Sound System into blocks of data (BINARY) each of
C  which consists of 512 Bytes of less. The input data is the output of
C  the following utilities :
C
C'Revision_History
C  TSDGEN  : Produces TSD BUS data and XILINX data
C  TMSGEN  : Produces XININX initialization data
C  WAVEGEN : Produces WAVEGEN CONTROLLER data
C  HARMONY : Produces sound tables data
C  TMS ASS : Produces TMS data
C
C
C
C  Revision 2.0 of FORMGEN produces a binary output download file in
C  contrast to  Revosion 1.0 which produces an ASCII output download
C  file. This  feature was  implemented in order to decrease the DMC
C  processing  time to  convert the ASCII data to BINARY data during
C  simulator load. This feature also reduces  the size of the output
C  download file by almost 50 percent.
C
C
C  Input Data Format
C  -----------------
C  The input data has two formats. These formats are the TMS format or the
C  INTEL format which are described below :
C
C  TMS Format :      &xxyyzz
C                    K0000taskname  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                             .
C                             .
C                             .
C                             .
C                    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    :
C
C
C  Where  &xxyyzz is the identifier of the data
C         xx      : DMC number
C         yy      : Data identification code (TI,TT)
C         zz      : Slot number
C         aaaa    : Actual data in ASCII
C         :       : Indicates end of data
C
C
C
C  INTEL  Format :   &xxyyzz
C                    :aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    :aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    :aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                             .
C                             .
C                             .
C                             .
C                    :aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    :aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
C                    :00000001FF
C
C  Where  &xxyyzz is the identifier of the data
C         xx          : DMC number
C         yy          : Data identification code (HX,HS,HH,HW,HF)
C         zz          : Slot number
C         aaaa        : Actual data in ASCII
C         :00000001FF : Indicates end of data
C
C
C
C
C  Output Data Format
C  ------------------
C
C  Block 1 (Header Block)
C
C     Word # 1 : DMC Number (Hex)
C     Word # 2 : Total number of blocks of output data excluding
C                the header block (Hex)
C     Word # 3 : Sound download address (X'0700')
C     Word # 4 : New Sound system identification ( NS ) (ASCII)
C     Word # 5
C            .
C            .
C     Word #29: Name of the output download file ,the time, and
C               the date of its creation.
C
C  Block x (Data Blocks)
C
C     Header (Word # 1 - Word # 8)
C
C     Word # 1 : Number of bytes in this block excluding the
C                header (8 Words) (Hex)
C     Word # 2 : Data identification code (HX,HS,HH,HW,HF,HD,HP,TI,TT)
C     Word # 3 : Number of this block (Hex)
C     Word # 4 : Number of blocks for this data identification per slot
C     Word # 5 : DMC number
C     Word # 6 : Slot number
C     Word # 7 : Sound memory page number
C     Word # 8 : Spare
C
C
C     Data (Word # 9 - Word # 192)
C
C     Word # 9 - 192 : Contains 512 bytes or less of data in BINARY
C
C
C>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
      PROGRAM FORMGEN
C
C
      IMPLICIT NONE
C
C
      INTEGER*4
C
     & Writ(3)        ,!Write enable for flags
     & IERR            !I/O Error number
C
C
      INCLUDE 'formgen.inc'
C
C     Set ESCAPE in ASCII
C     -------------------
C
      ESC = CHAR(27)//'['
C
C     Initialization for library routines
C     -----------------------------------
      CALL Init_Libgd(1,3)
C
C     Define input parameters
C     -----------------------
C
      CALL XLINK_READ(Config_S,Config_L,DMC_Num,Page_Num,Filetters
     &                ,Comp_Id,LinkFlag,Com,File_N,File_Len,IERR)
      IF(IERR .NE. 0) THEN
        CALL CUR_POS(3)
        WRITE(6, 300) ESC, POSDN, IERR
        CALL Beep(1)
        CALL Wait_Time(2)
        GO TO 500
      ENDIF
C
      IF (Filetters(1:2).EQ.'sn') THEN
         DATA_DIR = Config_S(1)(1:Config_L(1))//'sound/data/'
         INT_DIR = Config_S(1)(1:Config_L(1))//'sound/inter/'
      ELSE
         DATA_DIR = Config_S(1)(1:Config_L(1))//'audio/data/'
         INT_DIR = Config_S(1)(1:Config_L(1))//'audio/inter/'
      ENDIF
      L_DATA_DIR = Config_L(1)+11
      L_INT_DIR = Config_L(1)+12
C
      IF ( (.NOT.Com(1)) .OR. Com(2) ) THEN
C
C       Display utility header
C       ----------------------
C
        CALL HEADER
C
C
C       Open the files handled by the utility
C       -------------------------------------
C
        CALL FIL_OPEN(*100)
C
C
C       Print the information file header
C       ---------------------------------
C
        CALL INF_HEAD
C
C
C       Process the input data files
C       ----------------------------
C
        CALL FIL_PROC(*100)
C
C
 100    CONTINUE
C
C
C       Close input and output files
C       ----------------------------
C
        CALL FIL_CLOS
C
C
C
        IF( Com(2) ) THEN
C
C         Update the DOWNLOAD status
C         --------------------------
C
          Com(2) = .FALSE.
          Writ(2) = 1
          CALL XLINK_WRITE(Writ,Com,IERR)
          IF(IERR .NE. 0) THEN
            CALL CUR_POS(3)
            WRITE(6, 400) ESC, POSDN, IERR
            CALL Beep(1)
          ENDIF
        ENDIF
C
        CALL WAIT_KEY
        CALL CUR_POS(4)
C
C
      ENDIF
C
C
 500  CONTINUE
C
C
      CALL EXIT
C
C
 300  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ERROR DURING OPENING/READ
     &ING THE FILE AOSXLINK.INF; IOSTAT = ', I3)
C
C
 400  FORMAT(' ',A2,'2',A1,';H',2X,'%FORMGEN - ERROR DURING OPENING/WRIT
     &ING THE FILE AOSXLINK.INF; IOSTAT = ', I3)
C
C
      END
C
