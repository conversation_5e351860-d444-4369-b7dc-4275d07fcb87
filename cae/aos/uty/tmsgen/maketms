INCLUDE = tms.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
tmsgen: tms.o tmstms.o tmslib.o tmsunix.o tmsxil.o tmsmain.o tmsspc.o \
$(CAELIB)/libcae.a $(LIBDIR)/libaos.a
#
#
	xlf -C -qcharlen=1024 tms.o tmstms.o tmslib.o tmsunix.o tmsxil.o \
tmsmain.o tmsspc.o -L$(CAELIB) -lcae -lc -L$(LIBDIR) \
-laos -o $(EXEDIR)/tmsgen
#
#
tms.o: tms.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tms.f
#
tmstms.o: tmstms.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmstms.f
#
tmslib.o: tmslib.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmslib.f
#
tmsunix.o: tmsunix.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmsunix.f
#
tmsxil.o: tmsxil.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmsxil.f
#
tmsmain.o: tmsmain.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmsmain.f
#
tmsspc.o: tmsspc.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c tmsspc.f
#
