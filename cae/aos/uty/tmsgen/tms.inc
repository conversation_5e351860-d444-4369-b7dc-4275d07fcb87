C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                   TG.INC
C
C  This module contains global variables and COMMON declarations for
C  the TMSGEN utility.
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C  Parameters
C  ----------
C
      INTEGER*4
     & TMSDATID,TMSLODID,XILLODID,TMSDLDID,XILDLD<PERSON>,SPCDLDID
C
      INTEGER*2
     & MAXSLOT
C
      PARAMETER (TMSDATID = 1)          !DATA file logical number 
      PARAMETER (TMSLODID = 2)          !TMS load file logical number 
      PARAMETER (XILLODID = 3)          !XILINX load file logical number 
      PARAMETER (TMSDLDID = 4)          !TMS download file logical number 
      PARAMETER (XILDLDID = 11)         !XILINX download file logical number 
      PARAMETER (SPCDLDID = 12)         !SPC download file logical number
C
      PARAMETER (MAXSLOT = 27)          !Maximum number of slots in a chassis 
C
C
C
C  Character variables
C  -------------------
C
      CHARACTER
     & TMSDATLIN*132,                   !TMS DATA file line
     & TMSLODLIN*132,                   !TMS LOAD file line
     & XILLODLIN*132,                   !XILINX LOAD file line
     & OPLINE*80,                       !Output file line
     & RECORD*80,                       !Intel Hex record buffer
     & EMPTY*80,                        !Empty line
     & DOLLAR*80,                       !Dollar sign
     & STAR*80,                         !Star
     & AMPER*80,                        !Ampersand
     & EQUALS*80,
     & BRACKETS*80,
     & MTMSBUF*80,                      !Multi-slot TMS buffer
     & PARA_S(8)*40,                    !Logical names definition
     & TMSDATNAM*52,                    !TMS DATA file name 
     & TMSLODNAM*52,                    !TMS load file name 
     & XILLODNAM*52,                    !XILINX load file name 
     & TMSDLDNAM*52,                    !TMS download file name              
     & XILDLDNAM*52,                    !XILINX download file name 
     & SPCDLDNAM*52,                    !SPC download file name
     & BUFFERF*52,                      !Buffer for filenames
     & SHIPNAME*40,                     !Ship name
     & SHIPDIR*40,                      !Ship directory name
     & DATA_DIR*40,
     & INT_DIR*40,
     & TITLE*40,                        !TITLE name
     & SYMBOL*12,                       !Symbol (name, sign, number or keyword)
     & TYPE*3,                          !Card type: DSG, DSP or DAC
     & ANSWER*1,                        !User y/n response
     & DMC_Num*2,                       !DMC number from XLINK file...
     & Page_Num*2,                      !Page number from XLINK file...
     & Filetters*3,                     !'SN' or 'RF' + Config letter
     & File_N(5)*40,
     & Config_S(12)*80                  !Logical definitions from XLINK file
C
C
C
C  Integer variables
C  -----------------
C
      INTEGER*4
     & IERR,                            !Error counter
     & STATUS,                          !Status of I/O operations
     & PARA_L(8)                        !Logical names length
C
      INTEGER*2                                 
     & MODE,                            !Write mode
     & STRLENG,                         !STRLENG function
     & I,J,                             !Loop counters
     & DATLINCNT,                       !TMS DATA file line count
     & TMSLODCNT,                       !TMS LOAD file line count
     & XILLODCNT,                       !XILINX LOAD file line count
     & POS,                             !Position of a character within a line
     & NUMBER,                          !Multi-purpose variable
     & LEN,                             !Length
     & LEN2,                            !Another length
     & SLOTCNT,                         !Slot counter
     & DMC,                             !DMC number
     & SLOT,                            !Slot number
     & SLOTNUM,                         !Number of slots
     & L_DATA_DIR,
     & L_INT_DIR,
     & Config_L(12),                    !String length...
     & File_L(5),
     & Comp_Id                          !1=VAX, 2=GOULD
C
C
C        
C  Logical*1 variables
C  -------------------
C
      LOGICAL*1
     & UPDATE,                          !UPDATE flag
     & DOWNLOAD,                        !DOWNLOAD flag
     & WGSIZE,                          !WGSIZE flag 
     & TGSHIP,                          !Set to .TRUE. for stand alone mode
     & TGSTAT,                          !Set to .TRUE. if TMSGEN exits ok
     & DOTMS,                           !Set to .TRUE. if writing TMS file
     & DOXIL,                           !Set to .TRUE. if writing XILINX file
     & DOSPC,                           !Set to .TRUE. if writing SPC file
     & OPTMTMS,                         !Set to .TRUE. for option Multi-slot TMS
     & LinkFlag(4),                     !OnSite flag...from XLINK file
     & Com(6),                          !Communication flags from XLINK file
     & Sound_Gr                         !Group Flag
C
C
C
C  Common declarations
C  -------------------
C
      COMMON /Charac2/
     & DMC_Num,
     & Page_Num
C
      COMMON /Charac3/
     & TYPE,
     & Filetters
C
      COMMON /Charac12/
     & SYMBOL
C
      COMMON /Charac40/
     & SHIPNAME,
     & SHIPDIR,
     & PARA_S,
     & TITLE,
     & DATA_DIR,
     & INT_DIR
C
      COMMON /Charac52/
     & TMSDATNAM,
     & TMSLODNAM,
     & XILLODNAM,
     & TMSDLDNAM,
     & XILDLDNAM,
     & SPCDLDNAM,
     & BUFFERF
C 
      COMMON /Charac80/
     & MTMSBUF,
     & Config_S
C
      COMMON /Charac132/
     & TMSDATLIN,
     & TMSLODLIN,
     & XILLODLIN
C
      COMMON /Integer2/
     & MODE,
     & DATLINCNT,
     & TMSLODCNT,
     & XILLODCNT,
     & POS,
     & SLOTCNT,
     & DMC,
     & SLOT,
     & SLOTNUM,
     & L_DATA_DIR,
     & L_INT_DIR,
     & Config_L,
     & Comp_Id
C
      COMMON /Integer4/
     & IERR,
     & PARA_L
C
      COMMON /Logical1/
     & UPDATE,
     & DOWNLOAD,
     & WGSIZE,
     & DOTMS,
     & DOXIL,
     & DOSPC,
     & OPTMTMS,
     & LinkFlag,
     & Com
C
C
C  Data initialization
C  -------------------
C
      DATA DOLLAR /'$'/                                        
      DATA STAR /'*'/                                        
      DATA AMPER /'&'/                                        
      DATA EMPTY /'                                                     
     &        '/
      DATA EQUALS   /'$=================================================
     &====================='/
      DATA BRACKETS /'$<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>
     &>>>>>>>>>>>>>>>>>>>>>'/
C
