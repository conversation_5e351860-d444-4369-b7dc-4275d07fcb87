C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                  TMSSPC.FOR
C
C  This module contains SETUPGEN utility's subroutines which write the 
C  SPC DOWNLOAD file.
C
C  SPCHEAD
C  SPCDMC
C  SPCEMPTY
C  SPCSLT
C  SPCEOF
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                   SPCHEAD
C =============================================================================
C
C  This subroutine writes the SPC DOWNLOAD file start header. 
C
      SUBROUTINE SPCHEAD
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = DOLLAR
      OPLINE(26:42) = 'SPC PAGE REGISTER'
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(29:41) = 'DOWNLOAD FILE'
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   SPCDMC
C =============================================================================
C
C  This subroutine writes the SPC DMC header. 
C
      SUBROUTINE SPCDMC
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,SPCDLDID,STATUS)
      OPLINE = EQUALS
      OPLINE(31:40) = ' DMC #    '
      WRITE (OPLINE(38:39),'(Z2.2)') DMC
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
      CALL WRITELINE(DOLLAR,SPCDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   SPCEMPTY
C =============================================================================
C
C  This subroutine writes a message for an empty slot.
C
      SUBROUTINE SPCEMPTY
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = BRACKETS
      OPLINE(25:45) = ' NO SPC ON SLOT #    '
      WRITE (OPLINE(43:44),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   SPCSLT
C =============================================================================
C
C  This subroutine writes the SPC code for a slot.
C
      SUBROUTINE SPCSLT(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CHARACTER*4
     &  SPCOFF        /'0010'/        ,!SPC slot offset address
     &  SPCSEG        /'2000'/         !SPC slot segment address
C
      CHARACTER*2
C
     &   CHECKSUM,                   !Check sum for the record
     &   PAGE_VAL(15)                !Actual page register value for SPC
C                                    !not same format as DMC page
      CHARACTER*43
C
     &  CLEANREC                     !Clean record
C
      INTEGER*2
C
     &  DMC_PAGE
C
      DATA CLEANREC /'                                           '/
      DATA PAGE_VAL /'0E','0D','0C','0B','0A','09','08','07',
     &               '06','05','04','03','02','01','00'/
C
C
C  Write slot header
C  -----------------
C
      OPLINE = BRACKETS
      OPLINE(30:40) = ' SLOT #    '
      WRITE (OPLINE(38:39),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
C  Write header record
C  -------------------
C
      OPLINE = AMPER                             !First line     
      WRITE (OPLINE(2:3),'(Z2.2)') DMC           !    "
      OPLINE(4:5) = 'HP'                         !    "
      WRITE (OPLINE(6:7),'(Z2.2)') SLOT          !    "
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)     !    "
C
      OPLINE(1:13) =':020000022000'              !Second line
      OPLINE(10:13) = SPCSEG                     !    "   
      CALL CHECK_SUM(OPLINE,CHECKSUM,12)         !    "
      OPLINE(14:15) = CHECKSUM(1:2)              !    "
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)     !    "
C
      OPLINE(1:3) = ':02'                        !Third line
      OPLINE(4:7) = SPCOFF                       !    "
      OPLINE(8:13) = '000000'                    !    "
      READ (Page_Num(1:2),'(Z2.2)') DMC_PAGE     !    "
      OPLINE(10:11) = PAGE_VAL(DMC_PAGE)(1:2)    !    "
      CALL CHECK_SUM(OPLINE,CHECKSUM,12)         !    "
      OPLINE(14:15) = CHECKSUM(1:2)              !    "
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)     !    "
C
      OPLINE(1:43) = CLEANREC
      OPLINE(1:11) = ':00000001FF'               !Fourth line
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)     !    "
      OPLINE = CLEANREC                          !Clean the opline
C
      RETURN           
      END
C
C
C
C =============================================================================
C                                   SPCEOF
C =============================================================================
C
C  This subroutine writes the SPC DOWNLOAD file end-of-file message.
C
      SUBROUTINE SPCEOF
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,SPCDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(26:42) = 'SPC PAGE REGISTER'
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(30:40) = 'END OF FILE'
      CALL WRITELINE(OPLINE,SPCDLDID,STATUS)
C
      RETURN
      END
C
C =============================================================
C
C     THIS SUBROUTINE WILL RETURN THE CHECKSUM FOR A RECORD
C
C =============================================================
C
      SUBROUTINE CHECK_SUM(VECTOR,CHECKSUM,RECLEN)
      IMPLICIT NONE
C
      INTEGER*2
C
     &  CHEKSTAR     /2/                    ,!Check sum  start at
     &  I                                   ,!Counter for loop
     &  SUM                                 ,!Sum of all bytes in the record
     &  HEX_SUM                             ,!Check sum in Hex
     &  DECIM                                !Decimal Value from subroutine
C
      INTEGER*4
     &  RECLEN                               !Length of the RECORD
C
      CHARACTER*43
C
     &   VECTOR                              !Record file
C
      CHARACTER*2
C
     &   CARAC1*2                            ,!First caracter
     &   CARAC2                              ,!Second caracter
     &   CHECKSUM                             !Check sum for the record
C
C
      SUM = 0
      CARAC2(1:1) = '0'
C
      DO I = CHEKSTAR, RECLEN,2
        CARAC2(1:2) = VECTOR(I:I+1)
        CALL CAR_CHG(4,DECIM,CARAC1,CARAC2)
        SUM = SUM + DECIM
        IF (SUM.GT.255) SUM = SUM - 255
      ENDDO
C
      HEX_SUM = NOT(SUM) + 1
C
      CALL CAR_CHG(3,HEX_SUM,CARAC1,CHECKSUM)
C
      RETURN
C
      END
C
C =====================================================================
C                               CAR_CHG
C =====================================================================
C
C      These subroutine transforms number stored in character array in
C     ASCII format to decimal or hexadecimal number and vice-versa.
C
C           1-From decimal to ASCII character
C           2-ASCII character to decimal
C           3-Decimal to ASCII hexadecimal
C           4-ASCII character to hexadecimal
C
      SUBROUTINE CAR_CHG(BANK,DECIM,CARAC1,CARAC2)
C
      IMPLICIT NONE
C
      CHARACTER
     &  CARAC2*2         ,!Character value for input(CHA to DEC)
     &  CARAC1(4)*1       !Character value for output(DEC to CHA)
C
       INTEGER*2
     &  DECIM            ,!Decimal number
     &  CODE             ,!Decimal digit
     &  NDECIM           ,!Stored decimal value
     &  ASCII            ,!ASCII number
     &  BUFF(4)           !Buffer
C
       INTEGER*4
     &  BANK             ,!Type of operation
     &  II                !Loop counter
C
       LOGICAL*1
     &  ZERO              !Zero before flag
C
      IF (BANK.EQ.1) THEN
C
C        Change from decimal to ASCII
C        ----------------------------
         NDECIM = DECIM
         IF (NDECIM.GE.9999) THEN
            NDECIM = NDECIM-((DECIM/10000)*10000)
         ENDIF
C
         ZERO = .FALSE.
         DO II=1,4
             CODE = NDECIM/(10**(4-II))
             NDECIM = NDECIM -CODE*(10**(4-II))
             CARAC1(II) = CHAR(CODE+48)
             IF(CODE.NE.0)THEN
                ZERO=.TRUE.
             ELSEIF(.NOT.ZERO)THEN
                CARAC1(II)=' '
             ENDIF
         ENDDO
C
      ELSE IF (BANK.EQ.2) THEN
C
C        Change from ASCII to decimal
C        ----------------------------
         DECIM=0
         DO II=1,2
            ASCII = ICHAR(CARAC2(II:II))
            IF(ASCII.GE.48.AND.ASCII.LE.57) THEN
              CODE = ASCII-48
              DECIM = DECIM + CODE*10**(2-II)
            ENDIF
         ENDDO
C
      ELSE IF (BANK.EQ.3) THEN
C
C       Change from decimal to ASCII hex
C       --------------------------------
        IF (DECIM.LT.0) THEN                 !negatif value
          DECIM = (255 + DECIM) + 1
        ENDIF
C
        IF (DECIM.GT.15) THEN
          BUFF(1) = DECIM / 16
          BUFF(3) = BUFF(1) * 16
          BUFF(2) = DECIM - BUFF(3)
        ELSE
          BUFF(1) = 0
          BUFF(2) = DECIM
        ENDIF
        DO II=1,2
          IF(BUFF(II).GE.10)THEN
             CARAC2(II:II)= CHAR(BUFF(II)+55)
          ELSE
             CARAC2(II:II)= CHAR(BUFF(II)+48)
          ENDIF
        ENDDO
C
      ELSE IF (BANK.EQ.4) THEN
C
C        Change from ASCII to hexadecimal
C        --------------------------------
         DECIM=0
         DO II=1,2
            ASCII = ICHAR(CARAC2(II:II))
            IF(ASCII.GE.48.AND.ASCII.LE.57) THEN
              CODE = ASCII-48
              DECIM = DECIM + CODE*16**(2-II)
            ELSE IF(ASCII.GE.65.AND.ASCII.LE.70) THEN
              CODE = ASCII-55
              DECIM = DECIM + CODE*16**(2-II)
            ENDIF
         ENDDO
      ENDIF
C
      RETURN
      END
C
