C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                   TMS.FOR
C  
C  This program is used to invoke the TMSGEN utility.
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C  Set SHIPMODE to .TRUE. in order to run in a ship environment, .FALSE.
C  otherwise. TMSGEN returns with STATUS set to .TRUE. if no error was
C  encountered, .FALSE. otherwise.   
C
C
      PROGRAM TG
C
      LOGICAL*1
     & SHIPMODE,
     & STATUS
C
      SHIPMODE = .TRUE.        !Run in a site
C      SHIPMODE = .FALSE.       !Stand-alone mode
C
      CALL TMSGEN(SHIPMODE,STATUS)
      CALL EXIT
      END
C
