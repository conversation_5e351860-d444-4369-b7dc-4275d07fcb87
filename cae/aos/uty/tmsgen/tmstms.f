C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                  TMSTMS.FOR
C
C  This module contains SETUPGEN utility's subroutines which read the TMS
C  LOAD file and write the TMS DOWNLOAD file.
C
C  TMSHEAD
C  TMSDMC
C  TMSEMPTY
C  TMSSLT
C  TMSEOF
C  MTMSSLT
C  MTMSDMC
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                   TMSHEAD
C =============================================================================
C
C  This subroutine writes the TMS DOWNLOAD file start header. 
C
      SUBROUTINE TMSHEAD
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = DOLLAR
      OPLINE(32:39) = 'TMS CODE'
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(29:41) = 'DOWNLOAD FILE'
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   TMSDMC
C =============================================================================
C
C  This subroutine writes the TMS DMC header. 
C
      SUBROUTINE TMSDMC
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,TMSDLDID,STATUS)
      OPLINE = EQUALS
      OPLINE(31:40) = ' DMC #    '
      WRITE (OPLINE(38:39),'(Z2.2)') DMC
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
      CALL WRITELINE(DOLLAR,TMSDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   TMSEMPTY
C =============================================================================
C
C  This subroutine writes a message for an empty slot.
C
      SUBROUTINE TMSEMPTY
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = BRACKETS
      OPLINE(25:45) = ' NO TMS ON SLOT #    '
      WRITE (OPLINE(43:44),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   TMSSLT
C =============================================================================
C
C  This subroutine writes the TMS code for a slot.
C
      SUBROUTINE TMSSLT(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
C  Write slot header
C  -----------------
C
      OPLINE = BRACKETS
      OPLINE(30:40) = ' SLOT #    '
      WRITE (OPLINE(38:39),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
C  Write header record
C  -------------------
C
      OPLINE = AMPER      
      WRITE (OPLINE(2:3),'(Z2.2)') DMC
      OPLINE(4:5) = 'TT'
      WRITE (OPLINE(6:7),'(Z2.2)') SLOT
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
C  Reposition TMS LOAD file to its beginning
C  -----------------------------------------
C
      CALL REWINDFIL(TMSLODID)
C
C  Find beginning of TMS code
C  --------------------------
C
      CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
      TMSLODCNT = 1
C
      DO WHILE (TMSLODLIN(1:1).NE.'K')
         CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
         TMSLODCNT = TMSLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find start of file tag ''K''')
            CALL SHOWLINE(TMSLODLIN,TMSLODCNT)
            CALL TERMWRITE(TMSLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy 1st TMS code line
C  ----------------------
C
      LEN = STRLENG(TMSLODLIN(14:))                  !Find length of record 
      RECORD = TMSLODLIN(1:14+LEN-1)                !Extract record up to F tag
      CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
C  Copy other TMS code lines
C  -------------------------
C
      CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
      TMSLODCNT = TMSLODCNT + 1
C
      DO WHILE (TMSLODLIN(1:1).NE.':')
         RECORD = TMSLODLIN(1:STRLENG(TMSLODLIN))    !Extract record up to F tag
         CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
         CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
         TMSLODCNT = TMSLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find end of file tag '':''')
            CALL SHOWLINE(TMSLODLIN,TMSLODCNT)
            CALL TERMWRITE(TMSLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy end-of-file record
C  -----------------------
C
      RECORD = ':'
      CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
      RETURN           
      END
C
C
C
C =============================================================================
C                                   TMSEOF
C =============================================================================
C
C  This subroutine writes the TMS DOWNLOAD file end-of-file message.
C
      SUBROUTINE TMSEOF
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,TMSDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(32:39) = 'TMS CODE'
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(30:40) = 'END OF FILE'
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  MTMSSLT
C =============================================================================
C
C  This subroutine sets up the comment line containing the slot numbers
C  in the multi-slot TMS download file.
C
      SUBROUTINE MTMSSLT
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      IF (SLOTNUM.EQ.0) MTMSBUF = ' SLOT # '
C
      LEN = SLOTNUM*3
      WRITE (MTMSBUF(LEN+9:LEN+10),'(I2.2)') SLOT
      MTMSBUF(LEN+11:LEN+11) = ' '
C
      SLOTNUM = SLOTNUM + 1
C
      RETURN
      END
C
C
C
C
C =============================================================================
C                                  MTMSDMC
C =============================================================================
C
C  This subroutine writes the multi-slot TMS DOWNLOAD file for a DMC (chassis)
C  entry.
C
      SUBROUTINE MTMSDMC(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
C  Write slot header
C  -----------------
C
      OPLINE = BRACKETS
      LEN = 7 + SLOTNUM*3
      LEN2 = (72 - LEN)/2
      OPLINE(LEN2:LEN2+LEN) = MTMSBUF(:LEN)
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
C  Write header record
C  -------------------
C
      OPLINE = AMPER      
      WRITE (OPLINE(2:3),'(Z2.2)') DMC
      OPLINE(4:5) = 'MT'
      WRITE (OPLINE(6:7),'(Z2.2)') SLOTNUM
      CALL WRITELINE(OPLINE,TMSDLDID,STATUS)
C
C  Reposition TMS LOAD file to its beginning
C  -----------------------------------------
C
      CALL REWINDFIL(TMSLODID)
C
C  Find beginning of TMS code
C  --------------------------
C
      CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
      TMSLODCNT = 1
C
      DO WHILE (TMSLODLIN(1:1).NE.'K')
         CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
         TMSLODCNT = TMSLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find start of file tag ''K''')
            CALL SHOWLINE(TMSLODLIN,TMSLODCNT)
            CALL TERMWRITE(TMSLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy 1st TMS code line
C  ----------------------
C
      LEN = STRLENG(TMSLODLIN(14:))                  !Find length of record 
      RECORD = TMSLODLIN(1:14+LEN-1)                !Extract record up to F tag
      CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
C  Copy other TMS code lines
C  -------------------------
C
      CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
      TMSLODCNT = TMSLODCNT + 1
C
      DO WHILE (TMSLODLIN(1:1).NE.':')
         RECORD = TMSLODLIN(1:STRLENG(TMSLODLIN))    !Extract record up to F tag
         CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
         CALL READLINE(TMSLODLIN,TMSLODID,STATUS)
         TMSLODCNT = TMSLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find end of file tag '':''')
            CALL SHOWLINE(TMSLODLIN,TMSLODCNT)
            CALL TERMWRITE(TMSLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy end-of-file record
C  -----------------------
C
      RECORD = ':'
      CALL WRITELINE(RECORD,TMSDLDID,STATUS)
C
      RETURN           
      END
