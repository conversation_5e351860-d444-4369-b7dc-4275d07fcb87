C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                  TMSXIL.FOR
C
C  This module contains SETUPGEN utility's subroutines which read the XILINX
C  INIT LOAD file and write the XILINX INIT DOWNLOAD file.
C
C  XILHEAD
C  XILDMC
C  XILEMPTY
C  XILSLT
C  XILEOF
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C'Revision_History
C
C
C
C =============================================================================
C                                   XILHEAD
C =============================================================================
C
C  This subroutine writes the XILINX INIT DOWNLOAD file start header. 
C
      SUBROUTINE XILHEAD
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = DOLLAR
      OPLINE(27:43) = 'INIT. XILINX CODE'
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(29:41) = 'DOWNLOAD FILE'
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   XILDMC
C =============================================================================
C
C  This subroutine writes the XIL DMC header. 
C
      SUBROUTINE XILDMC
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,XILDLDID,STATUS)
      OPLINE = EQUALS
      OPLINE(31:40) = ' DMC #    '
      WRITE (OPLINE(38:39),'(Z2.2)') DMC
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
      CALL WRITELINE(DOLLAR,XILDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   XILEMPTY
C =============================================================================
C
C  This subroutine writes a message for an empty slot.
C
      SUBROUTINE XILEMPTY
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      OPLINE = BRACKETS
      OPLINE(25:45) = ' NO XIL ON SLOT #    '
      WRITE (OPLINE(43:44),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   XILSLT
C =============================================================================
C
C  This subroutine writes the XIL code for a slot.
C
      SUBROUTINE XILSLT(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
C  Write slot header
C  -----------------
C
      OPLINE = BRACKETS
      OPLINE(30:40) = ' SLOT #    '
      WRITE (OPLINE(38:39),'(I2.2)') SLOT
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
C  Write header record
C  -------------------
C
      OPLINE = AMPER      
      WRITE (OPLINE(2:3),'(Z2.2)') DMC
      OPLINE(4:5) = 'TI'
      WRITE (OPLINE(6:7),'(Z2.2)') SLOT
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
C  Reposition XIL LOAD file to its beginning
C  -----------------------------------------
C
      CALL REWINDFIL(XILLODID)
C
C  Find beginning of XIL code
C  --------------------------
C
      CALL READLINE(XILLODLIN,XILLODID,STATUS)
      XILLODCNT = 1
C
      DO WHILE (XILLODLIN(1:1).NE.'K')
         CALL READLINE(XILLODLIN,XILLODID,STATUS)
         XILLODCNT = XILLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find start of file tag ''K''')
            CALL SHOWLINE(XILLODLIN,XILLODCNT)
            CALL TERMWRITE(XILLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy 1st XIL code line
C  ----------------------
C
      LEN = STRLENG(XILLODLIN(14:))                  !Find length of record 
      RECORD = XILLODLIN(1:14+LEN-1)                !Extract record up to F tag
      CALL WRITELINE(RECORD,XILDLDID,STATUS)
C
C  Copy other XIL code lines
C  -------------------------
C
      CALL READLINE(XILLODLIN,XILLODID,STATUS)
      XILLODCNT = XILLODCNT + 1
C
      DO WHILE (XILLODLIN(1:1).NE.':')
         RECORD = XILLODLIN(1:STRLENG(XILLODLIN))       !Extract record up to F tag
         CALL WRITELINE(RECORD,XILDLDID,STATUS)
C
         CALL READLINE(XILLODLIN,XILLODID,STATUS)
         XILLODCNT = XILLODCNT + 1
C
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Can not find end of file tag '':''')
            CALL SHOWLINE(XILLODLIN,XILLODCNT)
            CALL TERMWRITE(XILLODLIN)
            RETURN 1
         END IF 
      END DO
C
C  Copy end-of-file record
C  -----------------------
C
      RECORD = ':'
      CALL WRITELINE(RECORD,XILDLDID,STATUS)
C
      RETURN           
      END
C
C
C
C =============================================================================
C                                   XILEOF
C =============================================================================
C
C  This subroutine writes the XIL DOWNLOAD file end-of-file message.
C
      SUBROUTINE XILEOF
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CALL WRITELINE(DOLLAR,XILDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(27:43) = 'INIT. XILINX CODE'
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
      OPLINE = DOLLAR
      OPLINE(30:40) = 'END OF FILE'
      CALL WRITELINE(OPLINE,XILDLDID,STATUS)
C
      RETURN
      END
C
