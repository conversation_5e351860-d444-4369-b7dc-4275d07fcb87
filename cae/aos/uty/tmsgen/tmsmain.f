C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 TMSMAIN.FOR
C
C  This module contains TMSGEN utility's main subroutines.
C
C  TMSGEN
C  PROCDMC
C  READHEAD
C  READOPT
C  PROCSLOT
C
C  Author: C. Lafleche, Dept. 73
C
C  Usage:  New digital sound processing system
C
C  Revision history: V1.0  Initial release              C.Lafleche    6-APR-88
C                    V1.1  Modifications for combined
C                          audio & sound utilities      P.Daigle     30-JUL-90
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                  TMSGEN
C =============================================================================
C
C  The TMSGEN utility reads a TMS DATA File, a TMS LOAD file, a XILINX
C  INITIALIZATION load file and creates a TMS DOWNLOAD file, a XILINX
C  INITIALIZATION DOWNLOAD file and a SPC PAGE DOWNLOAD file. The TGSHIP 
C  parameter tells TMSGEN whether it runs in a ship environment or not. 
C  The TGSTAT parameter is set by TMSGEN and indicates completion status.
C
      SUBROUTINE TMSGEN(TGSHIP,TGSTAT)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      CHARACTER
     &  TMP_STR*75
C
      INTEGER*2
     &  LEN_STR
C
      INTEGER*4
     &  Enable(3)
C
      LOGICAL*1
     &  SPC_IN,
     &  EOF
C
C  Initialize the library routines
C  -------------------------------
      CALL Init_Libgd(1,3)
C
C  Check if TMSGEN should set the DOWNLOAD flag
C  --------------------------------------------
C
      IF (TGSHIP) THEN
         CALL XLINK_READ(Config_S,Config_L,DMC_Num,Page_Num,Filetters,
     &                   Comp_Id,LinkFlag,Com,File_N,File_L,IERR)
C
         IF (IERR.NE.0) THEN
            CALL TERMWRITE('Error reading file')
            CALL SHOWFILE('AOSXLINK.INF')
            GO TO 20
         END IF
C
         IF (Filetters(1:2).EQ.'sn') THEN
            Sound_Gr = .TRUE.
            DATA_DIR = Config_S(1)(1:Config_L(1))//'sound/data/'
            INT_DIR = Config_S(1)(1:Config_L(1))//'sound/inter/'
         ELSE
            Sound_Gr = .FALSE.
            DATA_DIR = Config_S(1)(1:Config_L(1))//'audio/data/'
            INT_DIR = Config_S(1)(1:Config_L(1))//'audio/inter/'
         ENDIF
         L_DATA_DIR = Config_L(1)+11
         L_INT_DIR = Config_L(1)+12
C
         IF (Com(1)) THEN
            MODE = 1
            Enable(1) = 0
            Enable(2) = 1
            Enable(3) = 0
            Com(2) = .TRUE.
            CALL XLINK_WRITE(Enable,Com,IERR)
C
            IF (IERR.NE.0) THEN
               CALL TERMWRITE('Error writing file')
               CALL SHOWFILE('AOSXLINK.INF')
               GO TO 20
            END IF
         END IF
C
      END IF
C
C  Assume an error is present
C  --------------------------
C
      TGSTAT = .FALSE.
C
C  Show header
C  -----------
C
      CALL HEADER
C
C  Get file names
C  --------------
C
      TMSDATNAM = File_N(3)(1:File_L(3))
      XILLODNAM = File_N(4)(1:File_L(4))
      TMSLODNAM = File_N(5)(1:File_L(5))
      TMSDLDNAM = Filetters(1:2)//'ct.int'
      SPCDLDNAM = Filetters(1:2)//'cp.int'
      XILDLDNAM = Filetters(1:2)//'ci.int'
C
      CALL TERMREAD('Use default file names? [Y]',ANSWER)
      IF (ANSWER.EQ.'N') THEN
C
C
         TMP_STR = 'Enter TMS DATA file name ['//File_N(3)(1:File_L(3))
     &             //']'
         LEN_STR = 26 + File_L(3) + 1
         CALL TERMREAD(TMP_STR(1:LEN_STR),TMSDATNAM)
         IF (TMSDATNAM.EQ.' ') TMSDATNAM = File_N(3)(1:File_L(3))
C
C
         TMP_STR = 'Enter TMS LOAD file name ['//File_N(5)(1:File_L(5))
     &             //']'
         LEN_STR = 26 + File_L(5) + 1
         CALL TERMREAD(TMP_STR(1:LEN_STR),TMSLODNAM)
         IF (TMSLODNAM.EQ.' ') TMSLODNAM= File_N(5)(1:File_L(5))
C
C
CC         IF (Sound_Gr) THEN
CC            CALL TERMREAD('Enter TMS DOWNLOAD file name [SNCT.INT]',
CC     &                   TMSDLDNAM)
CC         ELSE
CC            CALL TERMREAD('Enter TMS DOWNLOAD file name [RFCT.INT]',
CC     &                   TMSDLDNAM)
CC         ENDIF
CC         IF (TMSDLDNAM.EQ.' ') TMSDLDNAM = Filetters(1:2)//'CT.INT'
C
C
         TMP_STR = 'Enter XILINX INITIALIZATION LOAD file name ['//
     &              File_N(4)(1:File_L(4))//']'
         LEN_STR = 44 + File_L(4) + 1
         CALL TERMREAD(TMP_STR(1:LEN_STR),XILLODNAM)
         IF (XILLODNAM.EQ.' ') XILLODNAM = File_N(4)(1:File_L(4)) 
C
C
CC         IF (Sound_Gr) THEN
CC            CALL TERMREAD('Enter XILINX INITIALIZATION DOWNLOAD file nam
CC     &e [SNCI.INT]',XILDLDNAM)
CC        ELSE
CC            CALL TERMREAD('Enter XILINX INITIALIZATION DOWNLOAD file nam
CC     &e [RFCI.INT]',XILDLDNAM)
CC         ENDIF
CC         IF (XILDLDNAM.EQ.' ') XILDLDNAM = Filetters(1:2)//'CI.INT'
C
CC         IF (Sound_Gr) THEN
CC            CALL TERMREAD('Enter SPC PAGE DOWNLOAD file name [SNCP.INT]
CC     &',SPCDLDNAM)
CC         ELSE
CC            CALL TERMREAD('Enter SPC PAGE DOWNLOAD file name [RFCP.INT]
CC     &',SPCDLDNAM)
CC         ENDIF
CC         IF (SPCDLDNAM.EQ.' ') SPCDLDNAM = Filetters(1:2)//'CP.INT'
      END IF
C

C  Add ship parameters if required
C  -------------------------------
C
      IF (TGSHIP) THEN
         SHIPNAME = Config_S(9)(:Config_L(9))
         SHIPDIR = Config_S(3)(:Config_L(3))
C
         LEN2 = STRLENG(SHIPNAME)
C
         BUFFERF = DATA_DIR(1:L_DATA_DIR)//TMSDATNAM
         TMSDATNAM = BUFFERF
C
         BUFFERF = TMSLODNAM
         TMSLODNAM = DATA_DIR(1:L_DATA_DIR)//BUFFERF
C
         BUFFERF = XILLODNAM
         XILLODNAM =  DATA_DIR(1:L_DATA_DIR)//BUFFERF
C
         BUFFERF = SHIPNAME(:LEN2)//TMSDLDNAM
         TMSDLDNAM = INT_DIR(1:L_INT_DIR)//BUFFERF
C
         BUFFERF = SHIPNAME(:LEN2)//XILDLDNAM
         XILDLDNAM =  INT_DIR(1:L_INT_DIR)//BUFFERF
C
         BUFFERF = SHIPNAME(:LEN2)//SPCDLDNAM
         SPCDLDNAM =  INT_DIR(1:L_INT_DIR)//BUFFERF
      END IF
C
C  Check if SPC board is present in CHASSIS (TMS data file)
C  --------------------------------------------------------
      SPC_IN = .FALSE.
      EOF = .FALSE.
      CALL OPNFILR(TMSDATNAM,TMSDATID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(TMSDATNAM)
         GO TO 20
      END IF
C
      DO WHILE (.NOT.EOF)
        CALL SEARFILE('XA',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
        IF (POS.NE.0) THEN
           IF (TMSDATLIN(POS+5:POS+7).EQ.'SPC') THEN
             SPC_IN = .TRUE.
             EOF = .TRUE.
           ELSE
             SPC_IN = .FALSE.
           ENDIF
        ENDIF
      ENDDO
C
      CALL CLOSEFIL(TMSDATID)
C     
C  Get job request
C  ---------------
C
      CALL TERMWRITE(EMPTY)
C
      CALL TERMREAD('Do you want the TMS DOWNLOAD file? [Y]',
     &                 ANSWER)
      IF (ANSWER.EQ.'N') THEN
         DOTMS = .FALSE.
      ELSE
         DOTMS = .TRUE.
      END IF
C
      CALL TERMREAD('Do you want the XILINX INITIALIZATION DOWNLOAD file
     &? [Y]',ANSWER)
      IF (ANSWER.EQ.'N') THEN
         DOXIL = .FALSE.
      ELSE
         DOXIL = .TRUE.
      END IF
C
      IF (SPC_IN) THEN
         CALL TERMREAD('Do you want the SPC PAGE DOWNLOAD file? [Y]',
     &                  ANSWER)
         IF (ANSWER.EQ.'N') THEN
            DOSPC = .FALSE.
         ELSE
            DOSPC = .TRUE.
         END IF
      ELSE
            DOSPC = .FALSE.
      ENDIF
C
C  Open files
C  ----------
C
      CALL OPNFILR(TMSDATNAM,TMSDATID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(TMSDATNAM)
         GO TO 20
      END IF
C
      CALL OPNFILR(TMSLODNAM,TMSLODID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(TMSLODNAM)
         GO TO 20
      END IF
C
      CALL OPNFILR(XILLODNAM,XILLODID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(XILLODNAM)
         GO TO 20
      END IF
C
      IF (DOTMS) THEN
         CALL OPNFILRW(TMSDLDNAM,TMSDLDID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(TMSDLDNAM)
            GO TO 20
         END IF
      END IF
C
      IF (DOXIL) THEN
         CALL OPNFILRW(XILDLDNAM,XILDLDID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(XILDLDNAM)
            GO TO 20
         END IF
      END IF
C
      IF (DOSPC) THEN
         CALL OPNFILRW(SPCDLDNAM,SPCDLDID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(SPCDLDNAM)
            GO TO 20
         END IF
      END IF
C
C  Create O/P files header
C  -----------------------
C
      IF (DOTMS) CALL TMSHEAD
      IF (DOXIL) CALL XILHEAD
      IF (DOSPC) CALL SPCHEAD
C
C  Read I/P files & create O/P files DMC entries
C  ---------------------------------------------
C
      CALL TERMWRITE(EMPTY)
      CALL TERMWRITE('Reading TMS DATA file')
      IF (DOTMS) CALL TERMWRITE('Reading TMS LOAD file')
      IF (DOXIL) CALL TERMWRITE('Reading XILINX INITIALIZATION LOAD file
     &')
C
      CALL SEARFILE('TITLE',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
C
      DO WHILE (POS.NE.0)
C
C        Read and process a DMC entry of TMS file
C        ------------------------------------------
C
         CALL PROCDMC(*20)
C
         POS = 1                                 !Search for next TITLE
         CALL SEARSTR('TITLE',TMSDATLIN,POS)
      END DO
C
C  Create O/P files end-of-file
C  ----------------------------
C
      IF (DOTMS) CALL TMSEOF
      IF (DOXIL) CALL XILEOF
      IF (DOSPC) CALL SPCEOF
C
      IF (DOTMS) CALL TERMWRITE('TMS DOWNLOAD file created')
      IF (DOXIL) CALL TERMWRITE('XILINX INITIALIZATION DOWNLOAD file cre
     &ated')
      IF (DOSPC) CALL TERMWRITE('SPC DOWNLOAD file created')
      IF (.NOT.DOTMS.AND..NOT.DOXIL.AND..NOT.DOSPC) THEN
         CALL TERMWRITE('NO output file created')
      END IF
      CALL TERMWRITE(EMPTY)
C
C  Close files
C  -----------
C
      CALL CLOSEFIL(TMSDATID)
      CALL CLOSEFIL(TMSLODID)
      CALL CLOSEFIL(XILLODID)
      IF (DOTMS) CALL CLOSEFIL(TMSDLDID)
      IF (DOXIL) CALL CLOSEFIL(XILDLDID)
      IF (DOSPC) CALL CLOSEFIL(SPCDLDID)
C
C  Normal exit
C  -----------
C
      CALL TERMWRITE('TMSGEN successfully terminated')
      CALL RSTWIND
      TGSTAT = .TRUE.           !Indicate TMSGEN successfully terminated
      CALL Wait_Time(4.0)
      RETURN
C
C  Error occured
C  -------------
C
 20   CALL RSTWIND
      RETURN                    !Leave with TGSTAT = .FALSE.
C
      END
C
C
C
C =============================================================================
C                                  PROCDMC
C =============================================================================
C
C  This subroutine reads and checks a TMS file DMC entry information and
C  creates O/P files DMC entry.
C
      SUBROUTINE PROCDMC(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      LOGICAL*1
     &  EOF
C
C  Search for TITLE name & DMC number
C  ----------------------------------
C
      CALL READHEAD(*20)
C
C  Search for OPTION
C  -----------------
C
      CALL READOPT(*20)
C
C  Do O/P files DMC entry header
C  -----------------------------
C
      IF (DOTMS) CALL TMSDMC
      IF (DOXIL) CALL XILDMC
      IF (DOSPC) CALL SPCDMC
C
C  Do TMS file SLOT entries
C  --------------------------
C
      SLOTCNT = 0
      SLOTNUM = 0
C
      POS = 1
      CALL SEARSTR('XA',TMSDATLIN,POS)
C
      DO WHILE (POS.NE.0)
C
         SLOTCNT = SLOTCNT + 1
         IF (SLOTCNT.GT.MAXSLOT) THEN
            CALL TERMWRITE('Too many SLOT entries')
            RETURN 1
         END IF
C
         CALL PROCSLOT(*20)
C
         CALL SEARFILE('XA',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
      END DO
C
      IF (SLOTCNT.NE.MAXSLOT) THEN
         CALL TERMWRITE('Missing SLOT entry')
         RETURN 1
      END IF
C
C  Do multi-slot TMS if selected
C  -----------------------------
C
      IF (OPTMTMS) CALL MTMSDMC(*20)
C
      RETURN
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  READHEAD
C =============================================================================
C
C  This subroutine reads and checks header information.
C
      SUBROUTINE READHEAD(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      LOGICAL*1
     &  EOF
C
      DATLINCNT = 0
C
C  Search for TITLE name
C  ---------------------
C
      CALL SEARSTR('=',TMSDATLIN,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      CALL SCANSTR(TMSDATLIN,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing TITLE name')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      TITLE = TMSDATLIN(POS:)
C
C  Search for DMC = <number>
C  -------------------------
C
      CALL SEARFILE('DMC',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing DMC keyword')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      CALL SEARSTR('=',TMSDATLIN,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      CALL SCANSTR(TMSDATLIN,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing DMC number')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      READ (TMSDATLIN(POS:POS+2),'(BN,Z2)',ERR=10) DMC
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(TMSDATNAM,DATLINCNT)
      CALL TERMWRITE(TMSDATLIN)
      RETURN 1
C
      END
C
C
C
C =============================================================================
C                                  READOPT
C =============================================================================
C
C  This subroutine reads and checks the OPTION statement. This statement is
C  optional.
C
      SUBROUTINE READOPT(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
      LOGICAL*1
     &  EOF
C
C  Initialize option flags
C  -----------------------
C
      OPTMTMS = .FALSE.
C
C  Search for OPTION = <option1>, <option2>, etc
C  ---------------------------------------------
C
      CALL SEARFILE('OPTION',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('No OPTION selected')
C
      ELSE
C
C        OPTION found, search for { = }
C        ------------------------------
C
         CALL SEARSTR('=',TMSDATLIN,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / = /')
            CALL SHOWLINE(TMSDATNAM,DATLINCNT)
            RETURN 1
         END IF
C
C        Search for <option(i)>
C        ----------------------
C
         CALL GETSYMBL(SYMBOL,TMSDATID,TMSDATLIN,DATLINCNT,POS)
C
         DO WHILE (POS.NE.0)
            IF (SYMBOL.NE.',') THEN
C
C              Multi TMS option
C              ----------------
C
               IF (SYMBOL.EQ.'MULTI_TMS') THEN
                  OPTMTMS = .TRUE.
                  CALL TERMWRITE('OPTION MULTI TMS selected')
               END IF
C
C              Add other option checks here
C              ----------------------------
C
            END IF
            CALL GETSYMBL(SYMBOL,TMSDATID,TMSDATLIN,DATLINCNT,POS)
         END DO
C
         CALL SEARFILE('XA',TMSDATID,TMSDATLIN,DATLINCNT,POS,EOF)
      END IF
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(TMSDATNAM,DATLINCNT)
      CALL TERMWRITE(TMSDATLIN)
      RETURN 1
C
      END
C
C
C
C =============================================================================
C                                  PROCSLOT
C =============================================================================
C
C  This subroutine reads and checks a slot line.
C
      SUBROUTINE PROCSLOT(*)
      IMPLICIT NONE
C
      INCLUDE 'tms.inc'
C
C  Search for slot number
C  ----------------------
C
      READ (TMSDATLIN(POS:POS+1),'(I2)',ERR=10) SLOT
      IF (SLOT.NE.SLOTCNT) THEN
         CALL TERMWRITE('Wrong slot number')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
      POS = POS + 2                             !Position after slot number
C
      CALL SEARSTR('=',TMSDATLIN,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(TMSDATNAM,DATLINCNT)
         CALL TERMWRITE(TMSDATLIN)
         RETURN 1
      END IF
C
C  Search for card type
C  --------------------
C
      CALL SCANSTR(TMSDATLIN,POS)
C
      IF (POS.EQ.0) THEN                        !Empty slot
         IF (DOTMS.AND..NOT.OPTMTMS) CALL TMSEMPTY
         IF (DOXIL) CALL XILEMPTY
         IF (DOSPC) CALL SPCEMPTY
         RETURN
C
      ELSE
         TYPE = TMSDATLIN(POS:POS+2)            !Read card type
C
C        DAC Type
C        --------
C
         IF (TYPE.EQ.'DAC') THEN
            IF (DOTMS.AND..NOT.OPTMTMS) CALL TMSEMPTY
            IF (DOXIL) CALL XILEMPTY
            IF (DOSPC) CALL SPCEMPTY
C
C        DSG Type
C        --------
C
         ELSE IF (TYPE.EQ.'DSG'.OR.TYPE.EQ.'DSP'.OR.TYPE.EQ.'SPC') THEN
            IF (DOTMS.AND..NOT.OPTMTMS) CALL TMSSLT(*20)
            IF (DOTMS.AND.OPTMTMS) CALL MTMSSLT
            IF (DOXIL) CALL XILSLT(*20)
            IF (TYPE.EQ.'SPC') THEN
                IF (DOSPC) CALL SPCSLT(*20)
            ELSE 
                IF (DOSPC) CALL SPCEMPTY
            END IF
C
C        Erraneous type
C        --------------
C
         ELSE
            CALL TERMWRITE('Unknown card type')
            CALL SHOWLINE(TMSDATNAM,DATLINCNT)
            CALL TERMWRITE(TMSDATLIN)
            RETURN 1
         END IF
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(TMSDATNAM,DATLINCNT)
      CALL TERMWRITE(TMSDATLIN)
 20   RETURN 1
C
      END
C

