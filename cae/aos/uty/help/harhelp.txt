!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!        This file is the HARMONY utility HELP text file         !
!                                                                !
!        Rev 2.0 - 20 June 1989 - G. De <PERSON>                    !
!        Rev 2.1 - 17 Oct 1989 - G. <PERSON>                     !
!        Rev 2.2 - 18 Feb 1990 - G. <PERSON>                     !
!                                                                !
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!
!  FORMAT: *xx,y where xx represent the help INDEX (All the lines
!                         after the *xx and before the next
!                         star line will be printed for the HELP.
!                      y is the number of help page for the help item.
!
!     INDEX:  0-Harmony main menu
!             1-Not used
!             2-Edit: table type
!             3-Wave table type menu
!             4-Other table type menu
!             5-External table type menu
!             6-Table number: only one table accepted
!             7-Table number: many table accepted
!             8-Phase selection
!             9-** Not used  **
!            10-Wave type selection
!            11-Input values for other
!            12-Harmonic number selection
!            13-Amplitude selection
!            14-Size selection
!            15-Title selection
!            16-Get external data type
!            17-** Not used  **
!            18-** Not used  **
!            19-** Not used  **
!            20-** Not used  **
!            21-Source number selection
!            22-New DSG selection
!            23-** Not used  **
!            24-** Not used  **
!            25-Delete menu
!            26-** Not used  **
!            27-** Not used  **
!            28-** Not used  **
!            29-** Not used  **
!            30-List menu
!            31-** Not used  **
!            32-** Not used  **
!            33-Configuration menu
!            34-Plot waveform X-Axis
!            35-Plot menu
!
!          END : End of text delimiter for each index
!          $$  : will cause a pause and a clear screen-reset for a new page
!
!              ** MAXIMUM OF 13 LINES PER PAGE **
!
*0,5
               HARMONY : SOUND WAVE GENERATION UTILITY
               ---------------------------------------
   HARMONY is used to create, process and generate data for the DSG wave
generators.  Each one uses a table containing the points of the wave to be
output in the simulator.   These points are created by HARMONY.
   The following commands are avalaible:

   EDIT : Used to create a new table or to edit an existing one
          by specifying the harmonic content (amplitude,frequency
          and wave type), the title and the size of the table (WAVE).
          It is also possible to create a table with breakpoints
          (BREAKPOINTS) or by entering the table point by point from
          an external data file (EXTERNAL).
$$
   DELETE : Use to delete complete table(s) created with EDIT.

   ASSIGN : Assign the tables to the sources. You can specify
            any of the tables created to any of the sources
            existing (SOUND SYSTEM max sources is 80 for TONE & 10 for SLAP).
   LIST :   Give information about tables and sources:
      -SUMMARY   : Summary about the content of all the tables (harmonics,
                   title,...) and the assignment to the sources.
      -POINTS    : List all the points in one table in hexadecimal format.
      -TABLE     : List the content of the tables.

         For each LIST functions, you can select the output media: screen or
   printer.
$$

   PLOT : Give graphic information about the tables:
      -WAVEFORM  : Plot to the tables to the printer.  If you specify a
                   full image of the X-Axis, it will enlarge the X axis in 4
                   sections (4 plots).
      -FFT       : Plot the Fast Fourier Transform of the table in a graph
                   form with frequency and amplitude axis.
                   (This option is not available on version 2.3)

   LOAD : The complete process of creating the download file to be
          sent to the DSG card.

$$
   CONF : Will change the configuration of the parameters:
          - Number of DSG, slot number, HARMONY data file, etc...
          After the new parameters have been entered, the old data file
          will be saved and the new one will be restored.

   SAVE : Will save in the current data file all the datas modified
          since last save or the start of the session.

     The command CONF and SAVE are only available on the main menu.
$$
   BOX : Will redraw the screen.

   EXIT or X : Will exit the present session and save all the
          changes made to the datas.
   QUIT : Will leave HARMONY with unsaved session.  If some data has
        been changed, you will have to confirm that you want to QUIT.
END
*1,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*2,1
       When the table is empty or doesn't exist, you can select
three different types of table.

  - WAVE    : you can enter sine waves, square waves, sawtooth waves or
              triangle waves.
  - BREAKPOINTS : you can enter a table with breakpoints
  - EXTERNAL: you can enter table points from an external data file.

       The default type is WAVE (<CR> entered).
END
*3,2
WAVE : PHASE, HARM, TITLE, SIZE, COPY, AUTO [or TRANSFERT] >

  PHASE will modify the phase between each harmonic of the table.
  TITLE will modify the title for the table.
  HARM  will enter the harmonic number (basic frequency of
          the table), the amplitude of each harmonic and the
          type of each harmonic (SINUS, TRIANGLE, SAWTOOTH, SQUARE).
  SIZE  will modify the size of the table.
  COPY  will copy the contents of another table to this one.

        * The contents of this table will be deleted.
        * The copied table needs to be a WAVE type table.
$$
  AUTO      will toggle the automated harmonic type mode.  When ON, the type
            is automatically set to SINEWAVE.  This mode is usefull when you
            enter information from a narrowband plot.
  TRANSFERT will initiate a Real Time Load (RTL) transfert of the table to
            the wavegen memory replacing an existing table.
            Only table number over 64 can be transferred.
  QUIT      will leave this editing table without keeping the modification
            made to it.   You will return to the table number prompt.
            Check is made to be sure that you don't want to keep the mods.
  EXIT,X    will leave and keep the changes you have made (You need to
            save to the data file with SAVE in the main menu if you
            want to save this new table in HARMONY).  This new table
            will be kept in memory for other commands like LIST,etc...
END
*4,2
BREAKPOINT TABLE : POINTS, TITLE, SIZE, COPY [or TRANSFERT] >

      BREAKPOINTS will modify the breakpoints (X-Y).
      TITLE       will modify the title for the table.
      SIZE        will modify the size of the table.
      COPY        will copy the contents of another table to this one.

        * The contents of this table will be deleted.
        * The copied table needs to be a WAVE type table.
$$
  AUTO      will toggle the automated harmonic type mode.  When ON, the type
            is automatically set to SINEWAVE.  This mode is usefull when you
            enter information from a narrowband plot.
  TRANSFERT will initiate a Real Time Download (RTL) transfert of the table to
            the wavegen memory replacing an existing table.
            Only table number over 64 can be transferred.
  QUIT      will leave this editing table without keeping the modification made
            to the table.  You will return to the table number prompt.
            Check is made to be sure that you won't keep change made.
  EXIT,X    will leave and keep the changes you have made (You need to
            save to the data file with SAVE in the main menu if you
            want to save this new table in HARMONY).  This new table
            will be kept in memory for other commands like LIST,etc...
END
*5,2
EXTERNAL : GET, TITLE, SIZE, COPY [or TRANSFERT] >

  INPUT will enter the points from the data file.
  TITLE will modify the title for the table.
  SIZE  will modify the size of the table.
  COPY  will copy the contents of another table to this one.

        * The contents of this table will be deleted.
        * The copied table needs to be a WAVE type table.
$$
  AUTO      will toggle the automated harmonic type mode.  When ON, the type
            is automatically set to SINEWAVE.  This mode is usefull when you
            enter information from a narrowband plot.
  TRANSFERT will initiate a Real Time Download (RTL) transfert of the table to
            the wavegen memory replacing an existing table.
            Only table number over 64 can be transferred.
  QUIT      will leave this editing table without keeping the modification made
            to the table.  You will return to the table number prompt.
            Check is made to be sure that you won't keep change made.
  EXIT,X    will leave and keep the changes you have made (You need to
            save to the data file with SAVE in the main menu if you
            want to save this new table in HARMONY).  This new table
            will be kept in memory for other commands like LIST,etc...
END
*6,1

        Enter the table number you want to be processed. The format is:

        TABLE # > n or QUIT/EXIT/X

                    where n is the table number from 1 to 70.

         * Note that table 65 to 70 are used for Real Time Download only.

         Harmony will then used this table for the current section.
END
*7,1
        Enter the table number you want to be processed. The format is:

        TABLE # >1-4,8-11,...,38,45 or ALL or QUIT/EXIT/X

         Harmony will process all the tables between the first number
       and the last number separate by a dash.  You can specify
       as many group of table as you want just by separating them with a
       comma.
       The table number should be between 1 to 70.

         * Note that table 65 to 70 are used for Real Time Download only.
END
*8,1
      PHASE     will select the phase between each harmonic of the
                table:
                 -ZERO is all the harmonics are in phase.
                 -SCHROEDER'S RULE is different phase for each harmonic
                    according to schroeder's rule (see user guide for
                    rule formula).
                 -RANDOM is random phase between each harmonic.
END
*9,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*10,2
       Wavetype selection:
         SIN : The harmonic will be built using a sinus function
               SIN (2*PI*(n/TSIZE)*H) where n is the point number
                                            TSIZE is the size of the table
                                            H is the harmonic number
         SQU : The harmonic will be built with a square wave of
               freqency H
         TRI : The harmonic will be built using a triangle wave of
               frequency H
         SAW : The harmonic will be built using a sawtooth wave of
               frequency H
$$
         <CR>: If the harmonic already exist in the table, it will
               took the previous type.
               If the harmonic does not exist, it will took SIN as
               the default one.

         QUIT,X or EXIT will return to AMPLITUDE prompt.

END
*11,3
       You can enter BREAKPOINTS wave by specifying there X and Y values
       until you reached the number of breakpoints you want.
       You cannot specify point which does not exist unless you point to
       the input new breakpoint section (The last one + 1).

       In order to modify an existing breakpoint, you have to point to it
       otherwise HARMONY will read your input as trying to insert a new
       breakpoint with an existing X value.
       If you enter a value between 0.0 to 100.0, that will specify the
       X value and will increment the breakpoint number by 1 after you
       have specified the Y value.
$$
       The following commands are available
         for X VAL> Pii will set the edited breakpoint to ii if it exist
                    <CR> will increment pointer by 1
                    L will got to the last breakpoint+1 (To enter a new one).
                    PREV will display the previous 20 breakpoints until you
                         have reached 1.
                    NEXT will display the next 20 breakpoints until you have
                         reached the highest one.
                    BRii will set the display area to the breakpoint ii.
                    Dii will delete the point ii.
                    QUIT,X or EXIT will exit breakpoint editing session.
                    xx.xx specify the X value of the point in real format
                          from 0.0 to 100.0 in %, it will automatically
                          increment by 1 the point number.
$$
         for  Y VAL > xx.xx specify the Y value of the point in real format
                            from 0.0 to 100.0 in %.

       Note:  If the number of breakpoint exceeds 20, the displayed section
              of the table will always be the one which include the edited
              breakpoint.
              If you delete a breakpoint that is not include in the current
              screen, you will stay at the current screen section but
              breakpoint display values will be readjusted.

       The maximum number of breakpoints for HARMONY rev 2.0 is 99.
END
*12,2
       HARMONIC # > iii which is the harmonic number between 1.0 to 327.67.
                    Rii will change the harmonic display area if you
                        are editing a table with more than 20 harmonics.
                        Note that ii is the harmonic pointer (1 to the number
                             of harmonic in the table)
                    DHii will change the harmonic display area if you
                        are editing a table with more than 20 harmonics.
                        Note that ii is the harmonic number.

                        The display area will automatically vary if the
                        harmonic entered is outside the current display
                        area.
$$

                    QUIT,X or EXIT will exit the harmonic editing session.

       The maximum number of harmonic in one table is 99 for HARMONY rev 2.0
       or higher.
END
*13,1
       You can select the harmonic amplitude in dB between 0.00 to 120.00.

       The amplitude of the harmonic is used only as a reference because
       the table is normalized.
       The dB amplitude level is then used only as a relative
       amplitude factor between the harmonics.

       If you set 0 for the amplitude, the harmonic will be deleted
       if it exist otherwise HARMONY will print an error message.

       QUIT,X or EXIT will return to HARMONIC # prompt.
END
*14,1
       The size of the table can be selected between four
       predefine values: 512, 1024, 2048 and 4096.

       The size of the table affects "only" the signal to noise
       ratio.  The output frequency is not affected because
       the WAVEGEN circuit (DSG) takes care of that size to calculate the
       output rate of the table.

       QUIT,X or EXIT will leave without affecting the current table size.
END
*15,1
       Modify the table title (max 30 characters).

END
*16,2
       It is possible to get a table from the external world of HARMONY.

       The file containing the data should be in the directory specified
       in this prompt:

          Enter file directory [ <CR>=DEFAULT ] >

          If you enter <CR>, the default directory listed before will be used
          otherwise the entered directory will be used.

$$
       The file name which contains the data should be a valid filename for
       the operating system:

          Enter external data filename [ EXTERNAL [.DAT] ] >

       The number of values in the data file should be at least the
       size of the table created.  All exceeding values will be ignored.
END
*17,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*18,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*19,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*20,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*21,1
hereici
       SOURCE # >1-4,8-11,...,38,45 or ALL or QUIT/EXIT/X

         Harmony will associate all the sources between the first number
       and the last number separate by a dash.  You can specify
       as many sourced as you want just by separating them with a comma.
       The source number should be between 1 and 80.

       If you specify ALL, all the sources will be associated to the table.
END
*22,1
       Select a new SLOT number for the corresponding DSG.

        ASSIGN: New DSG slot # > 

       The selected slot has to exist in order to be processed.

END
*23,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*24,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*25,1
        If you select ALL(1), all the tables will be deleted.
        A verification of this selection will be done in case you did not
        want to do so.  As a remainder, if you have done so by mistake, you
        can quit HARMONY and the data file will not be modified.

        If you select specific (2), you will be able to delete
        specific table by entering the table number.
END
*26,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*27,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*28,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*29,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*30,1
      LIST : SUMMARY, POINTS or TABLES >

      If you select SUMMARY, you will have the summary of all the
      inputs entered to HARMONY:
      If you select POINTS, you will be prompt for the table
      number:
          after you enter the table number(s), the table(s) will be
      listed point by point.
      If you select TABLES, the display of the table harmonics, breakpoints,
      data, phase, size and title will be done on the screen.

END
*31,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*32,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*33,2
       CONF : CHANGE or LIST >

             The configuration command allows to change the parameters stored
             in the HARMONY information logger file (HARMONY.INF).

             If you select CHANGE, you will be prompted to enter a new set
             of parameters.
                - Number of DSG in the sound chassis (Max is 1 for Rev 2.0)
                - DSG slot number in decimal (1 to 27)
$$
                - Harmony data file name.  If you don't want the letter
                  from the simulator configuration to be automatically
                  inserted, put '&' character before the filename (This cha-
                  racter will remain hidden to the system). The maximum
                  number of characters is 11 for filename without '&' and
                  12 with it.
                - Laser printer queue if the site can accept it (VAX only).
             If you select LIST, a list of the current parameters will be
             view on screen.  This command is automatically called when you
             enter HARMONY the first time and no log file exist.
END
*34,1
     ** NO TEXT FOR THIS HELP SECTION **

     By the way, you should not get this help message, something
wrong, please contact personel which are in charge of this utility!

END
*35,1
   PLOT : Give graphic information about the tables:

          The WAVEFORM command provides the user with a hardcopy of
       the waveform stored in table.
          The plot is submitted to the printer in the print
       queue.
          The file is automatically deleted after it has been
       submitted to the printer.
         Harmony will prompt if you want a full image of the plots.
         If you answer Y, the table will be divided in 4 plots.
   FFT : Give graphic information on the frequency content of the table.
         The table is processed with a Fast Fourier Transform algorithm.
         (This option is not available on version 2.3)
END
*
* End of file
*
