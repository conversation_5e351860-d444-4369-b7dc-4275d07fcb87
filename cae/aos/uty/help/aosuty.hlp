**1
       AOSUTY is the main access program for the Digital Sound System (DSS)
and Digital Audio System (DAS) utilities.
       It calls all other utilities needed to process the data files that
describe the configuration of each system.

       It is possible to call each utility by entering their respective
names.  The first letters which are not ambiguous with other commands can 
be entered to execute the utility.

  HARMONY... To generate the table contents used by the DSG card.
  TMSGEN.... To process the TMS code for each card.
  TSDGEN.... To configure the communication of the TSD bus.
  WAVEGEN... To configure the structure of the WAVEGEN (DSG card).
  FIRGEN.... To generate filter coefficient for DAS.
  FORMGEN... To generate the download file processed by SIMEX/SMU.

     There are other commands available to manage the utility's environment.

  BOX........... Redraws the screen
  CONF.......... Reconfigure the utility
                       - Directories, logical names, etc...
                       - Edit the simulator configuration list.
                       - Get display of the different parameters.
  GROUP........ Toggle the selected group between AUDIO and SOUND.
  MODE......... Toggle the operating mode flag (UPDATE or STANDALONE).
                UPDATE : All utilities needed to generate the final
                         download file are called one after the other when
                         one of the utility has processed new data.
                STANDALONE : Return to AOSUTY after user exits from
                             utility called
  EXIT,X,QUIT.. Exits the utility
**2
  BOX.......... Redraws the screen
  EXIT,X,QUIT.. Exits the utility
  GENERAL...... To reconfigure the directories and shipname which is common
                to both audio and sound systems.  You select also where the
                utilities are executed (SITE or STF).  All the information
                is kept in the CONF file and recalled every time you run.
  AUDIO......|  
  SOUND......|> To modify the simulator configuration list, the DMC and memory
                page number. This is mainly used when a simulator has different
                engine types, tail number, etc... All the entries are stored
                in the CONF file.

                If no configuration is set or recovered, the default letter 
                is 'c'.
**
