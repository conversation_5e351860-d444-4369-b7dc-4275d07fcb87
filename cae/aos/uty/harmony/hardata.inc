C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARDATA.INC                                              **
C   **  Function : Declaration of common variables                          **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON><PERSON>M.Alagar    Date: 13 December 1987  **
C   **  Rev 1.0             G. <PERSON>                   30 June 1988      **
C   **  Rev 2.0             G. <PERSON>                   10 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1990       **
C   **  Rev 2.3             P. Daigle                     25 Jul 1990       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     Input read_command definition
C     -----------------------------
      CHARACTER*80 COMMAND
      CHARACTER*60 HELPMES(36)
      CHARACTER*8 CNT_TCR(2), STAT_TCR(2), P_CNT_TCR(2), P_OUT_TCR(2,16)
     &           ,FREZ_TCR, CRG_TCR(2) 
      CHARACTER*3 WTYPE(MAX_HNUM)
C
      INTEGER*2 I,J,K,M,II,JJ,KK,MM     !Different loop counter
C
      INTEGER*2 TOP,BOTTOM    !Used to calculate amount of ext. mem.
C
      LOGICAL*1  OUTMODE,
     &           USER_OK,               !User ID processed
     &           TABLSORT,
     &           QUIT_FLG,
     &           VALUE,
     &           ON_SITE,
     &           RTD_FLAG,
     &           UPDATE,
     &           DOWNLOAD,
     &           WGSIZE,
     &           HLPOPEN,
     &           DATASAVE,
     &           VAXSEL,
     &           SIN_MOD,                    !Sinewave mode for WAVE type
     &           HELP_CLEAN,
     &           LASER_FLAG,
     &           LinkFlag(4),                !On site flag & spares...
     &           Com(6)                      !Communication flags for XLINK
C
      INTEGER*2  WAVETYPE(MAX_TAB+1,MAX_HNUM),
     &           EXT_NUM,
     &           FILE_Q(12),
     &           PARA_L(MAX_PARM),           !Length of parameters
     &           BUFF(MAX_BUF),
     &           TEMP_TYPE(MAX_HNUM),
     &           TMP_TBSZ,
     &           L_FILDAT,
     &           QUITYPE,
     &           OTHP(MAX_TAB+1,MAX_OTH),
     &           TMP_PVAL(MAX_OTH),
     &           DSG_NUMB,
     &           NUM_LIST,
     &           IT_LIST(MAXSOUR),
     &           FILE_L(12),
     &           DSG_TYPE(2),
     &           Config_Length(12),
     &           Comp_Id          
C
       INTEGER*4 INT_NUM,           ! Table number I4
     &           PHASE4,            ! Integer*4 value of phase type
     &           IERR,              !Error logger
     &           ITEM,              !
     &           L_COM              ! Length of command entered by Read_Command
C
       REAL*4 W_LONG,
     &        TEMP_HARM(MAX_HNUM),        !Temporary table harmonic
     &        HARM(MAX_TAB+1,MAX_HNUM),     !Harmonic type
     &        NUMBER,REAL_NUM,
     &        SIGAMPL(MAX_HNUM),!Harmonic amplitude in volt
     &        PHA(MAX_HNUM)    ,!Harmonic phase factor
     &        TEMP_AMPL(MAX_HNUM),
     &        EXTP(MAX_EXT,4096),
     &        TMP_EXPO(4096),
     &        TBL(MAX_BUF),
     &        BUFFER(2,0:MAX_BUF),
     &        AMPL(MAX_TAB+1,MAX_HNUM),
     &        TMP_XVAL(MAX_OTH),
     &        TMP_YVAL(MAX_OTH),
     &        OTHX(MAX_TAB+1,MAX_OTH),
     &        OTHY(MAX_TAB+1,MAX_OTH),
     &        AMPLITUDE,
     &        HARM_NUM,
     &        TYP
C
       INTEGER*2 
     &           SL_NBI(MAX_DSG),            !Slot number in integer
     &           EXTSIZ(MAX_EXT),            !External table sizes
     &           EXTBLN(MAX_TAB+1),          !External table code
     &           TMP_EXTSZ,                  !Temporary external table size
     &           TMP_HMSZ,                   !Temporary number of harmonic
     &           TMP_OSZ,                    !Temporary other table #of break.
     &           OTHSZ(MAX_TAB+1),           !Other table number of breakpoints
     &           PHASE,                      !Temporary table phase
     &           SOURCE(MAXSOUR,MAX_DSG),    !Assign table to source array
     &           SAV_TBL(MAX_TAB),           !Saved table number array
     &           PHASEL(MAX_TAB+1),          !Table phase
     &           TAB_NUM,                    !Table number currently edited
     &           HMSIZ(MAX_TAB+1),           !Number of harmonics in the table
     &           TABSIZE(MAX_TAB+1) ,        !Table sizes
     &           L_EXTDIR,                   !External data file directory
     &           L_DATA_DIR,
     &           L_INT_DIR,
     &           TBLNUM,                     !Number of table
     &           LLASQ,                      !Laser printer queue length
     &           LLINE                       !Length of line
C
       CHARACTER  INPLINE*80,                !General input line
     &            FILINE(30)*255,            !General line field
     &            PARA_S(MAX_PARM)*255      ,!Parameters to be passed
     &            EXTDATFIL*50              ,!External type data file
     &            EXTDATDIR*255             ,!External type data file directory
     &            FILE_DATA*80,              !Data filename
     &            PROJNAME*60               ,!Project name
     &            LASERQ*80                 ,!Laser printer queue
     &            SL_NB(MAX_DSG)*2          ,!Slot number in ASCII hex
     &            TEMP_MODATE*17            ,!Last modified field
     &            USER_ID*26                ,!User ID field
     &            TEMP_NAME*26              ,!User ID field
     &            MODATE(MAX_TAB)*17        ,!Last modified field
     &            NAME(MAX_TAB)*26          ,!User ID field
     &            TEMP_TIT*30               ,!Temp.Title for the table
     &            TITLE(MAX_TAB)*30         ,!Title for the table
     &            BLANK*80                  ,!Blank line
     &            SEND*80                   ,!All uses line
     &            FILE_S(12)*52             ,!General file strings
     &            DMC*2                     ,!DMC number 
     &            DATA_DIR*80               ,
     &            INT_DIR*80                ,
     &            Page_Num*2                ,!Page number
     &            Filetters*3               ,!SN or RF + config letter
     &            Config_String(12)*80       !Logical names in XLINK file 
C
      REAL*4 TEMPR,FFTH(4100),FFTA(2100),FFTB(2100)

      INTEGER*2 TEMPI,TEMPIX
C
C     Common block for read keyboard routines
C     ---------------------------------------
      LOGICAL*4 GOLD_KEY
      INTEGER*2 REPL_L(REPL_N),REPL_CODE(REPL_N),MAX_CHAR_L
      CHARACTER*4 REPL_STR(REPL_N)
C
C     This block dispacht the memory of the common block of HARMONY.  On SEL,
C     it used EXTENDED memory allocation to reduce the executable task size
C     for the data in normal task user space.  This allow increase of code
C     space for debug and inhibit(?) any overflow of the user task space.
C
C
        COMMON / INTEGER2 / TMP_HMSZ,TAB_NUM,TBLNUM,PHASE,TMP_TBSZ,
     &                      TMP_OSZ,SAV_TBL,TABSIZE,EXTBLN,EXTSIZ,
     &                      TMP_EXTSZ,EXT_NUM,LLINE,L_FILDAT,DSG_NUMB,
     &                      SL_NBI,QUITYPE,L_EXTDIR,LLASQ,REPL_L,
     &                      REPL_CODE,MAX_CHAR_L,NUM_LIST,
     &                      TEMPI,TEMPIX,Config_Length,Comp_Id,TOP,
     &                      WAVETYPE,BUFF,TEMP_TYPE,OTHP,TMP_PVAL,OTHSZ,
     &                      SOURCE,PHASEL,HMSIZ,PARA_L,FILE_Q,FILE_L,
     &                      IT_LIST,BOTTOM,L_DATA_DIR,L_INT_DIR,DSG_TYPE
C
        COMMON / INTEGER4 / INT_NUM,L_COM,ITEM
C
        COMMON / LOGICAL / SIN_MOD,OUTMODE,UPDATE,DOWNLOAD,WGSIZE,
     &                     HLPOPEN,DATASAVE,HELP_CLEAN,TABLSORT,
     &                     QUIT_FLG,VALUE,ON_SITE,VAXSEL,USER_OK,
     &                     LASER_FLAG,RTD_FLAG,LinkFlag,Com
C
        COMMON / LOGICAL4 / GOLD_KEY
C
        COMMON / CHARACTER / TEMP_TIT,TEMP_MODATE,USER_ID,TEMP_NAME,
     &                       INPLINE,FILE_DATA,SL_NB,PROJNAME,
     &                       EXTDATFIL,EXTDATDIR,LASERQ,BLANK,SEND,
     &                       REPL_STR,COMMAND, CNT_TCR,STAT_TCR,
     &                       P_CNT_TCR,P_OUT_TCR,
     &                       FREZ_TCR,CRG_TCR,Config_string,DMC,
     &                       Page_Num,Filetters,TITLE,PARA_S,FILE_S,
     &                       FILINE,WTYPE,HELPMES,MODATE,NAME,
     &                       DATA_DIR,INT_DIR
C
        COMMON / REAL4 / HARM_NUM,AMPLITUDE,TYP,W_LONG,TEMPR,REAL_NUM,
     &                   NUMBER,BUFFER,AMPL,OTHX,OTHY,HARM,TBL,
     &                   TEMP_AMPL,TMP_XVAL,TMP_YVAL,TEMP_HARM,EXTP,
     &                   TMP_EXPO,SIGAMPL,PHA,FFTH,FFTB,FFTA
