C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARPLOT.FOR                                              **
C   **  Function : Plot function subroutines                                **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON>. <PERSON>             Date: 24 March 1987     **
C   **  Rev 1.0             G. <PERSON>                   30 July 1988      **
C   **  Rev 2.0             G. <PERSON>                   20 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **    --                P. Daigle                     25 Jul 1990       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  PLOT                                                                **
C   **  WAVEFORM                                                            **
C   **  SEND_PLOT                                                           **
C   **  FFT_GRAPH                                                           **
C   **  SEND_FFT                                                            **
C   **  DFFT                                                                **
C   **  REALTR                                                              **
C   **  FFT                                                                 **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     ==================
      SUBROUTINE PLOT(*)
C     ==================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INTEGER*4
     & Stat  
C
      LOGICAL*1
     & END_READ
C
      DATA PROMPT /'PLOT : WAVEFORM or FFT > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'WAVEFORM'/
      DATA MENU(7)/'FFT'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,PROMPT,25,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,7,MENU,ITEM,IERR)
C
         IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                 CALL WAVEFORM
               ELSEIF(ITEM.EQ.7) THEN
                 CALL FFT_GRAPH
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(35)                !Help asked
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
            ENDIF
         ENDIF
      ENDDO
C
      RETURN 1
C
      END
C
C
C ===================================================================
C                            WAVEFORM
C ===================================================================
C
      SUBROUTINE WAVEFORM
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR3                  !Various error messages
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INTEGER*4
     & Stat
C
      LOGICAL*1 END_READ,             ! Read source flag
     &          PLOTONE              ,!Plot created flag
     &          TAB_FOUND             ! True if table is found
C
      DATA ERROR1/'%PLOT : Some of the table does not exist'/
      DATA ERROR3/'%PLOT : Invalid table number'/
C
      DATA PROMPT /'WAVEFORM : Enter TABLE # > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'ALL'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,27,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-6,MENU,ITEM,IERR)
C
          PLOTONE = .FALSE.
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN        !ALL
                 NUM_LIST = TBLNUM
                 DO I=1,NUM_LIST
                   IT_LIST(I)=SAV_TBL(I)
                 ENDDO
               ENDIF
               PLOTONE = .TRUE.
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(7)               !Help asked
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ELSE
C
C            TABLE numbers entered: get them
C            -------------------------------
             CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
             IF(IERR.EQ.0) THEN
C
C              Look if all tables exist
C              ------------------------
               I = 1
               TAB_FOUND = .TRUE.
               DO WHILE(I.LE.NUM_LIST.AND.TAB_FOUND)
                  TEMPI = IT_LIST(I)
                  CALL TAB_EXIST(TEMPI,TAB_FOUND)
                  IF(.NOT.TAB_FOUND) THEN
                     CALL ERR_MESS(ERROR1,40,-1,*30)
 30                  CONTINUE
                  ENDIF
                  I = I+1
               ENDDO
C
               PLOTONE = TAB_FOUND
          ELSE
             PLOTONE = .FALSE.
             CALL ERR_MESS(ERROR3,28,-1,*31)
 31          CONTINUE
             ENDIF
          ENDIF
C
          IF(PLOTONE) THEN
             CALL SEND_PLOT
          ENDIF
      ENDDO
C
      RETURN
C
      END
C
C     ====================
      SUBROUTINE SEND_PLOT
C     ====================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       CHARACTER
     & PLOTAB(2)*80       ,!Plot identification string
     & XAXIS*20           ,!X axis descriptor
     & YAXIS*97           ,!Y axis descriptor
     & XFORM*20           ,!X axis format
     & YFORM*20           ,!X axis format
     & ERRORMSG(5)*40     ,!Plot error bank
     & SIZE*4             ,!Character size of a table
     & STABLE*2           ,!Character table number of a table
     & PLOTNUM*1          ,!Number of image in character
     & SHIPNAME*20         !Ship name
C
      LOGICAL*4
     & INTERPOL            !Interpolation flag
C
      LOGICAL*1
     & END_READ,
     & FIRST /.TRUE./     ,!First pass flag
     & XFLAG              ,!X axis format flag (T=INTEGER)
     & YFLAG              ,!Y axis format flag (T=INTEGER)
     & PLOTITLE           ,!Plot the title flag
     & PLOTCOM            ,!Plot the title flag
     & PLOTBOT             !Plot the title flag
C
      INTEGER*2
     & PLOTCOUNT          ,!Number of image for plot
     & BUFMAX             ,!Maximum plot buffer element
     & SBUFMAX            ,!Spare maximum plot buffer element
     & BUFMIN             ,!Minimum plot buffer element
     & BUFSTEP            ,!Stepping plot buffer
     & COUNT              ,!Internal counter to fill plot buffer
     & STEP               ,!Stepping plot buffer
     & PLOTERR            ,!Plot error logger
     & TITLEN             ,!Title length
     & TABCALC             !Which table to calculate
C
      INTEGER*2
     & P_NUMBER           ,!Number of element in PLOT buffer
     & INCNT              ,! Increment counter
     & PRCNT               !Printer line counter
C
      INTEGER*4 NUMBER4,   !I4 number for GRAPH call compatibility
     & Stat               ,!
     & FILERR             ,!File open error
     & SELPHA             ,!
     & YNUMBINT           ,!Number of interval in Y axis
     & NUMBERX            ,!Number of element in PLOT buffer
     & STBL               ,!
     & XAXLEN             ,!X axis number length
     & YAXLEN              !Y axis number length
C
      CHARACTER
     &          PHSTR*25,                !Various output messages
     &          FMTH1*31                ,!
     &          FMTH2*28                ,!
     &          FMTH3*27                ,!
     &          FMTO1*28                ,!
     &          FMTO2*27                 !
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
C
      REAL*4
     & XSKIPITX           ,!Number of X element skipped between each interval
     & XSKIPIT            ,!Number of X element skipped between each interval
     & XINCH10            ,!10 intervals size for X
     & YINCH10            ,!10 intervals size for Y
     & XMIN               ,!Minimum X values in plot buffer
     & YMIN               ,!Minimum Y values in plot buffer
     & XMAX               ,!Maximum X values in plot buffer
     & YMAX                !Maximum Y values in plot buffer
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      DATA ERRORMSG /'     ***  XSKIPIT  IS TOO SMALL ***     ',
     &               '        *** YINCH IS TOO BIG ***        ',
     &               '** XSKIPIT: TOO SMALL, YINCH:TOO BIG ** ',
     &               '* Log laser printer x is 0.0, invalid * ',
     &               '*  Error when writing to laser output  *' /
      DATA   ERROR1/'%PLOT-WAVEFORM:Table     waveform plot is in proces
     &s '/
      DATA ERROR2/'%HARMONY : Error #      on file HARPLOT_xxx.LIS'/
C
      DATA PROMPT/'WAVEFORM : Do you want the X-Axis in one plot [Y]> '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'YES'/
      DATA MENU(7)/'NO'/
C
      IF(FIRST) THEN
         FIRST=.FALSE.
         FMTH1 = '(1X,A4,1X,''I'',13(2X,A3,1X,''|''))'
         FMTH2 = '(1X,A4,1X,''I'',13(F6.2,''|''))'
         FMTH3 = '(1X,A4,1X,''I'',13(F6.2,''|''))'
         FMTO1 = '(1X,A5,1X,''I'',15(3X,I2,''|''))'
         FMTO2 = '(1X,A5,1X,''I'',15(F5.1,''|''))'
      ENDIF
C
      IF(.NOT.LASER_FLAG) THEN
C
C       Output mode on the printer:create HARPLOT_xxx.LIS
C       -------------------------------------------------
        CALL FIL_OPEN(5,1,FILERR)
        IF(FILERR.NE.0) RETURN
      ELSE
C
        XAXIS = 'TABLE POINT'
        YAXIS = 'AMPLITUDE       '
        XFORM = '(3X,I4)'
        XFLAG = .TRUE.
        XAXLEN=7
        YFORM = '(9X,I6)'
        YFLAG = .TRUE.
        YAXLEN=15
C
      ENDIF
C
C     Find which kind of plot
C     -----------------------
      END_READ = .FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(0,22,1,PROMPT,51,COMMAND,L_COM,Stat)
         IF(L_COM.GT.0)THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,7,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               END_READ = .TRUE.
               IF(ITEM.EQ.7) THEN
C
C                Set number of plot and buffer step for full image
C                -------------------------------------------------
                 PLOTCOUNT = 4
                 PLOTCOM   = .TRUE.
               ELSEIF(ITEM.EQ.6) THEN
C
C                Set number of plot and buffer step for one image
C                ------------------------------------------------
                 PLOTCOUNT=1
                 PLOTCOM   = .FALSE.
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(34)                !Help asked
            ELSE                      !QUIT,EXIT or X command
               RETURN
            ENDIF
           ENDIF
         ELSE
           END_READ = .TRUE.
C
C          Set number of plot and buffer step for one image
C          ------------------------------------------------
           PLOTCOUNT=1
           PLOTCOM   = .FALSE.
         ENDIF
      ENDDO
C
      IF(Config_Length(10).LT.20) THEN
         SHIPNAME = Config_String(10)(1:Config_Length(10))
     &              //BLANK(1:20-Config_Length(10))
      ELSE
         SHIPNAME = Config_String(10)(1:Config_Length(10))
      ENDIF
C
      DO II=1,NUM_LIST
C
C        Print working message for user friendly waiting time
C        ----------------------------------------------------
         WRITE(ERROR1(22:24),'(I3)') IT_LIST(II)
         CALL MES23(0,ERROR1)
C
C        Print the title,number and size of that table
C        ---------------------------------------------
         SELPHA = PHASEL(IT_LIST(II))
         CALL TRANS_PHASE(SELPHA,PHSTR)
         PLOTAB(1)(1:80) = BLANK(1:80)
         PLOTAB(1)(1:10) = '** PLOT ** '
         PLOTAB(1)(11:19) = '  SIZE = '
         WRITE(SIZE,'(I4)',ERR=701) TABSIZE(IT_LIST(II))
 701     CONTINUE
         PLOTAB(1)(20:23) = SIZE
         PLOTAB(1)(30:42) = ' TABLE TYPE: '
         IF(HMSIZ(IT_LIST(II)).GT.0) THEN
            PLOTAB(1)(43:50) = 'WAVE    '
            PLOTAB(1)(55:62) = ' PHASE: '
            PLOTAB(1)(63:78) = PHSTR(9:24)
         ELSEIF(OTHSZ(IT_LIST(II)).GT.0) THEN
            PLOTAB(1)(43:52) = 'BREAKPOINT'
         ELSEIF(EXTBLN(IT_LIST(II)).GT.0) THEN
            PLOTAB(1)(43:50) = 'EXTERNAL'
         ENDIF
         YAXIS(17:97) = PLOTAB(1)(1:80)
C
C        Process the table to get the points
C        -----------------------------------
         TABCALC = IT_LIST(II)
         CALL CREATE_TABLE(TABCALC)
C
C        ==========================================
C        Compute the parameters passe for that plot
C        ==========================================
C
C         A-Common factors whatever the number of image
C           -------------------------------------------
C
C           1-Set the plot flag for interpolation
C             -----------------------------------
         INTERPOL = .TRUE.
         PLOTBOT  = .TRUE.
C
C           2-Set size of the plot on line printer
C             ------------------------------------
         XINCH10 = 8.5   !10 intervals
         YINCH10 = 2.0   !2.0 is the maximum !!!!!!
C
C           3-Set Y interval specification on line printer
C             --------------------------------------------
         YNUMBINT = 16     !(YSKIPINT=NUMBER/YNUMBINT)
C
C           4-Find the MAX and MIN of Y
C             -------------------------
         YMAX = -32768.0
         YMIN =  32767.0
         DO J=1,TABSIZE(IT_LIST(II))
            IF(BUFF(J).GT.INT(YMAX)) YMAX=FLOAT(BUFF(J))
            IF(BUFF(J).LT.INT(YMIN)) YMIN=FLOAT(BUFF(J))
         ENDDO
C
C           5-Set X interval specification on line printer
C             --------------------------------------------
         XSKIPIT = TABSIZE(IT_LIST(II))/(10.0*PLOTCOUNT)
C                   !XSKIPIT>(XMAX-XMIN)*XINCH/270 is a major requirement !!!!
C
C           6-Set the maximum and minimum to fill the buffer
C             ----------------------------------------------
         BUFMIN = 0
         BUFMAX = TABSIZE(IT_LIST(II))/(PLOTCOUNT)
         INCNT = BUFMAX
C
C        Set the step according to plot image
C        ------------------------------------
         IF (PLOTCOUNT.EQ.4) THEN
            BUFSTEP = 1
         ELSE
            BUFSTEP = 4  !Line printer is OK
         ENDIF
C
C        Plot all the image of the table
C        -------------------------------
         DO I=1,PLOTCOUNT
C
C          B-Specific factors to each image
C            ------------------------------
C          1-Fill the plot buffer (X and Y)
C            ------------------------------
           COUNT=0
           DO J=BUFMIN,BUFMAX,BUFSTEP
              BUFFER(2,COUNT)=FLOAT(J)            !X axis
              BUFFER(1,COUNT)=FLOAT(BUFF(J+1))    !Y axis
              COUNT = COUNT + 1
           ENDDO
C
C          2-Set the last point of the plot to the first one
C            -----------------------------------------------
           IF(BUFMAX.EQ.TABSIZE(TABCALC)) THEN
              BUFFER(1,COUNT-1) = FLOAT(BUFF(1))
           ENDIF
C
C          3-Set number of points in the buffers
C            -----------------------------------
           P_NUMBER = COUNT
C
C          4-Set the plot flags for TITLE
C            ----------------------------
           PLOTAB(2)(1:80) = BLANK(1:80)
           PLOTAB(2)(5:9) = 'Plot '
           WRITE(PLOTNUM,'(I1)',ERR=707) I
 707       CONTINUE
           PLOTAB(2)(10:10) = PLOTNUM
           PLOTAB(2)(11:15) = ' of 4'
           IF(I.EQ.1)THEN
              PLOTITLE = .TRUE.
           ELSE
              PLOTITLE = .FALSE.
           ENDIF
C
C          5-Find the MIN and MAX of X
C            -------------------------
           XMIN = FLOAT(BUFMIN)
           XMAX = FLOAT(BUFMAX)
C
           IF(.NOT.LASER_FLAG) THEN
C
C           LINE PRINTER plot
C           -----------------
            NUMBERX = P_NUMBER-1 !Protect number because it is overwritten
C                             somewhere in the plot subroutine
            XSKIPITX=XSKIPIT !Protect x skip interval because....
C
            CALL CREATE_PLOT(NUMBERX,XSKIPITX,
     &                  YNUMBINT,XMIN,XMAX,YMIN,YMAX,
     &                  PLOTAB(1),PLOTAB(2),PLOTITLE,PLOTBOT,PLOTCOM,
     &                  INTERPOL,XINCH10,YINCH10,PLOTERR,SHIPNAME,
     &                  TABCALC)
C
C
C           Print harmonic content at the bottom
C           ------------------------------------
            STBL = IT_LIST(II)
C
C           Print the table according to their type
C           ---------------------------------------
            IF (HMSIZ(STBL) .GT. 0) THEN
C
              WRITE(PLT_UNIT,14,ERR=702,IOSTAT=IERR )
C
C             Translate the integer type array into its character equivalent
C             --------------------------------------------------------------
              CALL TRANS_TYPE(STBL)
              SELPHA = PHASEL(STBL)
              CALL TRANS_PHASE(SELPHA,PHSTR)
C
              PRCNT = 1
              DO WHILE(PRCNT.LE.HMSIZ(STBL))
               IF(PRCNT.EQ.31)THEN  !Skip a page
                  WRITE(PLT_UNIT,18,ERR=702,IOSTAT=IERR )    !New page on printer
               ENDIF
               TEMPI = MIN(HMSIZ(STBL)+0,(PRCNT+12))
               TEMPIX = PRCNT+1
               WRITE(PLT_UNIT,FMTH1,ERR=702,IOSTAT=IERR )
     &            'TYPE',WTYPE(PRCNT),(WTYPE(J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,FMTH2,ERR=702,IOSTAT=IERR )
     &            'HM #',HARM(STBL,PRCNT),(HARM(STBL,J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,FMTH3,ERR=702,IOSTAT=IERR )
     &            'AMPL',AMPL(STBL,PRCNT),(AMPL(STBL,J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,*,ERR=702,IOSTAT=IERR ) ' '
               PRCNT=PRCNT+13
              ENDDO
C
             ELSE IF (OTHSZ(STBL) .GT. 0) THEN
C
              WRITE(PLT_UNIT,16,ERR=702,IOSTAT=IERR )
C
C             OTHER type table content
C             ------------------------
              PRCNT = 1
              DO WHILE(PRCNT.LE.OTHSZ(STBL))
               IF(PRCNT.EQ.31)THEN  !Skip a page
                  WRITE(PLT_UNIT,19,ERR=702,IOSTAT=IERR )    !New page on printer
               ENDIF
               TEMPI = MIN(OTHSZ(STBL)+0,(PRCNT+14))
               TEMPIX = PRCNT+1
               WRITE(PLT_UNIT,FMTO1,ERR=702,IOSTAT=IERR )
     &          'BPNT ',OTHP(STBL,PRCNT),(OTHP(STBL,J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,FMTO2,ERR=702,IOSTAT=IERR )
     &          'X VAL',OTHX(STBL,PRCNT),(OTHX(STBL,J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,FMTO2,ERR=702,IOSTAT=IERR )
     &          'Y VAL',OTHY(STBL,PRCNT),(OTHY(STBL,J),J=TEMPIX,TEMPI)
               WRITE(PLT_UNIT,*,ERR=702,IOSTAT=IERR ) ' '
               PRCNT=PRCNT+15
              ENDDO
C
             ELSE IF (EXTBLN(STBL).GT.0) THEN
C
              WRITE(PLT_UNIT,17,ERR=702,IOSTAT=IERR )
              WRITE(PLT_UNIT,233,ERR=702,IOSTAT=IERR )
     &                  EXTSIZ(EXTBLN(STBL))
C
             ENDIF
C
           ELSE
C
C           LASER PRINTER plot
C           ------------------
            CALL FIL_OPEN(5,1,FILERR)
            IF(FILERR.NE.0) THEN
              CALL MES23(1,BLANK) !Erase previous submit message
              RETURN
            ENDIF
            NUMBER4 = P_NUMBER
            CALL GRAPH(NUMBER4,XMIN,XMAX,YMIN,YMAX,8,10,1,1,4,5,4,
     &                 1,0,PLOTAB,SHIPNAME,XAXIS,YAXIS,XFORM,YFORM,
     &                 XAXLEN,YAXLEN,XFLAG,YFLAG,PLOTITLE,PLOTCOM,
     &                 PLOTERR,TABCALC)
C
            CALL FIL_OPEN(5,2,FILERR)
C
           ENDIF
C
C          Increase MAX and MIN for next image
C          -----------------------------------
           BUFMIN = BUFMIN + INCNT
           BUFMAX = BUFMAX + INCNT
C
           IF (PLOTERR.GT.0)THEN
C
C             Error on plot parameters
C             ------------------------
              SEND=ERRORMSG(PLOTERR)//BLANK(1:40)
              CALL MES23(0,SEND)
C
           ENDIF
C
         ENDDO
      ENDDO
C
      IF(.NOT.LASER_FLAG) THEN
C
         CALL FIL_OPEN(5,2,FILERR)   !Close HARPLOT_xxx.LIS file
         IF(FILERR.NE.0)THEN
            CALL MES23(1,BLANK) !Erase previous submit message
            RETURN
         ENDIF
      ENDIF
C
C     Submit to printer the plot file before living the routine
C     ---------------------------------------------------------
      CALL FIL_OPEN(6,1,FILERR)   !Open the submit job file
      IF(FILERR.EQ.0)THEN
         CALL FIL_OPEN(6,2,FILERR)   !Close submit file
      ENDIF
C
      CALL MES23(1,BLANK) !Erase previous submit message
C
      GOTO 704
702   CALL GET_ERR_STR(IERR,ERROR2(19:23))
      CALL ERR_MESS(ERROR2,47,1,*704)
704   CONTINUE
C
      RETURN
C
14    FORMAT(' ',/,'  List of the HARMONICS content of the table',/,
     &             '  ------------------------------------------')
16    FORMAT(' ',/,'  List of the BREAKPOINTS content of the table',/,
     &             '  --------------------------------------------')
17    FORMAT('   ')
18    FORMAT('1 List of the HARMONICS content of the table (Continued)',
     &     /,'  ------------------------------------------------------',
     &     /)
19    FORMAT('1 List of the BREAKPOINTS content of the table (Continued)
     &',/,'  --------------------------------------------------------',
     &/)
233   FORMAT(10X,'  External table of ',I4,' points read')
C
      END
C
C
C ==========================================================================
C                            FFT_GRAPH
C ==========================================================================
C
      SUBROUTINE FFT_GRAPH
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      CHARACTER*80
     & ERROR1                  !Various error messages
C
      DATA ERROR1/'%FFT : Not available YET '/
C
      CALL ERR_MESS(ERROR1,24,-1,*33)
 33   CONTINUE
      RETURN
C
      END
C
