#! /bin/csh
#
set AOSDISK "`printenv aos_disk`"
set LIBDIR  "${AOSDISK}/sound/aos/uty/library"
set EXEDIR  "${AOSDISK}/sound/aos/uty/exec"
set CAELIB  "/cae/lib"
#
setenv cae_smp_work `pwd`
simex get cdb.o
mv `revl cdb.o` cdb.o
simex get sp0c0init.for
fpc sp0c0init.for
f4l sp0c0init.for
mv `revl sp0c0init.obj` sp0c0init.o 
#
	xlf -C -qcharlen=1024 harmony.o hardisp.o haredit.o harlib1.o \
harlib2.o harplot.o harhelp.o harunix.o harlist.o harcalc.o harload.o \
cdb.o sp0c0init.o /cae/lib/disp.a \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o $(EXEDIR)/harmony
#
#
del cdb.o
del sp0c0init.*
#
setenv cae_smp_work "/cae1/ship"