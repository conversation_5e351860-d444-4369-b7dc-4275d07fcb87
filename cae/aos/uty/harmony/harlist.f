C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARLIST.FOR                                              **
C   **  Function : All LIST and ASSIGN function subroutines                 **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  See harmony.f file...                                               **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  LIST                                                                **
C   **  SUMMARY                                                             **
C   **  PRINTABLE                                                           **
C   **  LISTABL                                                             **
C   **  LIST_DATA                                                           **
C   **  ASSIGN                                                              **
C   **  DISP_POINT                                                          **
C   **  GET_SOURCE                                                          **
C   **  GET_NEWDSG                                                          **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C -- An explanation of each of these routines along with a description
C    of the parameters may be found in the appropriate subroutine.
C
C     ==================
      SUBROUTINE LIST(*)
C     ==================
      IMPLICIT NONE
C
C     This subroutine lists one of several items : data points,
C     directory, table contents or a summary of the entire table data.
C     The summary is available only on the printer.
C     ----------------------------------------------------------------
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*4
     & Stat
C
      LOGICAL*1
     &          END_READ               ! List flag
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      DATA PROMPT /'LIST : SUMMARY, POINTS or TABLES > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'MODE'/
      DATA MENU(7)/'SUMMARY'/
      DATA MENU(8)/'POINTS'/
      DATA MENU(9)/'TABLE'/
C
      CALL MODE_SET(2)
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,35,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,9,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN        !MODE
                  CALL MODE_SET(1)
               ELSEIF(ITEM.EQ.7) THEN    !SUMMARY
                  CALL SUMMARY
               ELSEIF(ITEM.EQ.8) THEN    !POINTS
                  CALL LIST_DATA
               ELSEIF(ITEM.EQ.9) THEN    !TABLE
                  CALL LISTABL
               ENDIF
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(2)            !Display output mode
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(30)                !Help asked
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ENDIF
      ENDDO
C
      CALL MODE_SET(-1)
      RETURN 1
      END
C
C     ==================
      SUBROUTINE SUMMARY
C     ==================
      IMPLICIT NONE
C
C     This subroutine generates a file which contains the summary of
C     the contents of all the tables.  This file is then routed to
C     the printer and deleted.
C     ---------------------------------------------------------------
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
      LOGICAL*1 TAB_FOUND                ! True if the table is found
      INTEGER*2
     &          OFFSET,                  !Wait offset return
     &          OUT(MAX_TAB)            ,!Sorted pointers
     &          PGCOUNT                 ,!Page line counter
     &          STBL                     !
C
      INTEGER*4
     &          CODE/1/,                 !Sort code means sort pointers
     &          UNIT,                    !
     &          PRCNT                    !Printer line counter
C
      CHARACTER*80
     & TEXT*100,
     & ERROR1                  !Various error messages
C
      CHARACTER
     &          STYPE*10                ,!Table type string
     &          SPEC*1                  ,!Special character
     &          BAC*1                    !Backspace character for double height
C
      COMMON /TAB/ TAB_FOUND
C
      DATA ERROR1/'%LIST_SUMMARY : Error #      on file HARSUM.LIS, summ
     &ary aborted'/
C
      BAC = CHAR(8)
C
C     Sort the table numbers in ascending order
C     -----------------------------------------
      CALL SORT(CODE,SAV_TBL,OUT,TBLNUM)
C
      IF(OUTMODE)THEN
C
C       PRINT to the line printer: open the output file/write header section
C       --------------------------------------------------------------------
        PGCOUNT = 1
        CALL FIL_OPEN(10,1,IERR)
        IF(IERR.NE.0) RETURN     !Leave if can't open file
C
        UNIT=SML_UNIT
        CALL PRINT_HEAD(PGCOUNT,UNIT)
        WRITE(UNIT,900,ERR=999,IOSTAT=IERR ) BAC,'SUMMARY OF TABLES: ',
     &                 PROJNAME,' * Configuration : ',Filetters(3:3)
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
C
        WRITE(UNIT,100,ERR=999,IOSTAT=IERR )
C
C       Write the table #, its size and title in a directory format
C       -----------------------------------------------------------
        DO I = 1,TBLNUM
          STBL = SAV_TBL(OUT(I))
          IF (HMSIZ(STBL).GT.0) THEN
             STYPE = 'WAVE      '
          ELSEIF(OTHSZ(STBL).GT.0) THEN
             STYPE = 'BREAKPOINT'
          ELSE
             STYPE = 'EXTERNAL  '
          ENDIF
          IF(STBL.GT.MAX_TAB_LOAD)THEN
             SPEC = '*'
          ELSE
             SPEC = ' '
          ENDIF
          WRITE(UNIT,150,ERR=999,IOSTAT=IERR ) STBL,SPEC,TABSIZE(STBL),
     &                                         STYPE,TITLE(STBL)
          IF(I.EQ.40.AND.TBLNUM.GT.40) THEN
              WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
              WRITE(UNIT,*,ERR=999,IOSTAT=IERR)
     &        '             * : Table for Real Time Download (RTL) ONLY'
              PGCOUNT=PGCOUNT+1
              CALL PRINT_HEAD(PGCOUNT,UNIT)
              WRITE(UNIT,100,ERR=999,IOSTAT=IERR )
          ENDIF
        ENDDO
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR)
     &   '             * : Table for Real Time Download (RTL) ONLY'
C
C       PRINT CONTENT of each table
C       ---------------------------
        PRCNT = 55
        DO K = 1,TBLNUM
C
            STBL = SAV_TBL(OUT(K))
C
C           Print the table contents
C           ------------------------
            CALL PRINTABLE(STBL,PGCOUNT,PRCNT,UNIT,IERR)
            IF(IERR.NE.0) GOTO 999
C
        ENDDO
C
C       PRINT table/source assignment
C       -----------------------------
        DO MM=1,DSG_NUMB
C
C       If DSG Type is TONE...
C       ----------------------
        IF (DSG_TYPE(MM).EQ.1) THEN
         PGCOUNT = PGCOUNT+1
         CALL PRINT_HEAD(PGCOUNT,UNIT)
         WRITE(UNIT,600,ERR=999,IOSTAT=IERR ) BAC,'SOURCE / TABLE ASSOCI
     &ATIONS    SLOT XA',SL_NB(MM)
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR )
     &'                                                 DSG TYPE: TONE'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR)
     &   '           T00 : Table has no source associated'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' A  : WAVE MODEL BLOCK (30
     &kHz) '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) '      ---------------------
     &----'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR )         '           Group #1
     &        Group #2        Group #3        Group#4'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         DO I = 1,11
           IF(I.EQ.1) THEN
             WRITE(UNIT,500,ERR=999,IOSTAT=IERR ) 'Repetitive ',I,
     &          SOURCE(I,MM),I+11,SOURCE(I+11,MM),I+22,SOURCE(I+22,MM),
     &          I+33,SOURCE(I+33,MM)
           ELSEIF(I.EQ.8)THEN
             WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
             WRITE(UNIT,500,ERR=999,IOSTAT=IERR ) 'Intermodul ',I,
     &          SOURCE(I,MM),I+11,SOURCE(I+11,MM),I+22,SOURCE(I+22,MM),
     &          I+33,SOURCE(I+33,MM)
           ELSE
             WRITE(UNIT,500,ERR=999,IOSTAT=IERR ) '           ',I,
     &          SOURCE(I,MM),I+11,SOURCE(I+11,MM),I+22,SOURCE(I+22,MM),
     &          I+33,SOURCE(I+33,MM)
           ENDIF
         ENDDO
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' B1 : WAVE BLOCK (30 kHz)'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) '      -------------------'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         DO I = 1,2
           WRITE(UNIT,501,ERR=999,IOSTAT=IERR ) I+44,SOURCE(I+44,MM),
     &                                          I+46,SOURCE(I+46,MM)
         ENDDO
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' B2 : WAVE BLOCK (15 kHz)'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) '      -------------------'
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
         DO I = 1,16
           WRITE(UNIT,501,ERR=999,IOSTAT=IERR ) I+48,SOURCE(I+48,MM),
     &                                          I+64,SOURCE(I+64,MM)
           IF((I/2)*2.EQ.I)THEN
             WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
           ENDIF
         ENDDO
C
C       If DSG type is OTHER
C       --------------------
C
       ELSE IF(DSG_TYPE(MM).EQ.2) THEN
        PGCOUNT = PGCOUNT+1
        CALL PRINT_HEAD(PGCOUNT,UNIT)
        WRITE(UNIT,600,ERR=999,IOSTAT=IERR ) BAC,'SOURCE / TABLE ASSOCIA
     &TIONS    SLOT    : XA',SL_NB(MM)
         WRITE(UNIT,*,ERR=999,IOSTAT=IERR )
     &'                                               DSG TYPE: OTHER'
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR)
     &   '           T00 : Table has no source associated'
        WRITE(UNIT,*,ERR=999,IOSTAT=IERR ) ' '
C
        DO I = 1,20
          WRITE(UNIT,500,ERR=999,IOSTAT=IERR ) '           ',I,
     &         SOURCE(I,MM),I+20,SOURCE(I+20,MM),I+40,SOURCE(I+40,MM),
     &         I+60,SOURCE(I+60,MM)
        ENDDO
       ENDIF
       ENDDO
C
C       Close the file, print it and then delete it
C       -------------------------------------------
        CALL FIL_OPEN(10,2,IERR)
C
        GOTO 987
 999    CALL GET_ERR_STR(IERR,ERROR1(24:28))
        CALL ERR_MESS(ERROR1,64,-1,*987)
 987    CONTINUE
C
      ELSE
C
C       SUMMARY to the screen: write header
C       -----------------------------------
        CALL CL_DISP
        TEXT = BLANK
        TEXT(1:99)= '         '//ESC//'[4mTABLE#'//ESC//'[0m     '//
     &ESC//'[4mSIZE'//ESC//'[0m  '//ESC//'[4m   TYPE   '//ESC//'[0m '//E
     &SC//'[4mTITLE                         '//ESC//'[0m  '
        CALL Term_Write(7,1,TEXT,99)
C
C       Sort the table numbers in ascending order
C       -----------------------------------------
        CALL SORT(CODE,SAV_TBL,OUT,TBLNUM)
C
C       Write the table #, its size and title in a directory format
C       -----------------------------------------------------------
        PRCNT=9
        DO I = 1,TBLNUM
             STBL = SAV_TBL(OUT(I))
             IF (HMSIZ(STBL).GT.0) THEN
                STYPE = 'WAVE      '
             ELSEIF(OTHSZ(STBL).GT.0) THEN
                STYPE = 'BREAKPOINT'
             ELSE
                STYPE = 'EXTERNAL  '
             ENDIF
             TEXT = BLANK
             IF(STBL.GT.MAX_TAB_LOAD)THEN
                TEXT(8:8) = '*'
             ENDIF
             WRITE(TEXT(10:13),'(I4)',ERR=201,IOSTAT=IERR)STBL
             WRITE(TEXT(21:24),'(I4)',ERR=201,IOSTAT=IERR)
     &                                         TABSIZE(STBL)
             TEXT(27:36) = STYPE
             TEXT(38:67) = TITLE(STBL)
C
 201         IF(IERR.NE.0) THEN
                TEXT=BLANK
                TEXT(1:40)='********* PRINT ERROR ON TABLE *********'
             ENDIF
C
             CALL Term_Write(PRCNT,1,TEXT,80)
C
             IF(PRCNT.EQ.22)THEN
               PRCNT=8
               CALL WAIT_CONT(1,OFFSET)
               IF(OFFSET.EQ.0)THEN
                  CALL Term_Write(7,1,BLANK,80)
                  GOTO 111
               ELSEIF(OFFSET.EQ.99) THEN
                  GOTO 112
               ENDIF
               CALL CL_DISP2
             ENDIF
             PRCNT=PRCNT+1
        ENDDO
C
 112    CONTINUE
C
        IF(PRCNT.GE.10) THEN
           CALL WAIT_CONT(1,OFFSET)
           IF(OFFSET.EQ.0)THEN
             CALL Term_Write(7,1,BLANK,80)
             GOTO 111
           ELSEIF(OFFSET.EQ.99)THEN
             GOTO 113
           ENDIF
        ENDIF
        CALL Term_Write(7,1,BLANK,80)
C
C       Write out the contents of each table
C       ------------------------------------
        PRCNT=8
        DO I = 1,TBLNUM
             TAB_NUM = SAV_TBL(OUT(I))
             TAB_FOUND = .TRUE.
             CALL REST_TABLE(2,*111)
             CALL WAIT_CONT(1,OFFSET)
             IF(OFFSET.EQ.0)THEN
               GOTO 111
             ELSEIF(OFFSET.EQ.99)THEN
               GOTO 113
             ENDIF
        ENDDO
C
C       Write the Source / table associations to the screen
C       ---------------------------------------------------
 113    <USER> <GROUP>(-1,-1)
        CALL WAIT_CONT(2,OFFSET)
 111    CALL Term_Write(7,1,BLANK,80)
        CALL CL_DISP
      ENDIF
C
100   FORMAT(' ','TABLE#',5X,'SIZE ',3X,'TYPE ',8X,'TITLE ',/,
     &       ' ','------',5X,'---- ',3X,'---- ',8X,'----- ')
150   FORMAT(' ',I4,3X,A1,3X,I4,4X,A10,3X,A30)
500   FORMAT(1X,A11,'SRC',I2.2,'=T',I2.2,
     &           7X,'SRC',I2.2,'=T',I2.2,
     &           7X,'SRC',I2.2,'=T',I2.2,
     &           7X,'SRC',I2.2,'=T',I2.2)
501   FORMAT(1X,20X,'SRC',I2.2,'=T',I2.2,
     &          16X,'SRC',I2.2,'=T',I2.2)
600   FORMAT(' ',18X,A1,A,A2)
900   FORMAT(10X,A1,A19,A20,A19,A1)
C
      RETURN
      END
C
C     ==============================================
      SUBROUTINE PRINTABLE(STBL,PAGE,LINE,UNIT,IERR)
C     ==============================================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER*3
     &          PHSTR*25                ,!Various output messages
     &          FMT0*15                 ,!External table format
     &          FMTH1*32                ,!Harmonic table format (type)
     &          FMTH2*32                ,!Harmonic table format (#,amplitude)
     &          FMTO1*32                ,!Breakpoints table format (point)
     &          FMTO2*32                 !Breakpoints table format (X,Y)
C
      INTEGER*2 STBL,                    !Table number
     &          NUMB,                    !Required number of line for table
     &          RWPNT,                   !Read/write pointer
     &          RWPNT2,                  !Read/write pointer
     &          PRCNT,                   !Previous counter
     &          OLDM400,                 !Previous Modulo 400 of external table data
     &          MOD400,                  !Modulo 400 of external table data
     &          SELPHA,                  !Selected phase character
     &          MIN,MAX,                 !MIN/MAX not external
     &          PAGE                     !Page counter

C
       INTEGER*4
     &          STBL_I4,
     &          UNIT,                    !Output file unit
     &          LINE                     !Line counter
C
       LOGICAL*1
     &  FIRST /.TRUE./                   !First loop flag
C
      IF(FIRST) THEN
         FIRST=.FALSE.
         FMTH1 = '(4X,A4,1X,''I'',10(2X,A3,1X,''|''))'
         FMTH2 = '(4X,A4,1X,''I'',10(F6.2,''|''))'
         FMTO1 = '(4X,A5,1X,''I'',10(2X,I2,1X,''|''))'
         FMTO2 = '(4X,A5,1X,''I'',10(F5.1,''|''))'
      ENDIF
C
C     Print the table according to their type
C     ---------------------------------------
      IF (HMSIZ(STBL) .GT. 0) THEN
C
C        Skip a page
C        -----------
         NUMB = ((HMSIZ(STBL)/10)+1)*4 + 7
         IF((55-LINE).LT.NUMB) THEN
           PAGE = PAGE+1
           CALL PRINT_HEAD(PAGE,UNIT)
           LINE = 4
         ELSE
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) '   **********************
     &****************************************************   '
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
           LINE = LINE+3
         ENDIF
C
C        WAVE type table content
C        -----------------------
         WRITE(UNIT,200,ERR=909,IOSTAT=IERR) STBL,TITLE(STBL),
     &                          TABSIZE(STBL)
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
         LINE = LINE + 2
C
C        Translate the integer type array into its character equivalent
C        --------------------------------------------------------------
         STBL_I4 = STBL
         CALL TRANS_TYPE(STBL_I4)
         SELPHA = PHASEL(STBL)
         CALL TRANS_PHASE(SELPHA,PHSTR)
C
         WRITE(UNIT,251,ERR=909,IOSTAT=IERR) HMSIZ(STBL)
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
         LINE = LINE + 2
C
         PRCNT = 1
         DO WHILE(PRCNT.LE.HMSIZ(STBL))
            RWPNT = MIN(HMSIZ(STBL)+0,(PRCNT+9))
            RWPNT2 = PRCNT+1
            WRITE(UNIT,FMTH1,ERR=909,IOSTAT=IERR )
     &            'TYPE',WTYPE(PRCNT),(WTYPE(J),J=RWPNT2,RWPNT)
            WRITE(UNIT,FMTH2,ERR=909,IOSTAT=IERR )
     &            'HM #',HARM(STBL,PRCNT),(HARM(STBL,J),J=RWPNT2,RWPNT)
            WRITE(UNIT,FMTH2,ERR=909,IOSTAT=IERR  )
     &            'AMPL',AMPL(STBL,PRCNT),(AMPL(STBL,J),J=RWPNT2,RWPNT)
            WRITE(UNIT,*,ERR=909,IOSTAT=IERR  ) ' '
            PRCNT=PRCNT+10
            LINE = LINE + 4
         ENDDO
C
      ELSE IF (OTHSZ(STBL) .GT. 0) THEN
C
C        OTHER type table content
C        ------------------------
C
C        Skip a page
C        -----------
         NUMB = ((OTHSZ(STBL)/10)+1)*4 + 7
         IF((55-LINE).LT.NUMB) THEN
           PAGE = PAGE+1
           CALL PRINT_HEAD(PAGE,UNIT)
           LINE = 4
         ELSE
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) '   **********************
     &****************************************************   '
           WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
           LINE = LINE+3
         ENDIF
C
         WRITE(UNIT,200,ERR=909,IOSTAT=IERR) STBL,TITLE(STBL),
     &                                 TABSIZE(STBL)
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR) ' '
         LINE = LINE + 2
C
         WRITE(UNIT,252,ERR=909,IOSTAT=IERR) OTHSZ(STBL)
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR ) ' '
         LINE = LINE + 2
C
         PRCNT = 1
         DO WHILE(PRCNT.LE.OTHSZ(STBL))
            RWPNT = MIN(OTHSZ(STBL)+0,(PRCNT+9))
            RWPNT2 = PRCNT+1
            WRITE(UNIT,FMTO1,ERR=909,IOSTAT=IERR)
     &          'BPNT ',OTHP(STBL,PRCNT),(OTHP(STBL,J),J=RWPNT2,RWPNT)
            WRITE(UNIT,FMTO2,ERR=909,IOSTAT=IERR)
     &          'X VAL',OTHX(STBL,PRCNT),(OTHX(STBL,J),J=RWPNT2,RWPNT)
            WRITE(UNIT,FMTO2,ERR=909,IOSTAT=IERR)
     &          'Y VAL',OTHY(STBL,PRCNT),(OTHY(STBL,J),J=RWPNT2,RWPNT)
            WRITE(UNIT,*,ERR=909,IOSTAT=IERR) ' '
            PRCNT=PRCNT+10
            LINE = LINE + 4
         ENDDO
C
      ELSEIF(EXTBLN(STBL).GT.0) THEN
C
C        Skip a page
C        -----------
         IF(LINE.GT.3) THEN
           PAGE = PAGE+1
           CALL PRINT_HEAD(PAGE,UNIT)
         ENDIF
         LINE = 4
         WRITE(UNIT,200,ERR=909,IOSTAT=IERR) STBL,TITLE(STBL),
     &                                   TABSIZE(STBL)
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR) ' '
C
C        EXTERNAL type table content
C        ---------------------------
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR) ' '
         WRITE(UNIT,233,ERR=909 ) EXTSIZ(EXTBLN(STBL))
         WRITE(UNIT,*,ERR=909,IOSTAT=IERR) ' '
         FMT0 = '(8(1X,F9.3))'
         OLDM400 = 0
         DO II=1,EXTSIZ(EXTBLN(STBL)),8
            MOD400=(II/400)*400
            IF(MOD400.NE.OLDM400) THEN
               PAGE = PAGE+1
               CALL PRINT_HEAD(PAGE,UNIT)
               LINE = 4
            ENDIF
            OLDM400 = MOD400
            RWPNT = II+7
            WRITE(UNIT,FMT0,ERR=909,IOSTAT=IERR)
     &                      (EXTP(EXTBLN(STBL),J),J=II,RWPNT)
            LINE = LINE + 1
         ENDDO
      ENDIF
C
C     Print user and date
C     -------------------
      WRITE(UNIT,287,ERR=909,IOSTAT=IERR) MODATE(STBL),NAME(STBL)       
      LINE = LINE + 2
C
 909  CONTINUE
C
      RETURN
C
 200  FORMAT(' TABLE-',I2,' TITLE: ',A30,2X,'SIZE=',I4)
 233  FORMAT(10X,'  External table of ',I4,' points')
 251  FORMAT(1X,' Number of HARMONICS in the table is ',I3)
 252  FORMAT(1X,' Number of BREAKPOINTS in the table is ',I3,/)
 287  FORMAT(1X,/,' Table Last Modified on : ',A17,'   Created by : ',
     &                                      A26)
C
      END
C
C     ==================
      SUBROUTINE LISTABL
C     ==================
      IMPLICIT NONE
C
C     This subroutine lists the contents of a requested table
C     -------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          TAB_FOUND,                    ! Table found flag
     &          ONETABL,                      ! One table found flag
     &          END_READ                      ! Read table # flag
C
      INTEGER*2 PAGE,OFFSET
C
      INTEGER*4 Stat,LINE
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      COMMON /TAB/ TAB_FOUND
C
      DATA ERROR1/'%LIST_TABLE : Some of the table does not exist'/
      DATA ERROR2/'%LIST_TABLE : Error #      on file HARTAB.LIS, summar
     &y aborted'/
      DATA ERROR3/'%LIST_TABLE : Invalid table number'/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'MODE'/
      DATA MENU(7)/'ALL'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
       CALL READ_COMMAND(-1,22,1,'TABLE # > ',10,COMMAND,L_COM,Stat)
       CALL PARSE_COMMAND(COMMAND,L_COM,-7,MENU,ITEM,IERR)
C
       ONETABL = .FALSE.
       IF(IERR.EQ.0) THEN
C
          IF(ITEM.EQ.6) THEN     !MODE command
           CALL MODE_SET(1)
          ELSEIF(ITEM.EQ.7) THEN     !MODE command
           NUM_LIST = TBLNUM
           DO I=1,NUM_LIST
             IT_LIST(I)=SAV_TBL(I)
           ENDDO
           ONETABL = .TRUE.
          ELSEIF(ITEM.EQ.1) THEN !BOX command
           CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
           CALL MODE_SET(2)           !Display output mode
          ELSEIF(ITEM.EQ.2) THEN    !HELP command
           CALL HELP(7)                !Help asked
          ELSE                      !QUIT,EXIT or X command
           END_READ=.TRUE.
          ENDIF
       ELSE
C
C         TABLE numbers entered: get them
C         -------------------------------
          CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
          IF(IERR.EQ.0) THEN
C
C           Look if all tables exist
C           ------------------------
            I = 1
            TAB_FOUND = .TRUE.
            DO WHILE(I.LE.NUM_LIST.AND.TAB_FOUND)
               TEMPI = IT_LIST(I)
               CALL TAB_EXIST(TEMPI,TAB_FOUND)
               IF(.NOT.TAB_FOUND) THEN
                  CALL ERR_MESS(ERROR1,46,-1,*50)
 50               CONTINUE
               ENDIF
               I = I+1
            ENDDO
C
            ONETABL = TAB_FOUND
          ELSE
            ONETABL = .FALSE.
            CALL ERR_MESS(ERROR3,34,-1,*51)
 51         CONTINUE
          ENDIF
       ENDIF
C
       IF(ONETABL) THEN
            IF(OUTMODE)THEN
C
C             Output to printer
C             -----------------
              CALL FIL_OPEN(1,1,IERR)
              IF(IERR.EQ.0)THEN
                PAGE = 0
                LINE = 55
                IERR = 0
                M = 1
                DO WHILE(M.LE.NUM_LIST.AND.IERR.EQ.0)
                  TEMPI = IT_LIST(M)
                  CALL PRINTABLE(TEMPI,PAGE,LINE,TBL_UNIT,IERR)
                  IF(IERR.NE.0) THEN
                     CALL GET_ERR_STR(IERR,ERROR2(22:26))
                     CALL ERR_MESS(ERROR2,62,-1,*52)
 52                  CONTINUE
                  ENDIF
                  M = M + 1
                ENDDO
                CALL FIL_OPEN(1,2,IERR)    !Close the file
              ENDIF
            ELSE
C
C             Output to screen
C             ----------------
              M = 1
              DO WHILE(M.LE.NUM_LIST.AND.IERR.EQ.0)
                TAB_NUM = IT_LIST(M)
                CALL REST_TABLE(2,*201)
                CALL WAIT_CONT(2,OFFSET)
                IF(OFFSET.EQ.0)THEN
                   GOTO 201
                ENDIF
                M = M + 1
              ENDDO
 201          CALL CL_DISP
            ENDIF
       ENDIF
C
      ENDDO
C
      RETURN
      END
C
C
C     ====================
      SUBROUTINE LIST_DATA
C     ====================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2
     &          PAGE                     !Page counter

C
      INTEGER*4
     &          Stat,
     &          LINE                     !Line counter
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR3                  !Various error messages
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      LOGICAL*1
     &          TAB_FOUND,                    ! Table found flag
     &          ONETABL,                      ! One table flag found
     &          END_READ                      ! Read table # flag
C
      COMMON /TAB/ TAB_FOUND
C
      DATA ERROR1/'%LIST_POINT : Some of the table does not exist'/
      DATA ERROR3/'%LIST_POINT : Invalid table number'/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'MODE'/
      DATA MENU(7)/'ALL'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
       CALL READ_COMMAND(-1,22,1,'TABLE # > ',10,COMMAND,L_COM,Stat)
       CALL PARSE_COMMAND(COMMAND,L_COM,-7,MENU,ITEM,IERR)
C
       ONETABL = .FALSE.
       IF(IERR.EQ.0) THEN
C
          IF(ITEM.EQ.6) THEN     !MODE command
           CALL MODE_SET(1)
          ELSEIF(ITEM.EQ.7) THEN     !MODE command
           NUM_LIST = TBLNUM
           DO I=1,NUM_LIST
             IT_LIST(I)=SAV_TBL(I)
           ENDDO
           ONETABL = .TRUE.
          ELSEIF(ITEM.EQ.1) THEN !BOX command
           CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
           CALL MODE_SET(2)           !Display output mode
          ELSEIF(ITEM.EQ.2) THEN    !HELP command
           CALL HELP(7)                !Help asked
          ELSE                      !QUIT,EXIT or X command
           END_READ=.TRUE.
          ENDIF
       ELSE
C
C         TABLE numbers entered: get them
C         -------------------------------
          CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
          IF(IERR.EQ.0) THEN
C
C           Look if all tables exist
C           ------------------------
            I = 1
            TAB_FOUND = .TRUE.
            DO WHILE(I.LE.NUM_LIST.AND.TAB_FOUND)
               TEMPI = IT_LIST(I)
               CALL TAB_EXIST(TEMPI,TAB_FOUND)
               IF(.NOT.TAB_FOUND) THEN
                  CALL ERR_MESS(ERROR1,46,-1,*53)
 53               CONTINUE
               ENDIF
               I = I+1
            ENDDO
C
            ONETABL = TAB_FOUND
          ELSE
            ONETABL = .FALSE.
            CALL ERR_MESS(ERROR3,34,-1,*54)
 54         CONTINUE
          ENDIF
       ENDIF
C
       IF(ONETABL) THEN
C
         IERR = 0
         M = 1
         PAGE = 0
         LINE = 0
         IF(OUTMODE)THEN
C
C           Output mode on the printer:create HARTAB.LIS
C           --------------------------------------------
            CALL FIL_OPEN(1,1,IERR)
            IF(IERR.NE.0) GOTO 101
         ENDIF
         DO WHILE(M.LE.NUM_LIST.AND.IERR.EQ.0)
            TEMPI = IT_LIST(M)
            CALL DISP_POINT(TEMPI,PAGE,LINE,IERR,*101)
            M = M + 1
         ENDDO
C
 101     CONTINUE
         IF(OUTMODE)THEN
C
C           Output the file to the printer
C           ------------------------------
            CALL FIL_OPEN(1,2,IERR)
         ELSE
            CALL CL_DISP
         ENDIF
       ENDIF
C
      ENDDO
C
      RETURN
C
      END
C
C     ====================================================
      SUBROUTINE DISP_POINT(TABCALC,PGCOUNT,LINE_C,IERR,*)
C     ====================================================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER
     & NMBER*2            ,!Character number table
     & SIZE*4             ,!Character size of a table
     & STABLE*2           ,!Character table number of a table
     & S_START*4          ,!Screen buffer character
     & S_HEX*4             !Screen buffer character
C
      CHARACTER*80
     & TEXT*69,
     & ERROR1                  !Various error messages
C
      INTEGER*2
     & OFFSET             ,!Wait offset return
     & PSTOP              ,!Print stop value
     & PGCOUNT,
     & START              ,!Buffer element counter
     & TABCALC             !Which table to calculate
C
      INTEGER*4
     & DISP               ,!Display unit number
     & LINE_C
C
      DATA ERROR1/'%LIST_POINT : Error #      on file HARTAB.LIS, summar
     &y aborted'/
C
      TEXT = BLANK
      TEXT(5:5) = ':'
C
C     Display the point on screen or on printer
C     -----------------------------------------
      IF(OUTMODE)THEN
         DISP = TBL_UNIT
      ELSE
         CALL CL_DISP
      ENDIF
C
C     Print the title,number and size of that table
C     ---------------------------------------------
      SEND = BLANK
      SEND(1:6) = 'TABLE '
      WRITE(NMBER,'(I2)',ERR=909 ) TABCALC
      SEND(8:9) = NMBER
      SEND(11:19) = '  SIZE = '
      WRITE(SIZE,'(I4)',ERR=909 ) TABSIZE(TABCALC)
      SEND(20:23) = SIZE
      SEND(24:32) = '  TITLE: '
      SEND(33:72) = TITLE(TABCALC)
      SEND(73:80) = '       '
      IF(.NOT.OUTMODE) THEN
         CALL Term_Write(7,1,SEND,80)
         LINE_C = 1
      ELSE
         PGCOUNT = PGCOUNT+1
         CALL PRINT_HEAD(PGCOUNT,DISP)
         WRITE(UNIT=DISP,FMT=101,ERR=999,IOSTAT=IERR ) SEND
         LINE_C = 6
      ENDIF
C
C     Process the table to get the points
C     -----------------------------------
      CALL CREATE_TABLE(TABCALC)
C
C     Insert the table points in the file
C     -----------------------------------
      START = 1
      PSTOP = TABSIZE(TABCALC)
      DO WHILE(START.LE.PSTOP)
C
C           Write to output unit 8 datas at a time
C           --------------------------------------
            IF(OUTMODE) THEN
              WRITE(UNIT=DISP,FMT=300,ERR=999,IOSTAT=IERR ) START,
     &                          (BUFF(START+J),J=0,7)
              LINE_C = LINE_C + 1
              IF(LINE_C.GE.50) THEN
                 PGCOUNT = PGCOUNT+1
                 CALL PRINT_HEAD(PGCOUNT,DISP)
                 LINE_C = 4
              ENDIF
            ELSE
              WRITE(S_START,'(I4)',ERR=909 ) START
              TEXT(1:4) = S_START
              DO J=0,7
                 WRITE(S_HEX,'(Z4)',ERR=909 ) BUFF(START+J)
                 TEXT(10+J*8:10+J*8+3)= S_HEX
              ENDDO
              CALL Term_Write(LINE_C+8,4,TEXT,69)
              LINE_C = LINE_C+1
              IF(LINE_C.EQ.15)THEN
                 CALL WAIT_CONT(1,OFFSET)
                 IF(OFFSET.EQ.0)THEN
                    RETURN 1
                 ELSEIF(OFFSET.EQ.99)THEN
                    RETURN
                 ENDIF
                 LINE_C = 1
                 CALL CL_DISP2 !Clear the line for command
              ENDIF
            ENDIF
            START = START + 8
      ENDDO
C
      IF (.NOT.OUTMODE) THEN
         IF(LINE_C.GE.2)THEN
            CALL WAIT_CONT(2,OFFSET)
            IF(OFFSET.EQ.0) RETURN 1
         ENDIF
      ENDIF
C
      GOTO 909
 999  CALL GET_ERR_STR(IERR,ERROR1(22:26))
      CALL ERR_MESS(ERROR1,62,-1,*909)
 909  CONTINUE
C
      RETURN
C
 101  FORMAT(' ',A,/)
 300  FORMAT(' ',I4,':',8('   ',Z4))
C
      END
C
C     ====================
      SUBROUTINE ASSIGN(*)
C     ====================
      IMPLICIT NONE
C
C     This subroutine determines the source/table associations.
C     ---------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CALL DISP_SRC(-1,1)
C
      CALL GET_SOURCE
C
      CALL Term_Write(7,1,BLANK,80)
      RETURN 1
C
      END
C
C
C     =====================
      SUBROUTINE GET_SOURCE
C     =====================
      IMPLICIT NONE
C
C     This subroutine determines the source/table associations.
C     ---------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      INTEGER*2
     & INVCNT                         ! Invalid source count
C
      INTEGER*4
     & DEF_DSG,                       ! DSG index selected
     & Tempi4,
     & Stat
C
      LOGICAL*1
     &          NEWDSG,               ! New DSG selected flag
     &          SRC_VAL,              ! Source valid flag
     &          TAB_VAL,              ! Valid table flag
     &          TAB_FOUND,            ! True if table exists
     &          END_LOOK,             ! Read table flag
     &          END_READ              ! Read source flag
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
     &,ERROR5                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      COMMON /TAB/ TAB_FOUND
C
      DATA ERROR1/'%ASSIGN : Assignation removed '/
      DATA ERROR2/'%ASSIGN : Invalid table number '/
      DATA ERROR3/'%ASSIGN : Table does not exist '/
      DATA ERROR4/'%ASSIGN : Invalid source(s) ignored'/
      DATA ERROR5/'%ASSIGN : All source(s) are invalid - All ignored'/
C
      DATA PROMPT /'ASSIGN : Enter SOURCE # or SWAP (DSG #) > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'ALL'/
      DATA MENU(7)/'SWAP'/
C
      DEF_DSG = 1
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,42,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-7,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             SRC_VAL = .FALSE.
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN        !ALL
                 NUM_LIST = MAXI_SOUR
                 DO I=1,NUM_LIST
                    IT_LIST(I)=I
                 ENDDO
                 SRC_VAL = .TRUE.
               ELSEIF(ITEM.EQ.7) THEN !SWAP
                 CALL GET_NEWDSG(DEF_DSG,NEWDSG) 
C
C                Redisplay the sources association if new DSG selected
C                ----------------------------------------------------- 
                 IF (NEWDSG) THEN
                    CALL DISP_SRC(-1,DEF_DSG)
                 ENDIF
               ENDIF
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 CALL DISP_SRC(-1,DEF_DSG)
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(21)               !Help asked
                 CALL DISP_SRC(-1,DEF_DSG)
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ELSE
C
C            Source numbers entered: get them
C            --------------------------------
             CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
             IF(IERR.EQ.0) THEN
                SRC_VAL = .TRUE.
             ELSE
                SRC_VAL = .FALSE.
             ENDIF
          ENDIF
C
          IF(SRC_VAL) THEN
C
C            Get table number
C            ----------------
             END_LOOK = .FALSE.
             DO WHILE(.NOT.END_LOOK)
              CALL READ_COMMAND(-1,22,1,'TABLE # > ',10,COMMAND,L_COM,
     &                          Stat)
              CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
              IF(IERR.EQ.9999) THEN
                  CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
                  IF(IERR.EQ.0) THEN
C
                   IF(ITEM.EQ.1) THEN    !BOX command
                      CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                      CALL MODE_SET(-1)           !Display output mode
                      CALL DISP_SRC(-1,DEF_DSG)
                   ELSEIF(ITEM.EQ.2) THEN    !HELP command
                      CALL HELP(6)                !Help asked
                      CALL DISP_SRC(-1,DEF_DSG)
                   ELSE                      !QUIT,EXIT or X command
                      END_LOOK = .TRUE.
                      TAB_VAL = .FALSE.
                   ENDIF
                  ENDIF
              ELSEIF(IERR.EQ.0)THEN
C
C               Number entered: set table number
C               --------------------------------
                INT_NUM = INT(NUMBER)
                REAL_NUM= FLOAT(INT_NUM)
                IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
                  IF( (INT_NUM.LE.MAX_TAB_LOAD).AND.
     &                  (INT_NUM.GT.0)                ) THEN
                     TAB_NUM = INT_NUM
                     CALL TAB_EXIST(TAB_NUM,TAB_FOUND)
                     IF(.NOT.TAB_FOUND) THEN
                        CALL ERR_MESS(ERROR3,30,-1,*57)
 57                     CONTINUE
                     ELSE
                        END_LOOK = .TRUE.
                        TAB_VAL = .TRUE.
                     ENDIF
                  ELSEIF(INT_NUM.EQ.0)THEN
                     TAB_NUM = 0
                     CALL ERR_MESS(ERROR1,30,-1,*58)
 58                  CONTINUE
                     END_LOOK = .TRUE.
                     TAB_VAL = .TRUE.
                  ELSE
                     CALL ERR_MESS(ERROR2,30,-1,*59)
 59                  CONTINUE
                  ENDIF
                ELSE
                  CALL ERR_MESS(ERROR2,30,-1,*60)
 60               CONTINUE
                ENDIF
              ELSE
                CALL ERR_MESS(ERROR2,30,-1,*61)
 61             CONTINUE
              ENDIF
             ENDDO
C
             IF(TAB_VAL) THEN
C
                INVCNT = 0
                DO I = 1,NUM_LIST
                   IF(IT_LIST(I).GT.0.AND.IT_LIST(I).LE.MAXI_SOUR)THEN
                      SOURCE(IT_LIST(I),DEF_DSG) = TAB_NUM
                      TEMPI = IT_LIST(I)
                      Tempi4 = TEMPI
                      CALL DISP_SRC(Tempi4,DEF_DSG)
                   ELSE
                      INVCNT = INVCNT+1
                   ENDIF
                ENDDO
                IF(INVCNT.EQ.NUM_LIST) THEN
                 CALL ERR_MESS(ERROR5,49,-1,*62)
 62              CONTINUE
                ELSEIF(INVCNT.GT.0) THEN
                 CALL ERR_MESS(ERROR4,35,-1,*63)
 63              CONTINUE
                 DATASAVE=.TRUE.
                ELSE
                 DATASAVE=.TRUE.
                ENDIF
             ENDIF
C
          ENDIF
      ENDDO
C
      RETURN
C
      END
C
C     =================================
      SUBROUTINE GET_NEWDSG(DSG,NEWONE)
C     =================================
C
C     This subroutine get the new DSG number for source asociation
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*4
     & DSG,                     !DSG slot index
     & Stat
C
      LOGICAL*1
     & FOUND,                  !DSG slot number found
     & NEWONE,                 !New DSG slot number requested         
     & END_READ                !Read table # flag
C
      CHARACTER*80
     & ERROR1                  !Various error messages
C
      CHARACTER*15 MENU(13)*15
C
      DATA ERROR1/'%GET_NEWDSG-E-NOTFOUND: Slot number does not exist'/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
C     Ask the user for the DSG
C     ------------------------
      NEWONE = .FALSE. 
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,'ASSIGN: New DSG slot # > ',25,
     &                      COMMAND,L_COM,Stat)
         CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
         IF(IERR.EQ.9999) THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               CALL DISP_SRC(-1,DSG)
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(22)                !Help asked
               CALL DISP_SRC(-1,DSG)
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
            ENDIF
           ENDIF
         ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: look for DSG index
C          ----------------------------------
           FOUND = .FALSE.
           INT_NUM = INT(NUMBER)
           REAL_NUM= FLOAT(INT_NUM)
           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
            IF( (INT_NUM.LE.MAX_TAB).AND.
     &         (INT_NUM.GT.0)                ) THEN
              DO II=1,DSG_NUMB
                IF (SL_NBI(II).EQ.INT_NUM) THEN
                    FOUND=.TRUE.
                    DSG = II
                ENDIF 
              ENDDO 
 789          CONTINUE
            ENDIF
           ENDIF
           IF(.NOT.FOUND) THEN
             CALL ERR_MESS(ERROR1,50,-1,*64)
 64          CONTINUE
           ELSE
             END_READ = .TRUE.
             NEWONE = .TRUE.
           ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
