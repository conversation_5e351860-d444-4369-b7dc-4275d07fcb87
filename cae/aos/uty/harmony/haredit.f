C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY For UNIX systems                                 **
C   **                                                                      **
C   **  Program  : HAREDIT.FOR                                              **
C   **  Function : All EDIT and DELETE function subroutines                 **
C   **                                                                      **
C   **  Revision_History :                                                  **
C   **  ------------------                                                  **
C   **  See harmony.f file                                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  TABLE                                                               **
C   **  WAVETABLE                                                           **
C   **  OTHERTABLE                                                          **
C   **  EXTERNTAB                                                           **
C   **  GET_EXTERNAL                                                        **
C   **  COPY_TAB                                                            **
C   **  SAVE_TABLE                                                          **
C   **  HARMONICS                                                           **
C   **  GET_PHASE                                                           **
C   **  STORE_INP                                                           **
C   **  GET_TYPE                                                            **
C   **  GET_OTHER                                                           **
C   **  REST_TABLE                                                          **
C   **  GET_HARM                                                            **
C   **  GET_AMPL                                                            **
C   **  GET_TITLE                                                           **
C   **  GET_SIZE                                                            **
C   **  TRANSFERT                                                           **
C   **  DELETE                                                              **
C   **  DEL_TABLE                                                           **
C   **  DEL_ASSIGN                                                          **
C   **  GET_USER                                                            **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C -- An explanation of each of these routines along with a description
C    of the parameters may be found in the appropriate subroutine.
C
C     ===================
      SUBROUTINE TABLE(*)
C     ===================
      IMPLICIT NONE

C -- This subroutine presents the user with the editing menu, and
C    branches to the routine selected.  If quit or exit is entered
C    then the routine exits to the label * in the calling program.
C
      INCLUDE 'harparm.inc'

      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      LOGICAL*1
     &          END_READ,                     ! Read table # flag
     &          TAB_FOUND,                    ! True if table exists
     &          FIRST,
     &          CHANGED,                      ! True if table is modified
     &          QUIT_ASK                      ! True if table mode is to exit
C
      INTEGER*2
     &          QUIT_TYPE,                    ! QUIT wave type question flag
     &          BYPASS                        ! Bypass wave type code
C
      INTEGER*4
     &          ED_DIS,                       ! Edit table number for display
     &          Stat,
     &          LLine4
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      CHARACTER*2
     & NOSTR /'NO'/
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
     &,ERROR5                  !
C
      COMMON /TAB/ TAB_FOUND
      COMMON /CHAN/CHANGED
C
      DATA ERROR1/'%EDIT - BRKPOINT or EXTERNAL type exists. WAVE not al
     &lowed'/
      DATA ERROR2/'%EDIT - WAVE or EXTERNAL type exists. BRKPOINT not al
     &lowed'/
      DATA ERROR3/'%EDIT - WAVE or BRKPOINT type exists. EXTERNAL not al
     &lowed'/
      DATA ERROR4/'%EDIT - Invalid table number  '/
      DATA ERROR5/'Table has changed, do you want to keep it [Y] ? '/
C
      DATA PROMPT  / 'EDIT TABLE TYPE : WAVE , BREAKPOINT or EXTERNAL [<
     &CR> = WAVE ] > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'WAVE'/
      DATA MENU(7)/'BREAKPOINT'/
      DATA MENU(8)/'EXTERNAL'/
C
C -- Set the QUIT_ASK flag to false.  If false, it means continue asking
C    the user for his choice of editing functions.  If true, then it means
C    quit asking the user for his selection and return to the label *
C    in the calling program.
C
      TAB_NUM = 1        !Set first table as the one to look for
C
      QUIT_ASK = .FALSE.
      DO WHILE (.NOT. QUIT_ASK)
C
C       Ask the user for the table number he wishes to edit.
C       ----------------------------------------------------
        END_READ=.FALSE.
C
C       Prompt the user for input, and branch to that routine which corresponds
C       to his selection.
C       -----------------------------------------------------------------------
        DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(0,22,1,'TABLE # > ',10,COMMAND,L_COM,Stat)
         IF(L_COM.GT.0)THEN
          CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
          IF(IERR.EQ.9999) THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(6)                !Help asked
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               QUIT_ASK=.TRUE.
            ENDIF
           ENDIF
          ELSEIF(IERR.EQ.0)THEN
C                                     
C          Number entered: set table number
C          --------------------------------
           INT_NUM = INT(NUMBER)      
           REAL_NUM= FLOAT(INT_NUM)   
           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
            IF( (INT_NUM.LE.MAX_TAB).AND.
     &         (INT_NUM.GT.0)                ) THEN
              TAB_NUM = INT_NUM       
              CALL TAB_EXIST(TAB_NUM,TAB_FOUND)
              END_READ = .TRUE.       
            ELSE                      
              CALL ERR_MESS(ERROR4,30,-1,*80)
 80           CONTINUE
            ENDIF
           ELSE
              CALL ERR_MESS(ERROR4,30,-1,*81)
 81           CONTINUE
           ENDIF
          ENDIF
         ELSE
          CALL TAB_EXIST(TAB_NUM,TAB_FOUND)
          IF(TAB_FOUND.OR.TAB_NUM.EQ.0)THEN
            TAB_NUM=TAB_NUM+1
            CALL TAB_EXIST(TAB_NUM,TAB_FOUND)
          ENDIF
          END_READ = .TRUE.
         ENDIF
        ENDDO
C
        IF(.NOT.QUIT_ASK) THEN
C
C        Restore the contents of the table requested
C        and print EDITING message
C        -------------------------------------------
         CALL REST_TABLE(1,*5)
 5       CONTINUE
         IF(TMP_HMSZ.GT.0) THEN
            CALL EDIT_DISP(1)
            BYPASS = 1
         ELSEIF(TMP_OSZ.GT.0) THEN
            CALL EDIT_DISP(2)
            BYPASS = 2
         ELSEIF(TMP_EXTSZ.GT.0) THEN
            CALL EDIT_DISP(3)
            BYPASS = 3
         ELSE
            CALL EDIT_DISP(0)
            BYPASS = 0
         ENDIF
C
C        Set the CHANGED flag, which indicates whether a change has been
C        made to the table, to false.  Continue asking the user for
C        inputs, until he wishes to quit.  The quit flag is set in
C        GET_HARM, GET_OTHER, GET_TITLE, and GET_SIZE.
C        ---------------------------------------------------------------
         CHANGED = .FALSE.
         ED_DIS=BYPASS
         FIRST=.TRUE.
C
         QUIT_TYPE = 0
         DO WHILE (QUIT_TYPE.EQ.0)
C
C         Find out what the user has selected from the menu and branch to
C         the selected routine.
C         ---------------------------------------------------------------
          IF(.NOT.FIRST) THEN
              CALL EDIT_DISP(ED_DIS)
          ELSE
              FIRST=.FALSE.
          ENDIF
          IF (BYPASS.EQ.0) THEN
C
             END_READ=.FALSE.
C
C            Prompt the user for input, and branch to that routine which corresponds
C            to his selection.
C            -----------------------------------------------------------------------
             DO WHILE(.NOT.END_READ)
                CALL READ_COMMAND(0,22,1,PROMPT,65,COMMAND,L_COM,Stat)
                IF(L_COM.GT.0) THEN
                CALL PARSE_COMMAND(COMMAND,L_COM,9,MENU,ITEM,IERR)
C

                 IF(IERR.EQ.0) THEN
C
                   IF(ITEM.GE.6) THEN
C
                      IF(ITEM.EQ.6.OR.ITEM.EQ.9) THEN
                         ITEM=6
                         PHASE = -1
                      ELSEIF(ITEM.EQ.7) THEN
                         PHASE = 99
                      ELSEIF(ITEM.EQ.8) THEN
                         PHASE = 99
                      ENDIF
                      END_READ=.TRUE. !Look for table type edit function
C
                   ELSEIF(ITEM.EQ.1) THEN    !BOX command
                      CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                      CALL MODE_SET(-1)           !Display output mode
                      CALL EDIT_DISP(0)
                      PHASE4 = PHASE
                      CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
                   ELSEIF(ITEM.EQ.2) THEN    !HELP command
                      CALL HELP(2)                !Help asked
                      CALL EDIT_DISP(0)
                      PHASE4 = PHASE
                      CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
                   ELSE                      !QUIT,EXIT or X command
                      END_READ=.TRUE.
                      QUIT_TYPE = 4   !Exit and return to TABLE prompt
                   ENDIF
                 ENDIF
                ELSE
                 ITEM=6               !CR is the default
                 PHASE = -1
                 END_READ=.TRUE.      !Look for table type edit function
                ENDIF
             ENDDO
             BYPASS = ITEM-5          !Use BYPASS to call right table type edit
          ENDIF
C
C         Bypass next section if return to TABLE prompt is asked
C         ------------------------------------------------------
          IF(QUIT_TYPE.EQ.0) THEN
            ED_DIS = BYPASS
            CALL EDIT_DISP(ED_DIS)
            PHASE4 = PHASE
            CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
C
C           Go directly to edit type
C           ------------------------
            IF (BYPASS .EQ. 1) THEN
             IF (TMP_OSZ .GT. 0.OR.TMP_EXTSZ.GT.0) THEN   !Wave table
                CALL ERR_MESS(ERROR1,58,-1,*83)
 83             CONTINUE
             ELSE
                CALL WAVETABLE(QUIT_TYPE)
             ENDIF
            ELSE IF (BYPASS .EQ. 2) THEN
             IF (TMP_HMSZ .GT. 0.OR.TMP_EXTSZ.GT.0) THEN  !Other table
               CALL ERR_MESS(ERROR2,58,-1,*84)
 84            CONTINUE
             ELSE
               CALL OTHERTABLE(QUIT_TYPE)
             ENDIF
            ELSE IF (BYPASS .EQ. 3) THEN                  !External table
             IF (TMP_HMSZ .GT. 0.OR.TMP_OSZ.GT.0) THEN
               CALL ERR_MESS(ERROR3,58,-1,*85)
 85            CONTINUE
             ELSE
               CALL EXTERNTAB(QUIT_TYPE)
             ENDIF
            ENDIF
            ED_DIS = 0
            BYPASS = 0
            CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,99)
          ENDIF
         ENDDO
C
C        If the changed flag was set, it means that some changes were made to
C        the table contents.  In this case, cll the save table routine
C        --------------------------------------------------------------------
         IF (CHANGED) THEN
           IF(QUIT_TYPE.NE.5) THEN
              CALL SAVE_TABLE
           ELSE
C
C             Ask if the edits are to be saved.  If not, then return.
C             -------------------------------------------------------
              CALL Term_Write(22,49,BLANK,32)
              CALL Term_Write(22,1,ERROR5,48)
              CALL Term_Read(0,SEND,LLine4,IERR)
              LLINE = LLine4
              IF ((SEND(1:LLINE).NE.NOSTR(1:LLINE)).OR.LLINE.EQ.0) THEN
                  CALL SAVE_TABLE
              ENDIF
           ENDIF
         ENDIF
         CALL CL_DISP
         CALL Term_Write(7,44,BLANK,36)    !Erase reverse video SINE mode (Reverse
                                        ! video is unerasable for CL_DISP )
        ENDIF
      ENDDO
      RETURN 1
      END
C
C
C     ==========================
      SUBROUTINE WAVETABLE(QUIT)
C     ==========================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      LOGICAL*1
     &          TAB_FOUND,                    ! True if table exists
     &          CHANGED                       ! True if table is modified
C
      INTEGER*2
     &          QUIT                          ! Quit transfer flag
C
      INTEGER*4
     &          L_PROMPT,                     ! Prompt length
     &          N_MENU,                       ! Number of item in the menu
     &          Stat,
     &          PREV_DISP                     ! Previous display function
C
      LOGICAL*1 END_READ
C
      CHARACTER*80 PROMPT,MENU(13)*15,NO_XFER*35
C
      COMMON /TAB/ TAB_FOUND
      COMMON /CHAN/CHANGED
C
      DATA NO_XFER/'%RTL: Can not transfer empty table '/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'PHASE'/
      DATA MENU(7)/'HARMONICS'/
      DATA MENU(8)/'TITLE'/
      DATA MENU(9)/'SIZE'/
      DATA MENU(10)/'COPY'/
      DATA MENU(11)/'AUTO'/
      DATA MENU(12)/'TRANSFER'/
C
      REPL_STR(13) = 'HARM'       !Set F17 to HARMONICS function
C
C     Set the prompt for transfert or not
C     -----------------------------------
      IF(TAB_NUM.GT.MAX_TAB_LOAD)THEN
         PROMPT='WAVE : PHASE, HARM, TITLE, SIZE, COPY, AUTO or TRANSFER
     & > '
         L_PROMPT=58
         N_MENU = 12
      ELSE
         PROMPT='WAVE : PHASE, HARM, TITLE, SIZE, COPY or AUTO > '
         L_PROMPT=48
         N_MENU = 11   !Remove TRANSFER as a command
      ENDIF
C
      PREV_DISP=1
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,PROMPT,L_PROMPT,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,N_MENU,MENU,ITEM,IERR)
C
         IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                 CALL GET_PHASE
               ELSEIF(ITEM.EQ.7) THEN
                 CALL HARMONICS(PREV_DISP)
               ELSEIF(ITEM.EQ.8) THEN
                 CALL GET_TITLE(1)
               ELSEIF(ITEM.EQ.9) THEN
                 CALL GET_SIZE(1)
               ELSEIF(ITEM.EQ.10) THEN
                 CALL COPY_TAB(1)
               ELSEIF(ITEM.EQ.11) THEN
                 SIN_MOD=.NOT.SIN_MOD
                 CALL EDIT_DISP(1)
               ELSEIF(ITEM.EQ.12) THEN
                 IF(TMP_HMSZ.GT.0) THEN
                    CALL TRANSFERT(1)
                 ELSE
                    CALL ERR_MESS(NO_XFER,35,-1,*86)
 86                 CONTINUE
                 ENDIF
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(3)                !Help asked
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               IF(CHANGED.OR.TMP_HMSZ.GT.0)THEN
                  QUIT = ITEM
               ELSE
                  QUIT = 0
               ENDIF
            ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C     ===========================
      SUBROUTINE OTHERTABLE(QUIT)
C     ===========================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      LOGICAL*1
     &          TAB_FOUND,                    ! True if table exists
     &          CHANGED,                      ! True if table is modified
     &          QUIT_ASK                      ! True if table mode is to exit
C
      INTEGER*2
     &          QUIT                          ! Quit transfer flag
C
      INTEGER*4
     &          L_PROMPT,                     ! Prompt length
     &          N_MENU,                       ! Number of item in the menu
     &          Stat,
     &          PREV_DISP                     ! Previous display function
C
      LOGICAL*1 END_READ
C
      CHARACTER*80 PROMPT,MENU(13)*15,NO_XFER*35
C
      COMMON /TAB/ TAB_FOUND
      COMMON /CHAN/CHANGED
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'POINTS'/
      DATA MENU(7)/'TITLE'/
      DATA MENU(8)/'SIZE'/
      DATA MENU(9)/'COPY'/
      DATA MENU(10)/'TRANSFER'/
C
      DATA NO_XFER/'%RTL: Can not transfer empty table '/
C
      REPL_STR(13) = 'POIN'        !Set F17 to BREAKPOINTS function
C
C     Set the prompt for transfer or not
C     ----------------------------------
      IF(TAB_NUM.GT.MAX_TAB_LOAD)THEN
         PROMPT='BREAKPOINT TABLE : POINTS, TITLE, SIZE, COPY or TRANSFE
     &R > '
         L_PROMPT=59
         N_MENU = 10
      ELSE
         PROMPT='BREAKPOINT TABLE : POINTS, TITLE, SIZE or COPY > '
         L_PROMPT=49
         N_MENU = 9   !Remove TRANSFER as a command
      ENDIF
C
      PREV_DISP=1
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,PROMPT,L_PROMPT,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,N_MENU,MENU,ITEM,IERR)
C
         IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                 CALL GET_OTHER(PREV_DISP)
               ELSEIF(ITEM.EQ.7) THEN
                 CALL GET_TITLE(2)
               ELSEIF(ITEM.EQ.8) THEN
                 CALL GET_SIZE(2)
               ELSEIF(ITEM.EQ.9) THEN
                 CALL COPY_TAB(2)
               ELSEIF(ITEM.EQ.10) THEN
                 IF(TMP_OSZ.GT.0) THEN
                    CALL TRANSFERT(2)
                 ELSE
                    CALL ERR_MESS(NO_XFER,35,-1,*87)
 87                 CONTINUE
                 ENDIF
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               CALL EDIT_DISP(2)
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(4)                !Help asked
               CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               CALL EDIT_DISP(2)
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               IF(CHANGED.OR.TMP_OSZ.GT.0)THEN
                  QUIT = ITEM
               ELSE
                  QUIT = 0
               ENDIF
            ENDIF
         ENDIF
      ENDDO
C
      RETURN
C
      END
C
C     ==========================
      SUBROUTINE EXTERNTAB(QUIT)
C     ==========================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
C
      INCLUDE 'hardata.inc'
C
      LOGICAL*1
     &          TAB_FOUND,                    ! True if table exists
     &          CHANGED                       ! True if table is modified
      INTEGER*2
     &          QUIT                          ! Quit transfer flag
C
      INTEGER*4
     &          L_PROMPT,                     ! Prompt length
     &          N_MENU,                       ! Number of item in the menu
     &          Stat,
     &          LLine4
C
      CHARACTER CONFIRM(2)*68                 !Confirm message
C
      CHARACTER*3
     &          YES /'YES'/                   !YES answer string
C
      CHARACTER*80 PROMPT
C
      CHARACTER*15
     &          MENU(13)
C
      CHARACTER*80
     & ERROR1                  !Various error messages
      LOGICAL*1 END_READ
      COMMON /TAB/ TAB_FOUND
      COMMON /CHAN/CHANGED
C
      DATA CONFIRM/'* External table size cannot be changed while the ta
     &ble exist',
     &             '* Do you want a new table [N] ? '/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'GET'/
      DATA MENU(7)/'TITLE'/
      DATA MENU(8)/'SIZE'/
      DATA MENU(9)/'COPY'/
      DATA ERROR1/'%EXTERNAL- Maximum number of external table reached'/
C
      REPL_STR(13) = 'GET '        !Set F17 to GET function
C
C     Set the prompt for transfer or not
C     ----------------------------------
      PROMPT='EXTERNAL : GET, TITLE, SIZE or COPY > '
      L_PROMPT=38
      N_MENU = 9   !Remove TRANSFER as a command
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,PROMPT,L_PROMPT,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,N_MENU,MENU,ITEM,IERR)
C
         IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                 IF(EXT_NUM.LT.MAX_EXT) THEN
                   CALL GET_EXTERNAL
                 ELSE
                   CALL ERR_MESS(ERROR1,51,-1,*88)
 88                CONTINUE
                 ENDIF
               ELSEIF(ITEM.EQ.7) THEN
                 CALL GET_TITLE(3)
               ELSEIF(ITEM.EQ.8) THEN
                 IF(TMP_EXTSZ.GT.0) THEN
                   IF(EXT_NUM.LT.MAX_EXT) THEN
                    CALL Term_Write(22,0,BLANK,80)
                    CALL Term_Write(18,1,CONFIRM(1),61)
                    CALL Term_Write(19,1,CONFIRM(2),32)
                    CALL Term_Read(0,INPLINE,LLine4,IERR)
                    LLINE = LLine4
                    CALL Term_Write(18,1,BLANK,61)
                    CALL Term_Write(19,1,BLANK,80)
                    IF(INPLINE(1:LLINE).EQ.YES(1:LLINE).AND.
     &                 LLINE.GT.0) THEN
                     CALL GET_SIZE(3)
                     TMP_EXTSZ = 0
                     CALL GET_EXTERNAL
                    ENDIF
                   ELSE
                     CALL ERR_MESS(ERROR1,51,-1,*89)
 89                  CONTINUE
                   ENDIF
                 ELSE
                   CALL GET_SIZE(3)
                 ENDIF
               ELSEIF(ITEM.EQ.9) THEN
                 CALL COPY_TAB(3)
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
               CALL EDIT_DISP(3)
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(5)                !Help asked
               CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
               CALL EDIT_DISP(3)
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               IF(CHANGED.OR.TMP_EXTSZ.GT.0)THEN
                  QUIT = ITEM
               ELSE
                  QUIT = 0
               ENDIF
            ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C     =======================
      SUBROUTINE GET_EXTERNAL
C     =======================
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*2
     &              EX_FLAG             ! Extension flag
C
      INTEGER*4
     &              PREV,               ! Previous line read position
     &              L_TEXT,             ! Length of text
     &              Stat
C
      LOGICAL*1
     &          LAST,                 ! Last point in the line flag
     &          ALL_POINT,            ! All point on the file read flag
     &          END_READ,             ! Read all inputs flag
     &          END_FILE,             ! Read filename flag
     &          CHANGED               ! Table change flag
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      CHARACTER*80
     & TEXT(5)
     &,ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
C
      INCLUDE 'hardisp.inc'
C
      COMMON /CHAN/CHANGED
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
      DATA ERROR1/'%EXTERNAL- Invalid point in the external file'/
      DATA ERROR2/'%EXTERNAL- Not enough point in the external file'/
      DATA ERROR3/'%EXTERNAL- Error #       when reading data file'/
C
      TEXT(1) = '    <<< GET EXTERNAL TABLE VALUES FROM A DATA FILE >>>'
      TEXT(2) = '* The data have to be stored in an ASCII file '
      TEXT(3) = '* Each value has to be separated by a space or a comma'
      TEXT(4) = '* The format of the input has to be specified in real'
      TEXT(5) = '* The default file directory is : '
C
C      Print introduction text
C      -----------------------
       CALL CL_DISP4
C
       TEXT(1)(1:4) = BRT_STRT
       TEXT(1)(55:59) = BRT_END
       TEXT(5)(35:34+L_EXTDIR) = EXTDATDIR(1:L_EXTDIR)
       L_TEXT = 34+L_EXTDIR
       CALL Term_Write(12,15,TEXT(1),59)
       CALL Term_Write(14,1,TEXT(2),46)
       CALL Term_Write(15,1,TEXT(3),54)
       CALL Term_Write(16,1,TEXT(4),53)
       CALL Term_Write(17,1,TEXT(5),L_TEXT)
C
       END_FILE=.FALSE.
       END_READ=.FALSE.
C
C      READ DEFAULT DIRECTORY:
C
C      Prompt the user for input, and branch to that routine which corresponds
C      to his selection.
C      -----------------------------------------------------------------------
       PROMPT='Enter file directory [ <CR>=DEFAULT ] > '
       CALL READ_COMMAND(0,19,1,PROMPT,40,COMMAND,L_COM,Stat)
C
C      If <CR> entered: use default directory, already set or check name
C      -----------------------------------------------------------------
       IF(L_COM.GT.0)THEN
          CALL PARSE_COMMAND(COMMAND,L_COM,-5,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 CALL EDIT_DISP(3)
                 CALL Term_Write(12,15,TEXT(1),59)
                 CALL Term_Write(14,1,TEXT(2),46)
                 CALL Term_Write(15,1,TEXT(3),54)
                 CALL Term_Write(16,1,TEXT(4),53)
                 CALL Term_Write(17,1,TEXT(5),L_TEXT)
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(16)               !Help asked
                 CALL EDIT_DISP(3)
                 CALL Term_Write(12,15,TEXT(1),59)
                 CALL Term_Write(14,1,TEXT(2),46)
                 CALL Term_Write(15,1,TEXT(3),54)
                 CALL Term_Write(16,1,TEXT(4),53)
                 CALL Term_Write(17,1,TEXT(5),L_TEXT)
             ELSE                      !QUIT,EXIT or X command
                 END_FILE=.TRUE.
                 END_READ = .TRUE.
             ENDIF
          ELSE
C
C            Set the directory
C            -----------------
             EXTDATDIR = COMMAND(1:L_COM)
             L_EXTDIR = L_COM
             L_TEXT = 34+L_COM
             TEXT(5)(35:34+L_COM) = COMMAND(1:L_COM)
             CALL Term_Write(17,1,TEXT(5),L_TEXT)
C
          ENDIF
       ENDIF
C
C      READ FILENAME:
C
C      Prompt the user for input, and branch to that routine which corresponds
C      to his selection.
C      -----------------------------------------------------------------------
       PROMPT='Enter external data filename [ external [.dat] ] > '
       DO WHILE(.NOT.END_FILE)
          CALL READ_COMMAND(0,20,1,PROMPT,51,COMMAND,L_COM,Stat)
          IF(L_COM.GT.0)THEN
            CALL PARSE_COMMAND(COMMAND,L_COM,-5,MENU,ITEM,IERR)
C
            IF(IERR.EQ.0) THEN
C
             IF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 CALL EDIT_DISP(3)
                 CALL Term_Write(12,15,TEXT(1),59)
                 CALL Term_Write(14,1,TEXT(2),46)
                 CALL Term_Write(15,1,TEXT(3),54)
                 CALL Term_Write(16,1,TEXT(4),53)
                 CALL Term_Write(17,1,TEXT(5),L_TEXT)
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(16)               !Help asked
                 CALL EDIT_DISP(3)
                 CALL Term_Write(12,15,TEXT(1),59)
                 CALL Term_Write(14,1,TEXT(2),46)
                 CALL Term_Write(15,1,TEXT(3),54)
                 CALL Term_Write(16,1,TEXT(4),53)
                 CALL Term_Write(17,1,TEXT(5),L_TEXT)
             ELSE                      !QUIT,EXIT or X command
                 END_FILE=.TRUE.
                 END_READ = .TRUE.
             ENDIF
            ELSE
C
C            Set the filename
C            ----------------
             EX_FLAG = INDEX(COMMAND(1:L_COM),'.')
             IF (EX_FLAG.EQ.0) THEN
                COMMAND(L_COM+1:L_COM+4) = '.dat'
                L_COM = L_COM+4
             ENDIF
             EXTDATFIL = COMMAND(1:L_COM)
C
C            Open data file
C            --------------
             CALL FIL_OPEN(12,1,IERR)
C
             END_FILE=.TRUE.
             IF(IERR.NE.0) THEN
                END_READ = .TRUE.
             ENDIF

            ENDIF
          ELSE
C
C           <CR> entered: Set filename EXTERNAL.DAT
C           ---------------------------------------
            EXTDATFIL = 'external.dat'
C
C           Open data file
C           --------------
            CALL FIL_OPEN(12,1,IERR)
C
            END_FILE=.TRUE.
            IF(IERR.NE.0) THEN
              END_READ = .TRUE.
            ENDIF
          ENDIF
       ENDDO
C
C      Read the external points
C      ------------------------
       IF(.NOT.END_READ) THEN
         ALL_POINT=.FALSE.
         II=1
         DO WHILE(.NOT.ALL_POINT)
 105        CONTINUE
            READ(EXT_UNIT,'(A)',ERR=102,IOSTAT=IERR) INPLINE
            LLINE = 80
            CALL STRING_LEN(INPLINE,LLINE)
            JJ = 1
 103        CONTINUE
            DO WHILE(INPLINE(JJ:JJ).EQ.' '.AND.JJ.LE.LLINE)
               JJ = JJ+1
            ENDDO
            PREV=JJ
            DO WHILE(INPLINE(JJ:JJ).NE.' '.AND.INPLINE(JJ:JJ).NE.','
     &                       .AND.JJ.LE.LLINE)
               JJ = JJ+1
            ENDDO
            IF(JJ.GT.LLINE) THEN
                LAST = .TRUE.
            ELSE
                LAST = .FALSE.
            ENDIF
            CALL NUMB_STR(INPLINE(PREV:JJ-1),(JJ-PREV),NUMBER,IERR)
            PREV = JJ+1
            IF(IERR.EQ.0) THEN
               TMP_EXPO(II) = NUMBER
               II = II + 1
               IF(II.GT.TMP_TBSZ) THEN
                  ALL_POINT=.TRUE.
               ELSE
                  JJ = JJ+1
                  IF(LAST) THEN
                     GOTO 105
                  ELSE
                     GOTO 103
                  ENDIF
               ENDIF
            ELSE
               IF(IERR.EQ.9999)THEN
                 CALL ERR_MESS(ERROR1,45,-1,*90)
 90              CONTINUE
               ELSE
                 IERR = 9999
               ENDIF
               ALL_POINT = .TRUE.
            ENDIF
         ENDDO
C
 102     IF (IERR.LT.0) THEN
            CALL ERR_MESS(ERROR2,48,-1,*91)
 91         CONTINUE
         ELSEIF(IERR.GT.0.AND.IERR.NE.9999)THEN
            CALL GET_ERR_STR(IERR,ERROR3(19:23))
            CALL ERR_MESS(ERROR3,47,-1,*92)
 92         CONTINUE
         ELSE
C
C           Successfull reading of the file
C           -------------------------------
            TMP_EXTSZ = TMP_TBSZ
            SEND(1:16) = '%GET_EXTERNAL : '
            WRITE(SEND(17:20),'(I4)') TMP_EXTSZ
            SEND(21:38) = ' values read from file '
            SEND(39:80) = EXTDATFIL(1:42)
            CALL ERR_MESS(SEND,80,0,*93)
 93         CONTINUE
C
            CHANGED = .TRUE.
C
         ENDIF
         CALL FIL_OPEN(12,2,IERR)
       ENDIF
C
      CALL CL_DISP
      CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
      CALL EDIT_DISP(3)
C
      RETURN
      END
C
C
C     =========================
      SUBROUTINE COPY_TAB(TYPE)
C     =========================
      IMPLICIT NONE
C
C     This subroutine copy an existing table to the editing buffer
C     ------------------------------------------------------------
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2
     & PRTAB_NUM                              !Previous tab number
C
      INTEGER*4
     & LLine4,
     & TYPE,                                  !Copy table type
     & Stat
C
      LOGICAL*1
     &          GET_NEW_TAB,                  ! Get a new table flag
     &          END_READ,                     ! Read table # flag
     &          PRTAB_FOUND,                  ! PREVIOUS True if table exists
     &          TAB_FOUND,                    ! True if table exists
     &          CHANGED                       ! True if table is modified
      CHARACTER*3
     &          YES /'YES'/                 !Yes answer character
C
      CHARACTER*80
     & ERROR1,                  !Various error messages
     & ERROR2,                 !
     & ERROR3,                 !
     & ERROR4                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      COMMON /TAB/ TAB_FOUND
      COMMON /CHAN/CHANGED
C
      DATA ERROR1/'%COPY - Invalid table number  '/
      DATA ERROR2/'%COPY_TAB: ERROR - Mismatch type between copied and e
     &dited table (WAVE type)   '/
      DATA ERROR3/'%COPY_TAB: ERROR - Mismatch type between copied and e
     &dited table (BRKPNT type) '/
      DATA ERROR4/'%COPY_TAB: ERROR - Mismatch type between copied and e
     &dited table (EXTRNL type) '/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
      SEND =   'COPY will overwrite this table: are you sure you want to
     & COPY ?[N] '
C
C     Ask the user for the table number he wishes to copy.
C     ----------------------------------------------------
      PRTAB_NUM = TAB_NUM
      PRTAB_FOUND = TAB_FOUND
C
C     Look for copying table
C     ----------------------
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,'COPY from TABLE # > ',20,COMMAND,
     &                                       L_COM,Stat)
         CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
         IF(IERR.EQ.9999) THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               IF(TYPE.EQ.1) THEN
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
               ELSEIF(TYPE.EQ.2) THEN
                CALL EDIT_DISP(2)
                CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               ELSEIF(TYPE.EQ.3) THEN
                CALL EDIT_DISP(3)
                CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
               ENDIF
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(6)                !Help asked
               IF(TYPE.EQ.1) THEN
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
               ELSEIF(TYPE.EQ.2) THEN
                CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                CALL EDIT_DISP(2)
               ELSEIF(TYPE.EQ.3) THEN
                CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
                CALL EDIT_DISP(3)
               ENDIF
            ELSE                      !QUIT,EXIT or X command
               TAB_FOUND=PRTAB_FOUND
               TAB_NUM = PRTAB_NUM
               END_READ=.TRUE.
            ENDIF
           ENDIF
         ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: set table number
C          --------------------------------
           TAB_FOUND = .FALSE.
           INT_NUM = INT(NUMBER)
           REAL_NUM= FLOAT(INT_NUM)
           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
            IF( (INT_NUM.LE.MAX_TAB).AND.
     &         (INT_NUM.GT.0)                ) THEN
              TAB_NUM = INT_NUM
              CALL TAB_EXIST(TAB_NUM,TAB_FOUND)
            ENDIF
           ENDIF
           IF(.NOT.TAB_FOUND) THEN
              CALL ERR_MESS(ERROR1,30,-1,*94)
 94           CONTINUE
           ELSE
             END_READ = .TRUE.
             IF((HMSIZ(TAB_NUM).GT.0.AND.TYPE.NE.1).OR.
     &            (OTHSZ(TAB_NUM).GT.0.AND.TYPE.NE.2).OR.
     &            (EXTBLN(TAB_NUM).GT.0.AND.TYPE.NE.3)   ) THEN
                 IF(TYPE.EQ.1)THEN
                    CALL MES23(0,ERROR2)
                 ELSEIF(TYPE.EQ.2)THEN
                    CALL MES23(0,ERROR3)
                 ELSE
                    CALL MES23(0,ERROR4)
                 ENDIF
                 CALL BEEP(1)
                 W_LONG = 4.0
                 CALL Wait_Time(W_LONG) !Extra time, it's a complex message
                 CALL MES23(0,BLANK)
                 TAB_NUM = PRTAB_NUM
                 TAB_FOUND = PRTAB_FOUND
             ELSE
C
                 IF (PRTAB_FOUND.OR.CHANGED) THEN
                    CALL Term_Write(23,1,SEND,66)
                    CALL Term_Read(0,INPLINE,LLine4,IERR)
                    LLINE = LLine4
                    CALL Term_Write(23,1,BLANK,80)
                    IF((INPLINE(1:LLINE).EQ.YES(1:LLINE)).AND.
     &                     LLINE.GT.0)THEN
                       GET_NEW_TAB=.TRUE.
                    ELSE
                       GET_NEW_TAB=.FALSE.
                    ENDIF
                 ELSE
                    GET_NEW_TAB=.TRUE.
                 ENDIF
                 IF(GET_NEW_TAB) THEN
C
C                       Restore the contents of the table requested
C                       Clear harmonic and other sizes counter to get only
C                       the new table data: erase the old one to avoid
C                       confusion if old table and new table type are
C                       different
C                       --------------------------------------------------
C
                        TMP_OSZ=0
                        TMP_HMSZ=0
                        TMP_EXTSZ = 0
                        CALL REST_TABLE(1,*6)
 6                      CONTINUE
                        TAB_NUM = PRTAB_NUM
                        TAB_FOUND = PRTAB_FOUND
                        IF(TMP_HMSZ.GT.0) THEN
                           CALL EDIT_DISP(1)
                        ELSEIF(TMP_OSZ.GT.0) THEN
                           CALL EDIT_DISP(2)
                        ELSEIF(TMP_EXTSZ.GT.0) THEN
                           CALL EDIT_DISP(3)
                        ENDIF
                        CHANGED = .TRUE.
                 ELSE
                        TAB_NUM = PRTAB_NUM
                        TAB_FOUND = PRTAB_FOUND
                 ENDIF
C
             ENDIF
           ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C     =====================
      SUBROUTINE SAVE_TABLE
C     =====================

      IMPLICIT NONE
C
C -- This subroutine asks the user if he wishes to save his edits.
C    If yes, then the temporary values are copied to their
C    permanent counterparts.
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1 FOUND,DELFOUND
C
      CHARACTER*80
     & VALDATE*11,VALTIME*11  ,!Date and time fields
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1 /'%SAVE_TABLE - Empty table deleted'/
      DATA ERROR2 /'%SAVE_TABLE - Empty table not saved'/
      DATA ERROR3 /'%SAVE_TABLE - Maximum number of EXTERNAL tables reac
     &hed: unable to save'/
C
C -- Determine if the table already exists
C
      CALL TAB_EXIST(TAB_NUM,FOUND)
C
C -- If the table number does not exist, then increment the
C    the number of tables and add the new table number to the saved
C    list.

      IF(TMP_HMSZ.GT.0.OR.TMP_OSZ.GT.0.OR.TMP_EXTSZ.GT.0) THEN
C
C       If table not empty, save it
C
       IF (.NOT. FOUND .OR. TBLNUM .EQ. 0) THEN
        TBLNUM = TBLNUM + 1
        SAV_TBL(TBLNUM) = TAB_NUM
       ENDIF
C
C      Set data save flag to generate new data file
C      --------------------------------------------
       DATASAVE=.TRUE.
C
C      Save the temporary values in the appropriate buffers
C      ----------------------------------------------------
       TABSIZE(TAB_NUM) = TMP_TBSZ
       TITLE(TAB_NUM) = TEMP_TIT
C
C      Save user and last mod time
C      ---------------------------
       CALL GET_USER
       NAME(TAB_NUM) = USER_ID
       CALL CDATE(VALDATE,VALTIME)
       MODATE(TAB_NUM) = VALDATE//' '//VALTIME(1:5) 
C
       IF (TMP_OSZ .GT. 0) THEN
C
C       Table contains OTHER type
C       -------------------------
        HMSIZ(TAB_NUM) = 0
        OTHSZ(TAB_NUM) = TMP_OSZ
        DO I = 1,TMP_OSZ
          OTHX(TAB_NUM,I) = TMP_XVAL(I)
          OTHY(TAB_NUM,I) = TMP_YVAL(I)
          OTHP(TAB_NUM,I) = TMP_PVAL(I)
        ENDDO
C
       ELSEIF(TMP_HMSZ.GT.0) THEN
C
C       Table contains harmonics
C       ------------------------
        PHASEL(TAB_NUM) = PHASE
        OTHSZ(TAB_NUM) = 0
        HMSIZ(TAB_NUM) = TMP_HMSZ
        DO I = 1,TMP_HMSZ
          HARM(TAB_NUM,I) = TEMP_HARM(I)
          AMPL(TAB_NUM,I) = TEMP_AMPL(I)
          WAVETYPE(TAB_NUM,I) = TEMP_TYPE(I)
        ENDDO
C
       ELSEIF(TMP_EXTSZ.GT.0) THEN
C
C       Table is an external table
C       --------------------------
        IF(EXTBLN(TAB_NUM).EQ.0) THEN
           IF(EXT_NUM.LT.MAX_EXT) THEN
             EXT_NUM = EXT_NUM + 1
             EXTBLN(TAB_NUM) = EXT_NUM
             EXTSIZ(EXT_NUM) = TMP_EXTSZ
             DO I = 1,TMP_EXTSZ
                EXTP(EXT_NUM,I)=TMP_EXPO(I)
             ENDDO
           ELSE
             CALL ERR_MESS(ERROR3,70,-1,*95)
 95          CONTINUE
           ENDIF
        ELSE
           EXTBLN(TAB_NUM) = EXT_NUM
           EXTSIZ(EXT_NUM) = TMP_EXTSZ
           DO I = 1,TMP_EXTSZ
              EXTP(EXT_NUM,I)=TMP_EXPO(I)
           ENDDO
        ENDIF
       ENDIF
C
      ELSE
C
C      Save an empty table: delete it if exist
C      ---------------------------------------
       IF(FOUND) THEN
C
C          If table exist before, delete it
C          --------------------------------
           I = 1
           DELFOUND = .FALSE.
           DO WHILE(I.LE.TBLNUM.AND..NOT.DELFOUND)
              IF(SAV_TBL(I).EQ.TAB_NUM)THEN
                 DELFOUND=.TRUE.
                 SAV_TBL(I) = 0
              ENDIF
              I=I+1
           ENDDO
C
C          Refill the save table array without the deleted table
C          -----------------------------------------------------
           DO I=1,TBLNUM
            IF(SAV_TBL(I).EQ.0)THEN
              DO J=I+1,TBLNUM
                 SAV_TBL(J-1)=SAV_TBL(J)
              ENDDO
              TBLNUM=TBLNUM-1
            ENDIF
           ENDDO
C
C          Delete the table in source assign array
C          ---------------------------------------
           DO MM=1,MAX_DSG
             DO J=1,MAXI_SOUR
               IF(SOURCE(J,MM).EQ.TAB_NUM) THEN
                  SOURCE(J,MM)=0
               ENDIF
             ENDDO
           ENDDO 
           CALL ERR_MESS(ERROR1,33,-1,*96)
 96        CONTINUE
C
C          Set data save flag to generate new data file
C          --------------------------------------------
           DATASAVE=.TRUE.
       ELSE
           CALL ERR_MESS(ERROR2,36,-1,*97)
 97        CONTINUE
       ENDIF
      ENDIF
      RETURN
      END
C
C
C     ===============================
      SUBROUTINE HARMONICS(PREV_DISP)
C     ===============================
      IMPLICIT NONE
C
C -- This subroutine obtains the necessary inputs when the user selects
C    to enter harmonics for the table data.
C
      INTEGER*2
     &          FOUND_NUM              ! Number found
C
      INTEGER*4
     &          PREV_DISP              ! Previous display harmonic
C
      LOGICAL*1 END_READ,END_HARM,END_AMPL,EXIST
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
C     As long as the the user does not quit, get the harmonic number, the
C     corresponding amplitude and the type.  Then store these values in
C     temporary buffers.
C     --------------------------------------------------------------------
C
      END_READ = .FALSE.
C
      DO WHILE (.NOT. END_READ)
        CALL GET_HARM(PREV_DISP,END_READ)
        END_HARM = END_READ
C
        DO WHILE(.NOT.END_HARM)
           CALL GET_AMPL(END_HARM)
           END_AMPL = END_HARM
C
           DO WHILE(.NOT.END_AMPL)
              CALL GET_TYPE(EXIST,FOUND_NUM,END_AMPL)
              IF(.NOT.END_AMPL) THEN
                CALL STORE_INP(PREV_DISP,EXIST,FOUND_NUM)
                END_AMPL = .TRUE.
                END_HARM = .TRUE.
              ENDIF
           ENDDO
        ENDDO
      ENDDO
C
      RETURN
      END
C
C     ===============================================
      SUBROUTINE STORE_INP(PREV_DISP,EXIST,FOUND_NUM)
C     ===============================================
C
C     This subroutine stores harmonics and their associated types and
C     amplitudes in temporary arrays
C     ---------------------------------------------------------------
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          D_FLAG,                ! Changing area flag
     &          DIFLAG,                ! One value resorted
     &          EXIST,                 ! True if table exists
     &          CHANGED                ! True if table is modified
C
      INTEGER*4
     &          LENGTH,                ! Length of changing screen area
     &          POSTRT,                ! Start position
     &          PREV_DISP              ! Prevision screen position
C
      INTEGER*2
     &          OUT(MAX_HNUM),         ! Sorted index for harmonics number
     &          NEWMOD,                ! New MOD 20 of pointer
     &          OLDMOD,                ! Old MOD 20 of pointer
     &          D_START,               ! Start of changing screen area
     &          D_END,                 ! End of changing screen area
     &          SORT_TYPE(MAX_HNUM),   ! Temporary storage for harmonic type
     &          SORT_HARM(MAX_HNUM),   ! Temporary storage for harmonic number
     &          FOUND_NUM              ! Number found
C
       REAL*4
     &          SORT_AMPL(MAX_HNUM)    ! Temporary storage for harmonic amplitude
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
C
      INCLUDE 'hardata.inc'
C
      COMMON /CHAN/CHANGED
C
      DATA ERROR1/'%WAVE_EDIT - Null harmonic not inserted      '/
      DATA ERROR2/'%WAVE_EDIT - Maximum number of harmonic reached '/
C
C     If the harmonic already exists, then store the input values in the
C     correct array elements.  Otherwise, store the input values after
C     incrementing the all array sizes.
C     ------------------------------------------------------------------
      DIFLAG = .FALSE.
      D_FLAG = .FALSE.
C
      IF (EXIST) THEN
       CHANGED = .TRUE.
       IF (AMPLITUDE .NE. 0) THEN
        EXIST = .FALSE.
        TEMP_AMPL(FOUND_NUM) = AMPLITUDE
        TEMP_TYPE(FOUND_NUM) = TYP
        D_FLAG=.TRUE.
        D_START=FOUND_NUM
        D_END = FOUND_NUM
       ELSE
C
C       If the amplitude is zero, then delete the associated harmonic and type
C       from all arrays
C       -----------------------------------------------------------------------
        IF(FOUND_NUM.LT.TMP_HMSZ) THEN
          DO I = FOUND_NUM,TMP_HMSZ-1
            TEMP_HARM(I) = TEMP_HARM(I+1)
            TEMP_AMPL(I) = TEMP_AMPL(I+1)
            TEMP_TYPE(I) = TEMP_TYPE(I+1)
          ENDDO
        ENDIF
        DIFLAG=.TRUE.
        D_START=FOUND_NUM
        D_END = TMP_HMSZ
        TMP_HMSZ = TMP_HMSZ - 1
       ENDIF
      ELSE
       IF(AMPLITUDE.GT.0) THEN
        IF (TMP_HMSZ .LT. MAX_HNUM) THEN
          TMP_HMSZ = TMP_HMSZ + 1
          CHANGED = .TRUE.
          TEMP_HARM(TMP_HMSZ) = HARM_NUM
          TEMP_AMPL(TMP_HMSZ) = AMPLITUDE
          TEMP_TYPE(TMP_HMSZ) = TYP
          D_START=TMP_HMSZ
          D_END = TMP_HMSZ
        ELSE
C
C         If no more harmonics can be input, then beep and return
C         -------------------------------------------------------
          CALL ERR_MESS(ERROR2,47,-1,*98)
 98       CONTINUE
        ENDIF
       ELSE
C
C       Don't accept zero harmonic amplitude
C       ------------------------------------
        CALL ERR_MESS(ERROR1,45,1,*565)
       ENDIF
      ENDIF
C
      IF(.NOT.DIFLAG) THEN
       IF(TMP_HMSZ.GT.0) THEN
C
C        Sort the harmonics in ascending order if table not empty
C        --------------------------------------------------------
         DO I = 1,TMP_HMSZ
           SORT_HARM(I) = INT(TEMP_HARM(I)*100.0)
           SORT_AMPL(I) = TEMP_AMPL(I)
           SORT_TYPE(I) = TEMP_TYPE(I)
         ENDDO
         CALL SORT(1,SORT_HARM,OUT,TMP_HMSZ)
         DO I = 1,TMP_HMSZ
           IF(OUT(I).NE.I)THEN
              IF(.NOT.D_FLAG) D_START=I
              D_FLAG=.TRUE.
              D_END = I
           ENDIF
           TEMP_HARM(I) = FLOAT(SORT_HARM(OUT(I)))/100.0
           TEMP_AMPL(I) = SORT_AMPL(OUT(I))
           TEMP_TYPE(I) = SORT_TYPE(OUT(I))
         ENDDO
       ENDIF
      ENDIF
C
C     Display the other harmonics
C     ---------------------------
      IF(D_FLAG)THEN
        OLDMOD=((PREV_DISP-1)/20)*20+1
        NEWMOD=((D_START-1)/20)*20+1
        IF(NEWMOD.NE.OLDMOD) THEN
          CALL CL_DISP4
          PREV_DISP = NEWMOD
          PHASE4 = PHASE
          CALL DISPLAY(PREV_DISP,1,-1,TMP_TBSZ,TMP_HMSZ,
     &                       PHASE4,TEMP_TIT)
        ELSE
          POSTRT = D_START-PREV_DISP+1
          LENGTH=MIN((D_END-D_START+1),(20-POSTRT+1))
          PHASE4 = PHASE
          CALL DISPLAY(PREV_DISP,POSTRT,LENGTH,TMP_TBSZ,TMP_HMSZ,
     &                       PHASE4,TEMP_TIT)
        ENDIF
      ELSE
        OLDMOD=((PREV_DISP-1)/20)*20+1
        NEWMOD=((D_START-1)/20)*20+1
        IF(NEWMOD.NE.OLDMOD) THEN
            PREV_DISP=NEWMOD
            CALL CL_DISP4
            PHASE4 = PHASE
            CALL DISPLAY(PREV_DISP,1,-1,TMP_TBSZ,TMP_HMSZ,
     &                   PHASE4,TEMP_TIT)
        ELSE
            POSTRT=D_START-PREV_DISP+1
            LENGTH=MIN((D_END-D_START+1),(20-POSTRT+1))
            PHASE4 = PHASE
            CALL DISPLAY(PREV_DISP,POSTRT,LENGTH,TMP_TBSZ,TMP_HMSZ,
     &                   PHASE4,TEMP_TIT)
        ENDIF
      ENDIF
C
 565  CONTINUE
      RETURN
      END
C
C     ====================
      SUBROUTINE GET_PHASE
C     ====================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          END_READ,              ! Read phase flag
     &          CHANGED                ! True if table is modified
C
      INTEGER*4
     & Stat
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      COMMON /CHAN/CHANGED
C
C     If the table already contains OTHER types, then write error message
C     and return
C     -------------------------------------------------------------------
      DATA PROMPT/'PHASE : RANDOM, ZERO, SCHROEDER''S RULE > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'RANDOM'/
      DATA MENU(7)/'ZERO'/
      DATA MENU(8)/'SCHROEDER'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,42,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,8,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                  PHASE = 1
               ELSEIF(ITEM.EQ.7) THEN
                  PHASE = -1
               ELSEIF(ITEM.EQ.8) THEN
                  PHASE = 0
               ENDIF
               CHANGED = .TRUE.
               PHASE4 = PHASE
               CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
               END_READ=.TRUE.
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                CALL MODE_SET(-1)           !Display output mode
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                CALL HELP(8)                !Help asked
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ENDIF
      ENDDO
C
      RETURN
      END
C
C
C     ==============================================
      SUBROUTINE GET_TYPE(EXIST,FOUND_NUM,END_ENTER)
C     ==============================================
C
C     This subroutine obtains the type of harmonic, checks for errors,
C     and sets TYP to the selected type
C     If mode is not sine wave, read type
C     ----------------------------------------------------------------
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
C
      INTEGER*2
     &          FOUND_NUM                     ! Previous type
C
      INTEGER*4
     &          Stat,
     &          L_PROMPT                      ! Prompt length
C
      LOGICAL*1
     &          EXIST,                        ! Previous type exist flag
     &          END_ENTER,                    ! End-Quit flag
     &          END_READ                      ! Read amplitude flag
C
      CHARACTER    PROMPT*80,
     &             MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'SINEWAVE'/
      DATA MENU(7)/'SQUAREWAVE'/
      DATA MENU(8)/'TRIANGLE'/
      DATA MENU(9)/'SAWTOOTH'/
C
      EXIST = .FALSE.   !Reset EXIST until this routine set it
C
      END_ENTER=.FALSE.
C
C     If the harmonic already exists, then the default type by hitting <cr>
C     is the one existing otherwise it is SINEWAVE.
C     ---------------------------------------------------------------------
      I = 1
      DO WHILE (I .LE. TMP_HMSZ .AND. .NOT. EXIST)
         IF (HARM_NUM .EQ. TEMP_HARM(I)) THEN
          FOUND_NUM = I
          EXIST = .TRUE.
         ENDIF
         I = I + 1
      ENDDO
C
C     If in sinewave mode, skip this check and set sinewave as default
C     Also skip if amplitude zero
C     ----------------------------------------------------------------
      IF(.NOT.SIN_MOD.AND.AMPLITUDE.GT.0) THEN
C
C       Set the corresponding prompt
C       ----------------------------
        IF(EXIST) THEN
           PROMPT='TYPE : SIN, SQU, TRI, OR SAW ( <CR> = no change ) > '
           L_PROMPT = 52
        ELSE
           PROMPT='TYPE : SIN, SQU, TRI, OR SAW ( <CR> = SIN ) > '
           L_PROMPT = 46
        ENDIF
C
C       Prompt the user for input, and branch to that routine which corresponds
C       to his selection.
C       -----------------------------------------------------------------------
        END_READ=.FALSE.
        DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(0,22,1,PROMPT,L_PROMPT,COMMAND,L_COM,Stat)
         IF(L_COM.GT.0)THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,9,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
               TYP = ITEM-5
               END_READ = .TRUE.
            ELSE
             IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(10)                !Help asked
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
             ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               END_ENTER=.TRUE.
             ENDIF
            ENDIF
           ENDIF
         ELSE
C
C          <CR> entered: set default
C          -------------------------
           IF(EXIST) THEN
              TYP = TEMP_TYPE(FOUND_NUM)  !Previous
           ELSE
              TYP = 1          !Sinewave
           ENDIF
           END_READ = .TRUE.
         ENDIF
       ENDDO
C
      ELSE
       TYP = 1
      ENDIF
C
      RETURN
      END
C
C
C     ==============================
      SUBROUTINE GET_OTHER(PNT_DISP)
C     ==============================
C
      IMPLICIT NONE
C
C     This subroutine obtains OTHER type data from the user.
C     ------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          FIRST_DIF,           ! First difference flag
     &          F_SORT,              ! First time sorted display flag
     &          D_FLAG,              ! Difference flag
     &          INVAL,               ! Invalid input flag
     &          CHANGED,             ! True if table is modified
     &          FOUND,               ! Misc. flag
     &          FIND,                ! Misc. flag
     &          END_OTH              ! True if OTHER mode is ended
C
      INTEGER*2
     &          D_START,               ! Start of changing screen area
     &          D_END,                 ! End of changing screen area
     &          START_DIF,           ! Start or new data in sorted array
     &          PRPOINT,             ! Point number selected (BYPASS 2X CHECK)
     &          TEMP_XI(MAX_OTH),    ! Temporary reorder X values in INTEGER
     &          POINT_X(MAX_OTH),    ! Reordered X value array pointer
     &          PMAX,                ! Max number of points
     &          OLDMOD,              ! Old MOD 20 of pointer
     &          OLDPNT,              ! Previous pointer
     &          LENGTH2,
     &          PNT                  ! Point number
C
      INTEGER*4
     &          POSTRT,             ! Start position
     &          PNT_DISP,           ! Pointer for display
     &          DISPNT1,            ! Location of pointer for display bfre sort
     &          LENGTH,             ! Length of refresh buffer
     &          Stat,
     &          NEWMOD              ! New MOD 20 of pointer
C
      REAL*4 VAL(2),
     &       TEMP_X(MAX_OTH),         !Temporary reorder X values
     &       TEMP_Y(MAX_OTH)         !Temporary reorder Y values
C
      LOGICAL*1
     &          END_X_READ,           ! Read X value flag
     &          END_Y_READ            ! Read Y value flag
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
     &,ERROR5                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      COMMON /CHAN/CHANGED
C
      DATA ERROR1  /'%GET_BRKPNT- The highest breakpoint is display on t
     &his screen'/
      DATA ERROR2  /'%GET_BRKPNT- The lowest breakpoint is display on th
     &is screen'/
      DATA ERROR3/'%BRKPNT_EDIT: Maximum number of other points reached'
     &/
      DATA ERROR4 /'%BRKPNT_EDIT: Invalid pointer number entered'/
      DATA ERROR5 /'%BRKPNT_EDIT: Invalid breakpoint value (0% to 100%)
     &'/
      DATA PROMPT/'EDIT BREAKPOINT #    : X VAL > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'NEXT'/
      DATA MENU(7)/'PREVIOUS'/
      DATA MENU(8)/'LAST'/
C
      SEND     =  '%BRKPNT_EDIT: This X value exist: 2 points with the s
     &ame X value not allow'
C
      CALL Term_Write(21,1,BLANK,80)
      CALL Term_Write(22,1,BLANK,80)
C
      PNT = MIN(TMP_OSZ+1,MAX_OTH+0)
      END_X_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_X_READ)
        WRITE(PROMPT(19:20),'(I2)') PNT
        CALL READ_COMMAND(0,21,1,PROMPT,31,COMMAND,L_COM,Stat)
        PRPOINT = PNT            !Allow changes of this point only
        IF(L_COM.GT.0)THEN
         CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
         IF(IERR.EQ.9999) THEN
          IF(COMMAND(1:2).EQ.'BR') THEN   ! Display BReakpoint pointer
             OLDPNT = PNT
             CALL NUMB_STR(COMMAND(3:80),(L_COM-2),NUMBER,IERR)
             FOUND = .FALSE.
             IF(IERR.EQ.0) THEN
C
C               If the point does not fall in the allowed range
C               -----------------------------------------------
                INT_NUM = INT(NUMBER)
                REAL_NUM= FLOAT(INT_NUM)
                IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
                  PMAX = MIN(TMP_OSZ + 1,MAX_OTH+0)
                  IF(INT_NUM.GT.0.AND.INT_NUM.LE.PMAX)THEN
                   PNT = INT_NUM
                   FOUND = .TRUE.
                   OLDMOD = ((PNT_DISP-1)/20)*20+1
                   NEWMOD = ((PNT-1)/20)*20+1
                   IF(OLDMOD.NE.NEWMOD)THEN  !Redisplay only when pass MOD 20
                    CALL DISP_OTH(NEWMOD,-2,-1,TMP_OSZ,TMP_TBSZ,
     &                                                   TEMP_TIT)
                    PNT_DISP=NEWMOD
                   ENDIF
                  ENDIF
                ENDIF
             ENDIF
             IF(.NOT.FOUND) THEN
                CALL ERR_MESS(ERROR4,44,-1,*2)
 2              CONTINUE
                PNT = OLDPNT
             ENDIF
          ELSEIF(COMMAND(1:1).EQ.'D') THEN   ! Display Real pointer
             OLDPNT = PNT
             CALL NUMB_STR(COMMAND(2:80),(L_COM-1),NUMBER,IERR)
             FOUND = .FALSE.
             IF(IERR.EQ.0) THEN
C
C               If the point does not fall in the allowed range
C               -----------------------------------------------
                INT_NUM = INT(NUMBER)
                REAL_NUM= FLOAT(INT_NUM)
                IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
                  PMAX = TMP_OSZ
                  IF(INT_NUM.GT.0.AND.INT_NUM.LE.PMAX)THEN
                   PNT = INT_NUM
                   FOUND = .TRUE.
C
C                  Delete the point in the array
C                  -----------------------------
                   FIND = .FALSE.
                   I = 1
                   DO WHILE (.NOT. FIND )
                     IF (TMP_PVAL(I).EQ.PNT) THEN
                       FIND = .TRUE.
                       DO KK=I,TMP_OSZ-1
                          TMP_XVAL(KK) = TMP_XVAL(KK+1)
                          TMP_YVAL(KK) = TMP_YVAL(KK+1)
                       ENDDO
                       TMP_OSZ=TMP_OSZ-1
                     ELSE
                       I = I+1
                     ENDIF
                   ENDDO
                   CHANGED = .TRUE.
                   IF(PNT.LT.PNT_DISP) THEN
                      LENGTH = MIN(20,(TMP_OSZ+1-PNT_DISP+1))
                      CALL DISP_OTH(-1,-2,LENGTH,TMP_OSZ,TMP_TBSZ,
     &                                       TEMP_TIT)
                   ELSEIF(PNT.GE.PNT_DISP.AND.PNT.LT.(PNT_DISP+20))THEN
                      DISPNT1 = PNT-PNT_DISP+1
                      LENGTH = MIN((TMP_OSZ+1-PNT+1),(20-DISPNT1+1))
                      IF(LENGTH.EQ.1.AND.DISPNT1.EQ.1
     &                        .AND.PNT_DISP.GT.TMP_OSZ)THEN
                         LENGTH2 = MIN(20,TMP_OSZ+0)
                         LENGTH = LENGTH2
                         DISPNT1 = 1
                         PNT_DISP = MAX((PNT_DISP-20),1)
                      ENDIF
                      CALL DISP_OTH(PNT_DISP,DISPNT1,LENGTH,TMP_OSZ,
     &                              TMP_TBSZ,TEMP_TIT)
                   ENDIF
                  ENDIF
                ENDIF
             ENDIF
             IF(.NOT.FOUND) THEN
                CALL ERR_MESS(ERROR4,44,-1,*3)
 3              CONTINUE
                PNT = OLDPNT
             ENDIF
          ELSE
           CALL PARSE_COMMAND(COMMAND,L_COM,8,MENU,ITEM,IERR)
           IF(IERR.EQ.0) THEN
            IF(ITEM.GE.6) THEN
               IF(ITEM.EQ.6) THEN     !NEXT command
                 OLDPNT = PNT
                 OLDMOD = ((PNT-1)/20)*20+1
                 PNT = MIN(PNT_DISP+20,TMP_OSZ+0)
                 NEWMOD = ((PNT-1)/20)*20+1
                 IF(OLDMOD.NE.NEWMOD) THEN
                   CALL DISP_OTH(NEWMOD,-2,-1,TMP_OSZ,TMP_TBSZ,
     &                                                   TEMP_TIT)
                   PNT_DISP=NEWMOD
                 ELSE
                   CALL ERR_MESS(ERROR1,61,-1,*4)
 4                 CONTINUE
                 ENDIF
               ELSEIF(ITEM.EQ.7)THEN  !PREV command
                 OLDPNT = PNT
                 OLDMOD = ((PNT-1)/20)*20+1
                 PNT = MAX(PNT_DISP-20,1)
                 NEWMOD = ((PNT-1)/20)*20+1
                 IF(OLDMOD.NE.NEWMOD) THEN
                   CALL DISP_OTH(NEWMOD,-2,-1,TMP_OSZ,TMP_TBSZ,
     &                                                   TEMP_TIT)
                   PNT_DISP=NEWMOD
                 ELSE
                   CALL ERR_MESS(ERROR2,60,-1,*5)
 5                 CONTINUE
                 ENDIF
               ELSEIF(ITEM.EQ.8) THEN !LAST command
                 OLDPNT = PNT
                 PNT = MIN(TMP_OSZ+1,MAX_OTH+0)
                 OLDMOD = ((PNT_DISP-1)/20)*20+1
                 NEWMOD = ((PNT-1)/20)*20+1
                 IF(OLDMOD.NE.NEWMOD)THEN  !Redisplay only when pass MOD 20
                    CALL DISP_OTH(NEWMOD,-2,-1,TMP_OSZ,TMP_TBSZ,
     &                                                   TEMP_TIT)
                    PNT_DISP=NEWMOD
                 ENDIF
               ENDIF
            ELSE
               IF(ITEM.EQ.1) THEN        !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                 CALL EDIT_DISP(2)
               ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(11)                !Help asked
                 CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                 CALL EDIT_DISP(2)
               ELSE                      !QUIT,EXIT or X command
                 CALL Term_Write(21,1,BLANK,80)
                 CALL Term_Write(22,1,BLANK,80)
                 END_X_READ = .TRUE.
               ENDIF
            ENDIF
           ENDIF
          ENDIF
         ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: set X VAL
C          -------------------------
C          Allow only one time an X element in the X array
C          -----------------------------------------------
           INVAL=.FALSE.
           DO II=1,TMP_OSZ
              IF(TMP_XVAL(II).EQ.NUMBER.AND.(II.NE.PRPOINT))THEN
                 CALL ERR_MESS(SEND,76,-1,*6)
 6               CONTINUE
                 INVAL=.TRUE.
              ENDIF
           ENDDO
           IF(.NOT.INVAL) THEN
              IF(NUMBER.LE.100.0.AND.NUMBER.GE.0.0)THEN
                 VAL(1)=NUMBER
C
C                Enter Y Value
C                -------------
                 END_Y_READ=.FALSE.
                 DO WHILE(.NOT.END_Y_READ)
                  CALL READ_COMMAND(-1,22,22,': Y VAL > ',10,COMMAND,
     &                                                     L_COM,Stat)
                  CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
                  IF(IERR.EQ.9999) THEN
                   CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
                   IF(IERR.EQ.0) THEN
                     IF(ITEM.EQ.1) THEN    !BOX command
                      CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                      CALL MODE_SET(-1)           !Display output mode
                      CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,
     &                                               TEMP_TIT)
                      CALL EDIT_DISP(2)
                      WRITE(PROMPT(32:36),'(F5.1)') VAL(1)
                      CALL Term_Write(21,1,PROMPT,36)
                     ELSEIF(ITEM.EQ.2) THEN    !HELP command
                      CALL HELP(11)                !Help asked
                      CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,
     &                                                TEMP_TIT)
                      CALL EDIT_DISP(2)
                      WRITE(PROMPT(32:36),'(F5.1)') VAL(1)
                      CALL Term_Write(21,1,PROMPT,36)
                     ELSE                      !QUIT,EXIT or X command
                      END_Y_READ=.TRUE.
                     ENDIF
                   ENDIF
                  ELSEIF(IERR.EQ.0)THEN
C
C                  Number entered: set Y VAL
C                  -------------------------
                   IF(NUMBER.LE.100.0.AND.NUMBER.GE.0.0)THEN
                      VAL(2)=NUMBER
                      END_Y_READ=.TRUE.
                      D_FLAG=.FALSE.
                      FOUND = .FALSE.
                      IF (PNT .LE. TMP_OSZ) THEN
                        I = 1
                        DO WHILE (.NOT. FOUND .AND. I .LE. TMP_OSZ)
                          IF (TMP_PVAL(I).EQ.PNT) THEN
                            CHANGED = .TRUE.
                            FOUND = .TRUE.
                            TMP_XVAL(I) = VAL(1)
                            TMP_YVAL(I) = VAL(2)
                            D_START = I
                            D_END = I
                          ELSE
                            I = I+1
                          ENDIF
                        ENDDO
                      ENDIF
C
C                     If the point is the next point, then store it in the next available
C                     array element
C                     ----------------------------------------------------------
                      IF(.NOT.FOUND) THEN
                        TMP_OSZ = TMP_OSZ + 1
                        IF(TMP_OSZ.LE.MAX_OTH) THEN
                            CHANGED = .TRUE.
                            TMP_PVAL(TMP_OSZ) = PNT
                            TMP_XVAL(TMP_OSZ) = VAL(1)
                            TMP_YVAL(TMP_OSZ) = VAL(2)
                            D_START = TMP_OSZ
                            D_END = TMP_OSZ
                        ELSE
                            TMP_OSZ = TMP_OSZ - 1
                            CALL ERR_MESS(ERROR3,52,-1,*7)
 7                          CONTINUE
                        ENDIF
C
C                       Restructure the other array in X order
C                       --------------------------------------
                        DO II=1,TMP_OSZ
                           TEMP_XI(II)=100.0*TMP_XVAL(II)
                           TEMP_X(II)=TMP_XVAL(II)
                           TEMP_Y(II)=TMP_YVAL(II)
                        ENDDO
                        CALL SORT(1,TEMP_XI,POINT_X,TMP_OSZ)
                        F_SORT = .TRUE.
                        DO II=1,TMP_OSZ
                           IF(POINT_X(II).NE.II) THEN
                              IF(F_SORT) THEN
                                F_SORT=.FALSE.
                                D_FLAG=.FALSE.
                              ENDIF
                              IF(.NOT.D_FLAG) D_START=II
                              D_FLAG=.TRUE.
                              D_END = II
                           ENDIF
                           TMP_PVAL(II) = II
                           TMP_XVAL(II) = TEMP_X(POINT_X(II))
                           TMP_YVAL(II) = TEMP_Y(POINT_X(II))
                        ENDDO
                      ENDIF
C
C                     Display the other harmonics
C                     ---------------------------
                      IF(D_FLAG)THEN
                        OLDMOD=((PNT_DISP-1)/20)*20+1
                        NEWMOD=((D_START-1)/20)*20+1
                        IF(NEWMOD.NE.OLDMOD) THEN
                            PNT_DISP=NEWMOD
                            PNT = D_START
                            CALL CL_DISP4
                            PHASE4 = PHASE
                            CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
                            CALL DISP_OTH(PNT_DISP,1,-1,TMP_OSZ,
     &                                    TMP_TBSZ,TEMP_TIT)
                            WRITE(PROMPT(32:36),'(F5.1)') VAL(1)
                            CALL Term_Write(21,1,PROMPT,36)
                        ELSE
                            LENGTH=D_END-D_START+1
                            POSTRT = D_START-PNT_DISP+1
                            CALL DISP_OTH(PNT_DISP,POSTRT,LENGTH,
     &                                    TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                        ENDIF
                      ELSE
                        OLDMOD=((PNT_DISP-1)/20)*20+1
                        NEWMOD=((D_START-1)/20)*20+1
                        IF(NEWMOD.NE.OLDMOD) THEN
                            PNT_DISP=NEWMOD
                            CALL CL_DISP4
                            PHASE4 = PHASE
                            CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
                            CALL DISP_OTH(PNT_DISP,1,-1,TMP_OSZ,
     &                                    TMP_TBSZ,TEMP_TIT)
                            WRITE(PROMPT(32:36),'(F5.1)') VAL(1)
                            CALL Term_Write(21,1,PROMPT,36)
                        ELSE
                            POSTRT=D_START-PNT_DISP+1
                            CALL DISP_OTH(PNT_DISP,POSTRT,1,TMP_OSZ,
     &                                    TMP_TBSZ,TEMP_TIT)
                        ENDIF
                      ENDIF
                      PNT = MIN(PNT+1,MAX_OTH+0)
                   ELSE
                      CALL ERR_MESS(ERROR5,51,-1,*8)
 8                    CONTINUE
                   ENDIF
                  ENDIF
                 ENDDO
                 CALL Term_Write(22,22,BLANK,10+L_COM)
              ELSE
                 CALL ERR_MESS(ERROR5,51,-1,*9)
 9               CONTINUE
              ENDIF
           ENDIF
         ENDIF
        ELSE
C
C        <CR> entered: increase pointer
C        ------------------------------
         PMAX = MIN(TMP_OSZ + 1,MAX_OTH+0)
         IF (PNT.LT.PMAX) THEN
            OLDPNT = PNT
C
C           If the point exist, allow X value to be a previous one
C           ------------------------------------------------------
            IF(PNT.LT.MAX_OTH) PNT = PNT+1
            OLDMOD = ((PNT_DISP-1)/20)*20+1
            NEWMOD = ((PNT-1)/20)*20+1
            IF(OLDMOD.NE.NEWMOD)THEN  !Redisplay only when pass MOD 20
               CALL DISP_OTH(NEWMOD,-2,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               PNT_DISP=NEWMOD
            ENDIF
         ENDIF
        ENDIF
      ENDDO
      CALL Term_Write(21,1,BLANK,80)
      RETURN
      END
C
C     ==============================
      SUBROUTINE REST_TABLE(COMPL,*)
C     ==============================
      IMPLICIT NONE
C
C     This subroutine restores the contents of the requested table
C     ------------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1 TAB_FOUND                ! True if table is found
C
      INTEGER*2
     &          INDEX,                   ! Index pointer
     &          OFFSET                   ! Wait offset return
C
      INTEGER*4 COMPL,                   ! Complete print mode number
     &          C_COUNT                  ! Counter
C
      INCLUDE 'hardata.inc'
      COMMON /TAB/ TAB_FOUND
C
C     If the table was found, then restore its contents
C     -------------------------------------------------
      IF (TAB_FOUND) THEN
        TEMP_TIT = TITLE(TAB_NUM)
        TMP_TBSZ = TABSIZE(TAB_NUM)
        TEMP_MODATE = MODATE(TAB_NUM)
        TEMP_NAME = NAME(TAB_NUM)
C
C       Restore harmonics or OTHER depending on which type of data the
C       table contains
C       --------------------------------------------------------------
        IF (HMSIZ(TAB_NUM) .GT. 0) THEN
          TMP_OSZ=0
          TMP_EXTSZ = 0
          PHASE = PHASEL(TAB_NUM)
          TMP_HMSZ = HMSIZ(TAB_NUM)
          DO I = 1,TMP_HMSZ
            TEMP_AMPL(I) = AMPL(TAB_NUM,I)
            TEMP_HARM(I) = HARM(TAB_NUM,I)
            TEMP_TYPE(I) = WAVETYPE(TAB_NUM,I)
          ENDDO
          IF(COMPL.EQ.2) THEN
            C_COUNT=1
            DO WHILE(C_COUNT.LE.TMP_HMSZ)
               PHASE4 = PHASE
               CALL DISPLAY(C_COUNT,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,
     &                                                      TEMP_TIT)
               CALL TAB_HEAD_DISP(TAB_NUM,1)
               C_COUNT = C_COUNT+20
               IF(C_COUNT.LE.TMP_HMSZ)THEN
                  CALL WAIT_CONT(2,OFFSET)
                  IF(OFFSET.EQ.0) THEN
                    RETURN 1
                  ENDIF
               ENDIF
            ENDDO
          ELSE
            PHASE4 = PHASE
            CALL DISPLAY(1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
          ENDIF
        ELSEIF(OTHSZ(TAB_NUM).GT.0) THEN
          PHASE = 99
          TMP_HMSZ=0
          TMP_EXTSZ = 0
          TMP_OSZ = OTHSZ(TAB_NUM)
          DO I = 1,TMP_OSZ
            TMP_PVAL(I) = OTHP(TAB_NUM,I)
            TMP_XVAL(I) = OTHX(TAB_NUM,I)
            TMP_YVAL(I) = OTHY(TAB_NUM,I)
          ENDDO
          IF(COMPL.EQ.2) THEN
            C_COUNT=1
            DO WHILE(C_COUNT.LE.TMP_OSZ)
               CALL DISP_OTH(C_COUNT,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               CALL TAB_HEAD_DISP(TAB_NUM,2)
               C_COUNT = C_COUNT+20
               IF(C_COUNT.LE.TMP_OSZ)THEN
                 CALL WAIT_CONT(2,OFFSET)
                 IF(OFFSET.EQ.0) THEN
                    RETURN 1
                 ENDIF
               ENDIF
            ENDDO
          ELSE
            CALL DISP_OTH(1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
          ENDIF
        ELSEIF(EXTBLN(TAB_NUM).GT.0) THEN
          PHASE = 99
          TMP_HMSZ=0
          TMP_OSZ=0
          INDEX = EXTBLN(TAB_NUM)
          DO I = 1,TMP_TBSZ
             TMP_EXPO(I) = EXTP(INDEX,I)
          ENDDO
          TMP_EXTSZ=EXTSIZ(INDEX)
          CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
          IF(COMPL.EQ.2) CALL TAB_HEAD_DISP(TAB_NUM,3)
        ENDIF
      ELSE
C
C -- If the table is not found, then default the size to 512 and
C    blank out the title
C
        TMP_TBSZ = 512
        PHASE = 99
        TEMP_TIT = BLANK(1:30)
        TEMP_MODATE = 'Not saved        '
        CALL GET_USER
        TEMP_NAME = USER_ID
        TMP_HMSZ = 0
        TMP_EXTSZ= 0
        TMP_OSZ = 0
        PHASE4 = PHASE
        CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
        CALL DISPLAY(-99,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT) !Set new pars
        CALL DISP_OTH(-99,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT) !Set new pars
      ENDIF
      RETURN
      END
C
C     ========================================
      SUBROUTINE GET_HARM(PREV_DISP,END_ENTER)
C     ========================================
      IMPLICIT NONE
C
C
C -- This subroutine obtains the harmonic number from the user
C    & checks for errors
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          END_READ,             ! Read harmonic # flag
     &          END_ENTER,            ! end-Quit command flag
     &          H_FOUND               ! Found flag
C
      INTEGER*4 
     &          Stat
C
      INTEGER*4
     &          PREV_DISP,            ! Previous display pointer
     &          DISPOINT              ! Display point selected
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1/'%GET_HARM- Invalid display pointer'/
      DATA ERROR2/'%GET_HARM- Invalid harmonic number'/
      DATA ERROR3   /'%GET_HARM- The highest harmonic is display on this
     & screen'/
      DATA ERROR4    /'%GET_HARM- The lowest harmonic is display on this
     & screen'/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'NEXT'/
      DATA MENU(7)/'PREVIOUS'/
      DATA MENU(8)/'LAST'/
C
C     Ask the user for the harmonic number he wishes to edit.
C     ------------------------------------------------------
      END_ENTER = .FALSE.
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
        CALL READ_COMMAND(-1,22,1,'HARMONIC # > ',13,COMMAND,L_COM,Stat)
        CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
        IF(IERR.EQ.9999) THEN
          IF(COMMAND(1:2).EQ.'DH') THEN   ! Display Harmonic pointer
             CALL NUMB_STR(COMMAND(3:80),(L_COM-2),NUMBER,IERR)
             H_FOUND=.FALSE.
             IF(IERR.EQ.0) THEN
CC                INT_NUM = INT(NUMBER)
CC                REAL_NUM= FLOAT(INT_NUM)
CC                IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
CC                IF(INT_NUM.LE.TEMP_HARM(TMP_HMSZ).AND.INT_NUM.GT.0)THEN
                IF(NUMBER.LE.TEMP_HARM(TMP_HMSZ).AND.NUMBER.GT.0.0)THEN
                   DO I=1,TMP_HMSZ
                      IF(TEMP_HARM(I).EQ.NUMBER) THEN
                         H_FOUND=.TRUE.
                         DISPOINT = I
                      ENDIF
                   ENDDO
CC                 ENDIF
                ENDIF
             ENDIF
C
C            Display something: right or wrong, black or white, etc ...
C            ----------------------------------------------------------
             IF(H_FOUND) THEN
               PHASE4 = PHASE
               CALL DISPLAY(DISPOINT,-2,-1,TMP_TBSZ,TMP_HMSZ,
     &                        PHASE4,TEMP_TIT)
               PREV_DISP=DISPOINT
             ELSE
               CALL ERR_MESS(ERROR2,34,-1,*11)
 11            CONTINUE
             ENDIF
          ELSEIF(COMMAND(1:1).EQ.'R') THEN   ! Display Real pointer
             CALL NUMB_STR(COMMAND(2:80),(L_COM-1),NUMBER,IERR)
             H_FOUND = .FALSE.
             IF(IERR.EQ.0) THEN
                INT_NUM = INT(NUMBER)
                REAL_NUM= FLOAT(INT_NUM)
                IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
                 IF(INT_NUM.LE.TMP_HMSZ.AND.INT_NUM.GT.0)THEN
                   DISPOINT = INT_NUM
                   PHASE4 = PHASE
                   CALL DISPLAY(DISPOINT,-2,-1,TMP_TBSZ,TMP_HMSZ,
     &                        PHASE4,TEMP_TIT)
                   PREV_DISP=DISPOINT
                   H_FOUND = .TRUE.
                 ENDIF
                ENDIF
             ENDIF
             IF(.NOT.H_FOUND) THEN
                CALL ERR_MESS(ERROR1,34,-1,*12)
 12             CONTINUE
             ENDIF
          ELSE
           CALL PARSE_COMMAND(COMMAND,L_COM,8,MENU,ITEM,IERR)
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
               IF(ITEM.EQ.6) THEN     !NEXT command
                 DISPOINT=(PREV_DISP/20)*20 +1 +20
                 IF(DISPOINT.LE.TMP_HMSZ) THEN
                   PHASE4 = PHASE
                   CALL DISPLAY(DISPOINT,-2,-1,TMP_TBSZ,TMP_HMSZ,
     &                        PHASE4,TEMP_TIT)
                   PREV_DISP=DISPOINT
                 ELSE
                   CALL ERR_MESS(ERROR3,57,-1,*13)
 13                CONTINUE
                 ENDIF
               ELSEIF(ITEM.EQ.7)THEN  !PREV command
                 DISPOINT=(PREV_DISP/20)*20 +1 -20
                 IF(DISPOINT.GE.1) THEN
                   PHASE4 = PHASE
                   CALL DISPLAY(DISPOINT,-2,-1,TMP_TBSZ,TMP_HMSZ,
     &                        PHASE4,TEMP_TIT)
                   PREV_DISP=DISPOINT
                 ELSE
                   CALL ERR_MESS(ERROR4,56,-1,*14)
 14                CONTINUE
                 ENDIF
               ELSEIF(ITEM.EQ.8) THEN !LAST command
                 DISPOINT=(TMP_HMSZ/20)*20 +1
                 IF(DISPOINT.NE.PREV_DISP) THEN
                   PHASE4 = PHASE
                   CALL DISPLAY(DISPOINT,-2,-1,TMP_TBSZ,TMP_HMSZ,
     &                        PHASE4,TEMP_TIT)
                   PREV_DISP=DISPOINT
                 ENDIF
               ENDIF
            ELSE
               IF(ITEM.EQ.1) THEN        !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                 CALL EDIT_DISP(1)
               ELSEIF(ITEM.EQ.2) THEN    !HELP command
                CALL HELP(12)                !Help asked
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
               ELSE                      !QUIT,EXIT or X command
                 END_READ = .TRUE.
                 END_ENTER = .TRUE.
               ENDIF
            ENDIF
           ENDIF
          ENDIF
        ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: set harmonic number
C          -----------------------------------
CC           INT_NUM = INT(NUMBER)
CC           REAL_NUM= FLOAT(INT_NUM)
CC           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
           IF( (NUMBER.LE.MAX_HARM).AND. (NUMBER.GE.1.00) ) THEN
              INT_NUM = NUMBER*100.0
              HARM_NUM = FLOAT(INT_NUM)/100.0
              END_READ = .TRUE.
           ELSE
              CALL ERR_MESS(ERROR2,34,-1,*50)
 50           CONTINUE
           ENDIF

CC           ELSE
CC              CALL ERR_MESS(ERROR2,34,-1,)
CC           ENDIF
        ENDIF
      ENDDO
C
      RETURN
      END
C
C     ==============================
      SUBROUTINE GET_AMPL(END_ENTER)
C     ==============================
C
C     This subroutine obtains the amplitude and checks for errors
C     -----------------------------------------------------------
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          END_ENTER,                    ! End-Quit flag
     &          END_READ                      ! Read amplitude flag
C
      INTEGER*4
     & Stat
C
      CHARACTER*80
     & ERROR1                  !Various error messages
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1 /'%GET_AMPL - Invalid amplitude     '/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
      END_ENTER=.FALSE.
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(0,22,1,'AMPLITUDE > ',12,COMMAND,L_COM,Stat)
         CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
         IF(IERR.EQ.9999) THEN
           CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
           IF(IERR.EQ.0) THEN
C
            IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(13)                !Help asked
               PHASE4 = PHASE
               CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
               CALL EDIT_DISP(1)
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               END_ENTER=.TRUE.
            ENDIF
           ENDIF
         ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: set table number
C          --------------------------------
           IF( (NUMBER.LE.MAX_AMPL).AND. (NUMBER.GE.0.0) ) THEN
              AMPLITUDE = NUMBER
              END_READ = .TRUE.
           ELSE
              CALL ERR_MESS(ERROR1,34,-1,*15)
 15           CONTINUE
           ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C
C     ==========================
      SUBROUTINE GET_TITLE(MODE)
C     ==========================
      IMPLICIT NONE
C
C     This subroutine obtains the title and checks for errors
C     -------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1 CHANGED,
     &          END_READ               ! Read phase flag
      INTEGER*2 MODE
C
      INTEGER*4
     &          Stat
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      COMMON /CHAN/ CHANGED
C
      DATA PROMPT /'TITLE > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'box'/
      DATA MENU(7)/'help'/
      DATA MENU(8)/'exit'/
      DATA MENU(9)/'x'/
      DATA MENU(10)/'quit'/
C
      END_READ=.FALSE.
      PROMPT(9:38) =TEMP_TIT
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(1001,22,1,PROMPT,8,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-10,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.EQ.1.OR.ITEM.EQ.6) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 IF (MODE.EQ.1) THEN
                    PHASE4 = PHASE
                    CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,
     &                                           TEMP_TIT)
                    CALL EDIT_DISP(1)
                 ELSEIF (MODE.EQ.2) THEN
                    CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(2)
                 ELSE
                    CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(3)
                 ENDIF
             ELSEIF(ITEM.EQ.2.OR.ITEM.EQ.7) THEN    !HELP command
                 CALL HELP(15)                !Help asked
                 IF (MODE.EQ.1) THEN
                    PHASE4 = PHASE
                    CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,
     &                                                     TEMP_TIT)
                    CALL EDIT_DISP(1)
                 ELSEIF (MODE.EQ.2) THEN
                    CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(2)
                 ELSE
                    CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(3)
                 ENDIF
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ELSE
             TEMP_TIT = COMMAND(1:30)
             CHANGED = .TRUE.
             PHASE4 = PHASE
             CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
             END_READ = .TRUE.
          ENDIF
      ENDDO
C
      RETURN
      END
C
C     =========================
      SUBROUTINE GET_SIZE(MODE)
C     =========================
      IMPLICIT NONE
C
C     This subroutine obtains the size and checks for errors
C     ------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1 CHANGED,
     &          END_READ               ! Read phase flag
C
      INTEGER*2 MODE
C
      INTEGER*4
     &          Stat
C
      CHARACTER*80
     & ERROR1                  !Various error messages
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      COMMON /CHAN/CHANGED
C
      DATA ERROR1 / '%GET_SIZE: Invalid size specified '/
C
      DATA PROMPT /'TABLE SIZE : 512, 1024 , 2048, 4096 > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'512'/
      DATA MENU(7)/'1024'/
      DATA MENU(8)/'2048'/
      DATA MENU(9)/'4096'/
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,38,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-9,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                  TMP_TBSZ = 512
               ELSEIF(ITEM.EQ.7) THEN
                  TMP_TBSZ = 1024
               ELSEIF(ITEM.EQ.8) THEN
                  TMP_TBSZ = 2048
               ELSEIF(ITEM.EQ.9) THEN
                  TMP_TBSZ = 4096
               ENDIF
               CHANGED = .TRUE.
               PHASE4 = PHASE
               CALL DISP_HEAD(TMP_TBSZ,TEMP_TIT,PHASE4)
               END_READ=.TRUE.
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
                 IF (MODE.EQ.1) THEN
                    PHASE4 = PHASE
                    CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,
     &                                                    TEMP_TIT)
                    CALL EDIT_DISP(1)
                 ELSEIF (MODE.EQ.2) THEN
                    CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(2)
                 ELSE
                    CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(3)
                 ENDIF
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(14)                !Help asked
                 IF (MODE.EQ.1) THEN
                    PHASE4 = PHASE
                    CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,
     &                                                   TEMP_TIT)
                    CALL EDIT_DISP(1)
                 ELSEIF (MODE.EQ.2) THEN
                    CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(2)
                 ELSE
                    CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
                    CALL EDIT_DISP(3)
                 ENDIF
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ELSE
             CALL ERR_MESS(ERROR1,34,-1,*16)
 16          CONTINUE
          ENDIF
      ENDDO
C
      IF (TAB_NUM .GE. 65) THEN
         TABSIZE(TAB_NUM) = TMP_TBSZ
      ENDIF
C
      RETURN
      END
C
C
C     ==========================
      SUBROUTINE TRANSFERT(TYPE)
C     ==========================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*2
     & POS_CARD,
     & VAL1,VAL2,
     & T_NUM                                  ! Table from transfer
C
      INTEGER*4
     & T_ERR,                                 ! Transfer error
     & DSG_RTL,                               ! Which DSG for RTL
     & Stat,
     & TYPE                                   ! Copy table type
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
     &,ERROR5                  !
     &,ERROR6                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15,
     & M_NORESP*49                          !Message for wrong transfer
C
      LOGICAL*1
     &          SLOWDOWN,
     &          END_READ,                     ! Read table # flag
     &          QUIT_ASK,                     ! True if table mode is to exit
     &          QUIT_ASK2,                    ! True if table mode is to exit
     &          FOUND,                        !
     &          T_FOUND                       ! True if table exists
C
      DATA M_NORESP/'%RTL : DSG card not responding, no transfer done '/
      DATA ERROR1/ '%RTL : Sound Program UNFrozen '/
      DATA ERROR2/ '%RTL : Sound Program Frozen '/
      DATA ERROR3/ '%RTL : Real Time Download is OFF, tranfer not availa
     &ble '/
      DATA ERROR4/ '%RTL : Unmatched SIZE tables, no transfer DONE '/
      DATA ERROR5/ '%RTL : Invalid table number   '/
      DATA ERROR6/ '%RTL : Invalid DSG slot number. Try again...'/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
C     Check that transfer is possible
C     -------------------------------
      IF(RTD_FLAG)THEN
C
C       Ask user on which DSG table should be replaced
C       ----------------------------------------------
        END_READ = .FALSE.
        QUIT_ASK2 = .FALSE.
C
        DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,'RTL: Enter DSG slot # > ',
     &                      24,COMMAND,L_COM,Stat)
          CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
          IF(IERR.EQ.9999) THEN
            CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
            IF(IERR.EQ.0) THEN
               END_READ=.TRUE.
               QUIT_ASK2 = .TRUE.
            ENDIF
C
          ELSEIF(IERR.EQ.0)THEN
C
C          Number entered: Look for DSG index
C          ----------------------------------
           FOUND = .FALSE.
           INT_NUM = INT(NUMBER)
           REAL_NUM = FLOAT(INT_NUM)
           IF (ABS(REAL_NUM-NUMBER).LT.0.001) THEN
             IF ((INT_NUM.LE.25).AND.(INT_NUM.GT.0)) THEN
               DO II = 1,DSG_NUMB
                  IF (SL_NBI(II).EQ.INT_NUM) THEN
                    FOUND = .TRUE.
                    DSG_RTL = DSG_TYPE(II)
                  ENDIF
               ENDDO
             ENDIF
           ENDIF
           IF (.NOT.FOUND) THEN
              CALL ERR_MESS(ERROR6,50,-1,*16)
 16           CONTINUE
           ELSE
              END_READ = .TRUE.
           ENDIF
C
          ENDIF
         ENDDO
C
         IF(.NOT.QUIT_ASK2) THEN
C
C        Check first that DMC is there and listening
C        -------------------------------------------
         IF(ON_SITE) THEN !Bypass if not on site
            CALL CDB_GET(VAL1,1,3,DSG_RTL,T_ERR)  !Get counter register
            IF(T_ERR.EQ.1) THEN
               W_LONG=0.100
               CALL Wait_Time(W_LONG) 
               CALL CDB_GET(VAL2,1,3,DSG_RTL,T_ERR)
               IF(T_ERR.EQ.1) THEN
                  IF(VAL2.EQ.VAL1) T_ERR=99
               ENDIF
            ENDIF
         ELSE
            T_ERR=1
         ENDIF
C
         IF(T_ERR.EQ.1) THEN
C
C         Look for table to be downloaded
C         -------------------------------
          END_READ=.FALSE.
          QUIT_ASK = .FALSE.
C
C         Prompt the user for input, and branch to that routine which
C         corresponds to his selection.
C         ------------------------------------------------------------
          DO WHILE(.NOT.END_READ)
           CALL READ_COMMAND(-1,22,1,'RTL : Replacing TABLE # > ',26,
     &                                               COMMAND,L_COM,Stat)
           POS_CARD=INDEX(COMMAND(1:L_COM),'#')
           IF(POS_CARD.GT.0)THEN
              IF(L_COM.GT.POS_CARD) THEN
                COMMAND(POS_CARD:L_COM-1)=COMMAND(POS_CARD+1:L_COM)
                COMMAND(L_COM:L_COM)=' '
              ENDIF
              SLOWDOWN=.TRUE.
           ELSE
              SLOWDOWN=.FALSE.
           ENDIF
           CALL NUMB_STR(COMMAND,L_COM,NUMBER,IERR)
           IF(IERR.EQ.9999) THEN
            CALL PARSE_COMMAND(COMMAND,L_COM,5,MENU,ITEM,IERR)
C
            IF(IERR.EQ.0) THEN
C
             IF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
               IF(TYPE.EQ.1) THEN
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
               ELSEIF(TYPE.EQ.2) THEN
                CALL EDIT_DISP(2)
                CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               ELSEIF(TYPE.EQ.3) THEN
                CALL EDIT_DISP(3)
                CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
               ENDIF
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(6)                !Help asked
               IF(TYPE.EQ.1) THEN
                PHASE4 = PHASE
                CALL DISPLAY(-1,-1,-1,TMP_TBSZ,TMP_HMSZ,PHASE4,TEMP_TIT)
                CALL EDIT_DISP(1)
               ELSEIF(TYPE.EQ.2) THEN
                CALL EDIT_DISP(2)
                CALL DISP_OTH(-1,-1,-1,TMP_OSZ,TMP_TBSZ,TEMP_TIT)
               ELSEIF(TYPE.EQ.3) THEN
                CALL EDIT_DISP(3)
                CALL DISP_EXT(TMP_EXTSZ,TMP_TBSZ,TEMP_TIT)
               ENDIF
             ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
               QUIT_ASK=.TRUE.
             ENDIF
            ENDIF
           ELSEIF(IERR.EQ.0)THEN
C
C           Number entered: set table number
C           --------------------------------
            INT_NUM = INT(NUMBER)
            REAL_NUM= FLOAT(INT_NUM)
            IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
             IF( (INT_NUM.LE.MAX_TAB).AND.
     &          (INT_NUM.GT.0)                ) THEN
              T_NUM = INT_NUM
              CALL TAB_EXIST(T_NUM,T_FOUND)
              IF(.NOT.T_FOUND)THEN
                CALL ERR_MESS(ERROR5,30,-1,*17)
 17             CONTINUE
              ELSE
                END_READ = .TRUE.
              ENDIF
             ELSE
              CALL ERR_MESS(ERROR5,30,-1,*18)
 18           CONTINUE
             ENDIF
            ELSE
              CALL ERR_MESS(ERROR5,30,-1,*19)
 19           CONTINUE
            ENDIF
           ENDIF
          ENDDO
C
          IF(.NOT.QUIT_ASK)THEN
C
C          Update SIZE variable of current table
C          -------------------------------------
           TABSIZE(TAB_NUM) = TMP_TBSZ
C
           IF(TABSIZE(T_NUM).EQ.TABSIZE(TAB_NUM))THEN
              CALL CDB_PUT(1,4,1,1,T_ERR)  !Freeze sound program
              IF(T_ERR.EQ.1)THEN
                CALL ERR_MESS(ERROR2,28,-1,*21)
 21             CONTINUE
                CALL NEW_LOAD(T_NUM,TAB_NUM,SLOWDOWN,DSG_RTL)
                CALL CDB_PUT(0,4,1,1,T_ERR)  !UnFreeze sound program
                IF(T_ERR.EQ.1)THEN
                   CALL ERR_MESS(ERROR1,30,-1,*22)
 22                CONTINUE
                ENDIF
C
              ENDIF
           ELSE
              CALL ERR_MESS(ERROR4,47,-1,*23)
 23           CONTINUE
           ENDIF
          ENDIF
C
         ELSE
          CALL ERR_MESS(M_NORESP,49,-1,*24)
 24       CONTINUE
         ENDIF
C
         ENDIF    !For QUIT_ASK2 ...
C
      ELSE
         CALL ERR_MESS(ERROR3,56,-1,*25)
 25      CONTINUE
      ENDIF
C
      RETURN
      END
C
C
C     ====================
      SUBROUTINE DELETE(*)
C     ====================
      IMPLICIT NONE
C
C     This subroutine deletes either all tables or specific tables.
C     -------------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      CHARACTER
     &          YESTR*3
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
C
      LOGICAL*1
     &          END_READ              ! Read phase flag
C
      INTEGER*4
     & Stat,
     & LLine4
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
C
      DATA YESTR /'YES'/
C
      DATA ERROR1/'Are you SURE you want to DELETE everything [N] ? '/
      DATA ERROR2/'%DELETE : All tables have been deleted from buffer'/
      DATA ERROR3/'%DELETE : No table deleted because NONE exist'/
      DATA ERROR4/'%DELETE : All tables assignation to sources have been
     & cancelled'/
C
      DATA PROMPT /'DELETE : TABLE, ASSIGN or CLEAN > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'TABLE'/
      DATA MENU(7)/'ASSIGN'/
      DATA MENU(8)/'CLEAN'/
C
C     Find out the delete mode required by user
C     -----------------------------------------
C
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,34,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,8,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN        !TABLE
                 CALL DEL_TABLE
               ELSEIF(ITEM.EQ.7) THEN    !ASSIGN
                 CALL DEL_ASSIGN
               ELSEIF(ITEM.EQ.8) THEN    !CLEAN
C
C                If no tables exist, then write message and return.  Otherwise set
C                TBLNUM to zero.
C                ---------------------------------------------------------------
                 CALL Term_Write(22,1,ERROR1,49)
                 CALL Term_Read(0,INPLINE,LLine4,IERR)
                 LLINE = LLine4
                 IF ((INPLINE(1:LLINE).EQ.YESTR(1:LLINE)).AND.
     &                                        LLINE.GT.0)THEN
                   IF (TBLNUM .NE. 0) THEN
C
C                     Set data save flag to generate new data file
C                     --------------------------------------------
                      DATASAVE=.TRUE.
C
                      TBLNUM = 0
                      EXT_NUM = 0
                      CALL ERR_MESS(ERROR2,50,-1,*26)
 26                   CONTINUE
                   ELSE
                      CALL ERR_MESS(ERROR3,46,-1,*27)
 27                   CONTINUE
                   ENDIF
C
C                  Clean-up source assignation array
C                  ---------------------------------
                   DO MM=1,MAX_DSG
                     DO II=1,MAXI_SOUR
                        SOURCE(II,MM) = 0
                     ENDDO
                   ENDDO 
                   CALL ERR_MESS(ERROR4,63,-1,*28)
 28                CONTINUE
                 ENDIF
C
               ENDIF
C
             ELSEIF(ITEM.EQ.1) THEN    !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(25)               !Help asked
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ENDIF
      ENDDO
C
      RETURN 1
C
      END
C
C     ====================
      SUBROUTINE DEL_TABLE
C     ====================
      IMPLICIT NONE
C
C     This subroutine deletes specific tables.
C     ----------------------------------------
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     &          DELFOUND                  !Delete table found flag
C
      INTEGER*4
     &          NJJ
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR3                  !
     &,ERROR4                  !
     &,ERROR5                  !
C
      LOGICAL*1
     &          ONE_DELETE,               ! One table is deleted
     &          END_READ                  ! Read phase flag
C
      INTEGER*4
     & Stat
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      DATA ERROR1/'%DELETE : Table    deleted from buffer'/
      DATA ERROR2/'%DELETE : The deleted table(s) removed from the sourc
     &e assignation buffer'/
      DATA ERROR3/'%DELETE : Invalid table specified in the list'/
      DATA ERROR4/'%DELETE : Table    not deleted, do not exist'/
      DATA ERROR5/'%DELETE : Invalid table specified'/
C
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
C
C     Find out which table to delete
C     ------------------------------
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,'DELETE : TABLE # > ',19,COMMAND,
     &                                                      L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-5,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF(ITEM.EQ.1) THEN        !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
             ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(26)               !Help asked
             ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
             ENDIF
          ELSE
C
C            Input is not a command: check for table #
C            -----------------------------------------
             CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
             IF(IERR.EQ.0) THEN
C
                ONE_DELETE=.FALSE.
C
C               For each of tables to be deleted, set the size to zero, and set the
C               corresponding element in saved table array to zero.  Also,
C               format the output message string so that the user will be informed
C               of which tables have been deleted.
C               ----------------------------------------------------------------
C
                DO I = 1,NUM_LIST
                   IF(IT_LIST(I).LE.MAX_TAB.AND.IT_LIST(I).GT.0) THEN
C
C                   Set data save flag to generate new data file
C                   --------------------------------------------
                    DATASAVE=.TRUE.
                    IF(EXTBLN(IT_LIST(I)).GT.0) THEN
C
C                    Transfert the External data type variables to lower position
C                    -----------------------------------------------------------
                     EXT_NUM=EXT_NUM-1
                     DO JJ=EXTBLN(IT_LIST(I)),EXT_NUM
                        EXTSIZ(JJ) = EXTSIZ(JJ+1)
                        DO II=1,EXTSIZ(JJ)
                           EXTP(JJ,II) = EXTP(JJ+1,II)
                        ENDDO
                        DO II=1,MAX_TAB
                          IF(EXTBLN(II).EQ.JJ+1) THEN
                             EXTBLN(II)= JJ
                          ENDIF
                        ENDDO
                     ENDDO
                    ENDIF
                    TABSIZE(IT_LIST(I)) = 0
C
C                   Find which element of the save table array as to be reset
C                   ---------------------------------------------------------
                    JJ = 1
                    DELFOUND = .FALSE.
                    DO WHILE(JJ.LE.TBLNUM.AND..NOT.DELFOUND)
                      IF(SAV_TBL(JJ).EQ.IT_LIST(I))THEN
                         DELFOUND=.TRUE.
                         ONE_DELETE=.TRUE.
                         SAV_TBL(JJ) = 0
                      ENDIF
                      JJ=JJ+1
                    ENDDO
C
                    IF(DELFOUND) THEN
                      WRITE(ERROR1(17:18),'(I2)') IT_LIST(I)
                      CALL ERR_MESS(ERROR1,38,-1,*29)
 29                   CONTINUE
                    ELSE
                      WRITE(ERROR4(17:18),'(I2)') IT_LIST(I)
                      CALL ERR_MESS(ERROR4,44,-1,*31)
 31                   CONTINUE
                    ENDIF
                   ELSE
                    CALL ERR_MESS(ERROR3,45,-1,*32)
 32                 CONTINUE
                   ENDIF
                ENDDO
C
                IF(ONE_DELETE) THEN
C
C                 Refill the save table array without the deleted table
C                 -----------------------------------------------------
                  JJ = 1
                  DO NJJ=1,TBLNUM
                   IF(SAV_TBL(JJ).EQ.0)THEN
                      DO I=JJ+1,TBLNUM
                         SAV_TBL(I-1)=SAV_TBL(I)
                      ENDDO
                      TBLNUM=TBLNUM-1
                   ELSE
                      JJ = JJ + 1
                   ENDIF
                  ENDDO
C
C                 Delete the table in source assign array
C                 ---------------------------------------
                  CALL ERR_MESS(ERROR2,73,-1,*33)
 33               CONTINUE
                  DO I=1,NUM_LIST
                   DO MM=1,MAX_DSG
                     DO JJ=1,MAXI_SOUR
                        IF(SOURCE(JJ,MM).EQ.IT_LIST(I)) THEN
                           SOURCE(JJ,MM)=0
                        ENDIF
                     ENDDO
                   ENDDO 
                  ENDDO
                ENDIF
             ELSE 
                CALL ERR_MESS(ERROR5,33,-1,*34)
 34             CONTINUE
             ENDIF
C
          ENDIF
      ENDDO
C
      RETURN
C
      END
C
C     =====================
      SUBROUTINE DEL_ASSIGN
C     =====================
      IMPLICIT NONE
C
C     This subroutine deletes specific tables.
C     ----------------------------------------
      INCLUDE 'harparm.inc'
C
      INTEGER*2
     & DEF_DSG                         ! DSG index selected
C
      INTEGER*4
     & Stat
C
      LOGICAL*1
     &          DELFOUND                  !Delete table found flag
C
      CHARACTER*80
     & ERROR1                  !Various error messages
     &,ERROR2                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      LOGICAL*1
     &          NEWDSG,               ! New DSG selected flag
     &          END_READ              ! Read phase flag
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1/'%DELETE : Assignment of source    is removed'/
      DATA ERROR2/'%DELETE : Invalid source in the list'/
C
      DATA PROMPT/'DELETE - ASSIGN for slot XA   : SOURCE # or SWAP > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'SWAP'/
C
C     Find out which table to delete
C     ------------------------------
      DEF_DSG = 1
      PROMPT(28:29)=SL_NB(1)
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
          CALL READ_COMMAND(-1,22,1,PROMPT,51,COMMAND,L_COM,Stat)
          CALL PARSE_COMMAND(COMMAND,L_COM,-6,MENU,ITEM,IERR)
C
          IF(IERR.EQ.0) THEN
C
             IF (ITEM.GE.6) THEN       !SWAP
              CALL GET_NEWDSG(DEF_DSG,NEWDSG) 
C
              IF (NEWDSG) THEN
                 PROMPT(28:29)=SL_NB(DEF_DSG)
              ENDIF
             ELSE
              IF(ITEM.EQ.1) THEN        !BOX command
                 CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
                 CALL MODE_SET(-1)           !Display output mode
              ELSEIF(ITEM.EQ.2) THEN    !HELP command
                 CALL HELP(27)               !Help asked
              ELSE                      !QUIT,EXIT or X command
                 END_READ=.TRUE.
              ENDIF
             ENDIF
          ELSE
C
C            Input is not a command: check for source #
C            ------------------------------------------
             CALL PARSE_NUMBER(COMMAND,L_COM,NUM_LIST,IERR)
             IF(IERR.EQ.0) THEN
C
                DO I = 1,NUM_LIST
                   IF(IT_LIST(I).LE.MAXSOUR.AND.IT_LIST(I).GT.0)THEN
C
C                    Set data save flag to generate new data file
C                    --------------------------------------------
                     DATASAVE=.TRUE.
C
                     SOURCE(IT_LIST(I),DEF_DSG) = 0
                     WRITE(ERROR1(32:33),'(I2)') IT_LIST(I)
                     CALL ERR_MESS(ERROR1,44,-1,*35)
 35                  CONTINUE
                   ELSE
                     CALL ERR_MESS(ERROR2,36,-1,*36)
 36                  CONTINUE
                   ENDIF
                ENDDO
             ENDIF
C
          ENDIF
      ENDDO
C
      RETURN
C
      END
C
C     ===================
      SUBROUTINE GET_USER
C     ===================
      IMPLICIT NONE
C
C     This subroutine obtains the title and checks for errors
C     -------------------------------------------------------
      INCLUDE 'harparm.inc'
C
      CHARACTER*80 PROMPT
C
      INTEGER*4
     & Stat
C
      INCLUDE 'hardata.inc'
C
      DATA PROMPT /'Your name > '/
C
      IF (.NOT.USER_OK) THEN
         CALL READ_COMMAND(1,22,1,PROMPT,12,COMMAND,L_COM,Stat)
         IF (L_COM.LT.26) THEN 
            USER_ID = COMMAND(1:L_COM)//BLANK(1:26-L_COM)
         ELSE
            USER_ID = COMMAND(1:26)
         ENDIF
         USER_OK = .TRUE.
      ENDIF
C
      RETURN
      END 
