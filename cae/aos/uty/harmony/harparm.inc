C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARPARM.INC                                              **
C   **  Function : Declaration of parameters                                **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON><PERSON>M.Alagar    Date: 13 December 1987  **
C   **  Rev 1.0             G. <PERSON>                   30 June 1988      **
C   **  Rev 2.0             G. <PERSON>                   20 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1990       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C

        INTEGER*2 MAXSOUR,         ! MAX NUMBER OF SOURCES
     &            MAXI_SOUR,       ! MAX NUMBER OF SOURCES
     &            MAX_PARM,        ! MAX NUMBER OF LOGICAL PARAMETERS
     &            MAX_BUF,         ! MAX NUMBER OF BUFFER TABLE DATA
     &            MAX_TAB,         ! MAX NUMBER OF TABLES
     &            MAX_DSG,         ! MAX NUMBER OF DSG ACCEPTABLE
     &            MAX_TAB_LOAD,    ! MAX NUMBER OF TABLES TO BE LOADED
     &            MAX_HNUM,        ! MAX NUMBER OF HARMONICS
     &            MAX_TYPE,        ! MAX NUMBER OF RTD VARIABLES TYPE
     &            MAX_OTH,         ! MAX NUMBER OF OTHER TYPE ENTRIES
     &            MAX_EXT,         ! MAX NUMBER OF EXTERNAL DATA TYPE TABLE
     &            REPL_N        ! MAX NUMBER OF KEY REPLACEMENT
C
        CHARACTER*5
     &            DSGTYP_NAME(2) /'TONE ','OTHER'/
C
        REAL*4    PI              ,!Value of PI
     &            MAX_HARM,        ! MAX HARMONIC NUMBER
     &            TWOPI           ,!2*PI
     &            Q15             ,!Ratio to translate for real -1/1 to Q15
     &            MAX_AMPL        ,! MAX AMPLITUDE
     &            REV_LEVEL        ! HARMONY REVISION LEVEL
C
      INTEGER*4
C
     &            INF_UNIT        ,!SNDUTY information file unit
     &            EXT_UNIT        ,!External data file unit
     &            SML_UNIT        ,!Summary list file unit
     &            TBL_UNIT        ,!List table data file unit
     &            DWL_UNIT        ,!Download data file unit
     &            DLD_UNIT        ,!Download file unit
     &            SIZ_UNIT        ,!WAVEGEN size file unit
     &            PLT_UNIT        ,!Plot file unit
     &            SBMT_UNIT       ,!Submit file unit
     &            HLP_UNIT        ,!Help file unit
     &            DAT_UNIT        ,!HARMONY DATA file unit
     &            XLK_UNIT         !SNDXLINK file unit
C
        PARAMETER (PI       = 3.141592654)
        PARAMETER (TWOPI    = 2*3.141592654)
        PARAMETER (MAX_EXT  = 8)
        PARAMETER (Q15 = 32767)   !That's (2**15-1)
        PARAMETER (MAX_BUF = 4096)
        PARAMETER (MAX_PARM = 20)
        PARAMETER (MAX_TAB = 70)
        PARAMETER (MAX_DSG = 10)
        PARAMETER (MAX_TAB_LOAD = 64)
        PARAMETER (MAX_HNUM = 99)
        PARAMETER (MAX_OTH = 99)
        PARAMETER (MAX_TYPE = 4)
        PARAMETER (MAX_HARM = 327.67)
        PARAMETER (MAX_AMPL = 120.0)
        PARAMETER (MAXI_SOUR = 80)
        PARAMETER (MAXSOUR = 96)
        PARAMETER (REV_LEVEL = 2.4)
        PARAMETER (REPL_N = 17)
C
        PARAMETER (INF_UNIT=11)
        PARAMETER (EXT_UNIT=12)
        PARAMETER (SML_UNIT=20)
        PARAMETER (TBL_UNIT=21)
        PARAMETER (DWL_UNIT=22)
        PARAMETER (DLD_UNIT=23)
        PARAMETER (SIZ_UNIT=24)
        PARAMETER (PLT_UNIT=25)
        PARAMETER (SBMT_UNIT=26)
        PARAMETER (HLP_UNIT=27)
        PARAMETER (DAT_UNIT=80)
        PARAMETER (XLK_UNIT=99)
C
C       GETKEY ROUTINE
C       --------------
C ---- The following parameters are returned by the LINEDIT routine --
C ---- when the user hits a PF key or tries to position the cursor ---
C ---- outside the boundaries of an input field ----------------------
C
C Revision History : Revised to include VT200 function keys
C                    April 1986 by Marc Boulos. NO VT100
C                    KEYS HAVE BEEN CHANGED WHATSOEVER.
C
C
      INTEGER*2
     &        ED_NUL, ED_PF1, ED_PF2, ED_PF3, ED_PF4, ED_CUP, ED_CDN,
     &        ED_CRT, ED_CLF, ED_FTB, ED_RTB, ED_TPO, ED_INS, ED_RET,
     &        ED_EOL, ED_NWD, ED_PWD, ED_DCH, ED_LWD, ED_DPW, ED_BKSP,
     &        ED_KP0, ED_KP1, ED_KP2, ED_KP3, ED_KP4, ED_KP5, ED_KP6,
     &        ED_KP7, ED_KP8, ED_KP9, ED_KPM, ED_KPC, ED_KPD, ED_ENT,
     &        ED_FND, ED_INH, ED_RMV, ED_SEL, ED_PVS, ED_NXS, ED_F06,
     &        ED_F07, ED_F08, ED_F09, ED_F10, ED_F11, ED_F12, ED_F13,
     &        ED_F14, ED_F17, ED_F18, ED_F19, ED_F20, ED_HLP, ED_DO,
     &        ED_CTW,
     &        ED_TYIN,ED_RBCK,ED_RESV,ED_EXIT,
     &        ED_GPF2,ED_GPF3,ED_GPF4,
     &        ED_GKP0,ED_GKP1,ED_GKP2,ED_GKP3,ED_GKP4,ED_GKP5,ED_GKP6,
     &        ED_GKP7,ED_GKP8,ED_GKP9,ED_GKPM,ED_GKPC,ED_GKPD,ED_GENT
C
      PARAMETER (
     &          ED_NUL = 0,       ! indicates no escape sequence
     &          ED_CUP = 5,       ! Cursor up
     &          ED_CDN = 6,       ! Cursor down
     &          ED_CRT = 7,       ! Cursor right
     &          ED_CLF = 8,       ! Cursor left
     &          ED_FTB = 9,       ! Forward Tab (TAB key)
     &          ED_RTB = 10,      ! Reverse Tab (BACKSPACE key)
     &          ED_TPO = 11,      ! Typeover Mode
     &          ED_INS = 12,      ! Insert Mode
     &          ED_RET = 13,      ! RETURN key
     &          ED_EOL = 14,      ! End of line
     &          ED_NWD = 15,      ! Next word
     &          ED_PWD = 16,      ! Previous word
     &          ED_DCH = 17,      ! Delete character at cursor position
     &          ED_LWD = 18,      ! End of Last word
     &          ED_DPW = 19,      ! Delete previous word
     &          ED_BKSP= 127      ! Bacspace
     &          )
C
C ---- The following parameters define the 14 keypad and GOLD keypad keys ----
C
      PARAMETER (
     &          ED_PF1 = 1,                     ! Keypad PF1
     &          ED_PF2 = 2,  ED_GPF2 = 62,      ! Keypad PF2
     &          ED_PF3 = 3,  ED_GPF3 = 63,      ! Keypad PF3
     &          ED_PF4 = 4,  ED_GPF4 = 64,      ! Keypad PF4
     &          ED_KP0 = 20, ED_GKP0 = 65,      ! Keypad 0 (zero)
     &          ED_KP1 = 21, ED_GKP1 = 66,      ! Keypad 1
     &          ED_KP2 = 22, ED_GKP2 = 67,      ! Keypad 2
     &          ED_KP3 = 23, ED_GKP3 = 68,      ! Keypad 3
     &          ED_KP4 = 24, ED_GKP4 = 69,      ! Keypad 4
     &          ED_KP5 = 25, ED_GKP5 = 70,      ! Keypad 5
     &          ED_KP6 = 26, ED_GKP6 = 71,      ! Keypad 6
     &          ED_KP7 = 27, ED_GKP7 = 72,      ! Keypad 7
     &          ED_KP8 = 28, ED_GKP8 = 73,      ! Keypad 8
     &          ED_KP9 = 29, ED_GKP9 = 74,      ! Keypad 9
     &          ED_KPM = 30, ED_GKPM = 75,      ! Keypad "-" (minus sign)
     &          ED_KPC = 31, ED_GKPC = 76,      ! Keypad "," (comma)
     &          ED_KPD = 32, ED_GKPD = 77,      ! Keypad "." (decimal point)
     &          ED_ENT = 33, ED_GENT = 78       ! Keypad ENTER key
     &          )
C
C ---- The following parameters define the VT220 function keys -------------
C
      PARAMETER (
     &          ED_FND = 40,      ! FIND Editing key
     &          ED_INH = 41,      ! INSERT HERE Editing key
     &          ED_RMV = 42,      ! REMOVE Editing key
     &          ED_SEL = 43,      ! SELECT Editing key
     &          ED_PVS = 44,      ! PREV SCREEN Editing key
     &          ED_NXS = 45,      ! NEXT SCREEN Editing key
     &          ED_F06 = 46,      ! F6  Function key
     &          ED_F07 = 47,      ! F7  Function key
     &          ED_F08 = 48,      ! F8  Function key
     &          ED_F09 = 49,      ! F9  Function key
     &          ED_F10 = 50,      ! F10 Function key
     &          ED_F11 = 51,      ! F11 Function key
     &          ED_F12 = 52,      ! F12 Function key
     &          ED_F13 = 53,      ! F13 Function key
     &          ED_F14 = 54,      ! F14 Function key
     &          ED_HLP = 55,      ! HELP Function key
     &          ED_DO  = 56,      ! DO  Function key
     &          ED_F17 = 57,      ! F17 Function key
     &          ED_F18 = 58,      ! F18 Function key
     &          ED_F19 = 59,      ! F19 Function key
     &          ED_F20 = 60,      ! F20 Function key
     &          ED_CTW = 61       ! Control W (refresh screen)
     &          )
C
C ---- The following parameters define the controls keys -------------
C
      PARAMETER (
     &          ED_TYIN = 79,     ! CTRL A key: TYPEOVERT/INSERT
     &          ED_RBCK = 80,     ! CTRL H key: Reverse backspace
     &          ED_RESV = 81,     ! CTRL U key: Reserve/Unreserved
     &          ED_EXIT = 82      ! CTRL Z key: Exit
     &          )
C
