C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY & SNDTEST                                        **
C   **                                                                      **
C   **  Program  : AOSCOMMON.INC  for UNIX systems                          **
C   **  Function : QMR based CDB declaration                                **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0  Written by <PERSON>. <PERSON>             Date: 15 December 1989  **
C   **  Rev 1.1             P. Daigle                     06 December 1990  **
C   **                                                                      **
C   **  Applicable to :                                                     **
C   **  -------------                                                       **
C   **  Rev 1.0  SNDTEST utility                                            **
C   **  Rev 2.3  HARMONY utility on AOSUTY                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C   ** Include all statements to be FPCed.
C   ** Label are fixed and CANNOT BE CHANGED.
C   ** Only shipname should be modified from one simulator to another
C
CQ    SA50 XRFTEST,
CQ         XRFTEST1,
CQ         XRFTEST2,
CQ         XRFTEST3,
CQ         XRFTEST4,
CQ         XRFTEST5
C
CP     SA50
C
CP   & NAFREEZE    ,! $0*1*1 : Sound program freeze flag
CP   & NATONCR1(1) ,! $1*1*1 : Tone card #1 control register
CP   & NATONSR1(1) ,! $1*1*1 : Tone card #1 status register
CP   & NATONCN1(1) ,! $1*2*1 : Tone card #1 foreground status register
CP   & NANOICR1(1) ,! $2*1*1 : Noise card #1 control register
CP   & NANOISR1(1) ,! $2*1*1 : Noise card #1 status register
CP   & NANOICN1(1) ,! $2*2*1 : Noise card #1 foreground status register
CP   & NAIMPCR1(1) ,! $3*1*1 : Impact card #1 control register
CP   & NAIMPSR1(1) ,! $3*1*1 : Impact card #1 status register
CP   & NAIMPCN1(1) ,! $3*2*1 : Impact card #1 foreground status register
CP   & NAMIXCR1(3) ,! $4*1*1 : Mixer card #1-2-3 control register
CP   & NAMIXSR1(3) ,! $4*1*1 : Mixer card #1-2-3 status register
CP   & NAMIXCN1(3) ,! $4*2*1 : Mixer card #1-2-3 foreground status register
CP   & NAPHACNT    ,! $4*2*1 : Tone card phase counter
CP   & NAPHOL01(16),! $4*2*1 : Tone card phase labels
C
CP   & NASLACN1(1), !        : Slap card foreground status register
CP   & NASLACR1(1), !        : Slap card control register
CP   & NASLASR1(1), !        : Slap card status register
CP   & NAPHACNB   , !        : Slap card phase counter
CP   & NAPHOL09(16) !        : Slap card phase labels
C+--- Inserted by CAE Fortran Pre-Processor Revision 6.13 on  5-DEC-1990 08:54:1
C$
C$    Labels Access Files :
C$
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA50.INF;10
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA50.SKX;10
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA50.SPX;10
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA50.SDX;10
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA50.XSL;14
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA501.XSL;6
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA502.XSL;6
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA503.XSL;5
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA504.XSL;5
C$@   CAE$SIMEX_PLUS:[ELEMENT]SA505.XSL;5
C$
C$    CDB inputs taken from CAE$SIMEX_PLUS:[ELEMENT]SA502.XSL;6       XRFTEST2
C$
      LOGICAL*1
     &  NAFREEZE         ! FREEZE FLAG FOR SOUND
C$
      LOGICAL*1
     &  DUM0200001(21066)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,NAFREEZE
C$
C$    CDB inputs taken from CAE$SIMEX_PLUS:[ELEMENT]SA50.XSL;14       XRFTEST
C$
      INTEGER*2
     &  NAIMPCN1(1)      ! IMP-01 COUNTER REGISTER               MI8002
     &, NAIMPCR1(1)      ! IMPACT #1 CONTROL REGISTER            MO8000
     &, NAIMPSR1(1)      ! IMPACT #1 STATUS REGISTER             MI8000
     &, NAMIXCN1(3)      ! MIX-01 COUNTER REGISTER               MI6801
C$      NAMIXCN2         ! MIX-02 COUNTER REGISTER               MI6001
C$      NAMIXCN3         ! MIX-03 COUNTER REGISTER               MI5801
     &, NAMIXCR1(3)      ! MIX-01 CONTROL REGISTER               MO68A4
C$      NAMIXCR2         ! MIX-02 CONTROL REGISTER               MO60A4
C$      NAMIXCR3         ! MIX-03 CONTROL REGISTER               MO58A4
     &, NAMIXSR1(3)      ! MIX-01 STATUS REGISTER                MI6800
C$      NAMIXSR2         ! MIX-02 STATUS REGISTER                MI6000
C$      NAMIXSR3         ! MIX-03 STATUS REGISTER                MI5800
     &, NANOICN1(1)      ! NOI-01 COUNTER REGISTER               MI7801
     &, NANOICR1(1)      ! NOI-01 CONTROL REGISTER               MO7859
     &, NANOISR1(1)      ! NOI-01 STATUS REGISTER                MI7800
     &, NAPHACNB         ! SLAP-01 PHASE COUNTER                 MO904C
     &, NAPHACNT         ! TON-01 PHASE COUNTER                  MO992E
     &, NAPHOL01(16)     ! TON-01 PHASE OUT LOW  (1)             MO993F
C$      NAPHOH01         ! TON-01 PHASE OUT HIGH (1)             MO9940
C$      NAPHOL02         ! TON-01 PHASE OUT LOW  (2)             MO9941
C$      NAPHOH02         ! TON-01 PHASE OUT HIGH (2)             MO9942
C$      NAPHOL03         ! TON-01 PHASE OUT LOW  (3)             MO9943
C$      NAPHOH03         ! TON-01 PHASE OUT HIGH (3)             MO9944
C$      NAPHOL04         ! TON-01 PHASE OUT LOW  (4)             MO9945
C$      NAPHOH04         ! TON-01 PHASE OUT HIGH (4)             MO9946
C$      NAPHOL05         ! TON-01 PHASE OUT LOW  (5)             MO9947
C$      NAPHOH05         ! TON-01 PHASE OUT HIGH (5)             MO9948
C$      NAPHOL06         ! TON-01 PHASE OUT LOW  (6)             MO9949
C$      NAPHOH06         ! TON-01 PHASE OUT HIGH (6)             MO994A
C$      NAPHOL07         ! TON-01 PHASE OUT LOW  (7)             MO994B
C$      NAPHOH07         ! TON-01 PHASE OUT HIGH (7)             MO994C
C$      NAPHOL08         ! TON-01 PHASE OUT LOW  (8)             MO994D
C$      NAPHOH08         ! TON-01 PHASE OUT HIGH (8)             MO994E
     &, NAPHOL09(16)     ! SLAP-01 PHASE OUT LOW  (1)            MO905D
C$      NAPHOH09         ! SLAP-01 PHASE OUT HIGH (1)            MO905E
C$      NAPHOL10         ! SLAP-01 PHASE OUT LOW  (2)            MO905F
C$      NAPHOH10         ! SLAP-01 PHASE OUT HIGH (2)            MO9060
C$      NAPHOL11         ! SLAP-01 PHASE OUT LOW  (3)            MO9061
C$      NAPHOH11         ! SLAP-01 PHASE OUT HIGH (3)            MO9062
C$      NAPHOL12         ! SLAP-01 PHASE OUT LOW  (4)            MO9063
C$      NAPHOH12         ! SLAP-01 PHASE OUT HIGH (4)            MO9064
C$      NAPHOL13         ! SLAP-01 PHASE OUT LOW  (5)            MO9065
C$      NAPHOH13         ! SLAP-01 PHASE OUT HIGH (5)            MO9066
C$      NAPHOL14         ! SLAP-01 PHASE OUT LOW  (6)            MO9067
C$      NAPHOH14         ! SLAP-01 PHASE OUT HIGH (6)            MO9068
C$      NAPHOL15         ! SLAP-01 PHASE OUT LOW  (7)            MO9069
C$      NAPHOH15         ! SLAP-01 PHASE OUT HIGH (7)            MO906A
C$      NAPHOL16         ! SLAP-01 PHASE OUT LOW  (8)            MO906B
C$      NAPHOH16         ! SLAP-01 PHASE OUT HIGH (8)            MO906C
     &, NASLACN1(1)      ! SLAP-01 COUNTER REGISTER              MI9036
     &, NASLACR1(1)      ! SLAP-01 CONTROL REGISTER              MO907D
     &, NASLASR1(1)      ! SLAP-01 STATUS REGISTER (1)           MI9030
     &, NATONCN1(1)      ! TON-01 COUNTER REGISTER               MI9836
     &, NATONCR1(1)      ! TON-01 CONTROL REGISTER (1)           MO995F
     &, NATONSR1(1)      ! TON-01 STATUS REGISTER (1)            MI9830
C$
      LOGICAL*1
     &  DUM0000001(16588),DUM0000002(18),DUM0000003(86)
     &, DUM0000004(18),DUM0000005(2),DUM0000006(316)
     &, DUM0000007(688),DUM0000008(5910),DUM0000009(48)
     &, DUM0000010(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,NAPHACNT,DUM0000002,NAPHOL01,NATONCR1,DUM0000003
     &, NAPHACNB,DUM0000004,NAPHOL09,NASLACR1,DUM0000005,NAIMPCR1
     &, DUM0000006,NANOICR1,DUM0000007,NAMIXCR1,DUM0000008,NATONSR1
     &, NATONCN1,DUM0000009,NASLASR1,NASLACN1,NAIMPSR1,DUM0000010
     &, NAIMPCN1,NANOISR1,NANOICN1,NAMIXSR1,NAMIXCN1
C------------------------------------------------------------------------------
C
C
C     Comment out this section if used on site
C     ----------------------------------------
CSTF+
C      INTEGER*2 NATONCR1(1),NATONCN1(1),NANOICR1(1),NANOICN1(1),
C     &          NAIMPCR1(1),NAIMPCN1(1),NAMIXCR1(3),NAMIXCN1(3),
C     &          NATONSR1(1),NAMIXSR1(3),NAIMPSR1(1),NANOISR1(1),
C     &          NAPHOL01(16),QMR_NAPHOL01(16),
C     &          NAPHACNT,QMR_NAPHACNT,
C     &          QMR_NATONSR1(1),QMR_NAMIXSR1(3),QMR_NAIMPSR1(1),
C     &          QMR_NANOISR1(1),
C     &          QMR_NATONCR1(1),QMR_NATONCN1(1),QMR_NANOICR1(1),
C     &          QMR_NANOICN1(1),QMR_NAIMPCR1(1),QMR_NAIMPCN1(1),
C     &          QMR_NAMIXCR1(3),QMR_NAMIXCN1(3)
C      LOGICAL*1 NAFREEZE,QMR_NAFREEZE
CSTF-
