C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY For UNIX Systems                                 **
C   **                                                                      **
C   **  Program  : HARUNIX.F                                                **
C   **  Function : Input/output functions routine on IBM computer           **
C   **                                                                      **
C   **  Revision History:                                                   **
C   **  -----------------                                                   **
C   **  See harmony.f file                                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  FIL_OPEN                                                            **
C   **  CDATE    ----> in Lib_sg.for                                        **
C   **  CREATE_PLOT                                                         **
C   **  FONTGEN                                                             **
C   **  DATFIL                                                              **
C   **  RANDOM                                                              **
C   **  EXTEND_MEM                                                          **
C   **  GRAPH and associated routines  ----> DUMMY...                       **
C   **  GETKEY (CHR,SEQ)                    \                               **
C   **  RDCHR (RCODE,CHANNEL,NCHAR,BUFFER)   >---> Deleted...               **
C   **  GETCHANL (RCODE,XNAME,CHANNEL)      /                               **
C   **  CDB_COM                                                             **
C   **  REV_CREG(REGISTER)                                                  **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C =====================================================
C                       FIL_OPEN
C =====================================================
C
C  This subroutine open and close all the files used
C  in HARMONY utility.
C
      SUBROUTINE FIL_OPEN(FILE,OPER,IERR)
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
C
       INTEGER*4
     &  RevStat          ,!Status of revision handling routine
     &  FILE             ,!File identification number
     &  PU               ,!Plot UNIT number
     &  OPER              !Type of operation (1-Open, 2-Close)
C
       INTEGER*2
     &  PL_NUMB1 /0/     ,!Plot number for file ID
     &  PL_NUMB2 /1/     ,!Plot number for file ID
     &  PL_SUB_NUMB /1/   !Plot number for file ID
C
       CHARACTER
     &  ERROR*5            ,!Error number in ASCII
     &  SUBMIT1*80         ,!Submit file to printer message
     &  SUBMIT2*80         ,!Submit file to printer message
     &  SUBMIT3*80         ,!Submit file to printer message
     &  SUBMIT4*80         ,!Submit file to printer message
     &  SUBMIT5*80         ,!Submit file to printer message
     &  NUMB1*1            ,!Plot number in ASCII for file ID
     &  NUMB2*2            ,!Plot number in ASCII for file ID
     &  NUMB3*2            ,!Plot number in ASCII for file ID
     &  MESSA(12)*27       ,!
     &  FILENAME*255       ,!File name
     &  Ex_Mess*81         ,!External data file name error
     &  N_FILENAME*80       !File name with revision number
C
      DATA Ex_Mess /'External data file does not respect file naming
     & convention               '/
C
      DATA MESSA/'HARTAB.LIS for list table  ',
     &           'HARDLD.LIS for download    ',
     &           'Harmony download .INT      ',
     &           'wavegen transfert .SIZ     ',
     &           'plot list HARPLOT_xxx.LIS  ',
     &           'plot submit HARPLOT_xxx.COM',
     &           'for help HARHELP.TXT       ',
     &           'for data storage .DAT      ',
     &           'for FFT plot HARFFT.LIS    ',
     &           'for summary list HARSUM.LIS',
     &           'for configure HARMONY.CNF  ',
     &           'for external data          '/
      DATA SUBMIT1 /'%HARMONY - File HARPLOT_xxx.LIS submitted to printe
     &r and deleted                '/
      DATA SUBMIT2 /'%HARMONY - File HARPLOT_xxx.LIS laser printed on qu
     &eue                          '/
C
      INCLUDE 'hardata.inc'
C
      IERR = 0
C
      IF (FILE.EQ.1) THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open DATA table file
C          --------------------
           FILENAME=Config_String(1)(1:Config_Length(1))//'hartab.lis'
           CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           OPEN(UNIT=TBL_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,
     &           ERR=1001)
C
         ELSEIF(OPER.EQ.2)THEN
C
C          Close DATA file
C          ---------------
           CLOSE(UNIT=TBL_UNIT,IOSTAT=IERR,ERR=1001)
           SUBMIT5 = '%LIST : Table file  hartab.lis  created'
     &               //BLANK(1:41)
C
C          Print DATA file created message
C          -------------------------------
           CALL MES23(0,SUBMIT5)
           W_LONG = 3.0
           CALL Wait_Time(W_LONG)
           CALL MES23(1,SUBMIT5)
C
         ENDIF
C
      ELSEIF(FILE.EQ.2) THEN
C
         IF (OPER.EQ.1) THEN
C
C           Open download data file
C           -----------------------
            FILENAME=Config_String(1)(1:Config_Length(1))//'hardld.lis'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DWL_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,ERR=1001)
C
         ELSE
C
C          Close download data file
C          ------------------------
           CLOSE(UNIT=DWL_UNIT,IOSTAT=IERR,ERR=1001)
           SUBMIT3 = '%DOWNLOAD: Data file HARDLD.LIS created'
     &                  //BLANK(1:41)
C
C          Print DATA file created message
C          -------------------------------
           CALL MES23(0,SUBMIT3)
           W_LONG = 3.0
           CALL Wait_Time(W_LONG)
           CALL MES23(1,SUBMIT3)
         ENDIF
C
      ELSEIF(FILE.EQ.3)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open .INT file
C           --------------
            FILENAME=INT_DIR(1:L_INT_DIR)
     &               //Config_String(9)(1:Config_Length(9))
     &               //Filetters(1:3)//'h.int'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DLD_UNIT,FILE=N_FILENAME,STATUS='UNKNOWN',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,ERR=1001)
         ELSE
C
C           Close .INT file
C           ---------------
            CLOSE(UNIT=DLD_UNIT,IOSTAT=IERR,ERR=1001)
         ENDIF
C
      ELSEIF(FILE.EQ.4)THEN
C
         IF(OPER.EQ.1)THEN
C
C           Open 'Shipname'W'Group & Conf letters'.SIZ file
C           -----------------------------------------------
            FILENAME= INT_DIR(1:L_INT_DIR)
     &                //Config_String(9)(1:Config_Length(9))
     &                //'w'//Filetters(1:3)//'.siz'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=SIZ_UNIT,FILE=N_FILENAME,STATUS='UNKNOWN',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,ERR=1001)
         ELSEIF(OPER.EQ.2)THEN
C
C           Close 'Shipname'W'Group & Conf letters'.SIZ file
C           ------------------------------------------------
            CLOSE(UNIT=SIZ_UNIT,IOSTAT=IERR,ERR=1001)
C
         ELSE
C
C           Close 'Shipname'W'Group & Conf letter'.SIZ file and delete it
C           -------------------------------------------------------------
            CLOSE(UNIT=SIZ_UNIT,IOSTAT=IERR,ERR=1001)
         ENDIF
C
      ELSEIF(FILE.EQ.5) THEN
C
         IF (OPER.EQ.1) THEN
C
C           Open PLOT data file
C           -------------------
            WRITE(NUMB1,'(I1.1)',ERR=2021) PL_NUMB1
            WRITE(NUMB2,'(I2.2)',ERR=2021) PL_NUMB2
            GOTO 2022
 2021       NUMB1 = '1'
            NUMB2 = '01'
 2022       FILENAME=Config_String(1)(1:Config_Length(1))//'harplot_'
     &                  //NUMB1//NUMB2//'.lis'
            PL_NUMB2=PL_NUMB2+1
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=PLT_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,
     &           ERR=1001)
C
         ELSE
C
C           Close PLOT data file
C           --------------------
            CLOSE(UNIT=PLT_UNIT,IOSTAT=IERR,ERR=1001)
         ENDIF
C
      ELSEIF(FILE.EQ.7) THEN
C
         IF (OPER.EQ.1) THEN
C
C           Open HELP text file
C           -------------------
            FILENAME=Config_String(6)(1:Config_Length(6))//'harhelp.txt'
            OPEN(UNIT=HLP_UNIT,FILE=FILENAME,STATUS='OLD',
     &           ERR=1002,IOSTAT=IERR )
C
         ELSE
C
C           Close HELP text file
C           --------------------
            CLOSE(UNIT=HLP_UNIT,IOSTAT=IERR,ERR=1001)
         ENDIF
C
      ELSEIF(FILE.EQ.8) THEN
C
         IF (OPER.EQ.1) THEN
C
C           Open DATA file
C           --------------
            FILENAME=DATA_DIR(1:L_DATA_DIR)
     &               //FILE_DATA(1:L_FILDAT)
            CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DAT_UNIT,FILE=N_FILENAME,STATUS='OLD',ERR=1001
     &           ,IOSTAT = IERR)
C
         ELSEIF(OPER.EQ.2.OR.OPER.EQ.9)THEN
C
C           Close DATA file
C           ---------------
            CLOSE(UNIT=DAT_UNIT,IOSTAT=IERR,ERR=1001)
C
         ELSEIF(OPER.EQ.3)THEN
C
C           Close DATA file and delete
C           --------------------------
            CLOSE(UNIT=DAT_UNIT,IOSTAT=IERR,ERR=1001)
C
         ELSE
C
C           Open a new DATA file
C           --------------------
            FILENAME=DATA_DIR(1:L_DATA_DIR)
     &               //FILE_DATA(1:L_FILDAT)
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=DAT_UNIT,FILE=N_FILENAME,STATUS='UNKNOWN',
     &           ERR=1001,IOSTAT = IERR)
C
         ENDIF
C
      ELSEIF (FILE.EQ.10) THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open SUMARY table file
C          ----------------------
           FILENAME=Config_String(1)(1:Config_Length(1))//'harsum.lis'
            CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
            OPEN(UNIT=SML_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR,
     &           ERR=1001)
C
         ELSEIF(OPER.EQ.2)THEN
C
C          Close SUMARY file and submit to printer
C          ---------------------------------------
           CLOSE(UNIT=SML_UNIT,IOSTAT=IERR,ERR=1001)
           SUBMIT4 = '%SUMMARY : Summary file HARSUM.LIS created'
     &                   //BLANK(1:38)
C
C          Print DATA file created message
C          -------------------------------
           CALL MES23(0,SUBMIT4)
           W_LONG = 3.0
           CALL Wait_Time(W_LONG)
           CALL MES23(1,SUBMIT4)
C
         ENDIF
C
      ELSEIF (FILE.EQ.11) THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open new HARMONY.CNF table file
C          -------------------------------
           FILENAME=Config_String(1)(1:Config_Length(1))//'harm'//
     &              Filetters(1:3)//'.cnf'
           CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           OPEN(UNIT=INF_UNIT,FILE=N_FILENAME,STATUS='NEW',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR)
C
         ELSEIF(OPER.EQ.2) THEN
C
C          Open old HARMONY.CNF table file
C          -------------------------------
           FILENAME=Config_String(1)(1:Config_Length(1))//'harm'//
     &              Filetters(1:3)//'.cnf'
           CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           OPEN(UNIT=INF_UNIT,FILE=N_FILENAME,STATUS='OLD',ACCESS=
     &           'SEQUENTIAL',FORM='FORMATTED',IOSTAT=IERR)
C
         ELSEIF(OPER.EQ.3)THEN
C
C          Close HARMONY.CNF file
C          ----------------------
           CLOSE(UNIT=INF_UNIT,IOSTAT=IERR,ERR=1001)
C
         ELSEIF(OPER.EQ.4) THEN
C
C          Close HARMONY.CNF file and delete it
C          ------------------------------------
           CLOSE(UNIT=INF_UNIT,STATUS='DELETE',IOSTAT=IERR,ERR=1001)
         ENDIF
C
      ELSEIF (FILE.EQ.12) THEN
C
         IF(OPER.EQ.1) THEN
C
C          Open DATA table file
C          --------------------
           FILENAME=EXTDATDIR(1:L_EXTDIR)//EXTDATFIL
           CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
           OPEN(UNIT=EXT_UNIT,FILE=N_FILENAME,STATUS='OLD',ACCESS=
     &           'SEQUENTIAL',IOSTAT=IERR,ERR=1001)
C
         ELSEIF(OPER.EQ.2)THEN
C
C          Close DATA file
C          ---------------
           CLOSE(UNIT=EXT_UNIT,IOSTAT=IERR,ERR=1001)
C
         ENDIF
C
      ENDIF
C
 1010 CONTINUE
C
      IF (IERR.NE.0) THEN
C
C        Write error message during IO operation
C        ---------------------------------------
         SEND(1:37) ='%FIL_OPEN: Error during OPEN on file '
         SEND(38:64)=MESSA(FILE)
         SEND(65:75)=' : Error # '
         CALL GET_ERR_STR(IERR,ERROR)
         SEND(76:80)=ERROR
         CALL ERR_MESS(SEND,80,-1,*50)
 50      CONTINUE
C
      ENDIF
C
 1002 RETURN
 1001 IERR = 99
      GOTO 1010
      END
C
C
CC+
C ============================================================================
C                                 CREATE_PLOT
C ============================================================================
C
      SUBROUTINE CREATE_PLOT(N,XI,NYI,XMIN,XMAX,YMIN,YMAX,
     &                    TITLEP,REF,TITFLAG,BOTFLAG,COMFLAG,
     &                     INTERP,XINCH,YINCH,PLOTERR,SHIPNAME,NTBL)
!
!     Written by G. Dunkelman
!     Revised by A. Clara          January 16  1981
!     Revised for sound by G.De Serre    28 November 1987
!
! ==== description of passed arguments ===================================
!
!         N       -   number of x axis plot values
!         BUFFER  -   plot buffer 1-Y 2-X
!         XI      -   size of 1 x axis interval
!         NYI     -   number of y axis intervals
!         XMIN    -   minimun x value
!         XMAX    -   maximun x value
!         YMIN    -   minimum y value
!         YMAX    -   maximun y value
!         TITLEP  -   title of plot string
!         REF     -   reference of plot string
!         INTERP  -   interpolation flag
!         TITFLAG -   Title flag
!         BOTFLAG -   Bottom title flag
!         COMFLAG -   Reference title flag
!         XINCH   -   size in inches of 10 x axis intervals
!         YINCH   -   size in inches of 10 y axis intervals
!         PLOTERR -   Plot error logger
!         SHIPNAME-   Ship name
!         NTBL    -   Table number
!
! ========================================================================
!
! ---- declerations ------------------------------------------------------
!
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'

      CHARACTER *1  BACK      !Back ASCII character
      CHARACTER REF*40,TITLEP*80,XAXIS*10,YAXIS*10
      LOGICAL INTERP
      LOGICAL*1 TITFLAG,COMFLAG,BOTFLAG
!
      CHARACTER YFTEST1*25,YFTEST2*25,FMT*15,CDIGIT*2,CTAB*3
      INTEGER ITAB(50),IYLOC(50),TABLE(6),UNIT,POSD
      INTEGER*2 SP0,SP1,SP2,PLOTERR,NTBL
      REAL INCH
      CHARACTER*20 SHIPNAME
      CHARACTER*11 DATE,TIME
      LOGICAL XFLAG,YFLAG,MINUSX,MINUSY
      INTEGER*1 MPLOT(300,150),DUMMY,FONT(8),PLOTMODE,SP3
      CHARACTER INTERR*44,WRITERR*48
!
! ---- equivalences ------------------------------------------------------
!
      EQUIVALENCE (SP2,SP3)
!
! ---- initializations ---------------------------------------------------
!
      DATA INTERR/'%CREATE_PLOT : Internal error in CREATE_PLOT'/
      DATA WRITERR/'%CREATE_PLOT : Error #       on file HARPLOT.LIS'/
      DATA TABLE /65,66,68,72,80,160/
      DATA PLOTMODE /5/, DUMMY /64/
!
! ---- reset these variables at the beginning of each plot ---------------
!
      BACK = CHAR(8)
      XFLAG=.FALSE.
      YFLAG=.FALSE.
      SWITCHX=1.0
      SWITCHY=1.0
      NYD=0
      YYD=0.
      IY=0
      NXD=0
      XXD=0.
      IX=1
!
! ---- transfer axis scaling ----------------------------------------------
!
      XL=XMIN
      XR=XMAX
      YT=YMAX
      YB=YMIN
!
!
! ---- determine if final sign of axes is positive of negative -----------
! ---- after sign flip ---------------------------------------------------
!
      MINUSX=(XL.LT.0.OR.XR.LT.0)
      MINUSY=(YB.LT.0.OR.YT.LT.0)
!
! ---- determine number of y interval required ---------------------------
!
      DYD=(YMAX-YMIN)/NYI
      IF (DYD.EQ.0.0) THEN
        IYB=YB
        IF (YB.LT.0.0) IYB=IYB-1
        IYT=YT+0.999
        IYT=IYT+NYI-MOD(IYT-IYB,NYI)
        YT=IYT
        YB=IYB
      ENDIF
      NY=(NYI*YINCH/10.)*72.
      DYD=NY/(1.0*NYI)
      DYB=(YT-YB)/NYI
!
! ---- determine size of y axis format -----------------------------------
!
      IPT_SIGN=1
      IDIGIT=INT(ALOG10(MAX(ABS(YT),ABS(YB),.9)))+1
      IF(MINUSY) IPT_SIGN=IPT_SIGN+1
!!!      JDIGIT = 3
      IJDIGIT=IDIGIT+IPT_SIGN  !!! +JDIGIT
      ISTART=IJDIGIT+2  !1 sp in front and 1 sp after number
!
! ---- compute number of x intervals required by test --------------------
!
      XINT=(XR-XL)/XI+0.999
      NXINT=XINT
      NX=(NXINT*XINCH/10.)*60.+.5
      NXSIZE=NX/6+ISTART !!! +5
      DXD=NX/(1.0*NXINT)
      XR=XL+NXINT*XI
      DXL=(XR-XL)/NXINT
!
! Modified by G. De Serre 28 Nov 87
!
      PLOTERR = 0
      IF (NXSIZE.GT.130.OR.NY.GT.290) THEN
         IF(NXSIZE.GT.130) PLOTERR=PLOTERR+1
         IF(NY.GT.290) PLOTERR=PLOTERR+2
         RETURN
      ENDIF
!
! ---- output title and descriptor message -------------------------------
!
!
! ---- clear plot output buffer ------------------------------------------
!
30    DO 40 I=1,NY+8
        DO 41 J=1,NXSIZE
          MPLOT(I,J)=DUMMY
41      CONTINUE
!
! ---- set up vertical line for y axis -----------------------------------
!
        IF (I.LT.NY+1) THEN
          MPLOT(I,ISTART)=32
          IF (I-1.EQ.NYD) THEN
            YYD=YYD+DYD
            NYD=YYD
            MPLOT(I,ISTART)=56
            IY=IY+1
            IYLOC(IY)=I
          ENDIF
        ENDIF
  40  CONTINUE
      IYLOC(IY+1)=NY
!
! ---- set up horizontal line for x axis ---------------------------------
!
      DO 43 I=ISTART+1,NXSIZE
        MPLOT(NY,I)=63
43    CONTINUE
!
      ITAB(1)=ISTART
      DO 44 I=0,NX
        IF(I.EQ.NXD) THEN
          XXD=XXD+DXD
          NXD=XXD
          IBYTE=(I+5)/6.
          IBIT=I-(IBYTE-1)*6.
          SP0=64
          SP1=TABLE(IBIT)
          SP2=IOR(SP0,SP1)
          SP3 = SP2
          MPLOT(NY+1,IBYTE+ISTART)=SP3
          MPLOT(NY+2,IBYTE+ISTART)=SP3
          IX=IX+1
          ITAB(IX)=ISTART+NXD/6
        ENDIF
44    CONTINUE
      ITAB(IX+1)=ISTART+NX/6
!
! ---- calculate scale factors -------------------------------------------
!
      XF=(NX-1.)/(XR-XL)
      YF=(NY-1.)/(YT-YB)
!
! ---- fill printronics plot buffer --------------------------------------
!
        DO 60 J=0,N-2
          XI=(BUFFER(2,J)-XL)*XF+1.
          YJ=(YT-BUFFER(1,J))*YF+1.
          XI1=(BUFFER(2,J+1)-XL)*XF+1.
          YJ1=(YT-BUFFER(1,J+1))*YF+1.
          DX=XI1-XI
          DY=YJ1-YJ
          IF((.NOT.INTERP).OR.(DX.EQ.0..AND.DY.EQ.0.)) THEN
            IB=XI+.5
            JB=YJ+.5
            IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 60
            IBYTE=(IB+5)/6
            IBIT=IB-(IBYTE-1)*6
            SP0=MPLOT(JB,IBYTE+ISTART)
            SP1=TABLE(IBIT)
            SP2=IOR(SP0,SP1)
            SP3 = SP2
            MPLOT(JB,IBYTE+ISTART)=SP3
          ELSEIF(ABS(DY).GT.ABS(DX)) THEN
            S=DX/DY
            B=XI1-S*YJ1
            JY=MIN(YJ,YJ1)+.5
            JY1=MAX(YJ,YJ1)+.5
            DO 45 JB=JY,JY1
              IB=S*JB+B+.5
              IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 45
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=MPLOT(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              SP3 = SP2
              MPLOT(JB,IBYTE+ISTART)=SP3
45          CONTINUE
          ELSE
            S=DY/DX
            B=YJ1-S*XI1
            IX=MIN(XI,XI1)+.5
            IX1=MAX(XI,XI1)+.5
            DO 48 IB=IX,IX1
              JB=S*IB+B+.5
              IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 48
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=MPLOT(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              SP3 = SP2
              MPLOT(JB,IBYTE+ISTART)=SP3
48          CONTINUE
          ENDIF
60    CONTINUE
!
! ---- output title and descriptor message -------------------------------
!
! Modified by G. De Serre 28 Nov 87
!
      IF (TITFLAG) THEN
C
C       Print title with date, time and shipname
C       ----------------------------------------
        CALL CDATE(DATE,TIME)
80      WRITE(PLT_UNIT,1,ERR=701,IOSTAT=IERR )
        WRITE(PLT_UNIT,999,ERR=701,IOSTAT=IERR )SHIPNAME,DATE,TIME
        WRITE(PLT_UNIT,997,ERR=701,IOSTAT=IERR )
1       FORMAT('1',/' ',94('='))
999     FORMAT(' ',10('<'),' HARMONY PLOT: ',A20,' - DATE :',
     &        A11,' - TIME : ',A8,1X,10('>'))
997     FORMAT(' ',94('='),/)
      ENDIF
C
      IF (COMFLAG) THEN
C
C         Print comment: and new page if title not print
C         ----------------------------------------------
          IF(TITFLAG)THEN
            WRITE(PLT_UNIT,996,ERR=701,IOSTAT=IERR ) REF
996         FORMAT(' ',/,' ',A,/,'     -----------',/)
          ELSE
            WRITE(PLT_UNIT,995,ERR=701,IOSTAT=IERR ) REF
995         FORMAT('1',/,/,/,/,' ',A,/,'     -----------',/)
          ENDIF
      ENDIF
!
! ---- generate y axis digit font ----------------------------------------
!
      IF (IJDIGIT.GE.10) THEN
        WRITE(CDIGIT,'(I2)',ERR=705) IJDIGIT
        POSD = 2
      ELSE
        WRITE(CDIGIT,'(I1)',ERR=705) IJDIGIT
        POSD = 1
      ENDIF
!
!     Modified by G. De Serre
!
      FMT  = '(I'//CDIGIT(1:POSD)//')'
C
      DO 5 I=1,NYI+1
        WRITE(YFTEST1,FMT,ERR=705) IFIX(YT*SWITCHY)
        DO 7 J=1,IJDIGIT
          CALL FONTGEN(YFTEST1(J:J),FONT)
          DO 8 K=1,8
            MPLOT(IYLOC(I)+K-1,J+1)=FONT(K)  !j+1 because of 1 sp in front of no.
8         CONTINUE
7       CONTINUE
!
        YT=YT-DYB
5     CONTINUE
!
! ---- output buffer removing trailling blanks ---------------------------
!
      DO 90 I=1,NY+8
        DO 88 K=NXSIZE,1,-1
          IF(MPLOT(I,K).EQ.DUMMY) GO TO 88
          GO TO 89
88      CONTINUE
89      WRITE(PLT_UNIT,2,ERR=701,IOSTAT=IERR )(MPLOT(I,J),J=1,K),
     &                        PLOTMODE
2       FORMAT(131A1)
  90  CONTINUE
!
! --- determine size of x axis format ------------------------------------
!
      IPT_SIGN=1
      IDIGIT=INT(ALOG10(MAX(ABS(XR),ABS(XL),.9)))+1
      IF(MINUSX) IPT_SIGN=IPT_SIGN+1
      JDIGIT = 0
      IJDIGIT=IDIGIT+JDIGIT+IPT_SIGN
!
! ---- output x axis numbers ---------------------------------------------
!
      N=NXINT+1
      DO 105 J=2,N
        IF(ITAB(J)-ITAB(1) .GE. IJDIGIT) GO TO 106
105   CONTINUE
!
106   WRITE(PLT_UNIT,91,ERR=701,IOSTAT=IERR )
      WRITE(CDIGIT,'(I1)',ERR=705) IJDIGIT
      DO 110 I=1,N,J-1
      IF (ITAB(I)-IJDIGIT/2+1.GE.100) THEN
        WRITE(CTAB,'(I3)',ERR=705) ITAB(I)-IJDIGIT/2+1
        POSD = 3
      ELSE IF (ITAB(I)-IJDIGIT/2+1.GE.10) THEN
        WRITE(CTAB,'(I2)',ERR=705) ITAB(I)-IJDIGIT/2+1
        POSD = 2
      ELSE
        WRITE(CTAB,'(I1)',ERR=705) ITAB(I)-IJDIGIT/2+1
        POSD = 1
      ENDIF
      FMT = '(''+'',T'//CTAB(1:POSD)//',I'//CDIGIT(1:1)//')'
      WRITE(PLT_UNIT,FMT,ERR=701,IOSTAT=IERR )
     &            INT((XL+(I-1)*DXL)*SWITCHX)
110   CONTINUE
!
91    FORMAT(1X,/)
!!!92    FORMAT('+',T<ITAB(I)-IJDIGIT/2+1>,F<IJDIGIT>.0)		!<JDIGIT>)
!
!     Added by G. De Serre
!
C
C     Write table number and title
C     ----------------------------
      WRITE(PLT_UNIT,1001,ERR=701,IOSTAT=IERR ) BACK,NTBL,TITLE(NTBL)
1001  FORMAT(/,/,' ',A1,15X,'TABLE # ',I2,5X,'TITLE: ',A30,/)
      IF (BOTFLAG) THEN
81      WRITE(PLT_UNIT,3,ERR=701,IOSTAT=IERR )BACK,TITLEP
3       FORMAT(' ',A1,1X,A80,/)
      ENDIF
C
      GOTO 706
705   CALL ERR_MESS(INTERR,44,1,*706)
701   WRITE(WRITERR(26:30),'(I5)',ERR=802) IERR
802   CONTINUE
      CALL ERR_MESS(WRITERR,48,1,*706)
706   CONTINUE
      RETURN
      END
      SUBROUTINE FONTGEN(SYMBOL,FONT)
C
C     MODULE TO GENERATE FONT
C
      CHARACTER*1 SYMBOL,LEGAL(13)*1
      BYTE FONT(8),CHARGEN(13,8)
C
C     INITIALIZE
C
      DATA LEGAL /'0','1','2','3','4','5','6','7','8','9',
     *            '-','.','!'/
      DATA CHARGEN /
     * 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 72,
     * 92, 72, 92, 92, 80, 62, 80, 62, 92, 92, 64, 64, 72,
     * 34, 76, 34, 34, 88, 66, 72, 32, 34, 34, 64, 64, 72,
     * 34, 72, 32, 32, 84, 94, 68, 32, 34, 34, 64, 64, 72,
     * 34, 72, 80, 88, 82, 32, 94, 80, 92, 60, 94, 64, 72,
     * 34, 72, 72, 32, 62, 32, 34, 72, 34, 80, 64, 64, 72,
     * 34, 72, 68, 34, 80, 34, 34, 68, 34, 72, 64, 64, 72,
     * 92, 92, 62, 92, 80, 92, 92, 66, 92, 68, 64, 66, 72/
C
C     LOCATE LETTER
C
      DO 10 I=1,13
      IF (SYMBOL.EQ.LEGAL(I)) GOTO 20
  10  CONTINUE
C
C     STORE A BLANK
C
      DO 15 I=1,8
      FONT(I)=64
  15  CONTINUE
      RETURN
C
C     STORE FONT
C
  20  DO 30 J=1,8
      FONT(J)=CHARGEN(I,J)
  30  CONTINUE
      RETURN
      END
CC-
C
C
C     =====================================
      SUBROUTINE OPEN_PLOT(PlotFile,IERR,*)
C     =====================================
C
      INTEGER*2
     & IERR
C
      CHARACTER
     & PlotFile*(*)
C
      RETURN
      END
C
C
C ===================================================================
C                             DATFIL
C ===================================================================
C
      SUBROUTINE DATFIL(Status)
C
      LOGICAL*1 Status
C
      LOGICAL*4 Present
C
      INTEGER*4
     & RevStat
C
      CHARACTER
     & FILNAM*255,
     & N_FILNAM*255
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      FILNAM = DATA_DIR(1:L_DATA_DIR)
     &        //FILE_DATA(1:L_FILDAT)
      CALL rev_curr(FILNAM,N_FILNAM,' ',.FALSE.,1,revstat)
      INQUIRE(FILE=N_FILNAM,EXIST=Present)
      IF (Present) THEN
        Status = .TRUE.
      ELSE
        Status = .FALSE.
      ENDIF
C
      RETURN
      END
C
C
C ======================================================================
C                           EXTEND_MEM
C =====================================================================
C
C     This subroutine is not used on IBM computer.
C
C
      SUBROUTINE EXTEND_MEM(ERR)
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*2 ERR  !Error number
C
      VAXSEL = .TRUE.
      ERR = 0
C
      RETURN
C
      END
C
C =========================================================================
C                         LASER PRINTER PLOTS ROUTINE
C =========================================================================
C
C     This is a dummy routine on IBM computer
C
      SUBROUTINE GRAPH
C
      RETURN
      END
C
C     ==================
      SUBROUTINE CDB_COM
C     ==================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
C
      CHARACTER*50 cdbname
      CHARACTER*8  pvalue
      CHARACTER*96 buf1
      INTEGER*4   cts_stat, dtype, offset, fetch(2),
     &            len_cdbname, bytebufd, PUT_ERR, GET_ERR
C
      INTEGER*2 INIT_ERR,COM_ERROR,LENGTH,STATUS,CDB_ERR,
     &          NUM_INDEX(MAX_TYPE)
      INTEGER*4 VALOUT
      INTEGER*4 REG1,REG2,VALIN,INDEX,INDEX2,TYPE,TYPE2
      CHARACTER*12 LABEL,RTLMESS*80
      INTEGER*4 CNT_FKEY(2),STAT_FKEY(2),
     &          P_CNT_FKEY(2),P_OUT_FKEY(2,16),
     &          CNT_UTYP(2),STAT_UTYP(2),
     &          P_CNT_UTYP(2),P_OUT_UTYP(2,16),
     &          FREZ_FKEY,FREZ_UTYP,
     &          CRG_FKEY(2),CRG_UTYP(2)
C
      DATA  RTLMESS   /'%RTL_INIT : Getting CDB label addresses for Real
     & Time Load ...                  '/
C
      INCLUDE 'hardata.inc'
C
      EQUIVALENCE (pvalue,bytebufd)
C
C     This routine gets the CDB label addresses.  It uses the CDB labels
C     defined in the harmony.f file.  The Real Time Load will work for a
C     Sound chassis containing 2 DSGs maximum (1 TONE and 1 SLAP).
C     ------------------------------------------------------------------
      ENTRY CDB_INIT(INIT_ERR)
C     ========================
C
      cdb_err = 0
      len_cdbname = Config_Length(9) + 24
      cdbname(1:28) = '/cae/simex_plus/element/'//Config_String(9)(1:4)
      IF (ON_SITE) THEN
        CALL yxrl_open(cdbname,len_cdbname,cts_stat)
      ELSE
        cts_stat = 1
      ENDIF
      IF (cts_stat.EQ.1) THEN
         CDB_ERR=0
         KK = 1
         DO WHILE(KK.LE.2 .AND. CDB_ERR.EQ.0)
           label = CNT_TCR(KK)(1:8)
           IF(ON_SITE) THEN
             CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
           ELSE
             cts_stat = 1
           ENDIF
           IF(cts_stat.EQ.1) THEN
             CNT_FKEY(KK) = fetch(1)
             CNT_UTYP(KK) = dtype
           ELSE
             CDB_ERR = cts_stat
           ENDIF
C
           IF(CDB_ERR.EQ.0)THEN
             label = STAT_TCR(KK)(1:8)
             IF(ON_SITE) THEN
               CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
             ELSE
               cts_stat = 1
             ENDIF
             IF(cts_stat.EQ.1) THEN
               STAT_FKEY(KK) = fetch(1)
               STAT_UTYP(KK) = dtype
             ELSE
               CDB_ERR = cts_stat
             ENDIF
           ENDIF
C
           IF(CDB_ERR.EQ.0)THEN
             label = CRG_TCR(KK)(1:8)
             IF(ON_SITE) THEN
               CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
             ELSE
               cts_stat = 1
             ENDIF
             IF(cts_stat.EQ.1) THEN
               CRG_FKEY(KK) = fetch(1)
               CRG_UTYP(KK) = dtype
             ELSE
               CDB_ERR = cts_stat
             ENDIF
           ENDIF
C 
           IF(CDB_ERR.EQ.0)THEN
             label = P_CNT_TCR(KK)(1:8)
             IF(ON_SITE) THEN
               CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
             ELSE
               cts_stat = 1
             ENDIF
             IF(cts_stat.EQ.1) THEN
               P_CNT_FKEY(KK) = fetch(1)
               P_CNT_UTYP(KK) = dtype
             ELSE
               CDB_ERR = cts_stat
             ENDIF
           ENDIF
C
           II = 1
           DO WHILE(II.LE.16 .AND. CDB_ERR.EQ.0)
             label = P_OUT_TCR(KK,II)(1:8)
             IF(ON_SITE) THEN
               CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
             ELSE
               cts_stat = 1
             ENDIF
             IF(cts_stat.EQ.1) THEN
                P_OUT_FKEY(KK,II) = fetch(1)
                P_OUT_UTYP(KK,II) = dtype
             ELSE
                CDB_ERR = cts_stat
             ENDIF
             II = II+1
           ENDDO
C
           KK = KK + 1
         ENDDO
C
         IF(CDB_ERR.EQ.0)THEN
           label = FREZ_TCR
           IF(ON_SITE) THEN
             CALL ctskey(label,cts_stat,fetch,buf1,dtype,offset)
           ELSE
             cts_stat = 1
           ENDIF
           IF(cts_stat.EQ.1) THEN
             FREZ_FKEY = fetch(1)
             FREZ_UTYP = dtype
           ELSE
             CDB_ERR = cts_stat
           ENDIF
         ENDIF
C
         IF(ON_SITE) THEN
           CALL yxrl_close
         ENDIF
C
      ELSE
         CDB_ERR = cts_stat
      ENDIF
C
      NUM_INDEX(1) = 3
      NUM_INDEX(2) = 1
      NUM_INDEX(3) = 16
      NUM_INDEX(4) = 1
C
      IF(CDB_ERR.GT.1) THEN
         CALL ERR_CDB_ROUT(cts_stat)
         INIT_ERR=CDB_ERR
      ELSE
         CALL ERR_MESS(RTLMESS,62,-1,*33)
 33      CONTINUE
         INIT_ERR = 0
      ENDIF
C
      RETURN
C
C
C     This routine reads a value from the CDB
C     ---------------------------------------
      ENTRY CDB_GET(VALOUT,TYPE,REG1,INDEX,GET_ERR)
C     =============================================
C
      IF(TYPE.EQ.1) THEN
        IF(INDEX.LE.2 .AND. INDEX.GT.0)THEN   !INDEX is used as the DSG pointer
           IF(REG1.GE.1.OR.REG1.LE.3) THEN    !REG1 is used as the register pnt
             IF(REG1.EQ.1)THEN                !Read Control Register (CR1)
               fetch(1) = CNT_FKEY(INDEX)
               dtype = CNT_UTYP(INDEX)
             ELSEIF(REG1.EQ.2) THEN           !Read Status Register (SR1)
               fetch(1) = STAT_FKEY(INDEX)
               dtype = STAT_UTYP(INDEX)
             ELSEIF(REG1.EQ.3) THEN           !Read Foreground counter (CN1)
               fetch(1) = CRG_FKEY(INDEX)
               dtype = CRG_UTYP(INDEX)
             ENDIF
             IF(ON_SITE) THEN
              CALL ctsread(cts_stat,fetch,pvalue,dtype,1)
             ELSE
              cts_stat = 1
             ENDIF
             VALOUT = bytebufd
             GET_ERR = cts_stat
           ELSE
             GET_ERR = 1003
           ENDIF
        ELSE
          GET_ERR = 1001
        ENDIF
C
      ELSEIF(TYPE.EQ.2) THEN       !Read Phase Couter
        IF(REG1.EQ.1) THEN
           fetch(1) = P_CNT_FKEY(INDEX)
           dtype = P_CNT_UTYP(INDEX)
           IF(ON_SITE) THEN
             CALL ctsread(cts_stat,fetch,pvalue,dtype,1)
           ELSE
             cts_stat = 1
           ENDIF
           VALOUT = bytebufd
           GET_ERR = cts_stat
        ELSE
           GET_ERR = 1003
        ENDIF
C
      ELSEIF(TYPE.EQ.3) THEN
        IF(INDEX.LE.NUM_INDEX(TYPE).AND.INDEX.GT.0)THEN
           fetch(1) = P_OUT_FKEY(REG1,INDEX)   !REG1 is used as the DSG pointer
           dtype = P_OUT_UTYP(REG1,INDEX)      !Same here...
           IF(ON_SITE) THEN
             CALL ctsread(cts_stat,fetch,pvalue,dtype,1)
           ELSE
             cts_stat = 1
           ENDIF
           VALOUT = bytebufd
           GET_ERR = cts_stat
        ELSE
           GET_ERR = 1001
        ENDIF
C
      ELSEIF(TYPE.EQ.4) THEN
        fetch(1) = FREZ_FKEY
        dtype = FREZ_UTYP
        IF(ON_SITE) THEN
           CALL ctsread(cts_stat,fetch,pvalue,dtype,1)
        ELSE
           cts_stat = 1
        ENDIF
        VALOUT = bytebufd
        GET_ERR = cts_stat
      ELSE
        GET_ERR = 1002
      ENDIF
C
      IF(GET_ERR.GT.1) THEN
         CALL ERR_CDB_ROUT(GET_ERR)
      ENDIF
C
      RETURN
C
C     This routine writes a value in the CDB
C     --------------------------------------
      ENTRY CDB_PUT(VALIN,TYPE,REG2,INDEX,PUT_ERR)
C     ============================================
C
      IF(TYPE.EQ.1)THEN
        IF(REG2.GE.1.OR.REG2.LE.3) THEN
          IF(REG2.EQ.1) THEN
            fetch(1) = CNT_FKEY(INDEX)
            dtype = CNT_UTYP(INDEX)
            bytebufd = VALIN
            IF(ON_SITE) THEN
              CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
            ELSE
              cts_stat = 1
            ENDIF
          ELSEIF(REG2.EQ.2)THEN
            fetch(1) = STAT_FKEY(INDEX)
            dtype = STAT_UTYP(INDEX)
            bytebufd = VALIN
            IF(ON_SITE) THEN
              CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
            ELSE
              cts_stat = 1
            ENDIF
          ELSEIF(REG2.EQ.3) THEN
            fetch(1) = CRG_FKEY(INDEX)
            dtype = CRG_UTYP(INDEX)
            bytebufd = VALIN
            IF(ON_SITE) THEN
              CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
            ELSE
              cts_stat = 1
            ENDIF
          ENDIF
          PUT_ERR = cts_stat
        ELSE
          PUT_ERR = 1001
        ENDIF
C
      ELSEIF(TYPE.EQ.2)THEN
         IF(REG2.EQ.1) THEN
            fetch(1) = P_CNT_FKEY(INDEX)
            dtype = P_CNT_UTYP(INDEX)
            bytebufd = VALIN
            IF(ON_SITE) THEN
              CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
            ELSE
              cts_stat = 1
            ENDIF
            PUT_ERR = cts_stat
         ELSE
            PUT_ERR = 1003
         ENDIF
C
      ELSEIF(TYPE.EQ.3)THEN
        IF(INDEX.LE.NUM_INDEX(TYPE).AND.INDEX.GT.0)THEN
           fetch(1) = P_OUT_FKEY(REG2,INDEX)
           dtype = P_OUT_UTYP(REG2,INDEX)
           bytebufd = VALIN
           IF(ON_SITE) THEN
              CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
           ELSE
              cts_stat = 1
           ENDIF
           PUT_ERR = cts_stat
        ELSE
           PUT_ERR = 1001
        ENDIF
C
      ELSEIF(TYPE.EQ.4) THEN
        fetch(1) = FREZ_FKEY
        dtype = FREZ_UTYP
        bytebufd = VALIN
        IF(ON_SITE) THEN
          CALL ctswrite(cts_stat,fetch,pvalue,dtype,1)
        ELSE
          cts_stat = 1
        ENDIF
        PUT_ERR = cts_stat
      ELSE
        PUT_ERR = 1002
      ENDIF
C
      IF(PUT_ERR.GT.1) THEN
         CALL ERR_CDB_ROUT(PUT_ERR)
      ENDIF
C
      RETURN
C
      END
C
C     =============================
      SUBROUTINE REV_CREG(REGISTER)
C     =============================
C
      IMPLICIT NONE
      INTEGER*2 REGISTER,STORE
C
      STORE    = IAND(REGISTER,'0FFF'X)
C
      REGISTER = NOT(REGISTER)
      REGISTER = IAND(REGISTER,'F000'X)
C
      REGISTER = IOR(REGISTER,STORE)
C
      RETURN
      END
C
