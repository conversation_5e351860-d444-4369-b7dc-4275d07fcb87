INCLUDE = harparm.inc hardata.inc hardisp.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
harmony: harmony.o hardisp.o haredit.o harlib1.o harlib2.o harplot.o harhelp.o harunix.o harlist.o harcalc.o harload.o $(CAELIB)/libcae.a $(LIBDIR)/libaos.a
#
#
	xlf -C -g -qcharlen=1024 harmony.o hardisp.o haredit.o harlib1.o \
harlib2.o harplot.o harhelp.o harunix.o harlist.o harcalc.o harload.o \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o $(EXEDIR)/harmony
#
#
harmony.o: harmony.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c harmony.f
#
hardisp.o: hardisp.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c hardisp.f
#
haredit.o: haredit.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c haredit.f
#
harlib1.o: harlib1.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c harlib1.f
#
harlib2.o: harlib2.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c harlib2.f
#
harplot.o: harplot.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c harplot.f
#
harunix.o: harunix.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c harunix.f
#
harhelp.o: harhelp.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c harhelp.f
#
harlist.o: harlist.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c harlist.f
#
harcalc.o: harcalc.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c harcalc.f
#
harload.o: harload.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c harload.f
