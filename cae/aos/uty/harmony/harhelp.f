C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARHELP.FOR                                              **
C   **  Function : Help function subroutines                                **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON>. <PERSON>             Date: 30 April 1987     **
C   **  Rev 1.0             G. <PERSON>                   30 July 1988      **
C   **  Rev 2.0             G. <PERSON>                   10 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1989       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  HELP                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C     =====================
      SUBROUTINE HELP(MODE)
C     =====================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
        LOGICAL*1
     & FIRST /.TRUE./          ,!First time help called
     & ONELINE                 ,!One line has been print flag
     & TEXTON                  ,!Continue to print help text flag
     & FIND1                   ,!Find the right help text flag
     & FIND2                    !Find the right help text flag
C
       INTEGER*2
     & POS_PG                  ,!Position in the page
     & OFFSET                  ,!Page offset
     & PAGE                    ,!Help file page count
     & POSCOM                  ,!Position of comma
     & PAGECNT                 ,!Help print page count
     & CARET                   ,!Cariage return position
     & POSPACE                 ,!Space position in the data line
     & POSTAR                   !Star position in the data line
C
      INTEGER*4
     & LLIN                    ,!Data line length
     & POSI                    ,!Screen position of the message
     & MODE                    ,!Help mode
     & ENDFIL                  ,!End of file counter
     & FILERR                   !File opening error logger
C
      CHARACTER
     & ISEND*100,
     & INP_LIN*80,
     & ERROR1*80,
     & ERROR2*80,
     & FMATA*3 /'(A)'/         ,!Read a line format
     & CARAC*2                  !Help code string
C
       DATA ERROR1/'%HARMONY - Error when opening HELP file '/
       DATA ERROR2/'%HARMONY - No text on HELP file for that item '/
C
C     Open the HELP text file the first time
C     --------------------------------------
      IF (FIRST) THEN
C
         HELPMES(1)='         ****  HELP - HARMONY GENERAL MENU **** ' !0
         HELPMES(2)='                                                ' !1
         HELPMES(3)='         ****  HELP - EDIT TABLES          **** ' !2
         HELPMES(4)='         ****  HELP - WAVE TABLE TYPE      **** ' !3
         HELPMES(5)='         **** HELP - BREAKPOINT TABLE TYPE **** ' !4
         HELPMES(6)='         ****  HELP - EXTERNAL TABLE TYPE  **** ' !5
         HELPMES(7)='         ****  HELP - INPUT TABLE NUMBER   **** ' !6
         HELPMES(8)='         ****  HELP - INPUT TABLE NUMBER   **** ' !7
         HELPMES(9)='         ****  HELP - INPUT PHASE          **** ' !8
         HELPMES(10)='        ****  HELP - GET EXTERNAL POINTS  **** ' !9
         HELPMES(11)='        ****  HELP - INPUT WAVE TYPE      **** ' !10
         HELPMES(12)='        ****  HELP - BRKPNT TABLE INPUTS  **** ' !11
         HELPMES(13)='        ****  HELP - INP HARMONIC NUMBER  **** ' !12
         HELPMES(14)='        ****  HELP - INPUT AMPLITUDE      **** ' !13
         HELPMES(15)='        ****  HELP - INPUT TABLE SIZE     **** ' !14
         HELPMES(16)='        ****  HELP - TABLE TITLE          **** ' !15
         HELPMES(17)='        ****  HELP - EXTERNAL DATA FILE   **** ' !16
         HELPMES(18)='                                               ' !17
         HELPMES(19)='                                               ' !18
         HELPMES(20)='                                               ' !19
         HELPMES(21)='                                               ' !20
         HELPMES(22)='        ****  HELP - INPUT SOURCE NUMBER  **** ' !21
         HELPMES(23)='                                               ' !22
         HELPMES(24)='                                               ' !23
         HELPMES(25)='                                               ' !24
         HELPMES(26)='        ****  HELP - DELETE TABLES        **** ' !25
         HELPMES(27)='        ****  HELP - DELETE TABLE NUMBER  **** ' !26
         HELPMES(28)='        ****  HELP - DELETE SOURCE NUMBER **** ' !27
         HELPMES(29)='                                               ' !28
         HELPMES(30)='                                               ' !29
         HELPMES(31)='        ****  HELP - LIST                 **** ' !30
         HELPMES(32)='        ****  HELP - LOAD                 **** ' !31
         HELPMES(33)='        ****  HELP - CONF                 **** ' !32
         HELPMES(34)='                                               ' !33
         HELPMES(35)='        ****  HELP - WAVEFORM PLOT X-AXIS **** ' !34
         HELPMES(36)='        ****  HELP - PLOT                 **** ' !35
C
         HLPOPEN = .TRUE. !To close the file when quiting the utility
         CALL FIL_OPEN(7,1,FILERR)
C
         IF (FILERR.NE.0) THEN
C
C          Print open error on HELP text file
C          ----------------------------------
           CALL ERR_MESS(ERROR1,40,-1,*50)
 50        CONTINUE
           RETURN
         ELSE
           FIRST = .FALSE.
         ENDIF
      ENDIF
C
      CALL SET_SCROLL(11,22)
      CALL CL_DISP
C
C     Print the help text until end of file or next text
C     --------------------------------------------------
      ISEND(1:13) = BRT_STRT//'        '
      ISEND(14:60) = HELPMES(MODE+1)(1:47)
      ISEND(61:71) = BRT_END//'  '//REV_STRT
      ISEND(72:95) = '  Page     of      '//REV_END
C
      PAGECNT = 1
 101  CONTINUE
      PAGE = 1
C
C     Find the text for the menu level
C     --------------------------------
      FIND1 = .FALSE.
      FIND2 = .FALSE.
      DO WHILE(.NOT.FIND2.AND.ENDFIL.EQ.0)
         READ(HLP_UNIT,11,IOSTAT=ENDFIL,ERR=780) INP_LIN(1:80)
 780     CONTINUE
         POSTAR=INDEX(INP_LIN,'*')
C
C        Look for a star in first position: delimiter between help text
C        --------------------------------------------------------------
         IF (POSTAR.EQ.1) THEN
            POSCOM=INDEX(INP_LIN,',')
            CARAC = INP_LIN(2:POSCOM-1)
            READ(CARAC,*,ERR=98)TEMPI
            IF (TEMPI.EQ.MODE)THEN
               FIND1=.TRUE.
               IF(PAGECNT.EQ.1)THEN
                  FIND2=.TRUE.
               ENDIF
            ENDIF
         ELSEIF(FIND1)THEN
            POS_PG=INDEX(INP_LIN(1:2),'$$')
            IF(POS_PG.EQ.1)THEN
               PAGE = PAGE+1
               IF(PAGE.EQ.PAGECNT)THEN
                  FIND2=.TRUE.
               ENDIF
            ENDIF
         ENDIF
 98   ENDDO
C
C     Check if no text found for that help
C     ------------------------------------
      IF(ENDFIL.NE.0.AND..NOT.FIND2)THEN
C
C        Print open error on HELP text file
C        ----------------------------------
         CALL ERR_MESS(ERROR2,46,-1,*51)
 51      CONTINUE
         CALL Term_Write(7,57,BLANK,22)
         RETURN
      ENDIF
C
      IF(PAGECNT.EQ.1)THEN
        CALL Term_Write(7,1,ISEND(1:66),66)
        POSPACE=INDEX(INP_LIN,' ')
        CARAC = INP_LIN(POSCOM+1:POSPACE-1)
        READ(CARAC,*,ERR=97) PAGE
        GOTO 96
 97     PAGE = 1
 96     CONTINUE
        WRITE(ISEND(86:88),'(I3)',ERR=701) PAGE
 701    CONTINUE
      ENDIF
      ONELINE=.FALSE.
      POSI=8
      TEXTON = .TRUE.
      DO WHILE(TEXTON)
 1001    READ(HLP_UNIT,11,IOSTAT=ENDFIL,ERR=781) INP_LIN(1:80)
 781     CONTINUE
         LLIN = 80
         CALL String_Length(INP_LIN,LLIN)
C
C        Stop to read if end of file or a END delimiter found
C        ----------------------------------------------------
         IF (ENDFIL.EQ.0)THEN
           POSTAR=INDEX(INP_LIN,'END')
           IF (POSTAR.NE.1) THEN
C
C             Ask to continue on print text
C             -----------------------------
              CARET=INDEX(INP_LIN,'$$')
              IF((CARET.NE.0).AND..NOT.ONELINE) GOTO 1001
              POSI=POSI +1
              IF(POSI.EQ.22.OR.CARET.NE.0) THEN
C
                 ONELINE=.FALSE.
C
C               Maximum number of line reached or cariage return caracter found
C               ---------------------------------------------------------------
                 IF(CARET.EQ.0)THEN
                    INP_LIN(LLIN+1:LLIN+3) = ESC//'[K'
                    CALL Term_Write(POSI,1,INP_LIN(1:LLIN+3),LLIN+3)
                 ENDIF
                 POSI=8
                 CALL WAIT_CONT(0,OFFSET)
                 IF(OFFSET.EQ.0) THEN
                     REWIND (UNIT=HLP_UNIT)
                     CALL SET_SCROLL(0,24)
                     CALL CL_DISP !Clear the line for command
                     CALL Term_Write(7,57,BLANK,22)
                     RETURN
                 ELSEIF(OFFSET.EQ.-1)THEN
                     PAGECNT=MAX(PAGECNT-1,1)
                     REWIND (UNIT=HLP_UNIT)
                     CALL CL_DISP3
                     GOTO 101
                 ELSEIF(OFFSET.EQ.1)THEN
                     PAGECNT=PAGECNT+1
                     CALL CL_DISP3
                 ENDIF
C
              ELSE
                 INP_LIN(LLIN+1:LLIN+3) = ESC//'[K'
                 CALL Term_Write(POSI,1,INP_LIN(1:LLIN+3),LLIN+3)
                 IF(.NOT.ONELINE) THEN
                     ONELINE=.TRUE.
                     WRITE(ISEND(79:81),'(I3)') PAGECNT
                     CALL Term_Write(7,59,ISEND(68:95),28)
                 ENDIF
              ENDIF
           ELSE
              TEXTON=.FALSE.
           ENDIF
         ELSE
           TEXTON=.FALSE.
         ENDIF
      ENDDO
C
      REWIND (UNIT=HLP_UNIT)
      IF(.NOT.ONELINE) GOTO 101
      CALL WAIT_CONT(0,OFFSET)
      IF(OFFSET.EQ.0) THEN
          CALL CL_DISP !Clear the line for command
          CALL SET_SCROLL(0,24)
          CALL Term_Write(7,57,BLANK,22)
          RETURN
      ELSEIF(OFFSET.EQ.-1)THEN
          PAGECNT=MAX(PAGECNT-1,1)
      ELSEIF(OFFSET.EQ.1)THEN
          PAGECNT=1
      ENDIF
C
      CALL CL_DISP3
      GOTO 101
C
 11   FORMAT(A)
      END
C
