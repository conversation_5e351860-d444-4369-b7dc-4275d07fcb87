C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARDISP.FOR                                              **
C   **  Function : All Display subroutines                                  **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by M<PERSON>/G. De <PERSON>re   Date: 15 March 1987     **
C   **  Rev 1.0             G. <PERSON>                   15 June 1988      **
C   **  Rev 2.0             G. <PERSON>                   10 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1990       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  MAIN_MENU                                                           **
C   **  MODE_SET                                                            **
C   **  DISPLAY                                                             **
C   **  DISP_HEAD                                                           **
C   **  DISP_SRC                                                            **
C   **  DISP_EXT                                                            **
C   **  DISP_OTH                                                            **
C   **  CL_DISP                                                             **
C   **  CL_DISP4                                                            **
C   **  CL_DISP2                                                            **
C   **  CL_DISP3                                                            **
C   **  TAB_HEAD_DISP                                                       **
C   **  EDIT_DISP                                                           **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C       ==================================
        SUBROUTINE MAIN_MENU(NITEM,HQXFLG)
C       ==================================
        IMPLICIT NONE
C
C -- This routine sets up the main menu
C
C +--------------------    DATA DECLARATIONS    ----------------------------+
C
        INCLUDE 'harparm.inc'
        INCLUDE 'hardata.inc'
        INCLUDE 'hardisp.inc'
C
        LOGICAL*1 HQXFLG,FIRST23/.TRUE./
        INTEGER*2 ITLEN(6),IPOSI(6),POSI(6),START
        INTEGER*2 PR_IT /0/
        INTEGER*4 NITEM
C
        CHARACTER ITEM1*8,
     &            ITEM2*8,
     &            ITEM3*8,
     &            ITEM4*10,
     &            ITEM5*8,
     &            ITEM6*8,
     &            ITEM7*8,
     &            ITEM8*8,
     &            ITSTR(6)*21,
     &            TEXT*100
C
        DATA ITEM1/'H : HELP'/,
     &       ITEM2/'M : MODE'/,
     &       ITEM3/'Q : QUIT'/,
     &       ITEM4/'X,E : EXIT'/,
     &       ITEM5/'C : CONF'/,
     &       ITEM6/'S : SAVE'/,
     &       ITEM7/'Q : EXIT'/,
     &       ITEM8/'B : BOX '/
       DATA ITSTR/'    EDIT     ','   DELETE    ','   ASSIGN    ',
     &            '     LIST    ','     PLOT    ','     LOAD    '/
       DATA ITLEN/13,13,13,13,13,13/
       DATA IPOSI/2,15,28,41,54,67/
C
C +--------------------      PROGRAM START  ----------------------------+
C
C -- Clear the screen, set the output mode to either PRINTER or SCREEN in
C    the header line of the main menu
C
       IF(NITEM.EQ.-1)THEN
          TEMPIX=PR_IT
          FIRST23=.TRUE.
       ELSE
          TEMPIX=NITEM
       ENDIF
C
       IF(HQXFLG) THEN
          CALL CL_SCREEN
       ENDIF
C
       TEXT(1:100) = BLANK//BLANK(1:20)
       DO JJ=1,6
          POSI(JJ)=IPOSI(JJ)
       ENDDO
       DO JJ=1,6
          START = POSI(JJ)
          IF(JJ.EQ.TEMPIX)THEN
             TEXT(START:START+7)=BRT_STRT//REV_STRT
             START=START+8
             TEXT(START:START+ITLEN(JJ))=ITSTR(JJ)(1:ITLEN(JJ))
             START=START+ITLEN(JJ)
             TEXT(START:START+9)=BRT_END//REV_END
             DO II = JJ+1,6
                POSI(II)=IPOSI(II)+18
             ENDDO
          ELSE
             TEXT(START:START+ITLEN(JJ))=ITSTR(JJ)(1:ITLEN(JJ))
          ENDIF
       ENDDO
C
       CALL Term_Write(3,1,TEXT,100)
C
C -- Display the menu options

       IF (HQXFLG.OR.((TEMPIX.NE.PR_IT).AND.
     &    (TEMPIX.EQ.0.OR.PR_IT.EQ.0)
     &    .AND.(TEMPIX.LE.7)) )THEN

C -- Display the first reverse video line, which contains the help , quit
C    and exit options.

CCCC          CALL Term_Write(-1,1,REV_STRT,4)
          CALL Start_Reverse
          IF(TEMPIX.NE.0.) THEN
            IF(TEMPIX.EQ.4.OR.TEMPIX.EQ.6) THEN
               TEXT = '   '//ITEM1//BLANK(1:8)//ITEM2//BLANK(1:8)
     &                //ITEM8//BLANK(1:8)//ITEM7//BLANK(1:8)//
     &                  ITEM4//'   '
            ELSE
               TEXT = '  '//ITEM1//BLANK(1:8)//BLANK(1:8)//
     &            BLANK(1:8)//ITEM8//BLANK(1:10)//ITEM7//BLANK(1:8)//
     &                  ITEM4//'  '
            ENDIF
          ELSE
            TEXT = '   '//ITEM1//BLANK(1:5)//
     &                ITEM5//'     '//ITEM6//'  '
     &           //BLANK(1:4)//ITEM8//'    '//ITEM3//'    '//
     &           ITEM4//'   '
          ENDIF
          CALL Term_Write(5,1,TEXT,80)
CCCCC          CALL Term_Write(-1,1,REV_END,5)
          CALL Stop_Reverse
C
C
C -- Display the second reverse video line , which is used to display
C    user input errors

          IF(FIRST23) THEN
             FIRST23=.FALSE.
             CALL MES23(1,BLANK)
          ENDIF
        ENDIF
        PR_IT = TEMPIX
C
        RETURN
        END
C
C     =========================
      SUBROUTINE MODE_SET(MODE)
C     =========================
      IMPLICIT NONE
C
C -- This subroutine sets the mode to either PRINTER or SCREEN depending
C    on the value of MODE.
C
C    INPUT : MODE -- if mode >= 1, then display mode on header
C                    if mode = 1, toggle mode setting
C                    if mode < 1, then don't display mode on header
C
      INCLUDE 'harparm.inc'
C
      CHARACTER OMODE*23,            ! String for PRINTER or SCREEN mode
     &          TEXT*100,
     &          MOVE*7,              ! Moves the curser to given line and col
     &          REV_CAR*4            ! Revision level character
C
      LOGICAL*1 FIRST_PASS /.TRUE./  ! First pass flag
C
      INTEGER*2 PMODE                ! Mode input value
      INTEGER*4 MODE
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C     Set the revision level number into string
C     -----------------------------------------
      WRITE(REV_CAR,'(F4.1)',ERR=999) REV_LEVEL
      GOTO 998
 999  REV_CAR = 'inval'
 998  CONTINUE
C
C -- If MODE is greater than or equal to one, then print the MODE
C    on the header line.
C
      IF (MODE .GE. 1) THEN

C -- If mode is = 1 then toggle the mode setting

        IF (MODE.EQ.1) THEN
          OUTMODE = .NOT. OUTMODE
        ENDIF
        IF (OUTMODE) THEN
          OMODE = ' OUTPUT MODE : PRINTER '
        ELSE
          OMODE = ' OUTPUT MODE : SCREEN  '
        ENDIF
        TEXT =  REV_STRT
     &  //'     Version '//REV_CAR//'             **     HARMONY    ** '
     &  //'      '//BRT_STRT//OMODE//
     &            REV_END//BRT_END
        CALL Term_Write(1,1,TEXT,99)
      ELSEIF(MODE.GE.-1) THEN
        OMODE = '                       '
        TEXT =  REV_STRT
     &  //'     Version '//REV_CAR//'             **     HARMONY    ** '
     &  //'      '//OMODE//
     &            REV_END
        CALL Term_Write(1,1,TEXT,90)
      ENDIF
C
      RETURN
      END
C
C     =================================================================
      SUBROUTINE DISPLAY(TMP_F_POINT,TMP_REFP,TMP_REFL,TBSIZ,DHMSIZ,
     &                   DPHASE,TIT)
C     =================================================================
C
      IMPLICIT NONE
C
C -- This subroutine displays tables which contain harmonics.  It
C    displays their type, their harmonic number and amplitude.  The
C    output is formatted according to the number of harmonics present.
C
      INCLUDE 'harparm.inc'
C
      INTEGER*2
     &          F_POINT,             !First data point to refresh
     &          PR_POINT,            !Previous position keeper
     &          REF_POS,             !Refresh starting position on screen
     &          REF_L,               !Length of refresh buffer
     &          SCR_POSV,            !Vertical position pointer
     &          SCR_POSH,            !Horizontal position pointer
     &          PR_POS,              !Previous position
     &          DHMSIZ,              !Number of harmonics
     &          TBSIZ                !Table size
C
      INTEGER*4
     &          TMP_F_POINT,     !Temporary first data point to refresh
     &          TMP_REFP,        !Temporary Refresh starting position on screen
     &          TMP_REFL,        !Temporary Length of refresh buffer
     &          DPHASE           !Phase
C
      CHARACTER TIT*(*)             ! Table title
C
      CHARACTER*7
     &          BUF*7               ! Character buffer
C
      CHARACTER*15

     &          HEAD1 /' [1mTYPE I [0m '/,! Header type
     &          HEAD2 /' [1mHM # I [0m '/,! Header number
     &          HEAD3 /' [1mAMPL I [0m '/ ! Header amplitude
C
      LOGICAL*1 HEAD_F1,            ! Request print header #1 flag
     &          SET_PR,             ! Set previous point flag
     &          HEAD_F2,            ! Request print header #2 flag
     &          FIRST/.TRUE./       ! First time routine processed flag
C
      CHARACTER*80
     & TEXT*100,               !Display text line
     & ERROR1                  !Various error messages
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      DATA ERROR1/'%DISPLAY - Internal error #      in routine D_WAVE '/
C
      IF(FIRST)THEN
          FIRST=.FALSE.
          HEAD1(1:1) = ESC
          HEAD1(11:11) = ESC
          HEAD2(1:1) = ESC
          HEAD2(11:11) = ESC
          HEAD3(1:1) = ESC
          HEAD3(11:11) = ESC
      ENDIF
C
      IF(TMP_F_POINT.EQ.-99) THEN  !Clean parameters requested
         PR_POINT=0
         RETURN
      ENDIF
C
C     Don't print anything when table empty
C     -------------------------------------
      IF(DHMSIZ.EQ.0) THEN
         PR_POINT=0
         CALL CL_DISP
         CALL DISP_HEAD(TBSIZ,TIT,DPHASE)
         CALL EDIT_DISP(1)
         RETURN
      ENDIF
C
C     Check if complete refresh
C     -------------------------
      IF(TMP_REFP.LE.-1) THEN
C
C        Clear the display and display the table header
C        ----------------------------------------------
         IF(TMP_REFP.EQ.-1) THEN
            CALL CL_DISP
            CALL DISP_HEAD(TBSIZ,TIT,DPHASE)
         ELSE
            CALL CL_DISP4
         ENDIF
C
         SET_PR =.TRUE.
         REF_POS = 1
         SCR_POSH = 1
         SCR_POSV = 2
         HEAD_F2 = .TRUE.
         HEAD_F1 = .TRUE.
C
      ELSEIF(TMP_REFP.EQ.1) THEN
         REF_POS=1
         HEAD_F2 = .TRUE.
         HEAD_F1=.TRUE.
      ELSE
         REF_POS=TMP_REFP
      ENDIF
C
C     Set previous position if requested
C     ----------------------------------
      IF(TMP_F_POINT.EQ.-1) THEN
         F_POINT=PR_POS
      ELSE
         F_POINT=TMP_F_POINT
      ENDIF
C
      IF(SET_PR)THEN
        SET_PR=.FALSE.
        PR_POINT = F_POINT+1
      ENDIF
C
C     Set default length if requested
C     -------------------------------
      IF(TMP_REFL.EQ.-1) THEN
         REF_L=MIN((DHMSIZ-F_POINT+1),20)
      ELSE
         REF_L = TMP_REFL
      ENDIF
C
C     Translate wave type in ASCII
C     ----------------------------
      CALL TRANS_TYPE(-1)
C
C     Set the starting values
C     -----------------------
      IF(REF_POS.LE.10) THEN
         SCR_POSH = (REF_POS-1)*7 + 1
         SCR_POSV = 3
      ELSE
         SCR_POSH = (REF_POS-11)*7+1
         SCR_POSV = 7
      ENDIF
C
C
C     Refresh one block at a time
C     ---------------------------
      DO II=REF_POS,REF_POS+REF_L-1
C
         JJ = II-1
C
         IF(II.EQ.1) THEN
            IF(HEAD_F1) THEN
               CALL Term_Write(11,1,HEAD1,15)
               CALL Term_Write(12,1,HEAD2,15)
               CALL Term_Write(13,1,HEAD3,15)
               HEAD_F1=.FALSE.
            ENDIF
            IF(DHMSIZ.LT.1) THEN
              HEAD_F1=.TRUE.
              CALL Term_Write(11,1,BLANK(1:7),7)
              CALL Term_Write(12,1,BLANK(1:7),7)
              CALL Term_Write(13,1,BLANK(1:7),7)
            ENDIF
         ENDIF
C
         IF(II.EQ.11) THEN
           IF(HEAD_F2) THEN
              HEAD_F2=.FALSE.
              CALL Term_Write(15,1,HEAD1,15)
              CALL Term_Write(16,1,HEAD2,15)
              CALL Term_Write(17,1,HEAD3,15)
           ENDIF
           IF(DHMSIZ.LT.(F_POINT+10)) THEN
              HEAD_F2=.TRUE.
              CALL Term_Write(15,1,BLANK(1:7),7)
              CALL Term_Write(16,1,BLANK(1:7),7)
              CALL Term_Write(17,1,BLANK(1:7),7)
           ENDIF
         ENDIF
C
         IF((F_POINT+JJ).LE.DHMSIZ) THEN
          BUF(1:7) = '  '//WTYPE(F_POINT+JJ)//' |'
          CALL Term_Write(SCR_POSV+8,SCR_POSH+7,BUF,7)
          BUF(1:7) = '      |'
          WRITE(BUF(1:6),'(F6.2)',ERR=701,IOSTAT=IERR )
     &                 TEMP_HARM(F_POINT+JJ)
          CALL Term_Write(SCR_POSV+9,SCR_POSH+7,BUF,7)
          BUF(1:7) = '      |'
          WRITE(BUF(1:6),'(F6.2)',ERR=701,IOSTAT=IERR )
     &                 TEMP_AMPL(F_POINT+JJ)
          CALL Term_Write(SCR_POSV+10,SCR_POSH+7,BUF,7)
         ELSE
          BUF(1:7) = '       '
          CALL Term_Write(SCR_POSV+8,SCR_POSH+7,BUF,7)
          CALL Term_Write(SCR_POSV+9,SCR_POSH+7,BUF,7)
          CALL Term_Write(SCR_POSV+10,SCR_POSH+7,BUF,7)
         ENDIF
C
C        Point to next block: check which level on the screen
C        ----------------------------------------------------
         SCR_POSH = SCR_POSH+7
         IF(SCR_POSH.GE.71.AND.SCR_POSV.LT.5) THEN
           SCR_POSH=1
           SCR_POSV=7
         ENDIF
C
      ENDDO
C
C     Update pointer to indicate position keeper
C     ------------------------------------------
      TEXT=ESC//'[1mTable Last Modified on :'//ESC//'[0m '//TEMP_MODATE
     &   //'   '//ESC//'[1mCreated by :'//ESC//'[0m '//TEMP_NAME
      CALL Term_Write(19,1,TEXT,100)
      TEXT=ESC//'[1mNUMBER OF HARMONICS in the TABLE:'//ESC//'[0m '
      WRITE(TEXT(43:44),'(I2)',ERR=296) DHMSIZ
296   CONTINUE
      CALL Term_Write(20,1,TEXT,44)
C
      IF(F_POINT.NE.PR_POINT) THEN
        TEXT=ESC//'[1mDisplay Area Pointer:'//ESC//'[0m '
        WRITE(TEXT(31:32),'(I2)',ERR=297) F_POINT
 297    CONTINUE
        IF(F_POINT.EQ.1) THEN
           TEXT(33:34) = 'st'
        ELSEIF(F_POINT.EQ.2) THEN
           TEXT(33:34) = 'nd'
        ELSEIF(F_POINT.EQ.3) THEN
           TEXT(33:34) = 'rd'
        ELSE
           TEXT(33:34) = 'th'
        ENDIF
        TEXT(35:43) =' Harmonic'
        CALL Term_Write(20,46,TEXT,43)
      ENDIF
C
      PR_POS = F_POINT
      PR_POINT = F_POINT
C
      GOTO 700
 701  CALL GET_ERR_STR(IERR,ERROR1(28:32))
      CALL ERR_MESS(ERROR1,51,-1,*700)
 700  CONTINUE
      RETURN

      END
C
C
C     =====================================
      SUBROUTINE DISP_HEAD(TBSIZ,TIT,NPHASE)
C     =====================================
      IMPLICIT NONE
C
C -- This subroutine displays the table header : the phase, size
C    and title.
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*2 TBSIZ,               ! Table size
     &          TIT_LEN              ! Title length
C
      INTEGER*4
     &          NPHASE,              ! Phase
     &          I4
C
      CHARACTER TIT*(*),             ! Table title
     &          PHASTR*25            ! String containing the phase
C
C -- Determine the length of the title
C
      I4 = 30
      CALL String_Length(TIT,I4)
C
C -- Set length to 72 if no title is entered : 72 is last column of
C    display.  Translate the phase : take the integer value and
C    convert it to its character equivalent.
C
C
      IF (I4 .EQ. 0) THEN
         I4 = 30
      ENDIF
C
      CALL TRANS_PHASE(NPHASE,PHASTR)
C
      SEND = PHASTR//BLANK
C
C -- Write the size to the display string
C
      SEND(28:33) = 'SIZE: '
      WRITE(SEND(35:38),'(I4)',ERR=701,IOSTAT=IERR ) TBSIZ
 701  CONTINUE
C
C -- Write the title to the display string
C
      SEND(39:50) = '     TITLE: '
      SEND(51:51+I4-1) = TIT
      CALL Term_Write(9,1,SEND,80)
      RETURN
      END
C
C
C     ==============================
      SUBROUTINE DISP_SRC(WHICH,DSG)
C     ==============================
      IMPLICIT NONE
C
C     This subroutine displays the source/table associations. Twenty
C     sources are displayed per page and up to a maximum of MAXSOUR
C     are allowed.
C     --------------------------------------------------------------
      INCLUDE 'harparm.inc'

      CHARACTER
     &          T_NUMB*2,
     &          S_NUMB*2,
     &          ANS*1                    ! User input
      INTEGER*2
     &          MMIN_DSG,                ! Minimum DSG number
     &          MMAX_DSG,                ! Maximum DSG number
     &          DSG_I,                   ! DSG number loop counter
     &          MIN_DISP,                ! Minimum source display counter
     &          MAX_DISP,                ! Maximum source display counter
     &          OFFSET,                  ! Wait index
     &          COUNT,                   ! Counter
     &          PROW
C
      INTEGER*4
     &          COL,
     &          ROW,
     &          WHICH,                   ! Which source to display
     &          DSG                      ! DSG slot number index
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C     Continue displaying the source/table associations
C     until the user does not want to view any more sources.
C     ------------------------------------------------------
      IF(WHICH.EQ.-1) THEN
         CALL CL_DISP
         SEND=ESC//'[4m'
         CALL Term_Write(7,1,SEND,4)
         CALL Term_Write(7,1,'| SRC  TAB |',12)
         CALL Term_Write(7,13,' SRC  TAB |',11)
         CALL Term_Write(7,24,' SRC  TAB |',11)
         CALL Term_Write(7,35,' SRC  TAB |',11)
         CALL Term_Write(7,46,' SRC  TAB |',11)
         CALL Term_Write(7,57,' SRC  TAB |',11)
         CALL Term_Write(7,68,' SRC  TAB |',11)
         SEND=ESC//'[0m'
         CALL Term_Write(7,1,SEND,4)
         MIN_DISP = 1
         MAX_DISP = MAXI_SOUR
      ELSE
         MIN_DISP = WHICH
         MAX_DISP = WHICH
      ENDIF
C
      IF (DSG.EQ.-1) THEN  
         MMIN_DSG = 1
         MMAX_DSG = DSG_NUMB
      ELSE
         MMIN_DSG = DSG
         MMAX_DSG = DSG
      ENDIF 
C
      DO DSG_I = MMIN_DSG,MMAX_DSG
C
       IF (DSG_I.NE.MMIN_DSG) THEN
         CALL WAIT_CONT(2,OFFSET)
         IF(OFFSET.EQ.0)THEN
            GOTO 111
         ENDIF
       ENDIF
       CALL Term_Write(20,1,'For DSG in slot XA',18)
       CALL Term_Write(20,19,SL_NB(DSG_I),2)
C
       DO COUNT=MIN_DISP,MAX_DISP
          WRITE(S_NUMB,'(I2.2)',ERR=998) COUNT
 998      CONTINUE
          IF(SOURCE(COUNT,DSG_I).GT.0) THEN
            WRITE(T_NUMB,'(I2.2)',ERR=999) SOURCE(COUNT,DSG_I)
 999        CONTINUE
          ELSE
            T_NUMB = ' x'
          ENDIF
          SEND =  '  '//S_NUMB//'--'//T_NUMB//'  |'
          IF(COUNT.LE.44) THEN
             ROW = COUNT-(((COUNT-1)/11)*11)+7
             COL = ((COUNT-1)/11)*11 +2
             IF(COUNT.LE.11) THEN
                CALL Term_Write(ROW,1,'|',1)
             ENDIF
          ELSE
             PROW = COUNT-44
             ROW = PROW-(((PROW-1)/12)*12)+7
             COL = ((PROW-1)/12)*11 +46
             IF(COUNT.EQ.56) THEN
                CALL Term_Write(ROW,COL-1,'|',1)
             ENDIF
          ENDIF
          CALL Term_Write(ROW,COL,SEND,11)
       ENDDO
      ENDDO
111   CONTINUE
      RETURN
C
      END
C
C     ======================================
      SUBROUTINE DISP_EXT(EXTSIZE,TSIZE,TIT)
C     ======================================
      IMPLICIT NONE

C -- This subroutine displays tables which contain OTHER values :
C    it displays up to 20 X and Y coordinates, along with their
C    associated point numbers.
C
      INCLUDE 'harparm.inc'
C
      INTEGER*2
     &        TSIZE,             ! Table size
     &        EXTSIZE            ! External Table size
      CHARACTER NCPOINT*4       ,! Number of points in character
     &        TEXT*100,          !Display text line
     &          TIT*(*)          ! Table title
C
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
C     Clear the display and display the table header
C     ----------------------------------------------
      CALL CL_DISP
      PHASE4 = PHASE
      CALL DISP_HEAD(TSIZE,TIT,PHASE4)
C
      IF(EXTSIZE.GT.0) THEN
         SEND = '***  EXTERNAL table exist: ALL points filled out with O
     &UTSIDE data  ***'
         CALL Term_Write(14,4,SEND,71)
      ENDIF
      TEXT=ESC//'[1mTable Last Modified on :'//ESC//'[0m '//TEMP_MODATE
     &   //'   '//ESC//'[1mCreated by :'//ESC//'[0m '//TEMP_NAME
      CALL Term_Write(19,1,TEXT,100)
      RETURN
      END
C
C
C     =========================================================
      SUBROUTINE DISP_OTH(TMP_F_POINT,TMP_REFP,TMP_REFL,OTSZ,
     &                                             TBSIZ,TIT)
C     =========================================================
C
      IMPLICIT NONE
C
C -- This subroutine displays tables which contain breakpoints.  It
C    displays their numbers, their X and Y values.  The
C    output is formatted according to the number of breakpoint present.
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2
     &          F_POINT,             !First data point to refresh
     &          PR_POINT,            !Previous position keeper
     &          REF_POS,             !Refresh starting position on screen
     &          REF_L,               !Length of refresh buffer
     &          SCR_POSV,            !Vertical position pointer
     &          SCR_POSH,            !Horizontal position pointer
     &          PR_POS,              !Previous position
     &          OTSZ,                !Number of harmonics
     &          TBSIZ                !Table size
C
      INTEGER*4
     &          TMP_F_POINT,     !Temporary first data point to refresh
     &          TMP_REFP,        !Temporary Refresh starting position on screen
     &          TMP_REFL         !Temporary Length of refresh buffer
C
      CHARACTER TIT*(*)             ! Table title
C
      CHARACTER
     &          ERROR1*80,
     &          TEXT*100,               !Display text line
     &          BUF*7                   ! Print buffer
C
      CHARACTER*16
     &          HEAD1 /' [1mBPNT  I [0m '/,   ! Header type
     &          HEAD2 /' [1mX VAL I [0m '/,   ! Header number
     &          HEAD3 /' [1mY VAL I [0m '/    ! Header amplitude
C
      LOGICAL*1 HEAD_F1,            ! Request print header #1 flag
     &          SET_PR,             ! Set previous point flag
     &          HEAD_F2,            ! Request print header #2 flag
     &          FIRST/.TRUE./       ! First time routine processed flag
C
      INTEGER*2
     &          TMP                 ! Temporary value
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1/'%DISP_BRK- Internal error #      in routine D_BRKPT'/

C
      IF(FIRST)THEN
          FIRST=.FALSE.
          HEAD1(1:1) = ESC
          HEAD1(12:12) = ESC
          HEAD2(1:1) = ESC
          HEAD2(12:12) = ESC
          HEAD3(1:1) = ESC
          HEAD3(12:12) = ESC
      ENDIF
C
      IF(TMP_F_POINT.EQ.-99) THEN  !Clean parameters requested
         PR_POINT=0
         RETURN
      ENDIF
C
C     Don't print anything when table empty
C     -------------------------------------
      IF(OTSZ.EQ.0) THEN
          PR_POINT=0
          CALL CL_DISP
          PHASE4 = PHASE
          CALL DISP_HEAD(TBSIZ,TIT,PHASE4)
          CALL EDIT_DISP(2)
         RETURN
      ENDIF
C
C     Check if complete refresh
C     -------------------------
      IF(TMP_REFP.LE.-1) THEN
C
C        Clear the display and display the table header
C        ----------------------------------------------
         IF(TMP_REFP.EQ.-1) THEN
            CALL CL_DISP
            PHASE4 = PHASE
            CALL DISP_HEAD(TBSIZ,TIT,PHASE4)
         ELSE
            CALL CL_DISP4
         ENDIF
C
         SET_PR = .TRUE.
         REF_POS = 1
         SCR_POSH = 1
         SCR_POSV = 2
         HEAD_F2 = .TRUE.
         HEAD_F1 = .TRUE.
C
      ELSEIF(TMP_REFP.EQ.1) THEN
         REF_POS=1
         HEAD_F2 = .TRUE.
         HEAD_F1=.TRUE.
      ELSE
         REF_POS=TMP_REFP
      ENDIF
C
C     Set previous position if requested
C     ----------------------------------
      IF(TMP_F_POINT.LE.-1) THEN
         F_POINT=PR_POS
      ELSE
         F_POINT=TMP_F_POINT
      ENDIF
C
      IF(SET_PR)THEN
        SET_PR=.FALSE.
        PR_POINT = F_POINT+1
      ENDIF
C
C     Set default length if requested
C     -------------------------------
      IF(TMP_REFL.EQ.-1) THEN
         REF_L=MIN((OTSZ-F_POINT+1),20)
      ELSE
         REF_L = TMP_REFL
      ENDIF
C
C     Set the starting values
C     -----------------------
      IF(REF_POS.LE.10) THEN
         SCR_POSH = (REF_POS-1)*7 + 1
         SCR_POSV = 3
      ELSE
         SCR_POSH = (REF_POS-11)*7+1
         SCR_POSV = 7
      ENDIF
C
C     Refresh one block at a time
C     ---------------------------
      DO II=REF_POS,REF_POS+REF_L-1
C
         JJ = II-1
C
         IF(II.EQ.1) THEN
            IF(HEAD_F1) THEN
               CALL Term_Write(11,1,HEAD1,16)
               CALL Term_Write(12,1,HEAD2,16)
               CALL Term_Write(13,1,HEAD3,16)
               HEAD_F1=.FALSE.
            ENDIF
            IF(OTSZ.LT.1) THEN
              HEAD_F1=.TRUE.
              CALL Term_Write(11,1,BLANK(1:7),7)
              CALL Term_Write(12,1,BLANK(1:7),7)
              CALL Term_Write(13,1,BLANK(1:7),7)
            ENDIF
         ENDIF
C
         IF(II.EQ.11) THEN
           IF(HEAD_F2) THEN
              HEAD_F2=.FALSE.
              CALL Term_Write(15,1,HEAD1,16)
              CALL Term_Write(16,1,HEAD2,16)
              CALL Term_Write(17,1,HEAD3,16)
           ENDIF
           IF(OTSZ.LT.(F_POINT+10)) THEN
              HEAD_F2=.TRUE.
              CALL Term_Write(15,1,BLANK(1:7),7)
              CALL Term_Write(16,1,BLANK(1:7),7)
              CALL Term_Write(17,1,BLANK(1:7),7)
           ENDIF
         ENDIF
C
         IF((F_POINT+JJ).LE.OTSZ) THEN
          BUF(1:7) = '      |'
          WRITE(BUF(3:4),'(I2)',ERR=701,IOSTAT=IERR )
     &                            TMP_PVAL(F_POINT+JJ)
          CALL Term_Write(SCR_POSV+8,SCR_POSH+8,BUF,7)
          BUF(1:7) = '      |'
          WRITE(BUF(1:5),'(F5.1)',ERR=701,IOSTAT=IERR )
     &                            TMP_XVAL(F_POINT+JJ)
          CALL Term_Write(SCR_POSV+9,SCR_POSH+8,BUF,7)
          BUF(1:7) = '      |'
          WRITE(BUF(1:5),'(F5.1)',ERR=701,IOSTAT=IERR )
     &                           TMP_YVAL(F_POINT+JJ)
          CALL Term_Write(SCR_POSV+10,SCR_POSH+8,BUF,7)
         ELSE
          BUF(1:7) = '       '
          CALL Term_Write(SCR_POSV+8,SCR_POSH+8,BUF,7)
          CALL Term_Write(SCR_POSV+9,SCR_POSH+8,BUF,7)
          CALL Term_Write(SCR_POSV+10,SCR_POSH+8,BUF,7)
         ENDIF
C
C        Point to next block: check which level on the screen
C        ----------------------------------------------------
         SCR_POSH = SCR_POSH+7
         IF(SCR_POSH.GE.71.AND.SCR_POSV.LT.5) THEN
           SCR_POSH=1
           SCR_POSV=7
         ENDIF
C
      ENDDO
C
C     Update pointer to indicate position keeper
C     ------------------------------------------
      TEXT=ESC//'[1mTable Last Modified on :'//ESC//'[0m '//TEMP_MODATE
     &   //'   '//ESC//'[1mCreated by :'//ESC//'[0m '//TEMP_NAME
      CALL Term_Write(19,1,TEXT,100)
      TEXT=ESC//'[1mNUMBER OF BREAKPOINTS in the TABLE:'//ESC//
     &         '[0m '
      WRITE(TEXT(45:46),'(I2)',ERR=296) OTSZ
296   CONTINUE
      CALL Term_Write(20,1,TEXT,46)
C
      IF(F_POINT.NE.PR_POINT) THEN
        TEXT=ESC//'[1mDisplay Area Pointer:'//ESC//'[0m '
        WRITE(TEXT(31:32),'(I2)',ERR=297) F_POINT
 297    CONTINUE
        IF(F_POINT.EQ.1) THEN
           TEXT(33:34) = 'st'
        ELSEIF(F_POINT.EQ.2) THEN
           TEXT(33:34) = 'nd'
        ELSEIF(F_POINT.EQ.3) THEN
           TEXT(33:34) = 'rd'
        ELSE
           TEXT(33:34) = 'th'
        ENDIF
        TEXT(35:45) =' Breakpoint'
        CALL Term_Write(20,44,TEXT,45)
      ENDIF
C
      PR_POS = F_POINT
      PR_POINT=F_POINT
C
      GOTO 700
 701  CALL GET_ERR_STR(IERR,ERROR1(28:32))
      CALL ERR_MESS(ERROR1,51,-1,*700)
 700  CONTINUE
C
      RETURN

      END
C     ==================
      SUBROUTINE CL_DISP
C     ==================
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C -- This subroutine clears the display between the two reverse video lines
C    on the menu.
C
C -- Move the curser to starting position, clear and place curser back
C    in desired location.
C
      CALL Term_Write(6,1,CLS_STR,4)
      CALL Term_Write(9,1,SEND,0)
      CALL MES23(1,BLANK)
C
      RETURN
      END
C
C     ===================
      SUBROUTINE CL_DISP4
C     ===================
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C -- This subroutine clears the display between the two reverse video lines
C    on the menu.
C
C -- Move the curser to starting position, clear and place curser back
C    in desired location.
C
      CALL Term_Write(10,1,CLS_STR,4)
      CALL MES23(1,BLANK)
C
      RETURN
      END
C
C     ===================
      SUBROUTINE CL_DISP2
C     ===================
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C -- This subroutine clears the display between the two reverse video lines
C    on the menu.

C -- Move the curser to starting position, clear and place curser back
C    in desired location.
C
      CALL Term_Write(8,1,CLS_STR,4)
      CALL Term_Write(9,1,SEND,0)
C
      RETURN
      END
C
C     ===================
      SUBROUTINE CL_DISP3
C     ===================
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C -- This subroutine clears the display between the two reverse video lines
C    on the menu.
C
C -- Move the curser to starting position, clear and place curser back
C    in desired location.
C
C
      CALL Term_Write(9,1,CLS_STR,4)
      CALL Term_Write(9,1,SEND,0)
C
      RETURN
      END
C
C     ===========================================
      SUBROUTINE TAB_HEAD_DISP(TABLE_NUMBER,TYPE)
C     ===========================================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER
     &          EDISPN(3)*10                 ,!Editing type text
     &          NOMBRE*2                      !Table number character
C
      INTEGER*2
     &   TABLE_NUMBER,                        ! Table number
     &   ED_LEN,                              ! Length of display type
     &   LEN_PAS                              ! Passing length of string
C
      INTEGER*4
     &   TYPE                                 ! Table type
C
      INCLUDE 'hardata.inc'
C
      DATA  EDISPN / 'WAVE      ','BREAKPOINT','EXTERNAL  '/
C
      IF(TYPE.NE.0) THEN
         SEND(46:55) = EDISPN(TYPE)
      ENDIF
C
      WRITE(NOMBRE,'(I2)',ERR=701) TABLE_NUMBER
 701  CONTINUE
      SEND(20:23) = BRT_STRT
      SEND(24:30) = 'TABLE #'
      SEND(31:32) = NOMBRE
      SEND(33:40) = ' TYPE : '
      SEND(41:45) = BRT_END
C
      CALL Term_Write(7,25,SEND(20:55),36)
C
      RETURN
      END
C
C     ==========================
      SUBROUTINE EDIT_DISP(TYPE)
C     ==========================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER
     &          EDISPN(3)*10                 ,!Editing type text
     &          NOMBRE*2                      !Table number character
C
      INTEGER*4
     &   TYPE                                !
C
      INCLUDE 'hardata.inc'
C
      DATA  EDISPN / 'WAVE      ','BREAKPOINT','EXTERNAL  '/
C
      IF(TYPE.NE.0) THEN
         SEND(58:67) = EDISPN(TYPE)
      ELSE
         SEND(58:67) = '          '
      ENDIF
C
      WRITE(NOMBRE,'(I2)',ERR=701) TAB_NUM
 701  CONTINUE
      SEND(20:23) = BRT_STRT
      SEND(24:39) = 'EDITING TABLE # '
      SEND(40:41) = NOMBRE
      SEND(42:52) = '    TYPE : '
      SEND(53:57) = BRT_END
C
      CALL Term_Write(7,1,SEND(20:67),48)
C
C     Print Sinewave mode
C     -------------------
      IF(TYPE.EQ.1.AND.SIN_MOD)THEN
          SEND(20:23) = REV_STRT
          SEND(24:33) = ' SINEWAVE '
          SEND(34:38) = REV_END
          CALL Term_Write(7,44,SEND(20:38),19)
      ELSE
          CALL Term_Write(7,44,BLANK,10)
      ENDIF
C
C     Print load mode
C     ---------------
      SEND(1:4) = REV_STRT
      IF(TAB_NUM.GT.MAX_TAB_LOAD)THEN
         SEND(5:22) = '  REAL TIME LOAD  '
      ELSE
         SEND(5:22) = '  DOWNLOAD  FILE  '
      ENDIF
      SEND(23:27) = REV_END
      CALL Term_Write(7,61,SEND,27)
C
      RETURN
      END
