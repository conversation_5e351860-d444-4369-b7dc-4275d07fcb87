C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY  For UNIX Systems                                **
C   **                                                                      **
C   **  Program  : HARMONY.F                                                **
C   **  Function : Main calling program                                     **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by M<PERSON>/<PERSON>. <PERSON>   Date: 13 December 1987  **
C   **  Rev 1.0             G. <PERSON>                   30 July 1988      **
C   **  Rev 2.0             G. <PERSON>                   20 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct  1989      **
C   **  Rev 2.2             G<PERSON> <PERSON>                   18 Feb  1989      **
C   **  Rev 2.3             P. Daigle                     27 Jul  1990      **
C   **  Rev 2.4             P. Daigle                     07 June 1991      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  HARMONY                                                             **
C   **  INIT_HARMONY                                                        **
C   **  INITIALIZE                                                          **
C   **  SURE_QUIT(*)                                                        **
C   **  INIT_RTL                                                            **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C       ===============
        PROGRAM HARMONY
C       ===============
C
        IMPLICIT NONE
C
C -- This is the main program for the harmony utility.  It calls on various
C    routines to perform initialization, set up parameter definitions,
C    open data & help files, and set up the main menu.  It then monitors
C    user input and branches to the subprogram which handles the requested
C    function.
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'aoscommon.inc'
C
      CHARACTER
     & NOTAB*48,             !No table exist
     & M_FPASS*73            !Abort in the first pass error message
C
      INTEGER*4
     & Stat,
     & INIT_ERR              !Initialization error
C
      INTEGER*2 IO_STATUS
      LOGICAL*1 CL_FIRST/.FALSE./,
     &          FIRST   /.TRUE./,
     &          SKIP1,
     &          END_READ
C
      CHARACTER*80 PROMPT
      CHARACTER*15 MENU(13)
C
      DATA NOTAB/'%HARMONY : No table exist, command not processed'/
      DATA M_FPASS /'%FIRST_PASS : Did not when thru first pass, ABORTIN
     &G, something wrong... '/
C
      DATA PROMPT /'HARMONY > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6) /'EDIT'/
      DATA MENU(7) /'DELETE'/
      DATA MENU(8) /'ASSIGN'/
      DATA MENU(9) /'LIST'/
      DATA MENU(10) /'PLOT'/
      DATA MENU(11) /'LOAD'/
      DATA MENU(12) /'CONF'/
      DATA MENU(13) /'SAVE'/
C
C     Map CDB labels to internal memory
C     ---------------------------------
      CALL cdbmap()
C
C     INITIALIZE the utility variables
C     --------------------------------
      SKIP1 = .FALSE.
      CALL Init_libgd(1,3)         ! SGI computer = 3
      CALL INIT_HARMONY(INIT_ERR)
C
C     Set up the main menu.
C     ---------------------
      CALL MAIN_MENU(0,FIRST)                  !Display main menu
      CALL MODE_SET(0)                         !Display output mode
C
      CALL INIT_RTL(RTD_FLAG)
C
 2224 CONTINUE
      IF(INIT_ERR.EQ.0)THEN
      IF(.NOT.SKIP1) THEN
C
C      Read the HARMONY log file for the number of DSG-slot
C      ----------------------------------------------------
       CALL READLOG(0,IERR)        !Read LOG file
       IF(IERR.NE.0) THEN
          END_READ=.TRUE.
       ELSE
          CALL READLOG(3,IERR)        !Display them on screen
          IF(IERR.NE.0) THEN
             END_READ=.TRUE.
          ELSE
             CALL REST_DATA               !Restore data
             END_READ=.FALSE.
          ENDIF
       ENDIF
      ENDIF     !For SKIP1...
      SKIP1 = .FALSE.
C
C      Prompt the user for input, and branch to that routine which corresponds
C      to his selection.
C      -----------------------------------------------------------------------
       DO WHILE(.NOT.END_READ)
         IF (CL_FIRST) THEN
            CALL CL_DISP             !Clear the screen before selection
            CALL MAIN_MENU(0,FIRST)  !Display main menu
            CL_FIRST=.FALSE.
         ENDIF
         CALL READ_COMMAND(-1,22,1,PROMPT,10,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,13,MENU,ITEM,IERR)
         IF (FIRST) THEN
            CALL CL_DISP             !Clear the screen after select to allow
            FIRST=.FALSE.            !print of restored parameters
         ENDIF
C
         IF(IERR.EQ.0) THEN
C
            CL_FIRST=.TRUE.
            IF(ITEM.GE.6) THEN
C
             IF((ITEM.GE.7.AND.ITEM.LE.11).AND.(TBLNUM.EQ.0))THEN
C
C              If no tables exist, do not process command that needs tables
C              ------------------------------------------------------------
               CALL ERR_MESS(NOTAB,48,-1,*88)
 88            CONTINUE
             ELSE
               CALL MAIN_MENU((ITEM-5),.FALSE.)
C
               GOTO (100,  !  CREATE/EDIT TABLE
     &          200,       !  DELETE TABLE(S)
     &          300,       !  ASSIGN SOURCES
     &          400,       !  LIST DATA POINTS/TABLE
     &          500,       !  PLOT
     &          600,       !  LOAD
     &          700,       !  CONF
     &          800)       !  SAVE
     &          (ITEM-5)
C
 100           CALL TABLE(*2222)      !EDIT command
C
 200           CALL DELETE(*2222)     !DELETE command
C
 300           CALL ASSIGN(*2222)     !ASSIGN command
C
 400           CALL LIST(*2222)       !LIST command
C
 500           CALL PLOT(*2222)       !PLOT command
C
 600           CALL HLOAD(*2222)      !LOAD command
C
 700           CALL CONF(*2222,IERR)  !CONF command
               IF(IERR.NE.0) THEN
                 END_READ=.TRUE.          !just leave and cry.
               ENDIF
 800           IF(DATASAVE) THEN      !SAVE + CONF command
                 CALL SAVE_DATA(*2223,IERR)
                 IF(IERR.NE.0) THEN
                     END_READ=.TRUE.      !Don't save anything...
                 ENDIF
               ENDIF
 2222          CONTINUE
             ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(0)                !Help asked
            ELSE                      !QUIT,EXIT or X command
               IF(ITEM.EQ.5) THEN
                  CALL SURE_QUIT(*2223)  !Confirm quit if data has changed
               ELSE
                  CALL SAVE_DATA(*2223,IERR)
               ENDIF
               END_READ=.TRUE.
            ENDIF
         ENDIF
       ENDDO
C
C      Close HELP text file if open before
C      -----------------------------------
       IF (HLPOPEN) THEN
            CALL FIL_OPEN(7,2,IERR)
       ENDIF
      ELSE
       CALL ERR_MESS(M_FPASS,73,-1,*87)
 87    CONTINUE
      ENDIF
C
      CALL EXIT
C
 2223 SKIP1 = .TRUE.
      GOTO 2224
C
      END
C
C     ==============================
      SUBROUTINE INIT_HARMONY(ERROR)
C     ==============================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*4
     &  ERROR
C
      INTEGER*2
     &  File_Len(5)
C
      CHARACTER 
     &  M_RINF*68,
     &  M_EXTM*80,
     &  File_N(5)*40
C
      DATA M_RINF/'%READ_FILE_SNDUTY : Error #       when reading SNDUTY
     & transfert file'/
      DATA M_EXTM/'%GET_MEMORY_GOULD  : Error #       with X:GDSPCE when
     & requesting extended memory'/
C
C     Look for memory space allocation
C     --------------------------------
      CALL EXTEND_MEM(ERROR)
C
      IF(ERROR.EQ.0) THEN
C
C       Initialise all the tables parameters/variables
C       ----------------------------------------------
        CALL INITIALIZE
C
C       Read all directories, flags and names from SNDUTY transfert file
C       ----------------------------------------------------------------
        CALL XLINK_READ(Config_String,Config_length,DMC,Page_Num,
     &                  Filetters,Comp_Id,LinkFlag,Com,File_N,File_Len,
     &                  ERROR)
        UPDATE = Com(1)
        DOWNLOAD=Com(2)
        WGSIZE=Com(3)
C
        EXTDATDIR = Config_String(1)(1:Config_Length(1))
        L_EXTDIR = Config_Length(1)
C
        IF (Filetters(1:2).EQ.'sn') THEN
         DATA_DIR = Config_String(1)(1:Config_Length(1))//'sound/data/'
         INT_DIR  = Config_String(1)(1:Config_Length(1))//'sound/inter/'
        ELSE
         DATA_DIR = Config_String(1)(1:Config_Length(1))//'audio/data/'
         INT_DIR  = Config_String(1)(1:Config_Length(1))//'audio/inter/'
        ENDIF
        L_DATA_DIR = Config_Length(1) + 11
        L_INT_DIR = Config_Length(1) + 12
C
        IF (ERROR.EQ.0)THEN
C
C         Set the On site flag for HARMONY
C         --------------------------------
          IF(LinkFlag(1)) THEN
             ON_SITE=.TRUE.
          ELSE
             ON_SITE=.FALSE.
          ENDIF
        ELSE
          CALL GET_ERR_STR(ERROR,M_RINF(29:33))
          CALL ERR_MESS(M_RINF,68,-1,*86)
 86       CONTINUE
          ERROR = 1000  !Fatal error
        ENDIF
      ELSE
        CALL GET_ERR_STR(ERROR,M_EXTM(30:34))
        CALL ERR_MESS(M_EXTM,80,-1,*85)
 85     CONTINUE
        ERROR = 1000    !Fatal error
      ENDIF
C
      RETURN
      END
C
C
C     =====================
      SUBROUTINE INITIALIZE
C     =====================
      IMPLICIT NONE
C
C -- This subroutine initializes the amplitudes, types, phases, titles
C    and sizes required by HARMONY.  It also sets certain commonly
C    used escape sequences.
C
      INCLUDE 'harparm.inc'
C
      CHARACTER*2 IBLANK(40)/40*'  '/           ! Integer equivalent of blank string
C
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
C -- Initialize the phase to zero phase (phase=-1), the amplitudes to 0,
C    the sizes to 512 and blank out the titles.
C
      BLANK(1:52)='                                                    '
      BLANK(53:80)='                                      '
C
      DO I = 1,MAX_TAB
        EXTBLN(I) = 0
        PHASEL(I) = 99
        SAV_TBL(I) = 0
        DO J = 1,MAX_HNUM
          AMPL(I,J) = 0.0
          HARM(I,J) = 0
          WAVETYPE(I,J) = 0
        ENDDO
        DO J = 1,MAX_OTH
          OTHX(I,J) = 0.0
          OTHY(I,J) = 0.0
          OTHP(I,J) = 0
        ENDDO
        TITLE(I) = BLANK(1:30)
        TABSIZE(I) = 512
        OTHSZ(I) = 0
        HMSIZ(I) = 0
        NAME(I) = BLANK(1:26)
        MODATE(I) = BLANK(1:17)
      ENDDO
C
C -- Set up the commonly used escape sequences.
C
C      Nul    = '
      Bel    = ''
      Esc    = '' 
      NUL = CHAR(0)                          ! Escape
CC      ESC = CHAR(27)                         ! Escape
CC      BEL = CHAR(7)                          ! Bell
      BRT_STRT = ESC//'[1m'                  ! Start bright intensity
      BRT_END = ESC//'[22m'                  ! End bright intensity
      REV_STRT = ESC//'[7m'                  ! Start reverse video
      REV_END = ESC//'[27m'                  ! End reverse video
      CLS = ESC//'[2J'                       ! Clear entire screen
      CLS_STR = ESC//'[0J'                   ! Clear entire screen
      CLEAR = ESC//'[0J'                     ! Clear from curser to screen end
C
C -- Initialise common block variable
C
      EXT_NUM=0
      TBLNUM = 0
      TMP_TBSZ = 0
      AMPLITUDE = 0
      HARM_NUM = 0
      TMP_EXTSZ = 0
      TMP_HMSZ = 0
      TMP_OSZ = 0
      PHASE = 0
      TAB_NUM = 0
      DATASAVE = .FALSE.
      USER_OK = .FALSE.
C
      DO I=1,MAX_HNUM
         TEMP_AMPL(I) = 0.0
         TEMP_TYPE(I) = 0
         TEMP_HARM(I) = 0
      ENDDO
C
      DO I=1,MAX_OTH
         TMP_XVAL(I) = 0
         TMP_YVAL(I) = 0
         TMP_PVAL(I) = 0
      ENDDO
C
      DO J=1,MAX_DSG
        DO I=1,MAXSOUR
           SOURCE(I,J) = 0
        ENDDO
      ENDDO 
C
      SIN_MOD = .FALSE.  !Default sinemode is don't
C
C     All RTL labels
C     --------------
      CNT_TCR(1) = 'NATONCR1'
      CNT_TCR(2) = 'NASLACR1'
      STAT_TCR(1) = 'NATONSR1'
      STAT_TCR(2) = 'NASLASR1'
      CRG_TCR(1) = 'NATONCN1'
      CRG_TCR(2) = 'NASLACN1'
      P_CNT_TCR(1) = 'NAPHACNT'
      P_CNT_TCR(2) = 'NAPHACNB'
      P_OUT_TCR(1,1) = 'NAPHOL01'
      P_OUT_TCR(1,2) = 'NAPHOH01'
      P_OUT_TCR(1,3) = 'NAPHOL02'
      P_OUT_TCR(1,4) = 'NAPHOH02'
      P_OUT_TCR(1,5) = 'NAPHOL03'
      P_OUT_TCR(1,6) = 'NAPHOH03'
      P_OUT_TCR(1,7) = 'NAPHOL04'
      P_OUT_TCR(1,8) = 'NAPHOH04'
      P_OUT_TCR(1,9) = 'NAPHOL05'
      P_OUT_TCR(1,10)= 'NAPHOH05'
      P_OUT_TCR(1,11)= 'NAPHOL06'
      P_OUT_TCR(1,12)= 'NAPHOH06'
      P_OUT_TCR(1,13)= 'NAPHOL07'
      P_OUT_TCR(1,14)= 'NAPHOH07'
      P_OUT_TCR(1,15)= 'NAPHOL08'
      P_OUT_TCR(1,16)= 'NAPHOH08'
C
      P_OUT_TCR(2,1) = 'NAPHOL09'
      P_OUT_TCR(2,2) = 'NAPHOH09'
      P_OUT_TCR(2,3) = 'NAPHOL10'
      P_OUT_TCR(2,4) = 'NAPHOH10'
      P_OUT_TCR(2,5) = 'NAPHOL11'
      P_OUT_TCR(2,6) = 'NAPHOH11'
      P_OUT_TCR(2,7) = 'NAPHOL12'
      P_OUT_TCR(2,8) = 'NAPHOH12'
      P_OUT_TCR(2,9) = 'NAPHOL13'
      P_OUT_TCR(2,10)= 'NAPHOH13'
      P_OUT_TCR(2,11)= 'NAPHOL14'
      P_OUT_TCR(2,12)= 'NAPHOH14'
      P_OUT_TCR(2,13)= 'NAPHOL15'
      P_OUT_TCR(2,14)= 'NAPHOH15'
      P_OUT_TCR(2,15)= 'NAPHOL16'
      P_OUT_TCR(2,16)= 'NAPHOH16'
      FREZ_TCR = 'NAFREEZE'
C
C     Initialize all replacement KEYS
C     -------------------------------
      MAX_CHAR_L = 80
      REPL_STR(1) = 'HELP'
      REPL_L(1)   = 4
      REPL_CODE(1)= ED_HLP
      REPL_STR(2) = 'BOX '
      REPL_L(2)   = 4
      REPL_CODE(2)= ED_CTW
      REPL_STR(3) = 'SIN'
      REPL_L(3)   = 3
      REPL_CODE(3)= ED_PF1
      REPL_STR(4) = 'SQU'
      REPL_L(4)   = 3
      REPL_CODE(4)= ED_PF2
      REPL_STR(5) = 'TRI'
      REPL_L(5)   = 3
      REPL_CODE(5)= ED_PF3
      REPL_STR(6) = 'SAW'
      REPL_L(6)   = 3
      REPL_CODE(6)= ED_PF4
      REPL_STR(7) = 'EXIT'
      REPL_L(7)   = 4
      REPL_CODE(7)= ED_EXIT
      REPL_STR(8) = '    '
      REPL_L(8)   = 0
      REPL_CODE(8)= ED_ENT
      REPL_STR(9) = 'NEXT'
      REPL_L(9)   = 4
      REPL_CODE(9)= ED_NXS
      REPL_STR(10) = 'PREV'
      REPL_L(10)   = 4
      REPL_CODE(10)= ED_PVS
      REPL_STR(11) = 'AUTO'
      REPL_L(11)   = 4
      REPL_CODE(11)= ED_SEL
      REPL_STR(12) = 'DELE'
      REPL_L(12)   = 4
      REPL_CODE(12)= ED_RMV
      REPL_STR(13) = 'HARM'
      REPL_L(13)   = 4
      REPL_CODE(13)= ED_F17
      REPL_STR(14) = 'TITL'
      REPL_L(14)   = 4
      REPL_CODE(14)= ED_F18
      REPL_STR(15) = 'SIZE'
      REPL_L(15)   = 4
      REPL_CODE(15)= ED_F19
      REPL_STR(16) = 'COPY'
      REPL_L(16)   = 4
      REPL_CODE(16)= ED_F20
      REPL_STR(17) = 'MODE'
      REPL_L(17)   = 4
      REPL_CODE(17)= ED_DO
C
C     Set output mode on SCREEN
C     -------------------------
      OUTMODE = .FALSE.
C
      RETURN
      END
C
C     =======================
      SUBROUTINE SURE_QUIT(*)
C     =======================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*4
     & LLine4
C
      CHARACTER
     &   YES*3                 !YES string
      DATA YES/'YES'/
C
C   Exit with QUIT: Ask the user if he wants to do so if changes have been made
C   ---------------------------------------------------------------------------
      IF(DATASAVE) THEN
         CALL BEEP(1)
             SEND= '>>> Data has been modified since last save and you w
     &ill lose all the'
         CALL Term_Write(20,1,SEND,72)
             SEND='>>> changes if you Quit HARMONY now. (Use EXIT or X t
     &o save data)'
         CALL Term_Write(21,1,SEND,66)
         SEND='>>> Do you really want to quit [N] ? '
         CALL Term_Write(22,1,SEND,37)
         CALL Term_Read(0,INPLINE,LLine4,IERR)
         LLINE = LLine4
         IF(INPLINE(1:LLINE).NE.YES(1:LLINE).OR.LLINE.EQ.0) THEN
             RETURN 1
         ENDIF
      ENDIF
C
      RETURN
      END
C
C     =============================
      SUBROUTINE INIT_RTL(FLAG)
C     =============================
C
C     * INITIALIZE Real Time Download
C       -----------------------------
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      LOGICAL*1 FLAG
C
      CALL CDB_INIT(IERR)
C
      IF(IERR.EQ.0) THEN
         FLAG = .TRUE.
      ELSE
         FLAG = .FALSE.
      ENDIF
C
      RETURN
      END


