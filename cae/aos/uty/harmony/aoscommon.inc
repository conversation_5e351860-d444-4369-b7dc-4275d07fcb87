C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY & SNDTEST                                        **
C   **                                                                      **
C   **  Program  : AOSCOMMON.INC  for UNIX systems                          **
C   **  Function : QMR based CDB declaration                                **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0  Written by <PERSON>. <PERSON>             Date: 15 December 1989  **
C   **  Rev 1.1             P. Daigle                     06 December 1990  **
C   **                                                                      **
C   **  Applicable to :                                                     **
C   **  -------------                                                       **
C   **  Rev 1.0  SNDTEST utility                                            **
C   **  Rev 2.3  HARMONY utility on AOSUTY                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C   ** Include all statements to be FPCed.
C   ** Label are fixed and CANNOT BE CHANGED.
C   ** Only shipname should be modified from one simulator to another
C
CP     usd8
C
CP   & NAFREEZE    ,! $0*1*1 : Sound program freeze flag
CP   & NATONCR1(1) ,! $1*1*1 : Tone card #1 control register
CP   & NATONSR1(1) ,! $1*1*1 : Tone card #1 status register
CP   & NATONCN1(1) ,! $1*2*1 : Tone card #1 foreground status register
CP   & NANOICR1(1) ,! $2*1*1 : Noise card #1 control register
CP   & NANOISR1(1) ,! $2*1*1 : Noise card #1 status register
CP   & NANOICN1(1) ,! $2*2*1 : Noise card #1 foreground status register
CP   & NAIMPCR1(1) ,! $3*1*1 : Impact card #1 control register
CP   & NAIMPSR1(1) ,! $3*1*1 : Impact card #1 status register
CP   & NAIMPCN1(1) ,! $3*2*1 : Impact card #1 foreground status register
CP   & NAMIXCR1(3) ,! $4*1*1 : Mixer card #1-2-3 control register
CP   & NAMIXSR1(3) ,! $4*1*1 : Mixer card #1-2-3 status register
CP   & NAMIXCN1(3) ,! $4*2*1 : Mixer card #1-2-3 foreground status register
CP   & NAPHACNT    ,! $4*2*1 : Tone card phase counter
CP   & NAPHOH01(16),! $4*2*1 : Tone card phase labels
C
CP   & NASLACN1(1), !        : Slap card foreground status register
CP   & NASLACR1(1), !        : Slap card control register
CP   & NASLASR1(1), !        : Slap card status register
CP   & NAPHACNB   , !        : Slap card phase counter
CP   & NAPHOH09(16) !        : Slap card phase labels
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 1.8 ON  3-Jul-1992 15:50:45 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.62
C$@   /cae/simex_plus/element/usd8.skx.62
C$@   /cae/simex_plus/element/usd8.spx.62
C$@   /cae/simex_plus/element/usd8.sdx.62
C$@   /cae/simex_plus/element/usd8.xsl.58
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.58               
C$
      INTEGER*2
     &  NAIMPCN1(1)      ! IMP-01 COUNTER REGISTER               MI8002
     &, NAIMPCR1(1)      ! IMPACT #1 CONTROL REGISTER            MO8000
     &, NAIMPSR1(1)      ! IMPACT #1 STATUS REGISTER             MI8000
     &, NAMIXCN1(3)      ! MIX-01 COUNTER REGISTER               MI6801
     &, NAMIXCR1(3)      ! MIX-01 CONTROL REGISTER               MO68A4
     &, NAMIXSR1(3)      ! MIX-01 STATUS REGISTER                MI6800
     &, NANOICN1(1)      ! NOI-01 COUNTER REGISTER               MI7801
     &, NANOICR1(1)      ! NOI-01 CONTROL REGISTER               MO7859
     &, NANOISR1(1)      ! NOI-01 STATUS REGISTER                MI7800
     &, NAPHACNB         ! SLAP-01 PHASE COUNTER                 MO904C
     &, NAPHACNT         ! TON-01 PHASE COUNTER                  MO992E
     &, NAPHOH01(16)     ! TON-01 PHASE OUT LOW  (1)             MO9940
     &, NAPHOH09(16)     ! SLAP-01 PHASE OUT LOW  (1)            MO905E
     &, NASLACN1(1)      ! SLAP-01 COUNTER REGISTER              MI9036
     &, NASLACR1(1)      ! SLAP-01 CONTROL REGISTER              MO907D
     &, NASLASR1(1)      ! SLAP-01 STATUS REGISTER (1)           MI9030
     &, NATONCN1(1)      ! TON-01 COUNTER REGISTER               MI9836
     &, NATONCR1(1)      ! TON-01 CONTROL REGISTER (1)           MO995F
     &, NATONSR1(1)      ! TON-01 STATUS REGISTER (1)            MI9830
C$
      LOGICAL*1
     &  NAFREEZE         ! FREEZE FLAG FOR SOUND
C$
      LOGICAL*1
     &  DUM0000001(7484),DUM0000002(18),DUM0000003(86)
     &, DUM0000004(18),DUM0000005(2),DUM0000006(316)
     &, DUM0000007(688),DUM0000008(3690),DUM0000009(48)
     &, DUM0000010(2),DUM0000011(295853)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,NAPHACNT,DUM0000002,NAPHOH01,NATONCR1,DUM0000003
     &, NAPHACNB,DUM0000004,NAPHOH09,NASLACR1,DUM0000005,NAIMPCR1
     &, DUM0000006,NANOICR1,DUM0000007,NAMIXCR1,DUM0000008,NATONSR1
     &, NATONCN1,DUM0000009,NASLASR1,NASLACN1,NAIMPSR1,DUM0000010
     &, NAIMPCN1,NANOISR1,NANOICN1,NAMIXSR1,NAMIXCN1,DUM0000011
     &, NAFREEZE  
C------------------------------------------------------------------------------
C
C
C     Comment out this section if used on site
C     ----------------------------------------
CSTF+
C      INTEGER*2 NATONCR1(1),NATONCN1(1),NANOICR1(1),NANOICN1(1),
C     &          NAIMPCR1(1),NAIMPCN1(1),NAMIXCR1(3),NAMIXCN1(3),
C     &          NATONSR1(1),NAMIXSR1(3),NAIMPSR1(1),NANOISR1(1),
C     &          NAPHOL01(16),QMR_NAPHOL01(16),
C     &          NAPHACNT,QMR_NAPHACNT,
C     &          QMR_NATONSR1(1),QMR_NAMIXSR1(3),QMR_NAIMPSR1(1),
C     &          QMR_NANOISR1(1),
C     &          QMR_NATONCR1(1),QMR_NATONCN1(1),QMR_NANOICR1(1),
C     &          QMR_NANOICN1(1),QMR_NAIMPCR1(1),QMR_NAIMPCN1(1),
C     &          QMR_NAMIXCR1(3),QMR_NAMIXCN1(3)
C      LOGICAL*1 NAFREEZE,QMR_NAFREEZE
CSTF-
