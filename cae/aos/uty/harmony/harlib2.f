C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARLIB2.FOR                                              **
C   **  Function : Library routine for HARMONY program                      **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON><PERSON>/G. <PERSON>   Date: 11 December 1987  **
C   **  Rev 1.0             G. <PERSON>                   15 May 1988       **
C   **  Rev 2.0             G. <PERSON>                   20 June 1989      **
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1989       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  BOXES(H1,V1,H2,V2,C1,C2,C3,C4,S1,S2,S3,S4,G)                        **
C   **  T_WRITE(POS1,POS2,SENTENCE,LENGTH)                                  **
C   **  BEEP(NUM)      --------------------> in Lib_gd.for                  **
C   **  WAITIME(HOW)                             T                          **
C   **  CL_SCREEN                                |                          **
C   **  T_READ(MODE,STRING,STR_LEN,IERR)                                    **
C   **  PARSE_WORD(COMMAND,LEN_COM,WORD,LENGTH,N)                           **
C   **  STRING_LEN(STRING,LENGTH)                                           **
C   **  WAIT_KEY(QUIT)                                                      **
C   **  LOW_UPR(INPLINE,LEN_LINE)                                           **
C   **  GET_ERR_STR(ERR_NUMB,STRING)                                        **
C   **  WAIT_CONT(QUIT)                                                     **
C   **  NUMB_STR(STRING,NUMBER)                                             **
C   **  PARSE_NUMBER(STRING,L_STRING,NNUM_LIST,IERR)                        **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C     =======================================================
      SUBROUTINE BOXES(H1,V1,H2,V2,C1,C2,C3,C4,S1,S2,S3,S4,G)
C     =======================================================
C
C     Display a box on the screen:
C
C       Coordinates H1,H2 are the rows of the box,
C                   V1,V2 are the columns of the box.
C       Flags C1,C2,C3 and C4 are the corner print flags (0=Hidden,x=Print)
C               x     C1    C2    C3    C4
C              ---   ----  ----  ----  ----
C               1      (Straight corner)
C               2     (Vertical T corner)
C               3    (Horizontal T corner)
C               4        (Cross corner)
C       Flags S1,S2,S3 and S4 are the side print flags (0=Hidden,1=Print)
C
C        C1     S1      C2
C   (V1,H1)+-----------+
C          |           |
C        S4|           |S2
C          |           |
C          +-----------+(V2,H2)
C        C4     S3      C3
C
C       G is a special flag for setting graphic screen mode
C            -1 : Reset graphic mode at the end of the box
C             1 : Set graphic mode at the beginning of the box
C             2 : Set graphic mode at the beginning of the box and reset
C                 at the end
C
      IMPLICIT NONE
C
      INTEGER*2 H1,H2,V1,V2,C1,C2,C3,C4,S1,S2,S3,S4,G
      LOGICAL*1 NO_ERR_CHECK
      CHARACTER V_LINE_CHAR(80)*1,V_LINE*80,SEND_LINE*80
      INTEGER*2 V_LINE_LEN
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      EQUIVALENCE (V_LINE_CHAR,SEND_LINE)
      DATA V_LINE_CHAR /80*'q'/
C
C     Check for valid coordinates
C     ---------------------------
      NO_ERR_CHECK = (H2.GT.H1+1).AND.(V2.GT.V1+1)
C
      IF(NO_ERR_CHECK) THEN
C
C       Set graphic mode if requested
C       -----------------------------
        IF(G.EQ.1.OR.G.EQ.2) THEN
          SEND = ESC//'(0'
          CALL Term_Write(-1,0,SEND,3)
        ENDIF
C
C       Print left/top corner
C       ---------------------
        IF(C1.EQ.1) THEN
          SEND = 'l'                  !      __
          CALL Term_Write(H1,V1,SEND,1)  !     |
        ELSEIF(C1.EQ.2) THEN
          SEND = 't'                  !     |__
          CALL Term_Write(H1,V1,SEND,1)  !     |
        ELSEIF(C1.EQ.3) THEN
          SEND = 'w'                  !   _____
          CALL Term_Write(H1,V1,SEND,1)  !     |
        ELSEIF(C1.EQ.4) THEN
          SEND = 'n'                  !   __|__
          CALL Term_Write(H1,V1,SEND,1)  !     |
        ENDIF
C
C       Print the top horizontal line
C       -----------------------------
        IF(S1.EQ.1) THEN
          V_LINE_LEN = V2-V1-1
          V_LINE = SEND_LINE(1:V_LINE_LEN)
          CALL Term_Write(H1,V1+1,V_LINE,V_LINE_LEN)
        ENDIF
C
C       Print right/top corner
C       ----------------------
        IF(C2.EQ.1) THEN
          SEND = 'k'                  !   --
          CALL Term_Write(H1,V2,SEND,1)  !     |
        ELSEIF(C2.EQ.2) THEN
          SEND = 'u'                  !   __|
          CALL Term_Write(H1,V2,SEND,1)  !     |
        ELSEIF(C2.EQ.3) THEN
          SEND = 'w'                  !   _____
          CALL Term_Write(H1,V2,SEND,1)  !     |
        ELSEIF(C2.EQ.4) THEN
          SEND = 'n'                  !   __|__
          CALL Term_Write(H1,V2,SEND,1)  !     |
        ENDIF
C
C       Print the right vertical line
C       -----------------------------
        IF(S2.EQ.1) THEN
          SEND = 'x'
          DO II=H1+1,H2-1
             CALL Term_Write(II,V2,SEND,1)
          ENDDO
        ENDIF
C
C       Print right/bottom corner
C       -------------------------
        IF(C3.EQ.1) THEN
          SEND = 'j'                  !     |
          CALL Term_Write(H2,V2,SEND,1)  !   __|
        ELSEIF(C3.EQ.2) THEN
          SEND = 'u'                  !   __|
          CALL Term_Write(H2,V2,SEND,1)  !     |
        ELSEIF(C3.EQ.3) THEN
          SEND = 'v'                  !     |
          CALL Term_Write(H2,V2,SEND,1)  !   __|__
        ELSEIF(C3.EQ.4) THEN
          SEND = 'n'                  !   __|__
          CALL Term_Write(H2,V2,SEND,1)  !     |
        ENDIF
C
C       Print the bottom horizontal line
C       --------------------------------
        IF(S3.EQ.1) THEN
          V_LINE_LEN = V2-V1-1
          V_LINE = SEND_LINE(1:V_LINE_LEN)
          CALL Term_Write(H2,V1+1,V_LINE,V_LINE_LEN)
        ENDIF
C
C       Print left/bottom corner
C       ------------------------
        IF(C4.EQ.1) THEN
          SEND = 'm'                  !    |
          CALL Term_Write(H2,V1,SEND,1)  !    |__
        ELSEIF(C4.EQ.2) THEN
          SEND = 't'                  !     |__
          CALL Term_Write(H2,V1,SEND,1)  !     |
        ELSEIF(C4.EQ.3) THEN
          SEND = 'v'                  !     |
          CALL Term_Write(H2,V1,SEND,1)  !   __|__
        ELSEIF(C4.EQ.4) THEN
          SEND = 'n'                  !   __|__
          CALL Term_Write(H2,V1,SEND,1)  !     |
        ENDIF
C
C       Print the left vertical line
C       ----------------------------
        IF(S4.EQ.1) THEN
          SEND = 'x'
          DO II=H1+1,H2-1
             CALL Term_Write(II,V1,SEND,1)
          ENDDO
        ENDIF
C
C       Reset graphic mode if requested
C       -------------------------------
        IF(G.EQ.-1.OR.G.EQ.2) THEN
          SEND = ESC//'(B'
          CALL Term_Write(-1,0,SEND,3)
        ENDIF
C
      ENDIF
C
      RETURN
C
      END
C
C
C     ==============================================
      SUBROUTINE T_WRITE(LINE,COL,SENTENCE,LENGTH)
C     ==============================================
C
C     This routine writes a string to the terminal at the specified
C     position.
C     If you specified at LINE -1, the string is written at the cursor
C     position.
C     If you specified at LINE -2, the routine will return the last cursor
C     position in LINE,COL.
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2 LINE,COL,LENGTH,PRLINE,PRCOL
C
      CHARACTER*(*) SENTENCE
C
      CHARACTER*50 INTERR
      CHARACTER*2 S_COL,S_LINE
      CHARACTER*3 S_LENGTH
      CHARACTER*28 FMAT
C
      DATA INTERR/'%T_WRITE: Internal error #      in routine T_WRITE'/

C
C     Set previous cursor position and leave
C     --------------------------------------
      IF(LINE.EQ.-2) THEN
         LINE = PRLINE
         COL = PRCOL
         RETURN
      ENDIF
C
      IF(LENGTH.GT.0)THEN
C
C      Get the character equivalence of the string length
C      --------------------------------------------------
       WRITE(S_LENGTH,'(I3.3)',ERR=701,IOSTAT=IERR ) LENGTH
C
       IF(LINE.NE.-1) THEN
C
C       Write string to the specified position
C       --------------------------------------
C       Get the character equivalence of the LINE,COL positions
C       -------------------------------------------------------
        WRITE(S_LINE,'(I2.2)',ERR=701,IOSTAT=IERR ) LINE
        WRITE(S_COL,'(I2.2)',ERR=701,IOSTAT=IERR ) COL
C
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'',A'
     &        //S_LENGTH(1:3)//',$)'
        ELSE
         FMAT = '(''+'',A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//
     &        'H'',A'//S_LENGTH(1:3)//')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NUL,ESC,SENTENCE(1:LENGTH)
       ELSE
C
C       Write the string at the cursor position
C       ---------------------------------------
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A'//S_LENGTH(1:3)//',$)'
        ELSE
         FMAT = '(''+'',A1,A'//S_LENGTH(1:3)//')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NUL,SENTENCE(1:LENGTH)
       ENDIF
      ELSE
C
C       When length is zero, just position the cursor
C       ---------------------------------------------
C       Get the character equivalence of the LINE,COL positions
C       -------------------------------------------------------
        WRITE(S_LINE,'(I2.2)',ERR=701,IOSTAT=IERR ) LINE
        WRITE(S_COL,'(I2.2)',ERR=701,IOSTAT=IERR ) COL
C
C       Set the format string according to computer index (VAX-SEL)
C       -----------------------------------------------------------
        IF(VAXSEL) THEN
         FMAT = '(A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'',$)'
        ELSE
         FMAT = '(''+'',A1,A1,''['//S_LINE(1:2)//';'//S_COL(1:2)//'H'')'
        ENDIF
C
C       Write the string on the screen
C       ------------------------------
        WRITE(6,FMAT,ERR=701,IOSTAT=IERR ) NUL,ESC
C
      ENDIF
C
      PRLINE = LINE
      PRCOL = MIN(COL+LENGTH+0,80)
C
      GOTO 700
 701  WRITE(INTERR(27:31),'(I5)',ERR=702) IERR
 702  CALL ERR_MESS(INTERR,50,-1,*700)
 700  CONTINUE

      RETURN
      END
C
C
C     ============================
      SUBROUTINE WAITIME(HOW_LONG)
C     ============================
C
C     This routine waits the amount of time HOW_LONG in seconds
C
      IMPLICIT NONE
C
      REAL*4 HOW_LONG
C
      CALL Wait_Time(HOW_LONG)
C
      RETURN
      END
C
C     ====================
      SUBROUTINE CL_SCREEN
C     ====================
C
C     This routine clears the entire screen
C
      IMPLICIT NONE
C
      INCLUDE 'hardisp.inc'
C
      WRITE(6,*) ESC,'[2J'
C
      RETURN
      END
C
C
C     =============================================
      SUBROUTINE T_READ(MODE,STRING,STR_LEN,STATUS)
C     =============================================
C
C     This routine reads input from terminal.
C     MODE select the action for CR processing and upper cases conversion:
C
C          MODE <= 0 is convert all characters to upper cases.
C          MODE >= 1 is keep characters as they are (Upper or lower).
C          MODE = 0 or 1 is accept CR as input.
C          MODE <> 0 or 1 is refuse CR as input (print error message).
C
C         |MODE|= 99 is no echo of character entered and return immediately
C                    after one character has been entered
C
C          MODE > 100 Special function
C                     Normal MODE = MODE-1000
C                     The prompt represent the input buffer to be transform
C                     The prompt length is the real prompt not to be modified
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER STRING*80,ERR_AMB*40,DUMMY*10,T_BUFF(21)*80
      CHARACTER INPUT*80
      INTEGER*2 STR_LEN,TYPE,MODE,INMODE,LINE,COLUM,C_CHAR,STATUS
      INTEGER*2 T_PNT,T_PNTX,SPEC_CHAR,TB_LEN(21),CODE
      LOGICAL*1 READ_CONT,FIRST/.TRUE./,NOECHO
C
      DATA ERR_AMB/'%READ: <CR> is not accepted as a command'/
C
      IF(FIRST) THEN
        DO I = 1,20
           T_BUFF(I) = BLANK
           TB_LEN(I) = 0
        ENDDO
        FIRST=.FALSE.
        T_PNTX = 0
      ENDIF
      T_BUFF(21) = BLANK
      TB_LEN(21) = 0
C
      T_PNT = T_PNTX+1
C
C     Special MODE function
C     ---------------------
      NOECHO = .FALSE.
      IF(MODE.GE.100) THEN         !Prompt contains string to be modified
         INMODE=MODE-1000
         T_BUFF(T_PNT)=STRING
         TB_LEN(T_PNT)=STR_LEN
      ELSEIF(ABS(MODE).EQ.99)THEN  !No echo on terminal and return after a
         IF(MODE.EQ.-99) THEN      !a character has been entered
            INMODE= 0
         ELSE
            INMODE= 1
         ENDIF
         NOECHO = .TRUE.
      ELSE
         INMODE=MODE
      ENDIF
C
C     Get cursor position
C     -------------------
      STATUS=0
      LINE = -2
      CALL Term_Write(LINE,COLUM,DUMMY,0)
C
C     Read input from terminal until valid one entered
C     ------------------------------------------------
      READ_CONT=.TRUE.
      DO WHILE(READ_CONT)
        IF(.NOT.NOECHO) THEN
          CALL Term_Write(LINE,COLUM,T_BUFF(T_PNT),(MAX_CHAR_L-COLUM+1))
          CALL Term_Write(LINE,COLUM+TB_LEN(T_PNT),DUMMY,0)
        ENDIF
        INPUT=T_BUFF(T_PNT)
        CALL INP_BUFF(INPUT,(COLUM-1),TB_LEN(T_PNT),STATUS,NOECHO)
        IF(.NOT.NOECHO)THEN
         IF(STATUS.EQ.ED_CUP) THEN
           T_PNT=T_PNT-1
           IF(T_PNT.EQ.0)THEN
              T_PNT = 1
           ENDIF
         ELSEIF(STATUS.EQ.ED_CDN)THEN
           T_PNT=T_PNT+1
           IF(T_PNT.GT.(T_PNTX+1))THEN
              T_PNT=T_PNTX+1
           ENDIF
         ELSE
           IF(STATUS.EQ.0)THEN
              STRING = INPUT
              STR_LEN=80
              CALL STRING_LEN(STRING,STR_LEN)
              IF(STR_LEN.GT.0) THEN
                 IF((INPUT(1:STR_LEN).NE.T_BUFF(T_PNTX)(1:STR_LEN))
     &               .OR.(STR_LEN.NE.TB_LEN(T_PNTX)))THEN
                   IF(T_PNTX.EQ.20) THEN
                      DO I=2,20
                        T_BUFF(I-1)=T_BUFF(I)
                        TB_LEN(I-1)=TB_LEN(I)
                      ENDDO
                      T_BUFF(20) = INPUT
                      TB_LEN(20) = STR_LEN
                   ELSE
                      T_PNTX=T_PNTX+1
                      T_BUFF(T_PNTX)=INPUT
                      TB_LEN(T_PNTX) = STR_LEN
                   ENDIF
                 ENDIF
C
C                Convert to uppercase if mode <=0
C                --------------------------------
                 IF(INMODE.LE.0)THEN
                    CALL LOW_UPR(STRING,STR_LEN)
                 ENDIF
                 READ_CONT=.FALSE.
C
              ELSEIF(INMODE.LT.0.OR.INMODE.GT.1)THEN
C
C                If mode is CR invalid, print error message and read again
C                ---------------------------------------------------------
                 CALL ERR_MESS(ERR_AMB,40,-1,*50)
 50              CONTINUE
C
              ELSE
C
C                If mode is CR valid, QUIT
C                -------------------------
                 READ_CONT=.FALSE.
              ENDIF
           ELSE
              READ_CONT=.FALSE.
           ENDIF
         ENDIF
        ELSE
         STRING = INPUT
         STR_LEN=1
         CALL STRING_LEN(STRING,STR_LEN)
C
C        Convert to uppercase if mode <=0
C        --------------------------------
         IF(INMODE.LE.0)THEN
            CALL LOW_UPR(STRING,STR_LEN)
         ENDIF
         READ_CONT=.FALSE.
        ENDIF
      ENDDO
C
      RETURN
      END
C
C
C     ====================================
      SUBROUTINE STRING_LEN(STRING,LENGTH)
C     ====================================
C
      IMPLICIT NONE
C
      INTEGER*2 LENGTH
      CHARACTER*(*) STRING
C
      DO WHILE(LENGTH.GE.1.AND.STRING(LENGTH:LENGTH).EQ.' ')
           LENGTH=LENGTH-1
      ENDDO
C
      RETURN
      END
C
C
C     =========================
      SUBROUTINE WAIT_KEY(QUIT)
C     =========================
C
      IMPLICIT NONE
C
      LOGICAL*1 QUIT
      CHARACTER*80 INPUT
      INTEGER*2 STATUS
      INTEGER*4 LENGTH
C
      CALL Term_Write(23,2,'        Press <CR> to continue or Q to exit ...
     &',47)
C
      CALL Term_Read(0,INPUT,LENGTH,STATUS)
C
      IF(INPUT(1:1).EQ.'Q') THEN
         QUIT=.TRUE.
      ELSE
         QUIT=.FALSE.
      ENDIF
C
      RETURN
      END
C
C
C     ====================================
      SUBROUTINE LOW_UPR(INPLINE,LEN_LINE)
C     ====================================
      IMPLICIT NONE
C
C     INPUT : INPLINE -- contains the input character string to be translated
C             LEN_LINE -- contains the length of the character string
C
C     OUTPUT : INPLINE -- contains the upper case equivalent of the strin
C
       INTEGER*2
     &  DELTA           ,!Difference between upper case and lower case
     &  I
C
       INTEGER*2
     &  LEN_LINE
C
      LOGICAL
     &  READ_F/.TRUE./  !First read pass flag

      CHARACTER INPLINE*(*)
C
C     First pass
C     ----------
C     If necessary, get the difference between 'a' and 'A'
C

      IF (READ_F) THEN
        READ_F = .FALSE.
        DELTA = ICHAR('a') - ICHAR('A')
      ENDIF
C
C     If no character in the line, leave subroutine
C     ---------------------------------------------
      IF (LEN_LINE.EQ.0) RETURN
C
C     Translate to UPPER CASE in place
C     --------------------------------
      DO I = 1,LEN_LINE
        IF (('a'.LE.INPLINE(I:I)).AND.(INPLINE(I:I).LE.'z')) THEN
          INPLINE(I:I) = CHAR(ICHAR(INPLINE(I:I))-DELTA)
        ENDIF
      ENDDO
      RETURN
      END
C
C
C     =======================================
      SUBROUTINE GET_ERR_STR(ERR_NUMB,STRING)
C     =======================================
C
      IMPLICIT NONE
C
      INTEGER*4 ERR_NUMB
      CHARACTER*5 STRING
C
      IF(ABS(ERR_NUMB).GT.99999) THEN
         ERR_NUMB = 0
         WRITE(STRING,'(I5.5)') ERR_NUMB
      ELSE
         WRITE(STRING,'(I5)') ERR_NUMB
      ENDIF
C
      RETURN
      END
C
C     ================================================
      SUBROUTINE INP_BUFF(STRING,COL,CPOS,CODE,NOECHO)
C     ================================================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER STRING*80,DUMMY*1,CHAR*1,M_MAXCAR*47
      INTEGER*2 CODE,CPOS,C_PNT,LENGTH,LINE,COL,MAX_L,DCOL
      LOGICAL*1 BUF_FUL,OVERSTRIKE/.TRUE./,NOECHO
C
      DATA M_MAXCAR /'%READ: Maximum characters in input line reached'/
C
      LINE=-2
      CALL Term_Write(LINE,DCOL,DUMMY,0)
      BUF_FUL = .FALSE.
      MAX_L = MAX_CHAR_L-COL
      C_PNT = MIN(CPOS+1,MAX_L+0)
      LENGTH= CPOS
      DO WHILE(.NOT.BUF_FUL)
         CHAR = NUL
         CALL GETKEY(CHAR,CODE)
         IF(NOECHO)THEN
            BUF_FUL=.TRUE.
            IF(CODE.EQ.ED_RET)THEN
              CODE=0
            ELSEIF(CODE.EQ.ED_NUL) THEN
              STRING(1:1)=CHAR
            ENDIF
         ELSE
          IF(CODE.EQ.ED_NUL) THEN
           IF(ICHAR(CHAR).NE.0)THEN
            IF(OVERSTRIKE)THEN
              STRING(C_PNT:C_PNT) = CHAR
              CALL Term_Write(LINE,C_PNT+COL,CHAR,1)
              C_PNT = C_PNT+1
              IF(C_PNT.GT.MAX_L)THEN
                 BUF_FUL=.TRUE.
                 CALL ERR_MESS(M_MAXCAR,47,-1,*54)
 54              CONTINUE
              ELSEIF(LENGTH.LT.C_PNT-1)THEN
                 LENGTH=LENGTH+1
              ENDIF
            ELSE
              STRING(C_PNT+1:LENGTH+1) = STRING(C_PNT:LENGTH)
              STRING(C_PNT:C_PNT) = CHAR
              LENGTH=LENGTH+1
              IF(LENGTH.GT.MAX_L)THEN
                 BUF_FUL=.TRUE.
                 LENGTH=MAX_L
                 CALL ERR_MESS(M_MAXCAR,47,-1,*55)
 55              CONTINUE
              ENDIF
              CALL Term_Write(LINE,C_PNT+COL,STRING(C_PNT:LENGTH),
     &                                  (LENGTH-C_PNT+1))
              C_PNT = C_PNT+1
              IF(C_PNT.LE.MAX_L)THEN
                 CALL Term_Write(LINE,C_PNT+COL,DUMMY,0)
              ENDIF
            ENDIF
           ENDIF
          ELSEIF(CODE.EQ.ED_CRT) THEN
            IF(C_PNT.LT.(LENGTH+1))THEN
               C_PNT=C_PNT+1
               CALL Term_Write(LINE,C_PNT+COL,DUMMY,0)
            ENDIF
          ELSEIF(CODE.EQ.ED_CLF) THEN
            IF(C_PNT.GT.1)THEN
               C_PNT=C_PNT-1
               CALL Term_Write(LINE,C_PNT+COL,DUMMY,0)
            ENDIF
          ELSEIF(CODE.EQ.ED_RET)THEN
            BUF_FUL=.TRUE.
            CODE=0
          ELSEIF(CODE.EQ.ED_TPO) THEN
            OVERSTRIKE=.TRUE.
          ELSEIF(CODE.EQ.ED_INS) THEN
            OVERSTRIKE=.FALSE.
          ELSEIF(CODE.EQ.ED_TYIN) THEN
            OVERSTRIKE=.NOT.OVERSTRIKE
          ELSEIF(CODE.EQ.ED_BKSP) THEN
            IF(C_PNT.GT.1)THEN
              IF(LENGTH.GE.C_PNT)THEN
                STRING(C_PNT-1:LENGTH) = STRING(C_PNT:LENGTH)//' '
              ELSE
                STRING(C_PNT-1:LENGTH) = ' '
              ENDIF
              C_PNT = C_PNT-1
              CALL Term_Write(LINE,C_PNT+COL,STRING(C_PNT:LENGTH),
     &                                  (LENGTH-C_PNT+1))
              LENGTH=LENGTH-1
              IF(LENGTH.LT.1)THEN
                 LENGTH=1
              ENDIF
              CALL Term_Write(LINE,C_PNT+COL,DUMMY,0)
            ENDIF
          ELSE
            BUF_FUL=.TRUE.
          ENDIF
         ENDIF
      ENDDO
C
      RETURN
      END
C
C
C     ===============================
      SUBROUTINE WAIT_CONT(MODE,QUIT)
C     ===============================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2 QUIT,Status,Dummy
      INTEGER*4 L_A,L_PROMPT,MODE
      LOGICAL*1 END_READ
C
      CHARACTER*1 Ch,Dummy_Str
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      IF(MODE.EQ.0)THEN
         PROMPT = ' [ Press <CR>, P[REV] or N[EXT] to continue or Q, E o
     &r X to leave ] '
         L_PROMPT = 68
         L_A = 7
      ELSEIF(MODE.EQ.1)THEN
         PROMPT =' [ Press <CR> to continue, N[EXT] to skip section or Q
     &, E or X to leave ] '
         L_PROMPT = 74
         L_A = 3
      ELSE
         PROMPT =' [ Press <CR> to continue or Q, E or X to leave ] '
         L_PROMPT = 50
         L_A = 15
      ENDIF
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      END_READ=.FALSE.
      DO WHILE(.NOT.END_READ)
        WRITE(6,'(A1,A6,A4,A,A5,$)') Esc,'[24;1H',REV_STRT,PROMPT,
     &        REV_END
        WRITE(6,'(A1,A6,A1)') Esc,'[23;1H',Nul
C        CALL Term_Write(24,1,PROMPT,L_PROMPT)
C        CALL Term_Write(24,1,Dummy_Str,0)
        CALL ReadKey(Dummy,Ch,Status)
C
        IF(Status.EQ.0) THEN
          IF(Ch.EQ.'Q'.OR.Ch.EQ.'X'.OR.Ch.EQ.'E') THEN
             QUIT=0
          ELSEIF(Ch.EQ.'P') THEN    !PREV command
               QUIT = -1
          ELSEIF(Ch.EQ.'N') THEN    !NEXT command
             IF(MODE.EQ.1)THEN
                  QUIT = 99
             ELSE
                  QUIT = 1
             ENDIF
          ENDIF
          END_READ=.TRUE.
        ELSE IF(Status.EQ.1) THEN    !<CR> command
            END_READ=.TRUE.
            QUIT = 1
        ELSE
           END_READ=.TRUE.
           QUIT = 0
        ENDIF
      ENDDO
      CALL MES23(0,BLANK)
C
      RETURN
      END
C
C
C     ================================================
      SUBROUTINE NUMB_STR(STRING,L_STRING,R_NUMB,IERR)
C     ================================================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
      CHARACTER STRING*(*),INV_NUM*39
      INTEGER*2 CAR_ASCII,L_COUNT,NUMB
      INTEGER*4 L_STRING
      REAL*4  R_NUMB
      LOGICAL*1 ONE_SPACE
C
      DATA INV_NUM /'%STRING_NUMBER: Invalid number entered '/
C
      NUMB = 0
      ONE_SPACE=.FALSE.
      L_COUNT=1
      DO WHILE(L_COUNT.LE.L_STRING)
       CAR_ASCII=ICHAR(STRING(L_COUNT:L_COUNT))
C
       IF(((CAR_ASCII.GE.48.AND.CAR_ASCII.LE.57).OR.(CAR_ASCII.EQ.46)
     &   .OR.(CAR_ASCII.EQ.45)).AND..NOT.ONE_SPACE) THEN
         NUMB = NUMB+1
       ELSE
         IF(CAR_ASCII.EQ.32)THEN
            NUMB=NUMB+1
            ONE_SPACE=.TRUE.
         ENDIF
       ENDIF
       L_COUNT = L_COUNT+1
      ENDDO
C
      IF((NUMB.EQ.L_STRING).AND.(L_STRING.NE.0)) THEN
         READ(STRING(1:L_STRING),*,ERR=99,IOSTAT=IERR) R_NUMB
         RETURN
 99      CONTINUE
         CALL ERR_MESS(INV_NUM,39,-1,*56)
 56      CONTINUE
      ELSE
         IERR = 9999
      ENDIF
C
      RETURN
      END
C
C
C     =======================================================
      SUBROUTINE PARSE_NUMBER(STRING,L_STRING,NNUM_LIST,IERR)
C     =======================================================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
      CHARACTER STRING*(*),INV_NUM*44,TOOMUCH*47
      INTEGER*2 L_COMMA,COMMA,HYPHEN,L_HYP_MAX,L_HYP_MIN,
     &          START_NUM,STOP_NUM,NNUM_LIST
      INTEGER*4 L_STRING,STR_LEN
      LOGICAL*1 COMMA_LOOK,WRONG
C
      DATA INV_NUM/'%PARSE_NUMBER : Real type input not accepted'/
      DATA TOOMUCH/'%PARSE_NUMBER : Too many input number specified'/
C
      NNUM_LIST = 0
      L_COMMA = 1
      WRONG=.FALSE.
      COMMA_LOOK = .TRUE.
C
      DO WHILE(COMMA_LOOK)
C
C       Look for all commas in the input line
C       -------------------------------------
        COMMA = INDEX(STRING(L_COMMA:L_STRING),',')
        L_HYP_MIN = L_COMMA
        IF(COMMA.GT.0) THEN
          COMMA = COMMA + L_COMMA -1
          L_HYP_MAX = COMMA-1
          L_COMMA = COMMA+1
        ELSE
          L_HYP_MAX = L_STRING
          COMMA_LOOK=.FALSE.
        ENDIF
C
C       Check for hyphen character between two numbers
C       ----------------------------------------------
        HYPHEN = INDEX(STRING(L_HYP_MIN:L_HYP_MAX),'-')
        IF(HYPHEN.EQ.0) THEN
C
C         No hyphen found: store number only if integer
C         ---------------------------------------------
          STR_LEN = L_HYP_MAX-L_HYP_MIN+1
          CALL NUMB_STR(STRING(L_HYP_MIN:L_HYP_MAX),STR_LEN,NUMBER,IERR)
          IF(IERR.EQ.0) THEN
           INT_NUM = INT(NUMBER)
           REAL_NUM= FLOAT(INT_NUM)
           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
              NNUM_LIST = NNUM_LIST + 1
              IF(NNUM_LIST.LE.MAXI_SOUR)THEN
                 IT_LIST(NNUM_LIST) = INT_NUM
              ELSE
                 WRONG=.TRUE.
                 COMMA_LOOK = .FALSE.
                 CALL ERR_MESS(TOOMUCH,47,-1,*57)
 57              CONTINUE
              ENDIF
           ELSE
              CALL ERR_MESS(INV_NUM,44,-1,*58)
 58           CONTINUE
              COMMA_LOOK = .FALSE.
              WRONG = .TRUE.
           ENDIF
          ELSE
           COMMA_LOOK = .FALSE.
           WRONG = .TRUE.
          ENDIF
        ELSE
C
C         Hyphen found: store all number between them
C         -------------------------------------------
          HYPHEN = HYPHEN + L_HYP_MIN - 1
          STR_LEN = HYPHEN - L_HYP_MIN
          CALL NUMB_STR(STRING(L_HYP_MIN:HYPHEN-1),STR_LEN,NUMBER,
     &                                                      IERR)
          IF(IERR.EQ.0) THEN
           INT_NUM = INT(NUMBER)
           REAL_NUM= FLOAT(INT_NUM)
           IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
              START_NUM = INT_NUM
           ELSE
              CALL ERR_MESS(INV_NUM,44,-1,*59)
 59           CONTINUE
              COMMA_LOOK = .FALSE.
              WRONG = .TRUE.
           ENDIF
C
           STR_LEN = L_HYP_MAX - HYPHEN
           CALL NUMB_STR(STRING(HYPHEN+1:L_HYP_MAX),STR_LEN,NUMBER,
     &                                                      IERR)
           IF(IERR.EQ.0) THEN
            INT_NUM = INT(NUMBER)
            REAL_NUM= FLOAT(INT_NUM)
            IF(ABS(REAL_NUM-NUMBER).LT.0.001)THEN
               STOP_NUM = INT_NUM
            ELSE
               CALL ERR_MESS(INV_NUM,44,-1,*69)
 69            CONTINUE          
               COMMA_LOOK = .FALSE.
               WRONG = .TRUE.
            ENDIF
C
            DO I=START_NUM,STOP_NUM
              NNUM_LIST = NNUM_LIST + 1
              IF(NNUM_LIST.LE.MAXI_SOUR)THEN
                 IT_LIST(NNUM_LIST) = I
              ELSE
                 WRONG=.TRUE.
                 COMMA_LOOK = .FALSE.
                 CALL ERR_MESS(TOOMUCH,47,-1,*60)
 60              CONTINUE
              ENDIF
            ENDDO
C
           ELSE
            COMMA_LOOK = .FALSE.
            WRONG = .TRUE.
           ENDIF
          ELSE
           COMMA_LOOK = .FALSE.
           WRONG = .TRUE.
          ENDIF
        ENDIF
      ENDDO
C
      IF(WRONG.OR.NNUM_LIST.EQ.0) THEN
         IERR = 9999
      ENDIF
C
      RETURN
      END
