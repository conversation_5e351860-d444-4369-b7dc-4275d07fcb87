C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY For UNIX systems.                                **
C   **                                                                      **
C   **  Program  : HARLOAD.F                                                **
C   **  Function : All download function subroutines                        **
C   **                                                                      **
C   **  Revision History                                                    **
C   **  ----------------                                                    **
C   **  See harmony.f file                                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  HLOAD                                                               **
C   **  SIZE_CREATE                                                         **
C   **  DWLD_CREATE                                                         **
C   **  CHECK_SUM                                                           **
C   **  NEW_ASSO                                                            **
C   **  NEW_LOAD                                                            **
C   **  SIZE_CHECK                                                          **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     ===================
      SUBROUTINE HLOAD(*)
C     ===================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & DWL_NUM                ,!Number of download table
     & NEWTABLE(MAX_TAB_LOAD) ,!Sorted table order
     & S_L,                    !Slot number
     & CHOICE,                 !Number indicating menu choice
     & SORTAB(MAX_TAB_LOAD)    !Sorted table order
C
      INTEGER*4
     & D_FLAG(6)               !Download spare flag
C
      CHARACTER*80
     & SUBMIT1*45             ,!Print submitted .SIZ file message
     & ERROR1                  !Various error messages
     &,ERROR2                  !
     &,ERROR5                  !
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
       LOGICAL*1
     & DM_FLAG(6)
C
      DATA  ERROR1/'%CREATE_SIZ: Error #       when creating .SIZ'/
      DATA  ERROR2/'%CREATE_DOWNLOAD: Error #       when creating DOWNLO
     &AD file'/
      DATA ERROR5/'%DOWNLOAD_WRIT_XLINK: Error when writing flags to SND
     &XLINK file'/
      DATA SUBMIT1 /'File     w   .siz created for WAVEGEN utility'/
C
      CALL CL_DISP
      CALL Term_Write(7,1,'Harmony LOAD process',20)
      CALL Term_Write(8,1,'--------------------',20)
C
C     Sort all the tables according to their sizes
C     --------------------------------------------
      CALL SORT(4,TABSIZE,SORTAB,MAX_TAB_LOAD)
C
C     Update file xxxxWSN/RFx.SIZ with table/size informations
C     -----------------------------------------------------
      CALL SIZE_CREATE(0,TBLNUM,SORTAB,DWL_NUM,NEWTABLE,IERR)
      IF(IERR.EQ.0)THEN
C
C        Print .SIZ created file
C        -----------------------
         SUBMIT1(6:9) = Config_String(9)(1:4)
         SUBMIT1(11:13) = Filetters(1:3)
         CALL Term_Write(12,1,SUBMIT1,45)
C
C        Create the download file
C        ------------------------
         CALL DWLD_CREATE(DWL_NUM,NEWTABLE,.TRUE.,IERR)
C
         IF(IERR.EQ.0) THEN
            IF (UPDATE) THEN
C   
C              Update the WGSIZE flag in the LOG file
C              --------------------------------------
               D_FLAG(2) = 1
               D_FLAG(3) = 1
               DM_FLAG(2) = .TRUE.
               DM_FLAG(3) = .TRUE.
               CALL XLINK_WRITE(D_FLAG,DM_FLAG,IERR)
               IF(IERR.NE.0) THEN
                  CALL ERR_MESS(ERROR5,80,-1,*2)
 2                CONTINUE
               ENDIF
            ENDIF
         ELSE
            CALL GET_ERR_STR(IERR,ERROR2(28:32))
            CALL ERR_MESS(ERROR2,59,-1,*3)
 3          CONTINUE
         ENDIF
      ELSEIF(IERR.NE.9999)THEN
         CALL GET_ERR_STR(IERR,ERROR1(22:26))
         CALL ERR_MESS(ERROR1,45,-1,*4)
 4       CONTINUE
      ENDIF
C
      RETURN 1
      END
C
C
C ======================================================================
C               SIZE_CREATE: Update xxxxWSN/RFx.SIZ file
C ======================================================================
C
      SUBROUTINE SIZE_CREATE(OUTPUT,MAXTAB,LISTAB,DWL_NUM,NEWTABLE,IERR)
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & DWL_NUM             ,!Number of download table
     & MAXTAB              ,!Maximum number of table processed
     & NEWSOURCE(MAXSOUR)  ,!Sorted table-source association
     & NEWTABLE(MAX_TAB)   ,!Sorted table-source association
     & LISTAB(MAX_TAB)     ,!Sorted table order
     & DSG                  !DSG counter...
C
       INTEGER*4
     & OUTPUT               !Output mode (0=Create file,1=just update)
C
       CHARACTER
     & P_LINE*80           ,!Print line
     & HYPHEN*80           ,!Hyphen character line
     & COMMENT*80          ,!Comment line
     & TITRE1*80           ,!Title character
     & CARNUM1*1           ,!Number in character
     & CARNUM2*2           ,!Number in character
     & WORDCAR*4           ,!Table size in character
     & CARWCNT*5            !Number of word in character
C
       LOGICAL*1
     & ZEROSRC              !One source not associated flag
C
      CHARACTER*80
     & IMESS1               !Information message
C
       INTEGER*2
     & WORDCNT              !Word count
C
      DATA HYPHEN/'*------------+---------------+---------+-------------
     &---------------------------'/
      DATA TITRE1/'*   NAME       SIZE in WORDS   TABLE #   COMMENT/TITL
     &E                          '/
      DATA COMMENT/'*
     &                            '/
      DATA IMESS1 /'Some of the table have no source associated: Table 1
     & is set for default     '/
C
C     Clean up sorted list of tables (NEWTABLE) 
C     ----------------------------------------------------
      CALL SIZE_CHECK(WORDCNT,TBLNUM,LISTAB,DWL_NUM,NEWTABLE)
C
C
      IF(OUTPUT.EQ.0) THEN
C
C        Open xxxxWSN/RFx.SIZ file
C        ----------------------
         CALL FIL_OPEN(4,1,IERR)
         IF(IERR.NE.0) RETURN
C
C        Print the title
C        ---------------
         P_LINE(1:80)= BLANK(1:80)
         P_LINE(1:1) = '*'
         P_LINE(32:49)='FILE = WAVEGEN.SIZ'
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
         P_LINE(1:80)=BLANK(1:80)
         P_LINE(1:23)='TITLE = SOUND CHASSIS - '
         P_LINE(24:24+Config_Length(10))=Config_String(10)
     &         (1:Config_Length(10))
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
         P_LINE(1:80)=COMMENT(1:80)
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
         P_LINE(1:80)=BLANK(1:80)
         P_LINE(1:6)='DMC = '
         P_LINE(7:8)=DMC(1:2)
         WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
C        Repeat for each DSG card
C        ------------------------
         DO DSG = 1,DSG_NUMB         
C
C           Update the new assign table array
C           ---------------------------------     
            CALL NEW_ASSO(DSG,NEWTABLE,NEWSOURCE,DWL_NUM)
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            P_LINE(1:80)=BLANK(1:80)
            P_LINE(1:7)='SLOT = ' 
            P_LINE(8:9)= SL_NB(DSG)
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C 
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C 
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            P_LINE(1:80)=BLANK(1:80)
            P_LINE(1:10)='SIZE_START'
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) TITRE1
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) HYPHEN
C
C
            JJ = 1
            II = 1
            DO WHILE(II.LE.DWL_NUM.AND.JJ.LE.MAX_TAB_LOAD)
C
C             Print size line for all the tables
C             ----------------------------------
              P_LINE(1:80)=BLANK(1:80)
              P_LINE(4:8) ='SIZE('
              WRITE(CARNUM2,'(I2.2)',ERR=731) II
 731          CONTINUE
              P_LINE(9:11)=CARNUM2//') '
              P_LINE(12:18) = ' =     '
C
C             Print table size and title
C             --------------------------
              WRITE(P_LINE(19:22),'(I4)',ERR=702) TABSIZE(NEWTABLE(JJ))
 702          CONTINUE
              WRITE(P_LINE(34:35),'(I2)',ERR=722) NEWTABLE(JJ)
 722          CONTINUE
              P_LINE(40:80) = '!'//TITLE(NEWTABLE(JJ))
C
C             Print the line in the file
C             --------------------------
              WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE
C
              II = II+1
C
              JJ = JJ + 1
            ENDDO
C
C           Print end header : word count less than 32767
C           --------------------------------------------- 
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) HYPHEN
C
            P_LINE(1:80) = BLANK(1:80)
            P_LINE(1:1) = '*'
            P_LINE(6:15)= 'TOTAL  =  '
            WRITE(CARWCNT,'(I5)',ERR=703) WORDCNT
 703        CONTINUE
            P_LINE(16:20) = CARWCNT
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            P_LINE(1:80)=BLANK(1:80)
            P_LINE(1:10)='SIZE_END'
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
C           Add the table-source association
C           --------------------------------
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            P_LINE(1:80)=BLANK(1:80)
            P_LINE(1:12)='SOURCE_START'
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            ZEROSRC = .FALSE.
C
            DO II=1,MAXI_SOUR
C
C              Print source association line
C              -----------------------------
               P_LINE(1:80)=BLANK(1:80)
               WRITE(CARNUM2,'(I2.2)',ERR=704) II
 704           CONTINUE
               P_LINE(5:20)='SOURCE'//CARNUM2//' = SIZE('
C
               IF(NEWSOURCE(II).GT.0)THEN
C
C                Print the associated table
C                --------------------------
                 IF(NEWSOURCE(II).GE.10)THEN
                    WRITE(CARNUM2,'(I2)',ERR=705) NEWSOURCE(II)
                    P_LINE(21:23)=CARNUM2//') '
                 ELSE
                    WRITE(CARNUM1,'(I1)',ERR=705) NEWSOURCE(II)
                    P_LINE(21:22)=CARNUM1//') '
                 ENDIF
 705             CONTINUE
C
               ELSE
C
C                If no table associated, default is table 1
C                ------------------------------------------
                 P_LINE(21:23) = '1) '
                 ZEROSRC = .TRUE.
               ENDIF
C
               WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C
            ENDDO
C
C           Print message that some of the source are not associated
C           --------------------------------------------------------
            IF (ZEROSRC) THEN
              CALL Term_Write(10,1,IMESS1,80)
            ENDIF
C
C           Print end of section 2
C           ----------------------
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) COMMENT
C
            P_LINE(1:80)=BLANK(1:80)
            P_LINE(1:12)='SOURCE_END'
            WRITE(SIZ_UNIT,10,IOSTAT=IERR,ERR=701) P_LINE(1:80)
C       
         ENDDO             ! (DO DSG = 1,DSG_NUMB)
C
C        CLOSE xxxxWSN/RFx.SIZ file
C        --------------------------
         CALL FIL_OPEN(4,2,IERR)
C
C
      ENDIF        ! (IF OUTPUT = 0)
C
 701  CONTINUE
      RETURN
 10   FORMAT(A)
      END
C
C
C
C ======================================================================
C          DWLD_CREATE: Create the table data download file
C ======================================================================
C
      SUBROUTINE DWLD_CREATE(DWL_NUM,SORTAB,OUTDATA,IERR)
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
       INTEGER*2
     &  DWL_NUM                               ,!Number of download table
     &  TMP_SLOT                              ,!Temporary slot number integer
     &  PRINTADD                              ,!Print address of download file
     &  TABCALC                               ,!Which table to calculate
     &  SORTAB(MAX_TAB)                       ,!Sorted table order
     &  LINE_C,                                !Line count for output
     &  LEN                                   ,!Lenght of record
     &  LENGTH2                               ,!Length of character file name
     &  MOD200                                ,!
     &  PGCOUNT                               ,!
     &  DSG                                    !DSG counter ...
C
      INTEGER*4
     &  LENGTH1                               ,!Length of character file name
     &  ADDRESS                               ,!Address of data
     &  LIN                                    !Line counter
C
      LOGICAL*1
C
     &  OUTDATA                               !Output file DATA flag
C
      CHARACTER
C
     &  STR1*11                               ,!Table number in character
     &  DSGSTR*2                              ,!DSG number in character
     &  TBLSTR*2                              ,!Table number in character
     &  WORKING*80                            ,!Submit file to printer message
     &  HEX_SLOT*2                            ,!Slot number in HEX
     &  ASCADD*4                              ,!Data address in ASCII
     &  SUBMIT1*80                            ,!Submit file to printer message
     &  ASCBTMP*2                             ,!ASCII buffer temporary storage
     &  ASCBUFF*4                             ,!Buffer datas in ASCII
     &  FMAT*6                                ,!Format specifier
     &  FILEDLD*50                            ,!Download filename
     &  CHECKSUM*2                             !Check sum
C
      CHARACTER*19
C
     &  DISPLAYP(4)                            !Display presentation message
     &                 /'                   ' ,
     &                  ' HARMONY TABLE DATA' ,
     &                  '  DOWNLOAD   FILE  ' ,
     &                  '                   '/
C
      CHARACTER*19
C
     &  DISPLAYE(4)                            !Display end message
     &                 /'                   ' ,
     &                  ' HARMONY TABLE DATA' ,
     &                  '    END OF FILE    ' ,
     &                  '                   '/
C
      CHARACTER*43
C
     &  RECORD                                ,!1 DATA Record
     &  COMMENT                               ,!Comment record
     &  CLEANREC                              ,!Clean Record
     &  CLEANCOM                               !Clean commnet Record
C
      DATA   WORKING /'PROCESS    of    - Table           '/
      DATA  CLEANREC  /'0000000000000000000000000000000000000000000'/
      DATA  CLEANCOM  /'0000000000000000000000000000000000000000000'/
C
      IERR = 0
C
C     Create the download file
C     ------------------------
      CALL FIL_OPEN(3,1,IERR)
C
      IF(IERR.NE.0) THEN
         RETURN
      ENDIF
C
C     Set the filename for submit message
C     -----------------------------------
      FILEDLD = INT_DIR(1:L_INT_DIR)
     &          //Config_String(9)(1:Config_Length(9))
     &          //Filetters(1:3)//'h.int'
      LENGTH1 = L_INT_DIR+Config_Length(9)+8
      SUBMIT1 = 'Creating the Download file '//FILEDLD(1:LENGTH1)
      CALL Term_Write(14,1,SUBMIT1,LENGTH1+27)
C
      DO I = 1,4
C
C       Comment Presentation section
C       ----------------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = DISPLAYP(I)
        COMMENT(32:43) = '            '
C
        LEN = 43
        WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001)
     &                                             COMMENT(1:LEN)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C     Repeat for each DSG card
C     ------------------------
      DO DSG = 1,DSG_NUMB

C        Comment DMC # section
C        ---------------------
         COMMENT(1:1)   = '$'                        !Comment mark
         COMMENT(2:16) = '===== SLOT #   '           !
         COMMENT(17:18) = SL_NB(DSG)
         COMMENT(19:24) = ' ==== '
         COMMENT(25:31) = ' DMC # '                !
         COMMENT(32:33) = DMC(1:2)          !
         COMMENT(34:43)  = '  ========'            !
         LIN = 15 + DSG
         WRITE(DSGSTR,'(I1)',ERR=1001) DSG
         CALL Term_Write(LIN,5,DSGSTR,1)
         CALL Term_Write(LIN,6,': Slot XA',9)
         CALL Term_Write(LIN,15,SL_NB(DSG),2) 
         CALL Term_Write(LIN,17,' - ',3) 
C
         LEN = 43
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) COMMENT(1:LEN)
         COMMENT = CLEANCOM                    !Clean the comment record
C
C        Header section
C        --------------
         RECORD(1:1) = '&'                     !Rec mark
         RECORD(2:3) =  DMC(1:2)               !dmc #
         RECORD(4:4) = 'H'                     !file type H = 86 hex
         RECORD(5:5) = 'H'                     !Destination H = harmony
         READ(SL_NB(DSG),'(I2)',ERR=1001) TMP_SLOT  !Slot number in HEX
         WRITE(HEX_SLOT,'(Z2.2)',ERR=1001) TMP_SLOT
         RECORD(6:7) =  HEX_SLOT                 !Card slot
C
         LEN = 7
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
         RECORD = CLEANREC                       !Clean the record
C
C        Extended record
C        ---------------
         RECORD(1:15) = ':020000020000FC'    !Rec mark
C
         LEN = 15
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
         RECORD = CLEANREC                       !Clean the record
C
         LEN = 43
         FMAT = '(Z4.4)'
         PRINTADD = 0
         ADDRESS = 0
         WRITE(TBLSTR,'(I2)',ERR=1001) DWL_NUM
         WORKING(15:16) = TBLSTR
         DO I = 1,DWL_NUM
C
C           Data section
C           ------------
            WRITE(TBLSTR,'(I2)',ERR=1001) I
            WORKING(9:10) = TBLSTR
            WRITE(TBLSTR,'(I2)',ERR=1001) SORTAB(I)
            WORKING(26:27) = TBLSTR
            CALL Term_Write(LIN,20,WORKING,27)
            TABCALC=SORTAB(I)
            CALL CREATE_TABLE(TABCALC)
C
            J = 0
            DO WHILE(J.LT.TABSIZE(SORTAB(I)))
C
               RECORD(1:1) = ':'                   !Rec mark
               RECORD(2:3) = '10'                  !Rec lenght
C
               WRITE(ASCADD,FMAT,ERR=1001) ADDRESS
               RECORD(4:7) = ASCADD                !Load address input
               RECORD(8:9) = '00'                  !Rec type
               DO M=1,8                            !Data
                  WRITE(ASCBUFF,FMAT,ERR=1001) BUFF(M+J)
C
C                 Reverse the 2 bytes for INTEL format (1234-->3412)
C                 --------------------------------------------------
                  ASCBTMP = ASCBUFF(1:2)
                  ASCBUFF(1:2) = ASCBUFF(3:4)
                  ASCBUFF(3:4) = ASCBTMP
                  RECORD(6+M*4:9+M*4) = ASCBUFF
               ENDDO
               J = J + 8
               ADDRESS = ADDRESS+16
C
               CALL CHECK_SUM(RECORD,CHECKSUM)
               RECORD(42:43) = CHECKSUM(1:2)
C
               WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001) RECORD(1:LEN)
               RECORD = CLEANREC                   !Clean the record
C
            ENDDO
C
         ENDDO
C
C
C        End section
C        -----------
         RECORD(1:11) = ':00000001FF'
         LEN = 11
         WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001)
     &                                             RECORD(1:LEN)
      ENDDO              ! (DO DSG = 1,DSG_NUMB ...)      
C
      DO I = 1,4
C
C       Comment end section
C       -------------------
        COMMENT(1:1)  = '$'
        COMMENT(2:12)  = '           '
        COMMENT(13:31) = DISPLAYE(I)
        COMMENT(32:43) = '            '
C
        LEN = 43
        WRITE(DLD_UNIT,10,IOSTAT=IERR,ERR=1001)
     &                                             COMMENT(1:LEN)
        COMMENT = CLEANCOM                        !Clean the comment record
C
      ENDDO
C
C     Close download file
C     -------------------
      CALL FIL_OPEN(3,2,IERR)
      IF (IERR.EQ.0) THEN
C
C        Print download file created message
C        -----------------------------------
         CALL Term_Write(19,1,'Download file created successfully',34)
      ELSE
        CALL Term_Write(19,1,'An error has been generated,
     & no file created',44)
      ENDIF
      CALL Wait_Time(8.0)
C
 1001 CONTINUE
C
      RETURN
C
  10  FORMAT(A)
 130  FORMAT(' ',Z4,':    ',4(I6,'(',Z4,')',5X),A11)
 131  FORMAT(' ',20X,A,/)
      END
C
C
C
C =============================================================
C
C     THIS SUBROUTINE WILL RETURN THE CHECKSUM FOR A RECORD
C
C =============================================================
C
      SUBROUTINE CHECK_SUM(VECTOR,CHECKSUM)
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
C
      INTEGER*2
C
     &  HEX_SUM                             ,!Check sum in Hex
     &  CHEKSTAR     /2/                    ,!Check sum  start at
     &  SUM                                 ,!Sum of all bytes in the record
     &  RECLEN      /41/                    ,!Length of the record
     &  DECIM                                !Decimal Value from subroutine
C
      CHARACTER*43
C
     &   VECTOR                              !Record file
C
      CHARACTER
C
     &   FMAT*6                              ,!Format
     &   I2CHKSUM*4                          ,!Check sum for the record
     &   CHECKSUM*2                           !Check sum for the record
C
      INCLUDE 'hardata.inc'
C
      FMAT ='(Z4.4)'
      SUM = 0
C
      DO KK = CHEKSTAR, RECLEN,2
        READ(VECTOR(KK:KK+1),'(Z2)',ERR=702)DECIM
 702    CONTINUE
        SUM = SUM + DECIM
        IF (SUM.GT.255) SUM = SUM - 256
      ENDDO
C
      HEX_SUM = NOT(SUM) + 1
C
      WRITE(I2CHKSUM,FMAT,ERR=701) HEX_SUM
 701  CONTINUE
C
      CHECKSUM(1:2)=I2CHKSUM(3:4)
C
      RETURN
C
      END
C
C
C ========================================================================
C                               NEW_ASSO
C ========================================================================
C
C   This routine set a new source assignment array according to the
C   sorted table array to allow a good match in the creation of
C   WAVEGEN.SIZ file.
C
      SUBROUTINE NEW_ASSO(WHICH_DSG,NEWTABLE,NEWSOURCE,DWL_NUM)
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & WHICH_DSG           ,!Which DSG to generate a new association array
     & OLDPNT              ,!Old source array table
     & NEWPNT              ,!New source array pointer
     & NEWSOURCE(MAXSOUR)  ,!New source association array
     & NEWTABLE(MAX_TAB)   ,!Sorted table list 
     & DWL_NUM
C
C     Reset the new source array
C     --------------------------
      DO II = 1 ,MAXI_SOUR
        NEWSOURCE(II) = 0
      ENDDO
C
      DO NEWPNT = 1,DWL_NUM
         OLDPNT = NEWTABLE(NEWPNT)
         DO MM = 1, MAXI_SOUR
C
            IF(SOURCE(MM,WHICH_DSG).EQ.OLDPNT)THEN
C
C             Set new array
C             -------------
              NEWSOURCE(MM) = NEWPNT
            ENDIF
         ENDDO
      ENDDO
C
      RETURN
      END
C
C     =====================================================
      SUBROUTINE NEW_LOAD(REP_TAB,NEW_TAB,SPEED_FLAG,W_DSG)
C     =====================================================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & X_VAL                               ,!Transfer value
     & BOUNDARY                            ,!Word address of table
     & WAVE_NUMBER                         ,!Word boundary 512 words step
     & C_REG                               ,!Control register value
     & S_REG                               ,!Status register value
     & NEWTABLE(MAX_TAB_LOAD)              ,!Sorted table order
     & SORTAB(MAX_TAB_LOAD)                 !Sorted table order
C
      INTEGER*4
     & T_ERR,                               !Transfer error
     & W_DSG,                               !Which DSG to transfer to ...
     & II4,
     & JJ4
C
      INTEGER*2
     & TAB2,
     & STATUS_COMMAND,
     & STATUS_INFO,
     & CONTROL_COMMAND,
     & DWL_NUM                             ,!Number of download table
     & NEW_TAB                             ,!Table to be created
     & REP_TAB                              !Table to be replaced
      CHARACTER
     & M_WRONG*59                          ,!Message for wrong transfer
     & M_WRONG2*67                         ,!Message for wrong transfer
     & M_SUCC*37,
     & M_RSP*75,
     & M_TRANS*72
      LOGICAL*1
     & COMMAND_RESP,
     & INFO_RESP,
     & SPEED_FLAG,                          !Speed flag for test
     & DSG_RESPOND,                         !DSG respond flag
     & FOUND                                !Look flag
C
      DATA M_RSP  /' Reduced transfer speed for test, press <CR> to tran
     &sfert next frame <CR>  '/
      DATA M_SUCC/'%RTL - Successful transfer completed '/
      DATA M_TRANS/'RTL: Transfer to table  #     : [ Word address     H
     & : Word count      ]'/
      DATA M_WRONG /'%RTL : DSG card not responding correctly, transfer
     &ABORTED '/
      DATA M_WRONG2/'%RTL : DSG card send back error, status is     H, t
     &ransfer ABORTED '/
C
C     Sort all the tables according to their sizes
C     --------------------------------------------
      CALL SORT(4,TABSIZE,SORTAB,MAX_TAB_LOAD)
C
C     Get tables in an orderly fashion: get last zero table
C     -----------------------------------------------------
      CALL SIZE_CREATE(1,TBLNUM,SORTAB,DWL_NUM,NEWTABLE,IERR)
C
C     Look for boundary position of the replaced table
C     ------------------------------------------------
      BOUNDARY = 0
      FOUND = .FALSE.
      I = 1
      DO WHILE(I.LE.DWL_NUM.AND..NOT.FOUND)
         IF(NEWTABLE(I).EQ.REP_TAB)THEN
            FOUND = .TRUE.
         ELSE
            BOUNDARY = BOUNDARY + TABSIZE(NEWTABLE(I))
         ENDIF
         I = I + 1
      ENDDO
C
C     Set WAVEGEN boundary (complexity is a simple concept!!!)
C     --------------------------------------------------------
      WAVE_NUMBER = BOUNDARY/256 + TABSIZE(REP_TAB)/512
C
C     Get new table points (Store it temporarily into special buffer MAX_TAB+1)
C     -------------------------------------------------------------------------
      TABSIZE(MAX_TAB+1)=TMP_TBSZ
      PHASEL(MAX_TAB+1)=PHASE
      HMSIZ(MAX_TAB+1)=TMP_HMSZ
      DO I = 1,TMP_HMSZ
          AMPL(MAX_TAB+1,I)=TEMP_AMPL(I) 
          HARM(MAX_TAB+1,I)=TEMP_HARM(I) 
          WAVETYPE(MAX_TAB+1,I)=TEMP_TYPE(I) 
      ENDDO
      OTHSZ(MAX_TAB+1)=TMP_OSZ 
      DO I = 1,TMP_OSZ
          OTHP(MAX_TAB+1,I)=TMP_PVAL(I) 
          OTHX(MAX_TAB+1,I)=TMP_XVAL(I) 
          OTHY(MAX_TAB+1,I)=TMP_YVAL(I) 
      ENDDO
C
      EXTBLN(MAX_TAB+1)= 1
C
      TAB2 = MAX_TAB + 1       
      CALL CREATE_TABLE(TAB2)
C
      T_ERR = 1
      I = 0
      WRITE(M_TRANS(27:29),'(I3)') REP_TAB
      C_REG = 16384 + WAVE_NUMBER            !Download command = X'40xx'...
      CALL Term_Write(22,1,BLANK,80)         !...where xx = wave number
      DO WHILE(T_ERR.EQ.1.AND.I.LT.TABSIZE(REP_TAB))
         II4 = I
         CALL CDB_PUT(II4,2,1,W_DSG,T_ERR)   !Set waveform address
         IF(T_ERR.EQ.1)THEN
           DO J=1,16
              X_VAL=BUFF(J+I) !Buffer is in extended memory for Gould
              II4 = X_VAL
              JJ4 = J
              CALL CDB_PUT(II4,3,W_DSG,JJ4,T_ERR) !Set output values
           ENDDO                                  ! in shared memory...
           IF(T_ERR.EQ.1)THEN
             WRITE(M_TRANS(48:51),'(Z4)') BOUNDARY+I
             WRITE(M_TRANS(67:70),'(I4)') I+16
             CALL Term_Write(22,1,M_TRANS,72)
             CALL REV_CREG(C_REG)
             II4 = C_REG                        !Set control register to
             CALL CDB_PUT(II4,1,1,W_DSG,T_ERR)  !  transfer data from shared
             IF(T_ERR.EQ.1)THEN                 !  memory to WAVEGEN RAM
               W_LONG=0.100                   !Wait for chassis to transfer and
C                                              return status register flag
               CALL Wait_Time(W_LONG) 
               CALL CDB_GET(S_REG,1,2,W_DSG,T_ERR)!Get status register
               IF(T_ERR.EQ.1)THEN
C
C                Check status register after every frame (16 words).
C                Bypass check if not on site. Status information is
C                contained in the last three bytes of the register.
C                The first byte shows the last command received...
C                ---------------------------------------------------
                 STATUS_INFO    =IAND(S_REG+0,4095)   !'0FFF'X
                 STATUS_COMMAND =IAND(S_REG+0,-4096)  !'F000'X
                 CONTROL_COMMAND=IAND(C_REG+0,-4096)  !'F000'X
C
                 COMMAND_RESP = STATUS_COMMAND.EQ.CONTROL_COMMAND
                 INFO_RESP    = STATUS_INFO.EQ.0 
                 DSG_RESPOND  = .NOT.ON_SITE .OR. 
     &                          (COMMAND_RESP.AND.INFO_RESP)
C
                 IF(.NOT.DSG_RESPOND) THEN
                   T_ERR=99
                   IF(.NOT.COMMAND_RESP) THEN
                     CALL ERR_MESS(M_WRONG,59,-1,*5)
 5                   CONTINUE
                   ELSE
                     WRITE(M_WRONG2(34:37),'(Z4)') STATUS_INFO
                     CALL ERR_MESS(M_WRONG2,67,-1,*6)
 6                   CONTINUE
                   ENDIF   
                 ENDIF
               ENDIF
             ENDIF
           ENDIF
         ENDIF
         IF(SPEED_FLAG) THEN 
           CALL Term_Write(23,3,M_RSP,75)
           CALL Term_Read(0,INPLINE,LLINE,IERR)
         ENDIF 
         I = I + 16
      ENDDO
      CALL Term_Write(23,3,BLANK,75)
C
      W_LONG = 2.0
      CALL Wait_Time(W_LONG)
      IF(T_ERR.EQ.1)THEN
         CALL ERR_MESS(M_SUCC,37,-1,*7)
 7       CONTINUE
         CALL Wait_Time(W_LONG)
      ENDIF
C
C     Reset board: control register + phase counter
C     ---------------------------------------------
      C_REG = 0
      II4 = C_REG
      CALL CDB_PUT(II4,1,1,W_DSG,T_ERR)    !ReSet control register
      CALL CDB_PUT(0,2,1,W_DSG,T_ERR)      !ReSet waveform address
C
      RETURN
      END
C
C
C ======================================================================
C               SIZE_CHECK: Update NEWTABLE array
C ======================================================================
C
      SUBROUTINE SIZE_CHECK(WORDCNT,MAXTAB,LISTAB,DWL_NUM,NEWTABLE)
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & OUTPUT              ,!Output mode (0=Create file,1=just update)
     & DWL_NUM             ,!Number of download table
     & MAXTAB              ,!Maximum number of table processed
     & NEWSOURCE(MAXSOUR)  ,!Sorted table-source association
     & NEWTABLE(MAX_TAB)   ,!Sorted table-source association
     & LISTAB(MAX_TAB)      !Sorted table order
C
C
       LOGICAL*1
     & ZEROSRC             ,!One source not associated flag
     & FOUND                !Search flag
C
      CHARACTER*80
     & ERROR1               !Various error messages
     &,IMESS1               !Information message
C
       INTEGER*2
     & WORDCNT              !Word count
C
      DATA ERROR1/'%DOWNLOAD: Number of data points exceed 32767  *** LO
     &AD ABORTED ***         '/
      DATA IMESS1 /'Some of the table have no source associated: Table 1
     & is set for default     '/
C
C
C     Clear number of words counter
C     -----------------------------
      WORDCNT=0
C
C
      JJ = 1
      II = 1
      DO WHILE(II.LE.MAXTAB.AND.JJ.LE.MAX_TAB_LOAD)
        IF (TABSIZE(LISTAB(JJ)).GT.0.AND.LISTAB(JJ).LE.MAX_TAB_LOAD)THEN
C
C           Check if table exist
C           --------------------
            FOUND = .FALSE.
            MM = 1
            DO WHILE(MM.LE.TBLNUM.AND..NOT.FOUND)
               IF(SAV_TBL(MM).EQ.LISTAB(JJ))THEN
                  FOUND=.TRUE.
               ENDIF
               MM = MM + 1
            ENDDO
C
            IF (FOUND) THEN
              WORDCNT = WORDCNT + TABSIZE(LISTAB(JJ))
C
C             Set new table array existing and in order
C             -----------------------------------------
              NEWTABLE(II) = LISTAB(JJ)
C
              II = II+1
            ENDIF
        ENDIF
C
        JJ = JJ + 1
      ENDDO
C
      IF(WORDCNT.LT.32767) THEN
C
C      Set real number of tables for download
C      --------------------------------------
       DWL_NUM = II-1
C
      ELSE
C
C        Too many words in the table data file :print error message
C        ----------------------------------------------------------
         CALL ERR_MESS(ERROR1,68,-1,*8)
 8       CONTINUE
         CALL FIL_OPEN(4,3,IERR)
         IERR = 9999
C
      ENDIF
C
      RETURN
      END
