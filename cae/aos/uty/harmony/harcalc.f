C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARCALC.FOR                                              **
C   **  Function : Create the table points routine                          **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 0.1  Written by <PERSON>. <PERSON>             Date: 13 December 1987  **
C   **  Rev 1.0             G. <PERSON>                   01 May  1988      **
C   **  Rev 2.0             G. <PERSON>                   10 June 1989      **
C'Revision_History
C   **  Rev 2.1             G. <PERSON>                   17 Oct 1989       **
C   **  Rev 2.2             G. <PERSON>                   18 Feb 1989       **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  CREATE_TABLE                                                        **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C     ===========================
      SUBROUTINE CREATE_TABLE(WT)
C     ===========================
C
      IMPLICIT NONE
C
C
C       This module generates the wavetables required by the DSG.  The
C       input, which is gain and phase versus harmonic, is scaled and mixed
C       to produce a normalized wavetable in Q15 format.
C
      INCLUDE 'harparm.inc'
C
        CHARACTER*80 ERROR1
        INTEGER*2
     & POINT100          !100 % point reference for other
C
        REAL*4
     & OTHAMPL          ,
     & SLOPE            ,!Slope
     & INTER            ,!Intercept
     & OTX1             ,!Other point position #1
     & PROTX2           ,!Previous other point position #2
     & OTX2             ,!Other point position #2
     & OTY1             ,!Other amplitude #1
     & OTY2             ,!Other amplitude #2
     & AM_TMP           ,!Amplitude temporary storage
     & PRAM_TMP          !Previous amplitude temporary storage
C
      REAL*4
     &  KKK             ,!Harmonic number
     &  D, DPTR,         !Loop pointer
     &  NORM,            !Highest level for normalization
     &  POWER/0/         !Total power of a table
C
      INTEGER*4
     &  SEED/32767/      !Random generator seed
C
      INTEGER*2
     & REMPTR           ,!Remember last PTR
     & MMM              ,!
     & III              ,!
     & WT               ,!Which table pointer
     & TBLSIZ            !Table size
C
      INCLUDE 'hardata.inc'
C
      DATA ERROR1 /'%CREATTTTABLE [CALC] : Invalid table to be created'/
C
      IF(WT.GT.0.AND.WT.LE.MAX_TAB+1)THEN
C
C      Clean buffer before writing on it
C      ---------------------------------
       DO KKK=1,MAX_BUF
         TBL(KKK) = 0
       ENDDO
C
C      Set the table size with table pointer
C      -------------------------------------
       TBLSIZ=TABSIZE(WT)
C
       IF(HMSIZ(WT).GT.0)THEN

C       Conversion from dBV to Volts
C       ----------------------------
C       V/Vref = 10 ** (dB/20)
C
        DO KKK=1,HMSIZ(WT)
          SIGAMPL(KKK) = 10.0 ** (AMPL(WT,KKK)/20)
          POWER = POWER + SIGAMPL(KKK)*SIGAMPL(KKK)*HARM(WT,KKK)
        ENDDO
C
C       Calculate power for Schroedler's rule phase
C       -------------------------------------------
        POWER = POWER/(2*TBLSIZ)
        POWER = SQRT(POWER)
C
C       Phase correction
C       ----------------
        IF (PHASEL(WT) .LT. 0) THEN
          DO KKK=1,HMSIZ(WT)             !Zero phase
            PHA(KKK) = 0.
          ENDDO
        ELSEIF (PHASEL(WT) .GT. 0) THEN
          CALL RANDOM(SEED,D)
          DO KKK=1,HMSIZ(WT)             !Random phase
             PHA(KKK) = D * PI
          ENDDO
        ELSE
          CALL RANDOM(SEED,D)
          PHA(1) = D * PI               !Schroeder's rule phase
          DO KKK=2,HMSIZ(WT)
            DO III=1,KKK-1
              TEMPR = TEMPR + (KKK-III)*AMPL(WT,III)
            ENDDO
            PHA(KKK) = PHA(KKK-1) + TWOPI*TEMPR/POWER
          ENDDO
        ENDIF
C
C       Wavetable Generation:
C       Process signal waves for all harmonics
C       --------------------------------------
        D = TWOPI/TBLSIZ
        DO III=1,HMSIZ(WT)
          KKK=HARM(WT,III)
          IF(WAVETYPE(WT,III).EQ.1)THEN
C
C            Sine wave
C            ---------
C                   no. of harmonics
C                    _
C            u(n) = \ a  sin(2*pi*r*n/N + P )  , where N=f /f is the table size
C                   /  r                   r              s  1
C                    -
C                   r = 1
C
             DO MMM = 1,TBLSIZ
                DPTR = D*FLOAT(MMM-1)
                TBL(MMM)=TBL(MMM)+SIGAMPL(III)*SIN(DPTR*KKK+
     &                              PHA(III))
             ENDDO
C
          ELSEIF(WAVETYPE(WT,III).EQ.2)THEN
C
C            Square wave
C            -----------
             DO MMM = 1,TBLSIZ
                DPTR = D*FLOAT(MMM-1)
                AM_TMP = SIN(DPTR*KKK + PHA(III))
                IF(AM_TMP.LT.0.001.AND.AM_TMP.GT.-0.001) THEN
                   AM_TMP = 0
                ENDIF
                IF (AM_TMP.GE.0.0) THEN
                   TBL(MMM) = SIGAMPL(III) + TBL(MMM)
                ELSE
                   TBL(MMM) =   TBL(MMM) - SIGAMPL(III)
                ENDIF
             ENDDO
          ELSEIF(WAVETYPE(WT,III).EQ.3) THEN
C
C            Triangular wave
C            ---------------
             SLOPE  = HARM(WT,III)*4.0/TBLSIZ
             INTER = -1.0
             AM_TMP = 0.0
             REMPTR=0
             DO MMM = 1,TBLSIZ
               DPTR = D*FLOAT(MMM-1)
               PRAM_TMP = AM_TMP
               AM_TMP = SIN(DPTR*KKK + PHA(III))
               IF ( (PRAM_TMP.GT.0.0.AND.AM_TMP.LE.0.0) .OR.
     &         (PRAM_TMP.LT.0.0.AND.AM_TMP.GE.0.0) )THEN
C
C                  Swap the slope and intercept
C                  ----------------------------
                   REMPTR = MMM
                   SLOPE=-SLOPE
                   INTER = -INTER
               ENDIF
               TBL(MMM) = ((MMM-REMPTR)*SLOPE+INTER)*SIGAMPL(III)
     &                      +TBL(MMM)
            ENDDO
C
          ELSEIF(WAVETYPE(WT,III).EQ.4) THEN
C
C            Sawtooth wave
C            -------------
             REMPTR=0
             SLOPE  = KKK*2.0/TBLSIZ
             INTER = -1.0
             DO MMM = 0,TBLSIZ-1
               IF(MMM.GT.((TBLSIZ/KKK)+REMPTR))THEN
                  REMPTR=MMM
               ENDIF
               TBL(MMM+1) = ((MMM-REMPTR)*SLOPE+INTER)*SIGAMPL(III)
     &                        +TBL(MMM+1)
             ENDDO
C
          ENDIF
        ENDDO
C
C       Normalize the wavetable
C       -----------------------
C       Wavetable = Wavetable/MAX(Wavetable)
C
        NORM = ABS(TBL(1))
        DO MMM = 2,TBLSIZ
          IF (NORM .LT. ABS(TBL(MMM))) NORM = ABS(TBL(MMM))
        ENDDO
C
        DO MMM = 1,TBLSIZ
          TBL(MMM) = MAX(MIN(TBL(MMM)/NORM,1.00),-1.00)
        ENDDO
C
       ELSEIF(OTHSZ(WT).GT.0) THEN
C
C       Other wave
C       -----------
        POINT100=TBLSIZ
        OTHAMPL = 2.0
C
        DO M=2,OTHSZ(WT)
            OTX1 = OTHX(WT,M-1) * POINT100 / 100.0
            OTX2 = OTHX(WT,M) * POINT100 / 100.0
            OTY1 = OTHY(WT,M-1) * OTHAMPL / 100.0
            OTY2 = OTHY(WT,M) * OTHAMPL / 100.0
C
            SLOPE = (OTY2 - OTY1) / (OTX2 - OTX1)
            INTER = ( (OTX2*OTY1) - (OTX1*OTY2) ) / (OTX2-OTX1)
C
            IF(M.EQ.2) PROTX2 = OTX1
            DO MMM = PROTX2+1,OTX2
               TBL(MMM) = MAX(MIN(((SLOPE*FLOAT(MMM)+INTER)-1.0),1.00),
     &                                 -1.00)
            ENDDO
C
            PROTX2 = OTX2
        ENDDO
C
       ELSEIF(EXTBLN(WT).GT.0) THEN
C
C       External data type table
C       ------------------------
        DO MMM=1,TBLSIZ
           TBL(MMM) = EXTP(EXTBLN(WT),MMM)
        ENDDO
C
C       Normalize the wavetable
C       -----------------------
C       Wavetable = Wavetable/MAX(Wavetable)
C
        NORM = ABS(TBL(1))
        DO MMM = 2,TBLSIZ
          IF (NORM .LT. ABS(TBL(MMM))) NORM = ABS(TBL(MMM))
        ENDDO
C
C       Skip the normalization if table is all zeros
C       --------------------------------------------
        IF (NORM.NE.0) THEN
          DO MMM = 1,TBLSIZ
            TBL(MMM) = MAX(MIN(TBL(MMM)/NORM,1.00),-1.00)
          ENDDO
        ENDIF
C
       ENDIF
C
C      Conversion from decimal to Q15
C      ------------------------------
       MMM = 1
       DO WHILE(MMM.LE.TBLSIZ)
        BUFF(MMM) = INT(Q15*TBL(MMM))
        MMM = MMM+1
       ENDDO
C
      ELSE
       CALL ERR_MESS(ERROR1,50,-1,*88)
 88    CONTINUE
      ENDIF
C
      RETURN
      END
