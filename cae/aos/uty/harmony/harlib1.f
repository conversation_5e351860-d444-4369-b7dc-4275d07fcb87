C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : HARMONY                                                  **
C   **                                                                      **
C   **  Program  : HARLIB1.F                                                **
C   **  Function : Miscellanious subroutines 1                              **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  See harmony.f file                                                  **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  TRANS_PHASE                                                         **
C   **  SRC_IO : Entry SRC_SAVE, SRC_READ                                   **
C   **  TRANS_TYPE                                                          **
C   **  DATA_IO : Entry SAVE_DATA, REST_DATA                                **
C   **  TAB_EXIST                                                           **
C   **  MES23                                                               **
C   **  ERR_MESS                                                            **
C   **  VALIDATA                                                            **
C   **  READLOG                                                             **
C   **  PRINT_HEAD                                                          **
C   **  SET_SCROLL                                                          **
C   **  CONF                                                                **
C   **  ERR_CDB_ROUT(STATUS)                                                **
C   **  SORT                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C     ====================================
      SUBROUTINE TRANS_PHASE(PHASE,STRING)
C     ====================================
      IMPLICIT NONE
C
C -- This subroutine translates the integer value in PHASE to
C    its character equivalent STRING.

      CHARACTER*25 
     &          STR1 /'PHASE : SCHROEDER''S RULE'/,
     &          STR2 /'PHASE : RANDOM PHASE     '/,
     &          STR3 /'PHASE : ZERO PHASE       '/,
     &          STR4 /'                         '/,
     &          STRING

      INTEGER*4 PHASE             ! Integer phase value

C -- Set display string according to phase type

      IF (PHASE .EQ. 0) THEN
        STRING = STR1
      ELSE IF (PHASE .GT. 0) THEN
        IF (PHASE.EQ.99) THEN
           STRING = STR4
        ELSE
           STRING = STR2
        ENDIF
      ELSE IF (PHASE .LT. 0) THEN
        STRING = STR3
      ENDIF

      RETURN
      END
C
C
C     =================
      SUBROUTINE SRC_IO
C     =================
      IMPLICIT NONE
C
C     This subroutine saves & restores source/table associations
C     from the data file
C
      INCLUDE 'harparm.inc'
C
      LOGICAL*1
     & NEW_REV                        ! New revision of data file
C
      INTEGER*2
     &          N_DSG,                ! Number of DSG in datafile
     &          LM,                   ! Source number read
     &          LSOURCE,              ! Table number associated with
     &          COUNT                 ! Counter
C
      INTEGER*4
     &          L_WM1,                ! Warning message length
     &          L_WM2,                ! Warning message length
     &          SRCERR                ! Source error
C
      CHARACTER*80
     & WMESS1                        ,! Warning message for unmatched number ofslot 
     & WMESS2                         ! Warning message for unmatched slot # 
C
      INCLUDE 'hardata.inc'
C
      DATA WMESS1 /'%REST_DATA-W-UNMATCH, Unmatched number of slot betwe
     &en DATA and config files'/
      DATA WMESS2 /'%REST_DATA-W-UNMATCH, Unmatched slots between DATA a
     &nd configuration files'/
      DATA L_WM1 /76/
      DATA L_WM2 /74/
C
      ENTRY SRC_SAVE(SRCERR,*)
C     ==============
C
C     This subroutine saves the source/table associations to the the
C     data file
C
C     Save all the sources with comment
C     ---------------------------------
      WRITE(DAT_UNIT,140,ERR=899,IOSTAT=SRCERR) DSG_NUMB
      DO MM=1,DSG_NUMB
         WRITE(DAT_UNIT,142,ERR=899,IOSTAT=SRCERR) SL_NB(MM)
         COUNT = 1
         DO WHILE (COUNT .LE. MAXI_SOUR)
            WRITE(DAT_UNIT,150,ERR=899,IOSTAT=SRCERR) COUNT,
     &            SOURCE(COUNT,MM)
            COUNT = COUNT + 1
         ENDDO
      ENDDO
C
      RETURN
 899  RETURN 1
C
      ENTRY SRC_READ(NEW_REV,SRCERR,*)
C     ================================
C
C     This subroutine reads the sources from the data file
C     ----------------------------------------------------
      IF (NEW_REV) THEN
        READ(DAT_UNIT,141,ERR=101,IOSTAT=SRCERR) N_DSG
        IF (N_DSG.NE.DSG_NUMB) THEN
           CALL ERR_MESS(WMESS1,L_WM1,-1,*50)
 50        CONTINUE
           DATASAVE=.TRUE. !Save DATA before QUIT if unmatch
        ENDIF
      ELSE
        N_DSG=1  
      ENDIF
      DO MM=1,N_DSG
         IF (NEW_REV) THEN
             READ(DAT_UNIT,143,ERR=101,IOSTAT=SRCERR) LM
         ELSE
             READ(SL_NB(1),'(I2.2)') LM 
             DATASAVE=.TRUE.
         ENDIF
         IF (LM.EQ.SL_NBI(MM)) THEN
C
C          Read all valid data 
C          -------------------
           COUNT = 1
           DO WHILE (COUNT .LE. MAXI_SOUR)
            READ(DAT_UNIT,151,ERR=101,IOSTAT=SRCERR) LM,LSOURCE
            SOURCE(LM,MM)=LSOURCE
            COUNT = COUNT + 1
           ENDDO
         ELSE
C
C          Read it to check next SLOT data
C          -------------------------------
           CALL ERR_MESS(WMESS2,L_WM2,-1,*51)
 51        CONTINUE
           COUNT = 1
           DO WHILE (COUNT .LE. MAXI_SOUR)
            READ(DAT_UNIT,151,ERR=101,IOSTAT=SRCERR) LM,LSOURCE
           ENDDO
           DATASAVE=.TRUE. !Save DATA before QUIT if unmatch
         ENDIF
      ENDDO
      RETURN
C
101   RETURN 1
C
140   FORMAT(' ',2X,'Number of DSG is ',I2)
141   FORMAT(T21,I2)
142   FORMAT(' ',2X,'***** SLOT XA',A2,' *****')
143   FORMAT(T17,I2)
150   FORMAT(' ',2X,'Source #',I3,' associated with table #',I3)
151   FORMAT(T12,I3,T39,I3)
C
      END
C
C     ==============================
      SUBROUTINE TRANS_TYPE(TAB_SET)
C     ==============================
C
      IMPLICIT NONE
C
C -- This subroutine translates each element in the TYPE array to its
C    equivalent character representation.
C
C    INPUT --- TYPE : Array of types to be translated
C    OUTPUT --- WTYPE : Character array containing the translated types
C
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*4   TAB_SET               ! Table number
C
C -- For each element in the TYPE array, assign its character equivalent
C    depending on its value.
C
      IF(TAB_SET.GT.0) THEN
C
C       Replace the type in one of the stored table
C       -------------------------------------------
        DO I = 1,HMSIZ(TAB_SET)
          IF(WAVETYPE(TAB_SET,I) .EQ. 1) THEN
            WTYPE(I) = 'SIN'
          ELSE IF (WAVETYPE(TAB_SET,I) .EQ. 2) THEN
            WTYPE(I) = 'SQU'
          ELSE IF (WAVETYPE(TAB_SET,I) .EQ. 3) THEN
            WTYPE(I) = 'TRI'
          ELSE IF (WAVETYPE(TAB_SET,I) .EQ. 4) THEN
            WTYPE(I) = 'SAW'
          ELSE
            WTYPE(I) = '   '
          ENDIF
         ENDDO
      ELSEIF(TAB_SET.LT.0) THEN
C
C       Replace the type in the working table
C       -------------------------------------
        DO I = 1,TMP_HMSZ
          IF(TEMP_TYPE(I) .EQ. 1) THEN
            WTYPE(I) = 'SIN'
          ELSE IF (TEMP_TYPE(I) .EQ. 2) THEN
            WTYPE(I) = 'SQU'
          ELSE IF (TEMP_TYPE(I) .EQ. 3) THEN
            WTYPE(I) = 'TRI'
          ELSE IF (TEMP_TYPE(I) .EQ. 4) THEN
            WTYPE(I) = 'SAW'
          ELSE
            WTYPE(I) = '   '
          ENDIF
         ENDDO
      ENDIF
C
      RETURN
      END
C
C     ==================
      SUBROUTINE DATA_IO
C     ==================
      IMPLICIT NONE
C
C     This subroutine has two entry points : REST_DATA restores data
C     from the data file to internal buffers, and SAVE_DATA saves
C     data from the internal buffers to the data file.
C     ---------------------------------------------------------------
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      INTEGER*2 START,
     &          CNT,
     &          STOP
C
      LOGICAL*1 PRESENT                ,! True if the data file is present
     &          NEW_REV                 ! Name ID and mod date field present
C
      INTEGER*2
     &          TYPE,
     &          TMP,                    ! Temporary variable
     &          TOTTBL                  ! Total number of tables
C
      INTEGER*2 DUMMY,MIN_ONE/-1/       ! Marker to indicate end of data type
C
      INTEGER*4
     & LLine4
C
      CHARACTER
     & SAVDATE*11,SAVTIME*11           ,! Date and time fields
     & EMPTY_READ*1                    ,! Dummy read of data line
     & ERRORD*80                       ,! Error on data file message
     & MESSA(6)*80                     ,! Data save error messages
     & CONFIRM*80                      ,! Confirm data save message
     & QUEST*46                         ! Question for error data save handling
C
      CHARACTER FMAT*2,                 ! Character strings used for format
     &          FMAT1*14,
     &          FMAT2*16,
     &          FMAT31*16,
     &          FMAT3*14,
     &          FMAT4*16,
     &          FMAT5*16,
     &          DATRESTOR*80           ,! Message:RESTORE DATA FILE
     &          DATEMPTY*80            ,! Message:DATA FILE EMPTY
     &          CREATMES*80             ! Message:CREATE NEW DATA TABLE
      CHARACTER*80
     & CHARFIRST*23           ,!First line of data file read
     & ERROR1                  !Various error messages
     &,ERROR2                  !
C
      INTEGER*4
     &          IOERR                   ! IO routines error
C
      INTEGER*2 HAFSRC,                 ! Half the number of sources
     &          INTHARM(10),            ! Old data file Harmonic number in integer
     &          SAVERR,                 ! IO routines error
     &          NUM                     ! Indicates number of harmonics or others
C
      INCLUDE 'hardata.inc'
C
      DATA CONFIRM /'%SAVE_DATA : Data saved in the data file
     &                            '/
      DATA DATEMPTY /'%REST_DATA: Data file
     &                             '/
      DATA   ERRORD /'            ***   ERROR ON DATA FILE: #        ***
     &                             '/
      DATA  CREATMES/'%REST_DATA: Creating the new DATA file
     &                             '/
      DATA DATRESTOR/'%REST_DATA: Restore DATA from file
     &                             '/
      DATA MESSA    /'                              *** CANNOT SAVE DATA
     & ***                         ',
     &               '
     &                             ',
     &               '       An error as ocurred during save DATA operat
     &ion with HARMONY.            ',
     &               '            No data will be saved if you quit the
     & utility NOW.                ',
     &               '    You can return to HARMONY main menu and use ot
     &her terminal to correct      ',
     &               '    the problem if possible (Answer NO ) or quit w
     &ith unsaved session (YES).   ' /
      DATA QUEST    /' Do you want to QUIT the utility (YES or NO)? '/
      DATA ERROR1   /'%REST_DATA: Error when reading DATA file - Some da
     &ta invalid'/
      DATA ERROR2   /'%REST_DATA: Data reinitialize to zero: no DATA res
     &tore'/
C
C
      ENTRY SAVE_DATA(*,SAVERR)
C     =======================
C
C -- This routine saves the all the data from the internal buffers to the
C    data file
C
      SAVERR = 0
      CONFIRM(43:42+L_FILDAT) = FILE_DATA(1:L_FILDAT)
C
C     If data not change, don't save it!
C
      IF(.NOT.DATASAVE) RETURN
C
C     If the data file exists then open it.
C     -------------------------------------
      MESSA(1)(1:4) = ESC//'[1m'
      MESSA(1)(77:80) = ESC//'[0m'
      HAFSRC = MAXI_SOUR/2
      CALL FIL_OPEN(8,4,IOERR)
      IF(IOERR.NE.0) GOTO 901
C
C     Identify the data file of this utility 
C     --------------------------------------
      WRITE(DAT_UNIT,'(1H#,/,A,F5.2,A,/,1H#)',ERR=901,IOSTAT=IOERR) 
     &  '# HARMONY data file Version ',REV_LEVEL,
     &  ' ; Release 1.2 of data format'
C
C     Determine the total number of tables to be stored.  This is
C     TBLNUM ( number of tables )
C     -----------------------------------------------------------
      TOTTBL = TBLNUM
      IF (TOTTBL .GT. 0) THEN
        WRITE(DAT_UNIT,105,ERR=901,IOSTAT=IOERR) TOTTBL
C
C       For each table, save it only if its associated table number is not
C       equal to zero.  If it is zero, it means that the table had been
C       deleted.
C       ------------------------------------------------------------------ 
        DO I = 1,TBLNUM
         IF (SAV_TBL(I) .NE. 0) THEN
          WRITE(DAT_UNIT,106,ERR=901,IOSTAT=IOERR) SAV_TBL(I)
          K = SAV_TBL(I)
          WRITE(DAT_UNIT,500,ERR=901,IOSTAT=IOERR) TITLE(K)
          WRITE(DAT_UNIT,400,ERR=901,IOSTAT=IOERR)
     &              MODATE(K),NAME(K)
C
C         Each table contains either OTHER types or harmonics.  
C         Save accordingly.
C         ----------------------------------------------------
          IF (HMSIZ(K) .GT. 0) THEN
C
C          Harmonics type table
C          --------------------
           WRITE(DAT_UNIT,998,ERR=901,IOSTAT=IOERR) 4,
     &                              ' * Wave table type : real'
           WRITE(DAT_UNIT,250,ERR=901,IOSTAT=IOERR) K,TABSIZE(K)
           WRITE(DAT_UNIT,200,ERR=901,IOSTAT=IOERR) PHASEL(K)
           WRITE(DAT_UNIT,200,ERR=901,IOSTAT=IOERR) HMSIZ(K)
           CNT = 0
           DO WHILE(CNT.LT.HMSIZ(K))
            START = CNT+1
            STOP  = MIN(CNT+10,HMSIZ(K)+0)
            IF (STOP.LT.10) THEN
              WRITE(FMAT,'(I1)') STOP-CNT
              FMAT1 = '(1X,'//FMAT(1:1)//'(I3,3X))'
              FMAT2 = '(1X,'//FMAT(1:1)//'(F6.2))'
              FMAT31 = '(1X,'//FMAT(1:1)//'(F6.2))'
            ELSE
              WRITE(FMAT,'(I2)') STOP-CNT
              FMAT1 = '(1X,'//FMAT(1:2)//'(I3,3X))'
              FMAT2 = '(1X,'//FMAT(1:2)//'(F6.2))'
              FMAT31 = '(1X,'//FMAT(1:2)//'(F6.2))'
            ENDIF
            WRITE(DAT_UNIT,FMAT1,ERR=901,IOSTAT=IOERR) (WAVETYPE(K,J),
     &                                              J=START,STOP)
            WRITE(DAT_UNIT,FMAT2,ERR=901,IOSTAT=IOERR)
     &                              (HARM(K,J),J=START,STOP)
            WRITE(DAT_UNIT,FMAT31,ERR=901,IOSTAT=IOERR)
     &                              (AMPL(K,J),J=START,STOP)
            CNT = CNT+10
           ENDDO
          ELSE IF (OTHSZ(K) .GT. 0) THEN
C
C          Breakpoints type table
C          ----------------------
           WRITE(DAT_UNIT,998,ERR=901,IOSTAT=IOERR) 2,
     &                                     ' * Breakpoint table type '
           WRITE(DAT_UNIT,250,ERR=901,IOSTAT=IOERR) K,TABSIZE(K)
           WRITE(DAT_UNIT,200,ERR=901,IOSTAT=IOERR) OTHSZ(K)
           CNT = 0
           DO WHILE(CNT.LT.OTHSZ(K))
            START = CNT+1
            STOP  = MIN(CNT+10,OTHSZ(K)+0)
            IF (STOP.LT.10) THEN
              WRITE(FMAT,'(I1)') STOP-CNT
              FMAT3 = '(1X,'//FMAT(1:1)//'(I2,4X))'
              FMAT2 = '(1X,'//FMAT(1:1)//'(F5.1,1X))'
            ELSE
              WRITE(FMAT,'(I2)') STOP-CNT
              FMAT3 = '(1X,'//FMAT(1:2)//'(I2,4X))'
              FMAT2 = '(1X,'//FMAT(1:2)//'(F5.1,1X))'
            ENDIF
            WRITE(DAT_UNIT,FMAT3,ERR=901,IOSTAT=IOERR)
     &                         (OTHP(K,J),J=START,STOP)
            WRITE(DAT_UNIT,FMAT2,ERR=901,IOSTAT=IOERR)
     &                         (OTHX(K,J),J=START,STOP)
            WRITE(DAT_UNIT,FMAT2,ERR=901,IOSTAT=IOERR)
     &                         (OTHY(K,J),J=START,STOP)
             CNT = CNT+10
           ENDDO
          ELSEIF (EXTBLN(K).GT.0) THEN
C
C          External type table
C          -------------------
           WRITE(DAT_UNIT,998,ERR=901,IOSTAT=IOERR) 3,
     &                               ' * External table type  '
           WRITE(DAT_UNIT,250,ERR=901,IOSTAT=IOERR) K,TABSIZE(K)
           WRITE(DAT_UNIT,200,ERR=901,IOSTAT=IOERR) EXTBLN(K)
           FMAT5 = '(8(F9.2,1X))'
           DO II = 1,TABSIZE(K),8
              TEMPI = II+7
              WRITE(DAT_UNIT,FMAT5,ERR=901,IOSTAT=IOERR)
     &              (EXTP(EXTBLN(K),J),J=II,TEMPI)
           ENDDO

         ENDIF
C
C        Put a -1 to indicate the end of a table
C        ---------------------------------------
         WRITE(DAT_UNIT,200,ERR=901,IOSTAT=IOERR) MIN_ONE
C
         ENDIF
        ENDDO
C
C       Save the source/table associations and close the file
C       -----------------------------------------------------
        CALL SRC_SAVE(IOERR,*901)
      ELSE
C
C       If there are no tables to be saved, then set empty data file
C       ------------------------------------------------------------
        TOTTBL = 0
        WRITE(DAT_UNIT,105,ERR=901,IOSTAT=IOERR) TOTTBL
C
      ENDIF
      CALL FIL_OPEN(8,2,IOERR)
      CALL ERR_MESS(CONFIRM,42+L_FILDAT,0,*201)
 201  CONTINUE
      DATASAVE = .FALSE.
      SAVERR=IOERR
      RETURN
C
C     SPECIAL CODE IF AN ERROR OCCUR DURING DATA SAVING
C     -------------------------------------------------
 901  <USER> <GROUP>
      CALL BEEP(3)
      DO I = 1,6
         CALL Term_Write(9+I,1,MESSA(I),80)
      ENDDO
      CALL GET_ERR_STR(IOERR,ERRORD(40:44))
      CALL ERR_MESS(ERRORD,80,-1,*53)
 53   CONTINUE
      DO WHILE(INPLINE(1:2).NE.'NO'.AND.INPLINE(1:3).NE.'YES')
         CALL Term_Write(20,1,QUEST,46)
         CALL Term_Read(-1,INPLINE,LLine4,IOERR)
         LLINE = LLine4
      ENDDO
      CALL FIL_OPEN(8,3,IOERR) !Delete DATA file
      IF(INPLINE(1:3).EQ.'YES')THEN
        SAVERR = 1
        RETURN
      ELSE
        SAVERR = IOERR
        RETURN 1
      ENDIF
C     ================
C     END OF SAVE_DATA
C     ================
C
      ENTRY REST_DATA
C     ===============
C
C     This routine restores the data from the file Harmony.dat to the
C     appropriate internal buffers.
C
C     Inquire if the data file is present.  If it exists, then restore the
C     data from the previous session of harmony.  Otherwise, create
C     a new data file.
C
      CREATMES(40:39+L_FILDAT) = FILE_DATA(1:L_FILDAT)
      DATEMPTY(23:80) = BLANK(23:80)
      DATEMPTY(23:22+L_FILDAT) = FILE_DATA(1:L_FILDAT)
      DATEMPTY(23+L_FILDAT:28+L_FILDAT) = ' EMPTY'
      DATRESTOR(36:35+L_FILDAT) = FILE_DATA(1:L_FILDAT)
C
      CALL DATFIL(PRESENT)!Look if the DATA file exist
      IF(PRESENT)THEN
         CALL FIL_OPEN(8,1,IOERR)
      ELSE
         CALL MES23(0,CREATMES)
         W_LONG = 2.0
         CALL Wait_Time(W_LONG)
         CALL MES23(1,CREATMES)
         RETURN
      ENDIF
C
      HAFSRC = MAXI_SOUR/2
C
C     Read the number of tables
C     -------------------------
      READ(DAT_UNIT,'(A23)',ERR=101) CHARFIRST
      IF (CHARFIRST(1:1).EQ.'#') THEN
          NEW_REV=.TRUE.
C
C         Read # characters (3 lines) for new identification strings
C         ----------------------------------------------------------
          READ(DAT_UNIT,'(A1)',ERR=101) EMPTY_READ
          READ(DAT_UNIT,'(A1)',ERR=101) EMPTY_READ
          READ(DAT_UNIT,108,ERR=101) TBLNUM
      ELSE  
          READ(CHARFIRST,108,ERR=101) TBLNUM
          NEW_REV = .FALSE. 
      ENDIF
C
C     Restore the contents of each table
C     ----------------------------------
      IF(TBLNUM.GT.0)THEN
      DO MM = 1,TBLNUM
        READ(DAT_UNIT,109,ERR=101 ) SAV_TBL(MM)
        K = SAV_TBL(MM)
        READ(DAT_UNIT,501,ERR=101 ) TITLE(K)
C
        IF (NEW_REV) THEN
           READ(DAT_UNIT,401,ERR=101) MODATE(K),NAME(K)
        ELSE
           CALL CDATE(SAVDATE,SAVTIME)
           MODATE(K) = SAVDATE//' '//SAVTIME(1:5)
           CALL GET_USER
           NAME(K) = USER_ID
        ENDIF
C
        READ(DAT_UNIT,999,ERR=101 ) TYPE
        READ(DAT_UNIT,250,ERR=101 ) NUM,TABSIZE(K)
C
C       Add a new type = 4 for harmonic number in real
C
        IF (TYPE .EQ. 1.OR.TYPE.EQ.4) THEN
          READ(DAT_UNIT,200,ERR=101 ) PHASEL(K)
          READ(DAT_UNIT,200,ERR=101) HMSIZ(K)
          CNT = 0
          DO WHILE(CNT.LT.HMSIZ(K))
            START = CNT+1
            STOP  = MIN(CNT+10,HMSIZ(K)+0)
            IF (STOP.LT.10) THEN
              WRITE(FMAT,'(I1)',ERR=101) STOP-CNT
              IF (TYPE.EQ.1) THEN
                 FMAT1 = '(1X,'//FMAT(1:1)//'(I3,3X))'
                 FMAT2 = '(1X,'//FMAT(1:1)//'(I3,3X))'
                 FMAT31= '(1X,'//FMAT(1:1)//'(F5.1,1X))'
              ELSE
                 FMAT1 = '(1X,'//FMAT(1:1)//'(I3,3X))'
                 FMAT2 = '(1X,'//FMAT(1:1)//'(F6.2))'
                 FMAT31= '(1X,'//FMAT(1:1)//'(F6.2))'
              ENDIF
            ELSE
              WRITE(FMAT,'(I2)',ERR=101) STOP-CNT
              IF (TYPE.EQ.1) THEN
                 FMAT1 = '(1X,'//FMAT(1:2)//'(I3,3X))'
                 FMAT2 = '(1X,'//FMAT(1:2)//'(I3,3X))'
                 FMAT31= '(1X,'//FMAT(1:2)//'(F5.1,1X))'
              ELSE
                 FMAT1 = '(1X,'//FMAT(1:2)//'(I3,3X))'
                 FMAT2 = '(1X,'//FMAT(1:2)//'(F6.2))'
                 FMAT31= '(1X,'//FMAT(1:2)//'(F6.2))'
              ENDIF 
            ENDIF
            READ(DAT_UNIT,FMAT1,ERR=101 ) (WAVETYPE(K,J),J=START,STOP)
            IF (TYPE.EQ.4) THEN 
               READ(DAT_UNIT,FMAT2,ERR=101 ) (HARM(K,J),J=START,STOP)
            ELSE
               READ(DAT_UNIT,FMAT2,ERR=101 ) (INTHARM(J),J=START,STOP)
               DO JJ=START,STOP
                  HARM(K,JJ) = INTHARM(JJ)
               ENDDO   
            ENDIF 
            READ(DAT_UNIT,FMAT31,ERR=101 ) (AMPL(K,J),J=START,STOP)
            CNT = CNT+10
          ENDDO
        ELSE IF (TYPE.EQ.2) THEN
          READ(DAT_UNIT,200,ERR=101) OTHSZ(K)
          CNT = 0
          DO WHILE(CNT.LT.OTHSZ(K))
            START = CNT+1
            STOP  = MIN(CNT+10,OTHSZ(K)+0)
            IF (STOP.LT.10) THEN
              WRITE(FMAT,'(I1)',ERR=101) STOP-CNT
              FMAT3 = '(1X,'//FMAT(1:1)//'(I2,4X))'
              FMAT2 = '(1X,'//FMAT(1:1)//'(F5.1,1X))'
            ELSE
              WRITE(FMAT,'(I2)',ERR=101) STOP-CNT
              FMAT3 = '(1X,'//FMAT(1:2)//'(I2,4X))'
              FMAT2 = '(1X,'//FMAT(1:2)//'(F5.1,1X))'
            ENDIF
            READ(DAT_UNIT,FMAT3,ERR=101 ) (OTHP(K,J),J=START,STOP)
            READ(DAT_UNIT,FMAT2,ERR=101 ) (OTHX(K,J),J=START,STOP)
            READ(DAT_UNIT,FMAT2,ERR=101 ) (OTHY(K,J),J=START,STOP)
            CNT = CNT + 10
          ENDDO
       ELSE IF (TYPE.EQ.3) THEN
          EXT_NUM = EXT_NUM + 1
          READ(DAT_UNIT,200,ERR=101) EXTBLN(K)
          FMAT5 = '(8(F9.2,1X))'
          DO II=1 ,TABSIZE(K),8
              TEMPI = II+7
              READ(DAT_UNIT,FMAT5,ERR=101)
     &              (EXTP(EXTBLN(K),J),J=II,TEMPI)
          ENDDO
          EXTSIZ(EXTBLN(K)) = TABSIZE(K)
       ENDIF
       READ(DAT_UNIT,200,ERR=101 ) DUMMY           !INDICATES NEXT REC IS TABLE NUM
      ENDDO
C
C -- Read the source/table associations and close the file
C
      CALL SRC_READ(NEW_REV,IOERR,*101)
      CALL MES23(0,DATRESTOR)
      W_LONG = 3.0
      CALL Wait_Time(W_LONG)
      CALL MES23(1,BLANK)
      ELSE
        CALL MES23(0,DATEMPTY)
        W_LONG = 3.0
        CALL Wait_Time(W_LONG)
        CALL MES23(1,BLANK)
      ENDIF
      CALL FIL_OPEN(8,2,IOERR)
      RETURN
C
C -- If there is an error in read, then print error message, and close the
C    file
C
101   CALL BEEP(1)
      CALL MES23(0,ERROR1)
      W_LONG = 4.0
      CALL Wait_Time(W_LONG)
      CALL BEEP(1)
      CALL MES23(0,ERROR2)
      W_LONG = 4.0
      CALL Wait_Time(W_LONG)
      CALL FIL_OPEN(8,2,IOERR)
      CALL INITIALIZE  !Reinitialize with nothing, better than garbage
      RETURN

100   FORMAT(I4,2X,I4)
105   FORMAT('Number of table is ',I4)
106   FORMAT('Table number ',I4)
108   FORMAT(T20,I4)
109   FORMAT(T14,I4)
200   FORMAT(I4)
250   FORMAT(I4,I4)
998   FORMAT(' ',I2,A25)
999   FORMAT(T2,I2)
400   FORMAT(' ',A17,';',A34)
401   FORMAT(T2,A17,1X,A34) 
500   FORMAT(' ',A30)
501   FORMAT(T2,A30)
      END
C
C     =================================
      SUBROUTINE TAB_EXIST(TBNUM,FOUND)
C     =================================
      IMPLICIT NONE

C -- This subroutine determines whether the table number TBNUM exists
C    in the set of tables that are stored.
C
C    INPUT : TBNUM -- Table number
C    OUTPUT : FOUND -- Flag indicating whether table exists
C
      INCLUDE 'harparm.inc'

      INTEGER*2 TBNUM             ! Table number
      LOGICAL*1 FOUND             ! True if table is found

      INCLUDE 'hardata.inc'

C -- Search for the table number to be saved in the list of table
C    numbers already saved and set the FOUND flag accordingly.

      I = 1
      FOUND = .FALSE.
      IF(TBLNUM.GT.0) THEN
        DO WHILE (I .LE. TBLNUM .AND. .NOT. FOUND)
          IF (SAV_TBL(I) .EQ. TBNUM) THEN
             FOUND = .TRUE.
          ELSE
             I = I + 1
          ENDIF
        ENDDO
      ENDIF
      RETURN
      END
C
C
C ================================================================
C                         MES23
C ================================================================
C
C   This routine write a message (or erase) on line 23 in
C   reverse video mode.
C
      SUBROUTINE MES23(MODE,MESSAGE)
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER*80 MESSAGE
      INTEGER*4 MODE
C
C     Set reverse video mode
C     ----------------------
CCCC      CALL Term_Write(-1,1,REV_STRT,4)
      CALL Start_Reverse
C
      IF(MODE.EQ.1)THEN
C
C       Erase any message on line 23
C       ----------------------------
        CALL Term_Write(24,1,BLANK,80)
      ELSE
C
C       Print the message on line 23
C       ----------------------------
        CALL Term_Write(24,1,MESSAGE,80)
      ENDIF
C
C     Return to normal mode
C     ---------------------
CCCCC      CALL Term_Write(-1,1,REV_END,5)
      CALL Stop_Reverse
      CALL Term_Write(21,1,SEND,0)
C
      RETURN
      END
C
C     ===================================
      SUBROUTINE ERR_MESS(MESS,LEN,NUM,*)
C     ===================================
      IMPLICIT NONE
C
C     This subroutine displays an error message MESS of length LEN on
C     the second reverse video mode line in the menu.  It then beeps
C     the number of times indicated by the absolute value of NUM.  If
C     NUM is less than 0, then it does not return to the statement
C     indicated in *.  Otherwise, the subroutine returns to the label *
C     in the calling program.
C     -----------------------------------------------------------------
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER*(*)MESS,              ! Message to be displayed
     &             CMP_MES*80         ! Complete message
C
      INTEGER*4 NUM,                  ! Number of times to beep
     &          NNUM,                 ! Absolute value of NUM
     &          LEN                   ! Length of character string
C
      NNUM = ABS(NUM)
C
C -- Blank out message string and obtain message to be displayed
C
      CMP_MES = BLANK
      CMP_MES = MESS(1:LEN)
C
C     Display message, beep NNUM times, wait 2 seconds and clear
C     message line
C     ----------------------------------------------------------
      CALL MES23(0,CMP_MES)
      CALL Beep(NNUM)
      CALL Wait_Time(3.0)
      CALL MES23(0,BLANK)
C
C     If the number is positive, then return to the statement indicated
C     by * in the calling program.
C     -----------------------------------------------------------------
      IF (NUM .LT. 0) THEN
        RETURN
      ELSE
        RETURN1
      ENDIF
C
      END
C
C
C =========================================================================
C                               VALIDATA
C =========================================================================
C
      SUBROUTINE VALIDATA(CARNUM1,ERRFLAG)
C
       LOGICAL*1
     & ERRFLAG         !Error flag
C
       CHARACTER
     & CARNUM1*3       !Number in character
C
       INTEGER*2
     & ASCII           !Ascii number of the character
C
      DO II=1,3
        ASCII = ICHAR(CARNUM1(II:II))
        IF((ASCII.GT.57.OR.ASCII.LT.48).AND.
     &    (ASCII.NE.32.AND.ASCII.NE.0) )THEN
          ERRFLAG=.TRUE.
        ENDIF
      ENDDO
C
      RETURN
      END
C
C
C =====================================================================
C                            READLOG
C =====================================================================
C
C    This subroutine read the HARMONY log file
C
      SUBROUTINE READLOG(MODE,IERR)
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*2 
     & TMP_SLOTI(MAX_DSG),
     & TMP_TYPEI(MAX_DSG), 
     & SLOT,
     & III,
     & II2, 
     & POS_FDOT
C
      INTEGER*2 
     & FAIL_OPEN
C
      INTEGER*4
     & POS,
     & MODE,
     & RevStat,
     & LLine4
C
      LOGICAL*4 
     & PRESENT
C
      LOGICAL*1
     & VALID,
     & VALID_FLAG,       !Record of on site flags difference
     & REC_ONSITE,       !Recovered site status  
     & SKIP1
C
      CHARACTER*3
     & YESTR /'YES'/
C
      CHARACTER
     & TMP_SLOT(MAX_DSG)*2,
     & TMP_TYPE(MAX_DSG)*4, 
     & FATAL*80,
     & FATAL1*80,        !More than one fail to work on HARMONY.CNF message
     & FATAL2*80,
     & FATAL3*80,
     & NODOT*80,
     & TOOMANY*80,
     & DSGERR*80,
     & SLOTERR*80,
     & SAMESLOT*80,
     & DSG_CAR*2,
     & DSGTYP*5,
     & LOGNAME*255,      !HARMONY.CNF complete filename
     & N_LOGNAME*255,
     & FMT1*2,           !
     & FMT2*40,          !
     & TEMP_STR*60       !
C
       DATA TOOMANY  /'%CONFIG-E-TOOMANY, Too many characters in the fil
     &ename(11 or 12 with &)'/
       DATA NODOT    /'%CONFIG-E-NODOT, No dot found in the data file na
     &me, reenter again     '/
       DATA SLOTERR  /'%CONFIG-E-RANGE, slot number outside valid range
     & (1 to 27).            '/
       DATA SAMESLOT /'%CONFIG-E-USED, slot number already used by other
     & DSG. Try again!       '/
       DATA DSGERR   /'%CONFIG-E-MAXDSG, maximum number of DSG boards is
     & 5.                    '/
       DATA FATAL    /'%CONFIG-E-FATAL, fatal error on HARMONY.CNF.
     &                      '/
       DATA FATAL1   /'%CONFIG-E-WRONG, Something wrong with HARMONY.CNF
     & : session aborted.   '/
       DATA FATAL2   /'%CONFIG-E-RECOVER, fatal error when recovering pa
     &rameters.             '/
       DATA FATAL3   /'%CONFIG-E-FATAL,   fatal error while writing to c
     &onfiguration file     '/
C
      SKIP1 = .FALSE.
      FAIL_OPEN=0
      IERR = 0
      LOGNAME = Config_String(1)(1:Config_Length(1))//'harm'//
     &          Filetters(1:3)//'.cnf'
C
C
      IF (MODE.EQ.1) THEN
C
C        If mode is 1, delete HARMONY.CNF file and create a new one
C        ---------------------------------------------------------- 
         CALL FIL_OPEN(11,2,IERR)
         IF (IERR.EQ.0) THEN
           CALL FIL_OPEN(11,4,IERR)
         ENDIF
C
      ELSEIF(MODE.EQ.3) THEN
C
C          If mode is 3, then only display on the screen
C          ---------------------------------------------
           POS = 7
           CALL CL_DISP
           SEND='HARMONY current session parameters restored are'
           CALL Term_Write(POS,1,SEND,47)
           POS = POS + 1
           SEND='-----------------------------------------------'
           CALL Term_Write(POS,1,SEND,47)
           POS = POS + 2
C
C          Print the parameters recovered from HARMONY configuration file
C          -------------------------------------------------------------- 
           SEND='DMC number : '//BRT_STRT//DMC(1:2)//BRT_END
           CALL Term_Write(POS,1,SEND,22+2)
           POS = POS + 1
           DO II=1,DSG_NUMB 
             SEND='Slot number of DSG #  '//
     &          ' : '//BRT_STRT//'XA'//SL_NB(II)//BRT_END
             WRITE(SEND(21:22),'(I2.2)') II
             SEND(45:67)='Type is: '//BRT_STRT//
     &       DSGTYP_NAME(DSG_TYPE(II))(1:5)//BRT_END
             CALL Term_Write(POS,1,SEND,67)
             POS = POS + 1
           ENDDO
           SEND='Data file name : '//BRT_STRT//
     &           FILE_DATA(1:L_FILDAT)//BRT_END
           CALL Term_Write(POS,1,SEND,26+L_FILDAT)
           POS = POS + 1
           IF(LASER_FLAG) THEN
              SEND='IMAGEN Laser queue : '//BRT_STRT//
     &              LASERQ(1:LLASQ)//BRT_END
              CALL Term_Write(POS,1,SEND,30+LLASQ)
              POS = POS + 1
           ENDIF
           POS = POS + 1
C
C          Print the parameters recovered from XLINK.INF file
C          --------------------------------------------------
           PROJNAME(1:20) = Config_String(10)(1:20)
           SEND='Project name : '//BRT_STRT//PROJNAME(1:20)//BRT_END
           CALL Term_Write(POS,1,SEND,44)
           POS = POS + 1
           SEND='AOS directory : '//BRT_STRT//
     &           Config_String(1)(1:Config_Length(1))//BRT_END
           CALL Term_Write(POS,1,SEND,26+Config_Length(1))
           POS = POS + 1
           SEND='Data directory : '//BRT_STRT//
     &           DATA_DIR(1:L_DATA_DIR)//BRT_END
           CALL Term_Write(POS,1,SEND,37+L_DATA_DIR)
           POS = POS + 1
           SEND='Help directory : '//BRT_STRT//
     &           Config_String(6)(1:Config_Length(6))//BRT_END
           CALL Term_Write(POS,1,SEND,26+Config_Length(6))
           POS = POS + 2
C
C          Send the CDB open XREF signal to the user
C          -----------------------------------------
           SEND='CDB access channel for Real Time Load is '
           IF(RTD_FLAG) THEN
              SEND(42:61) = BRT_STRT//'OPEN       '//BRT_END
           ELSE
              SEND(42:61) = BRT_STRT//'UNAVAILABLE'//BRT_END
           ENDIF
           CALL Term_Write(POS,1,SEND,61)
           RETURN
C
C          Should come here only when major error 
C          --------------------------------------
 701       <USER> <GROUP>(FATAL2,80,-1,*65)
 65        CONTINUE
           RETURN
      ENDIF
C
C     Get the parameters used by HARMONY 
C     ----------------------------------
      CALL rev_curr(LOGNAME,N_LOGNAME,' ',.FALSE.,1,revstat)
      INQUIRE(FILE=N_LOGNAME,EXIST=PRESENT)
C
 250  CONTINUE
C
      IF (.NOT.PRESENT) THEN
        IF (.NOT.SKIP1) THEN
C
C        Create a new set of parameters from user input and store it
C        -----------------------------------------------------------
         CALL FIL_OPEN(11,1,IERR)
C
C        Clear screen only if new configuration required
C        -----------------------------------------------
         IF(MODE.EQ.1) THEN
             CALL CL_DISP
         ENDIF
         POS = 8
         SEND = '*** HARMONY CONFIGURATION FILE INPUT ***'
         CALL Term_Write(POS,1,SEND,41)
C
C        Number of DSGs
C        --------------
         VALID = .FALSE.
         DO WHILE(.NOT.VALID)
           POS = 10
           SEND = ' HARMONY: How many DSG in the chassis: '
 1001      CALL Term_Write(POS,1,SEND,39)
           CALL Term_Read(0,INPLINE,LLine4,IERR)
           LLINE = LLine4
           IF(LLINE.EQ.0) THEN
              DSG_NUMB = 1
           ELSE
              READ(INPLINE(1:LLINE),'(I1)',ERR=1001) DSG_NUMB
              VALID = .TRUE.
              IF(DSG_NUMB.LE.0) THEN
                 DSG_NUMB=1
              ELSEIF(DSG_NUMB.GT.MAX_DSG) THEN
                 CALL ERR_MESS(DSGERR,80,-1,*72)
 72              CONTINUE
                 VALID = .FALSE.
              ENDIF
           ENDIF
         ENDDO
         WRITE(INF_UNIT,100,ERR=1000) DSG_NUMB
         POS = POS+1
C
C        All DSG slot number
C        -------------------
         DO II=1,DSG_NUMB
            VALID = .FALSE.
            DO WHILE(.NOT.VALID)
              WRITE(DSG_CAR,'(I2)',ERR=55) II
              SEND(1:79) = '                                           
     &                                   '
              CALL Term_Write(POS,1,SEND,79)
              SEND = ' HARMONY: Slot number (5 to 23) of DSG#   : '
              SEND(40:41)= DSG_CAR
 55           CALL Term_Write(POS,1,SEND,44)
              CALL Term_Read(0,INPLINE,LLine4,IERR)
              LLINE = LLine4
              TMP_SLOT(II)=INPLINE(1:2)
              IF(TMP_SLOT(II).EQ.'  ') THEN
                 SLOT = 20
                 TMP_SLOT(II) = '20'
              ELSE
                 READ(TMP_SLOT(II),'(I2)',ERR=55) SLOT
                 WRITE(TMP_SLOT(II),'(I2.2)',ERR=55) SLOT
              ENDIF
              IF(SLOT.LT.5.OR.SLOT.GT.23) THEN
               CALL ERR_MESS(SLOTERR,80,-1,*56)
 56            CONTINUE
              ELSE
               VALID = .TRUE.
              ENDIF
              III = II - 1
              DO WHILE(III.GE.1)
                 IF (TMP_SLOT(II).EQ.TMP_SLOT(III)) THEN
                    CALL ERR_MESS(SAMESLOT,80,-1,*73)
 73                 CONTINUE
                    VALID = .FALSE.
                 ENDIF
                 III = III - 1
              ENDDO
            ENDDO
            TMP_SLOTI(II) = SLOT
C
C           Ask for DSG type
C           ----------------
            VALID = .FALSE.
            DO WHILE(.NOT.VALID)
               SEND = 'DSG type (TONE or OTHER): '
               CALL Term_write(POS,49,SEND,26)
               CALL Term_Read(0,INPLINE,LLine4,IERR)
               LLINE = LLine4
               VALID = .TRUE.
               IF (INPLINE(1:4).EQ.'TONE') THEN
                  TMP_TYPEI(II) = 1
                  TMP_TYPE(II) = 'TONE'
               ELSE IF (INPLINE(1:5).EQ.'OTHER') THEN
                  TMP_TYPEI(II) = 2
                  TMP_TYPE(II) = 'OTHER'
               ELSE
                  VALID = .FALSE.
               ENDIF
            ENDDO
         ENDDO
C
C        Sort all slot numbers in ascending order and store
C        --------------------------------------------------
         CALL SORT_SLOT(TMP_SLOTI,SL_NBI,TMP_TYPEI,DSG_NUMB)
         DO II2 = 1,DSG_NUMB
            WRITE(SL_NB(II2),'(I2.2)',ERR=60) SL_NBI(II2)
 60         CONTINUE
            WRITE(INF_UNIT,110,ERR=1000) II2,SL_NB(II2)
            IF (DSG_TYPE(II2).EQ.1) THEN
                WRITE(INF_UNIT,115,ERR=1000) II2,'TONE'
            ELSE IF (DSG_TYPE(II2).EQ.2) THEN
                WRITE(INF_UNIT,116,ERR=1000) II2,'OTHER'
            ELSE
                CALL ERR_MESS(FATAL,80,-1,*74)
 74             CONTINUE
            ENDIF
         ENDDO            
C
         POS = POS+1
C
        ENDIF       ! For SKIP1...
C
C        Data filename
C        -------------
         FILE_DATA = BLANK
         IF (.NOT.ON_SITE) THEN
           SEND = ' HARMONY: Data file name (Max 11 characters without v
     &ersion number or ''&'')'
           CALL Term_Write(POS,1,SEND,74)
           POS = POS+1
           SEND=  '          Put ''&'' as 1st character to ignore automa
     &tic substitution of simulator'
           CALL Term_Write(POS,1,SEND,70)
           POS = POS+1
           SEND=  '          simulator configuration letter'
           CALL Term_Write(POS,1,SEND,40)
           POS = POS+1
           SEND = '          Enter file name : '
 1004      CALL Term_Write(POS,1,SEND,28)
           CALL Term_Read(1,FILE_DATA,LLine4,IERR)
           LLINE = LLine4
C
           IF( (FILE_DATA(1:1).EQ.'&'.AND.LLINE.GT.13).OR.
     &       (FILE_DATA(1:1).NE.'&'.AND.LLINE.GT.11)     )THEN
             CALL ERR_MESS(TOOMANY,80,1,*2004)
           ENDIF
           L_FILDAT = LLINE
C
C          Check for a dot in the filename 
C          -------------------------------
           POS_FDOT=INDEX(FILE_DATA,'.')
           IF(POS_FDOT.LT.1) THEN
              CALL ERR_MESS(NODOT,80,1,*2004)
           ENDIF
         ELSE
           POS_FDOT = 8
           FILE_DATA=Config_String(9)(1:4)//'har.dat'
           LLINE = 11
           SEND = ' HARMONY: On site data file is set to '//FILE_DATA
           CALL Term_Write(POS,1,SEND,50)
           POS=POS+1
         ENDIF  
C
         L_FILDAT = LLINE
         WRITE(FMT1,'(I2)',ERR=2004) LLINE
         FMT2 ='('' '',''HARMONY data file name is '',A'//FMT1//')'
         WRITE(INF_UNIT,FMT2,ERR=1000) FILE_DATA(1:L_FILDAT)
         IF(FILE_DATA(1:1).EQ.'&') THEN
             FILE_DATA(1:L_FILDAT-1) = FILE_DATA(2:L_FILDAT)
             L_FILDAT = L_FILDAT-1
         ELSE
             TEMP_STR = FILE_DATA
             FILE_DATA(1:L_FILDAT+1) = TEMP_STR(1:POS_FDOT-1)//
     &                               Filetters(3:3)//
     &                               TEMP_STR(POS_FDOT:L_FILDAT)
             L_FILDAT = L_FILDAT+1
         ENDIF
C
C        Laser printer queue if on VAX
C        -----------------------------
         POS = POS+1
         IF(VAXSEL) THEN
            IF(ON_SITE)THEN
             SEND = ' HARMONY: Is an IMAGEN Laser Printer on this site [
     &N]? '
             CALL Term_Write(POS,1,SEND,55)
             POS = POS+1
             CALL Term_Read(0,INPLINE,LLine4,IERR)
             LLINE = LLine4
             IF(LLINE.NE.0.AND.INPLINE(1:LLINE).EQ.YESTR(1:LLINE)) THEN
                LASER_FLAG=.TRUE.
             ELSE
                LASER_FLAG=.FALSE.
             ENDIF
            ELSE
              LASER_FLAG = .TRUE.
            ENDIF
            IF(LASER_FLAG)THEN
             SEND = ' HARMONY: Laser printer queue : '
 1006        CALL Term_Write(POS,1,SEND,32)
             POS = POS+1
             CALL Term_Read(0,INPLINE,LLine4,IERR)
             LLINE = LLine4
            ELSE
             INPLINE(1:2) = 'NO'
             LLINE = 2
            ENDIF
         ELSE
           LASER_FLAG = .FALSE.
           INPLINE(1:2) = 'NO'
           LLINE = 2
         ENDIF
         LASERQ(1:LLINE)=INPLINE(1:LLINE)
         LLASQ = LLINE
         WRITE(INF_UNIT,150,ERR=2000) LLASQ,LASERQ(1:LLASQ)
C
C        Save the on site status flag
C        ----------------------------
         WRITE(INF_UNIT,160,ERR=2000) ON_SITE
C         
C        Send waiting message to user
C        ----------------------------
         SEND = ' %CONFIG-I-SAVE, Parameters are saved.'
         CALL Term_Write(POS+1,1,SEND,38)
         W_LONG = 2.0
         CALL Wait_Time(W_LONG)
      ELSE
C
C        Restore the parameters from the file
C        ------------------------------------
         CALL FIL_OPEN(11,2,IERR)
C
C        Number of DSG 
C        -------------
         READ(INF_UNIT,200,ERR=1000) DSG_NUMB
C
C        All the slot numbers of each DSG
C        --------------------------------
         DO II=1,DSG_NUMB
            READ(INF_UNIT,210,ERR=1000) SL_NB(II)
            READ(SL_NB(II),'(I2.2)') SL_NBI(II)
            READ(INF_UNIT,215,ERR=1000) DSGTYP
            IF (DSGTYP(1:4).EQ.'TONE') THEN
               DSG_TYPE(II) = 1
            ELSE
               DSG_TYPE(II) = 2
            ENDIF
         ENDDO
C
C        Data filename 
C        -------------
         READ(INF_UNIT,220,ERR=1000) FILE_DATA
         LLINE = 20
         DO WHILE((FILE_DATA(LLINE:LLINE).EQ.' '.OR.
     &            FILE_DATA(LLINE:LLINE).EQ.NUL).AND.LLINE.GT.0)
              LLINE=LLINE-1
         ENDDO
         L_FILDAT = LLINE
C
C        Replace the configuration letter if filename is not protected '&'
C        -----------------------------------------------------------------
         IF(FILE_DATA(1:1).NE.'&')THEN
           POS_FDOT=INDEX(FILE_DATA,'.')
           TEMP_STR = FILE_DATA
           FILE_DATA(1:L_FILDAT+1) = TEMP_STR(1:POS_FDOT-1)//
     &                               Filetters(3:3)//
     &                               TEMP_STR(POS_FDOT:L_FILDAT)
           L_FILDAT = L_FILDAT+1
         ELSE
           FILE_DATA(1:L_FILDAT-1) = FILE_DATA(2:L_FILDAT)
           L_FILDAT = L_FILDAT-1
         ENDIF
C
C        IMAGEN laser queue
C        ------------------
         READ(INF_UNIT,251,ERR=1000) LLASQ,LASERQ(1:LLASQ)
         IF(LASERQ(1:2).EQ.'NO'.AND.LLASQ.EQ.2) THEN
           LASER_FLAG=.FALSE.
         ELSE
           LASER_FLAG=.TRUE.
         ENDIF
C
C        Read the ON SITE status that was saved
C        --------------------------------------
         READ(INF_UNIT,260,ERR=1000) REC_ONSITE
C
C        If the site status has changed, redo configuration
C        --------------------------------------------------
         VALID_FLAG = REC_ONSITE.XOR.ON_SITE
         IF (VALID_FLAG) THEN
            CALL FIL_OPEN(11,4,IERR) !Delete the file
            PRESENT=.FALSE.
            GOTO 250
         ENDIF 
      ENDIF
C
C     Close the configuration file
C     ---------------------------- 
      CALL FIL_OPEN(11,3,IERR)
C
      RETURN
C
C     Retry I/Oing the file but not forever
C     -------------------------------------
1000  IF (FAIL_OPEN.LT.1) THEN
         FAIL_OPEN=FAIL_OPEN+1 
         CALL ERR_MESS(FATAL,80,-1,*57)
 57      CONTINUE
         CALL FIL_OPEN(11,4,IERR)
         PRESENT=.FALSE.
         GOTO 250
      ENDIF
      CALL ERR_MESS(FATAL1,80,-1,*58)
 58   CONTINUE
      IERR = 999
      RETURN 
C
 2000 CALL ERR_MESS(FATAL3,80,-1,*59)
 59   CONTINUE
      IERR = 99
      RETURN
C
 2004 SKIP1 = .TRUE.
      GOTO 250
C
100   FORMAT(' ','Number of DSG in the sound chassis is ',I2)
110   FORMAT(' ','Slot number of DSG#',I2,' is ',A2)
115   FORMAT(' ','Type for DSG#',I2,' is ',A4)
116   FORMAT(' ','Type for DSG#',I2,' is ',A5)
140   FORMAT(' ','Project name is ',A20)
150   FORMAT(' ','Laser Queue is L=',I2,' Q=',A20)
160   FORMAT(' ','The site status flag is ',L1)
200   FORMAT(T40,I2)
210   FORMAT(T27,A2)
215   FORMAT(T21,A5)
220   FORMAT(T28,A)
230   FORMAT(T18,A20)
251   FORMAT(T19,I2,T24,A20)
260   FORMAT(T26,L1)
      END
C
C
C     ==================================
      SUBROUTINE PRINT_HEAD(PAGE_C,UNIT)
C     ==================================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER
     &          INTERR*51,
     &          TIME*11,
     &          DATE*11
C
      INTEGER*2
     &          PAGE_C                     !Page count
C
      INTEGER*4
     &          UNIT                       !Output file unit
C
      INCLUDE 'hardata.inc'
C
      DATA INTERR/'%PRINTER - Internal error #      in routine PR_HEAD'/
C
      IF(PAGE_C.GT.1) THEN
        WRITE(UNIT,100,ERR=701,IOSTAT=IERR )        !Skip a page
      ELSE
        CALL CDATE(DATE,TIME)
      ENDIF
C
      WRITE(UNIT,200,ERR=701,IOSTAT=IERR )
      WRITE(UNIT,210,ERR=701,IOSTAT=IERR ) DATE,TIME,PAGE_C
      WRITE(UNIT,200,ERR=701,IOSTAT=IERR )
      WRITE(UNIT,101,ERR=701,IOSTAT=IERR )          !Skip one line
C
      GOTO 700
 701  WRITE(INTERR(28:32),'(I5)',ERR=702)
 702  CALL ERR_MESS(INTERR,51,-1,*700)
 700  CONTINUE
      RETURN
C
 100  FORMAT('1')
 101  FORMAT('  ')
 200  FORMAT(' ',79('='))
 210  FORMAT(' |  HARMONY OUTPUT LIST FILE | Date: ',A11,' Time: ',A8,
     &       '  |  Page ',I3,'   |')
C
      END
C
C     ===============================
      SUBROUTINE SET_SCROLL(VERT,HOR)
C     ===============================
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
C
      CHARACTER
     &   SCROLL*8                            ,!Scroll characters
     &   CPOS1*2                             ,!Line number in character
     &   CPOS2*2                              !Column number in character
C
      INTEGER*2
     &   VERT,                                ! Line number
     &   HOR                                  ! Column number
C
      INCLUDE 'hardata.inc'
C
      WRITE(CPOS1,'(I2.2)',ERR=999) VERT
      WRITE(CPOS2,'(I2.2)',ERR=999) HOR
      GOTO 998
 999  CPOS1='01'
      CPOS2='01'
 998  CONTINUE
      SCROLL = ESC//'['//CPOS1//';'//CPOS2//'r'
      CALL Term_Write(-1,1,SCROLL,8)
C
      RETURN
      END
C
C     =======================
      SUBROUTINE CONF(*,IERR)
C     =======================
C
      IMPLICIT NONE
C
      INCLUDE 'harparm.inc'
      INCLUDE 'hardisp.inc'
      INCLUDE 'hardata.inc'
C
      INTEGER*4 Stat
C
      LOGICAL*1 END_READ
C
      CHARACTER*80 PROMPT,MENU(13)*15
C
      DATA PROMPT /'CONF : CHANGE or LIST > '/
      DATA MENU(1)/'BOX'/
      DATA MENU(2)/'HELP'/
      DATA MENU(3)/'EXIT'/
      DATA MENU(4)/'X'/
      DATA MENU(5)/'QUIT'/
      DATA MENU(6)/'CHANGE'/
      DATA MENU(7)/'LIST'/
C
C     Ask for CHANGE or LIST
C     ----------------------
      END_READ=.FALSE.
C
C     Prompt the user for input, and branch to that routine which corresponds
C     to his selection.
C     -----------------------------------------------------------------------
      DO WHILE(.NOT.END_READ)
         CALL READ_COMMAND(-1,22,1,PROMPT,24,COMMAND,L_COM,Stat)
         CALL PARSE_COMMAND(COMMAND,L_COM,7,MENU,ITEM,IERR)
C
         IF(IERR.EQ.0) THEN
C
            IF(ITEM.GE.6) THEN
C
               IF(ITEM.EQ.6) THEN
                 CALL CL_DISP
                 CALL SAVE_DATA(*2222,IERR)
                 CALL READLOG(1,IERR)
                 IF(IERR.EQ.0) THEN
                   CALL READLOG(3,IERR)
                   IF(IERR.EQ.0) THEN
                     CALL INITIALIZE
                     CALL REST_DATA
                   ELSE
                     END_READ=.TRUE.
                   ENDIF
                 ELSE
                   END_READ=.TRUE.
                 ENDIF
               ELSEIF(ITEM.EQ.7) THEN
                 CALL READLOG(3,IERR)
               ENDIF
C
            ELSEIF(ITEM.EQ.1) THEN    !BOX command
               CALL MAIN_MENU(-1,.TRUE.)   !Display main menu
               CALL MODE_SET(-1)           !Display output mode
            ELSEIF(ITEM.EQ.2) THEN    !HELP command
               CALL HELP(33)                !Help asked
            ELSE                      !QUIT,EXIT or X command
               END_READ=.TRUE.
            ENDIF
         ENDIF
      ENDDO
C
      CALL CL_DISP
 2222 IF(IERR.EQ.0)THEN
        RETURN 1
      ELSE
        RETURN
      ENDIF
C
      END
C
C     ===============================
      SUBROUTINE ERR_CDB_ROUT(STATUS)
C     ===============================
C
      IMPLICIT NONE
C
      CHARACTER*47 MESSAGE(12),MESINV*49
      INTEGER*2 NUM_MES(12),I,L_MINV,INDEX
      INTEGER*4 STATUS
      LOGICAL*1 FOUND
C
      DATA MESINV /'%CDB_ACCESS: Error #     during CTS R/W operation'/
      DATA L_MINV/49/
      DATA NUM_MES /900,950,960,965,966,970,980,990,999,1001,1002,1003/
      DATA MESSAGE /'%CDB_ACCESS: Common Data Base file already open',
     &              '%CDB_ACCESS: File opening error for RTL process',
     &              '%CDB_ACCESS: File read error for RTL process   ',
     &              '%CDB_ACCESS: End of file                       ',
     &              '%CDB_ACCESS: End of file                       ',
     &              '%CDB_ACCESS: Index file content error          ',
     &              '%CDB_ACCESS: Label not found in the data base  ',
     &              '%CDB_ACCESS: Label file content error          ',
     &              '%CDB_ACCESS: Subroutine internal error         ',
     &              '%CDB_ACCESS: Invalid label index               ',
     &              '%CDB_ACCESS: Invalid type of label             ',
     &              '%CDB_ACCESS: Invalid register index            '/
C
      FOUND=.FALSE.
      I=1
      DO WHILE(.NOT.FOUND.AND.I.LE.11)
C
         IF(NUM_MES(I).EQ.STATUS) THEN
            FOUND=.TRUE.
            INDEX = I
         ENDIF
         I = I + 1
      ENDDO
C
      IF(FOUND)THEN
C
C       Error during CDB access label routines
C       --------------------------------------
        CALL ERR_MESS(MESSAGE(INDEX),47,-1,*58)
 58     CONTINUE
C
      ELSE
C
C       Invalid error...
C       ----------------
        CALL GET_ERR_STR(STATUS,MESINV(21:25))
        CALL ERR_MESS(MESINV,L_MINV,-1,*59)
 59     CONTINUE
      ENDIF
C
      RETURN
      END
C
C ======================================================================
C            SORT: Sort all the table according to their sizes
C ======================================================================
C
      SUBROUTINE SORT(CODE,INPVEC,OUTVEC,DIM)
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & MIN                ,!Minimum
     & DIM                ,!Dimension of the input/output vector
     & INDEX              ,!Max position index
     & POSI(200)          ,!Scratch vector
     & INPVEC(DIM)        ,!Input element array
     & OUTVEC(DIM)         !Output element array

C
       INTEGER*4
     & CODE                !Output mode
C
       LOGICAL*1
     & FOUND(200)          !Found element flags
C
C      Set all flags false before starting the sort
C      --------------------------------------------
       DO II = 1,DIM
          FOUND(II) = .FALSE.
       ENDDO
C
       IF(CODE.LE.3)THEN
         DO II=1,DIM
C
           MIN=32767
C
C          Find the minimum in the array but not found before
C          --------------------------------------------------
           DO JJ=1,DIM
              IF(INPVEC(JJ).LT.MIN.AND..NOT.FOUND(JJ)) THEN
                 MIN=INPVEC(JJ)
                 INDEX=JJ
              ENDIF
           ENDDO
C
           FOUND(INDEX) = .TRUE.
           POSI(II)=INDEX
         ENDDO
       ENDIF
C
       IF(CODE.EQ.1) THEN
C
C        Output array is in ascending order: contains the input array pointer
C        --------------------------------------------------------------------
         DO II=1,DIM
           OUTVEC(II)=POSI(II)
         ENDDO
       ELSEIF(CODE.EQ.2)THEN
C
C        Output array is in ascending order: contains the values of input
C        ----------------------------------------------------------------
         DO II=1,DIM
           OUTVEC(II)=INPVEC(POSI(II))
         ENDDO
       ELSEIF(CODE.EQ.3)THEN
C
C        Output array is in descending order: contains the input array pointer
C        ---------------------------------------------------------------------
         DO II=1,DIM
           OUTVEC(II)=POSI(DIM-II+1)
         ENDDO
       ELSEIF(CODE.EQ.4)THEN
C
          DO II=1,DIM
C
             MIN=-32768
C
C            Find the minimum in the array but not found before
C            --------------------------------------------------
             DO JJ=1,DIM
               IF(INPVEC(JJ).GT.MIN.AND..NOT.FOUND(JJ))THEN
                  MIN=INPVEC(JJ)
                  INDEX=JJ
               ENDIF
             ENDDO
C
             FOUND(INDEX) = .TRUE.
             OUTVEC(II)=INDEX
          ENDDO
C
       ENDIF
C
       RETURN
       END
C ======================================================================
C        SORT_SLOT: Sort all the slot numbers in ascending order
C ======================================================================
C
      SUBROUTINE SORT_SLOT(INPUT,OUTPUT,TYPE,DIM)
C
      IMPLICIT NONE
      INCLUDE 'harparm.inc'
      INCLUDE 'hardata.inc'
C
       INTEGER*2
     & MIN                ,!Minimum
     & DIM                ,!Dimension of the input/output vector
     & POINTER            ,!Max position index
     & INPUT(DIM)         ,!Input element array
     & OUTPUT(DIM)        ,!Output element array
     & TYPE(DIM)
C
       LOGICAL*1
     & FOUND(MAX_DSG)          !Found element flags
C
C      Set all flags false before starting the sort
C      --------------------------------------------
       DO II = 1,DIM
          FOUND(II) = .FALSE.
       ENDDO
C
       DO II=1,DIM
C
          MIN=27
C
C         Find the minimum in the array but not found before
C         --------------------------------------------------
          DO JJ=1,DIM
             IF(INPUT(JJ).LT.MIN.AND..NOT.FOUND(JJ)) THEN
                MIN=INPUT(JJ)
                POINTER=JJ
             ENDIF
          ENDDO
C
           FOUND(POINTER) = .TRUE.
           OUTPUT(II) = INPUT(POINTER)
           DSG_TYPE(II) = TYPE(POINTER)
         ENDDO
C
       RETURN
       END
C
