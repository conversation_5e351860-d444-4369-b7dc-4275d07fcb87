C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : AOSUTY for UNIX systems                                  **
C   **                                                                      **
C   **  Program  : AOSDAT.INC                                               **
C   **  Function : This is the include file for AOSUTY programs             **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0             G. De Serre                   05 July 1990      **
C   **  Rev 2.0             P. Daigle                     21 aug. 1990      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  None                                                                **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
      CHARACTER
     &  Filename*255, 
     &  Util_Dir*255,
     &  Blank*255
C
      CHARACTER 
     &  Common_Letter(Max_Config,2)*1,
     &  Common_Desc(Max_Config,2)*31,
     &  Audio_Letter(Max_Config)*1,
     &  Audio_Desc(Max_Config)*31,
     &  Sound_Letter(Max_Config)*1,
     &  Sound_Desc(Max_Config)*31,
     &  Logicals(Max_Logicals)*11,        ! Environement variables
     &  Conf_String(Max_C_Logicals)*255,
     &  Xlink_String(Max_C_Logicals)*255   
C
      INTEGER*2 
     &  Array_Index,
     &  Array(Max_Logicals),
     &  Common_Active(2),
     &  Audio_Active,
     &  Sound_Active,
     &  Common_Max_Config(2),
     &  Audio_Max_Config,
     &  Sound_Max_Config,
     &  Index,
     &  PassNum1,
     &  PassNum2,
     &  PassNum3,
     &  PassNum4
C
      INTEGER*4 REPL_N,REPL_L(20),REPL_CODE(20),MAX_CHAR_L,
     &          Xlink_Length(Max_C_Logicals),
     &          Conf_length(Max_C_Logicals),
     &          Length_Logicals(Max_Logicals)
C
      CHARACTER*4 REPL_STR(20)
C
      LOGICAL*1 
     &  Oper_Mode,
     &  Default_Group,
     &  DSS_In,
     &  DAS_In
C
      COMMON /Screen/ Blank
      COMMON /Group/ Default_Group,DSS_In,DAS_In
      COMMON /Mode/ Oper_Mode 
      COMMON /Directories/ Util_Dir
      COMMON /File_String/ Xlink_String,Conf_String,Filename
      COMMON /KEYBOARD/ REPL_N,REPL_STR,REPL_L,
     &                  REPL_CODE,MAX_CHAR_L
C
      COMMON /It_is_The_List/ Common_Letter,Common_Desc
C
      COMMON /Integer2Type/ Xlink_Length,
     &                      Common_Active,Common_Max_Config
C
      COMMON /Integer4Type/ Conf_Length
C
      EQUIVALENCE (Common_Letter(1,1),Audio_Letter(1)) 
      EQUIVALENCE (Common_Letter(1,2),Sound_Letter(1)) 
      EQUIVALENCE (Common_Desc(1,1),Audio_Desc(1)) 
      EQUIVALENCE (Common_Desc(1,2),Sound_Desc(1)) 
      EQUIVALENCE (Common_Active(1),Audio_Active) 
      EQUIVALENCE (Common_Active(2),Sound_Active) 
      EQUIVALENCE (Common_Max_Config(1),Audio_Max_Config) 
      EQUIVALENCE (Common_Max_Config(2),Sound_Max_Config) 
C
      DATA Array /1,2,3,5,6,9,10/ 
      DATA Logicals /'CAE_AOS','CAE_SIMEX','CAE_SHIP','CAE_CDB',
     &               'AOS_HELP','CAE_SHIPNAM','CAE_SHIPNM'/
      DATA Length_Logicals /7,9,8,7,8,11,10/
C
