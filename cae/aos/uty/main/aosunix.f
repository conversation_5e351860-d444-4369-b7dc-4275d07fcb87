C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : AOSUTY                                                   **
C   **                                                                      **
C   **  Module   : AOSUNIX.F                                                **
C   **  Function : This program contains all I/O functions for IBM.         **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0             G. De Serre                   05 July 1990      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  Delete_File                                                         **
C   **  File_IO                                                             **
C   **  Get_Parameters                                                      **
C   **  Computer_ID                                                         **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
C
C     ========================================
      SUBROUTINE Delete_File(File_Name,Status)
C     ========================================
C
      INCLUDE 'aospar.inc'
C
      INTEGER*4
     &  Status           ,! Status of file found
     &  revstat
C
      CHARACTER
     &  File_Name*(*)    ,! Name of the file
     &  n_file*80
C
      INCLUDE 'aosdat.inc'
C
      CALL rev_curr(File_Name,n_file,' ',.FALSE.,1,revstat)
      OPEN (UNIT=89,FILE=n_file,STATUS='OLD',IOSTAT=Status,ERR=999)
      CLOSE (UNIT=89,STATUS='DELETE',IOSTAT=Status,ERR=999)
C
 999  CONTINUE
C
      RETURN
      END
C
C
C     ============================================
      SUBROUTINE File_IO(CODE,OPER,FILELOG,IERR,*)
C     ============================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
       INTEGER*4
     &  IERR           !Error counter
C
      CHARACTER*(*) FILELOG !Filename
C
      CHARACTER*80
     &  N_FILE
C
      INTEGER*4
     & revstat        ,!Status of revision handling routine
     & CODE           ,!File code
     & OPER            !Operation code
C
       INCLUDE 'aosdat.inc'
C
       IF (CODE.EQ.1) THEN
          IF(OPER.EQ.1) THEN
            CALL rev_curr(FILELOG,N_FILE,' ',.FALSE.,1,revstat)
            OPEN(UNIT=CNF_UNIT,FILE=N_FILE,STATUS='OLD',
     &        ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.2)THEN
            CALL rev_next(FILELOG,N_FILE,' ',.FALSE.,1,revstat)
            OPEN(UNIT=CNF_UNIT,FILE=N_FILE,STATUS='NEW',
     &        ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.3)THEN
            CLOSE(UNIT=CNF_UNIT,IOSTAT=IERR,ERR=1000)
          ELSEIF(OPER.EQ.4) THEN
            CLOSE(UNIT=CNF_UNIT,STATUS='DELETE',IOSTAT=IERR,ERR=1000)
          ENDIF
       ELSEIF(CODE.EQ.2) THEN
C
          IF(OPER.EQ.1) THEN
C
C           Open a old file AOSXLINK.INF
C           ----------------------------
            CALL rev_curr(FILELOG,N_FILE,' ',.FALSE.,1,revstat)
            OPEN(UNIT=XLK_UNIT,FILE=N_FILE,STATUS='OLD',
     &           ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.2) THEN
            CALL rev_next(FILELOG,N_FILE,' ',.FALSE.,1,revstat)
            OPEN(UNIT=XLK_UNIT,FILE=N_FILE,STATUS='NEW',
     &        ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.3) THEN
            CLOSE(UNIT=XLK_UNIT,IOSTAT=IERR,ERR=1000)
          ELSEIF(OPER.EQ.4) THEN
            CLOSE(UNIT=XLK_UNIT,STATUS='DELETE',IOSTAT=IERR,ERR=1000)
          ENDIF
       ELSEIF(CODE.EQ.3) THEN
C
          IF(OPER.EQ.1) THEN
C
C           Open a file AOSLIB.HLP
C           -----------------------
            OPEN(UNIT=HLP_UNIT,FILE=FILELOG,STATUS='OLD',
     &           ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.2) THEN
            CLOSE(UNIT=HLP_UNIT,IOSTAT=IERR,ERR=1000)
          ENDIF
       ELSEIF(CODE.EQ.4) THEN
C
          IF(OPER.EQ.1) THEN
C
C           Open a old FORMGEN data file
C           ----------------------------
            OPEN(UNIT=FORM_UNIT,FILE=FILELOG,STATUS='OLD',
     &           ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.2) THEN
            OPEN(UNIT=FORM_UNIT,FILE=FILELOG,STATUS='NEW',
     &        ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.3) THEN
            CLOSE(UNIT=FORM_UNIT,IOSTAT=IERR,ERR=1000)
          ELSEIF(OPER.EQ.4) THEN
            CLOSE(UNIT=FORM_UNIT,STATUS='DELETE',IOSTAT=IERR,ERR=1000)
          ENDIF
       ELSEIF(CODE.EQ.5) THEN
C
          IF(OPER.EQ.1) THEN
C
C           Open a new file SNDXLINK.INF
C           ----------------------------
            OPEN(UNIT=FMG_UNIT,FILE=FILELOG,STATUS='NEW',
     &           ACCESS='SEQUENTIAL',IOSTAT=IERR,ERR=1000)
C
          ELSEIF(OPER.EQ.2) THEN
            CLOSE(UNIT=FMG_UNIT,IOSTAT=IERR,ERR=1000)
          ENDIF
       ENDIF
C
 1000  IF(IERR.NE.0) THEN
         RETURN 1
       ENDIF
C
       RETURN
       END
C
C
C ===========================================================
C                     Get_Parameters
C ===========================================================
C
C   This subroutine read the input line passed from command level
C  and stored each parameters one by one (separate by a comma, a
C  space or parenthesis.
C
      SUBROUTINE Get_Parameters(Directory,Param1,Recurrent,Group,Mode)
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      LOGICAL*1
     &  Recurrent
C
      INTEGER*4
     &  Status       ,!Status of translation of logical names
     &  INP_LEN      ,!Length of sound logical name
     &  Length        !Length of passed parameters
C
      CHARACTER
     &  INP_PAR*11,
     &  Directory*255, !
     &  Param1*255,    !
     &  String*255     !Input parameters
C
      LOGICAL*1
     &  Mode, 
     &  Group
C 
      INCLUDE 'aosdat.inc'
C
      DATA INP_PAR /'CAE_AOS'/
      DATA INP_LEN / 7 /
C
      Directory(1:80) = Blank
      Directory(81:160) = Blank
      Directory(161:240) = Blank
      Directory(241:255) = Blank(1:15)
C
      Param1(1:80) = Blank
      Param1(81:160) = Blank
      Param1(161:240) = Blank
      Param1(241:255) = Blank(1:15)
C
      CALL Translate(INP_PAR,INP_LEN,Directory,Length,Status)
C
      Recurrent = .TRUE.
      Group = .TRUE.
      Mode = .FALSE.
      Param1 = 'NO_PAR'
C
 99   CONTINUE
      RETURN
C
      END
C
C
C     =====================================================
C                       COMPUTER_ID
C     =====================================================
C
C     This subroutine identify VAX computer
C
      SUBROUTINE COMPUTER_ID
C
      INCLUDE 'aospar.inc'
      INCLUDE 'aosdat.inc'
C
      CALL Init_Libgd(1,3)    ! SGI computer = 3
C
      RETURN
C
      END
C
