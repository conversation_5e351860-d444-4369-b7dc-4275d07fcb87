INCLUDE = aosdat.inc aospar.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
aosmain: aosmain.o aosunix.o $(CAELIB)/libcae.a $(LIBDIR)/libaos.a
#
#
	xlf -C -qcharlen=1024 aosmain.o aosunix.o \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o $(EXEDIR)/aosmain
#
#
aosmain.o: aosmain.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c aosmain.f
#
aosunix.o: aosunix.f $(INCLUDE)
	xlf -g -qcharlen=1024 -c aosunix.f
#
