C
C   **************************************************************************
C   **************************************************************************
C   **                                                                      **
C   **  Utility  : AOSUTY  UNIX Version                                     **
C   **                                                                      **
C   **  Program  : AOSMAIN.FOR                                              **
C   **                                                                      **
C   **  Function : This program does all the handshake necessary to control **
C   **             the AOS utility environment.                             **
C   **                                                                      **
C   **  Revision history :                                                  **
C   **  ----------------                                                    **
C   **  Rev 1.0           G.DeSerre/P.Daigle              05 July 1990      **
C   **                                                                      **
C   **                                                                      **
C   **  Subroutines included:                                               **
C   **  --------------------                                                **
C   **  AOSUTY                                                              **
C   **  Check_File                                                          **
C   **  Check_Keyboard                                                      **
C   **  Create_Conf                                                         **
C   **  Create_File                                                         **
C   **  Create_Xlink                                                        **
C   **  Disp_Current                                                        **
C   **  Disp_Error                                                          **
C   **  Disp_Mode                                                           **
C   **  Disp_Group                                                          **
C   **  Disp_Specific                                                       **
C   **  Enter_Logicals                                                      **
C   **  GET_ERR_STR                                                         **
C   **  Get_Logicals_Translation                                            **
C   **  Help                                                                **
C   **  Initialize                                                          **
C   **  Modify_Current                                                      **
C   **  Modify_Files                                                        **
C   **  Modify_General                                                      **
C   **  Modify_Specific                                                     **
C   **  Read_Conf                                                           **
C   **  Screen_init                                                         **
C   **                                                                      **
C   **************************************************************************
C   **************************************************************************
C
C
      PROGRAM AOSUTY
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      LOGICAL*1 
     &  Create_Formgen,    ! Create FORMGEN.TMP file flag
     &  Cont_Read,         ! Read all input
     &  Xlink_OK,          ! Generate the XLINK file
     &  Process,           ! Process the menu driven type of command
     &  Recurrent          ! This is a recurrent call from the command level
C
      CHARACTER
     &  Item(15)*15,       ! List of selectable input at menu level
     &  P1*255,            ! P1 command level parameter
     &  Command*80,        ! Command input from user
     &  M_INVCOM*45,       ! Invalid parameter error message
     &  M_CALL*73,         ! Calling a utility message
     &  M_Recur*61,        ! Invalid recurrent call from command level message
     &  M_ERRCOM*41        ! Invalid command error message
C
      CHARACTER*6
     &  STR_FIR   /'FIRGEN'/,
     &  STR_TSD   /'TSDGEN'/,
     &  STR_TMS   /'TMSGEN'/
C
      CHARACTER*7
     &  STR_WAV  /'WAVEGEN'/,
     &  STR_FORM /'FORMGEN'/,
     &  STR_HAR  /'HARMONY'/  ! Misc string of P1 parameter
C
      CHARACTER*43
     &  M_ERRAMB,          ! Ambiguous command error message
     &  M_OnlyCom          ! Only AUDIO utility error message
C
      INTEGER*4 
     &  L_M_Rec /61/,       ! Length of error message
     &  L_M_INC /45/,
     &  L_M_CAL /73/,
     &  L_M_Onl /43/
C
      Integer*2 
     &  aa(25000),bb(25000),cc
C
      INTEGER*4
     &  Which,             ! Which command selected in the command list
     &  Read_Status,       ! Read status from READ_COMMAND routine  
     &  Severe_Error,      ! Severe error status
     &  Status,            ! Status of misc routines
     &  Length             ! Length of string
C
      INCLUDE 'aosdat.inc'
C
      DATA M_ERRCOM /'%PARSE-E-INVAL, Invalid command specified'/
      DATA M_ERRAMB /'%PARSE-E-AMBIG, Ambiguous command specified'/
      DATA M_INVCOM /'%P1_PASSED-E-INV , Invalid parameter supplied'/
      DATA M_OnlyCom/'%FIRGEN-E-ONLY, Can be called only by AUDIO'/
      DATA M_Recur /'%CONF-E-ONETIME, Error on CNF file when AOSUTY is c
     &alled back'/
      DATA M_CALL   /'EXECUTE: The         utility is called, process re
     &turned to Command Level'/
      DATA Item /'HELP           ','CONFIGURATION  ',
     &           'GROUP          ','MODE           ',
     &           'QUIT           ','EXIT           ',
     &           'X              ','BOX            ',
     &           'HARMONY        ','FIRGEN         ',
     &           'TSDGEN         ','TMSGEN         ',
     &           'WAVEGEN        ','FORMGEN        ',
     &           'WASTE          '/
C
C
      CALL Computer_id
C
      CALL Initialize
C
C     Decode the parameter line and store each ones
C     ---------------------------------------------
      CALL Get_Parameters(Util_Dir,P1,Recurrent,Default_Group,Oper_Mode)
      CALL String_Length(Util_Dir,Length)
C
C
C     Check if XLINK file exist
C     -------------------------
      Filename = Util_Dir(1:Length)//'aosxlink.inf'
      CALL Check_File(Filename,Status)
      IF (Status.NE.1) THEN           !File doesn't exist...
         Recurrent = .FALSE.
      ENDIF
C
C     Check if CNF file exist
C     -----------------------
 
      Filename = Util_Dir(1:Length)//'aoslib.cnf'
      CALL Check_File(Filename,Status)
      IF (Status.NE.1) THEN           !File doesn't exist...
C
         IF (.NOT.Recurrent) THEN
C
C           File does not exist, create a new one 
C           -------------------------------------
            CALL Create_Conf(1,Severe_Error)
            IF (Severe_Error.NE.0) THEN
               Process = .FALSE.
               CALL Create_File('abort.tmp')
            ELSE
               Process = .TRUE.
            ENDIF
         ELSE
            CALL Disp_Error(M_Recur,L_M_Rec,1)
            Process = .FALSE.
            CALL Create_File('abort.tmp')
         ENDIF
      ELSE
C
C        File exist, read it
C        -------------------
         CALL Read_Conf(Severe_Error)
         IF (Severe_Error.NE.0) THEN 
C
C           Creates a new one if unable to read
C           -----------------------------------
            IF (.NOT.Recurrent) THEN
               CALL Create_Conf(1,Severe_Error)
            ENDIF
C
C           If unable to create, definitively something is wrong 
C           ----------------------------------------------------
            IF (Severe_Error.NE.0) THEN 
               Process = .FALSE.
               CALL Create_File('abort.tmp')
            ELSE 
               Process = .TRUE.
            ENDIF
         ELSE
            Process = .TRUE.
            IF (Recurrent) THEN
C
C              Check for previously defined default option
C              -------------------------------------------
               CALL Check_File('standalone.tmp',Status)
               IF (Status.EQ.1) THEN
                  Oper_Mode = .FALSE.
                  CALL Delete_File('standalone.tmp',Status)
               ELSE
                  Oper_Mode = .TRUE.
               ENDIF
               CALL Check_File('sound.tmp',Status)
               IF (Status.EQ.1) THEN
                  Default_Group = .TRUE.
                  CALL Delete_File('sound.tmp',Status)
               ELSE
                  IF (DAS_In) THEN
                    Default_Group = .FALSE.
                  ELSE IF (DSS_In) THEN
                    Default_Group = .TRUE.
                  ENDIF
               ENDIF
            ELSE          !Not recurrent...
               IF (DSS_In) THEN
                  Default_Group = .TRUE.
               ELSEIF (DAS_In) THEN
                  Default_Group = .FALSE.
               ENDIF
            ENDIF   
         ENDIF
      ENDIF      
C         
      IF (Process) THEN
C
         CALL Get_Logicals_Translation
C
C        Check if one of the utility was called
C        --------------------------------------
         CALL String_Length(P1,Length)
C
         IF (P1(1:Length).NE.'NO_PAR'.AND.Length.GT.0) THEN
            Cont_Read = .FALSE.  
            IF (P1(1:Length).EQ.STR_HAR(1:Length)) THEN
               M_CALL(14:20)='HARMONY'
               CALL Create_File('harmony.tmp')
               IF (Oper_Mode) THEN
                  CALL Create_File('wavegen.tmp')
               ENDIF
            ELSE IF (P1(1:Length).EQ.STR_FIR(1:Length)) THEN
               IF (.NOT.Default_Group) THEN
                  M_CALL(14:20)='FIRGEN '
                  CALL Create_File('firgen.tmp')
               ELSE
                  CALL Disp_Error(M_OnlyCom,L_M_Onl,1)
               ENDIF  
            ELSE IF (P1(1:Length).EQ.STR_FORM(1:Length)) THEN
               M_CALL(14:20)='FORMGEN'
               CALL Create_File('formgen.tmp')
            ELSE IF (P1(1:Length).EQ.STR_TSD(1:Length)) THEN
               M_CALL(14:20)='TSDGEN'
               CALL Create_File('tsdgen.tmp')
            ELSE IF (P1(1:Length).EQ.STR_TMS(1:Length)) THEN
               M_CALL(14:20)=' TMSGEN'
               CALL Create_File('tmsgen.tmp')
            ELSE IF (P1(1:Length).EQ.STR_WAV(1:Length)) THEN
               M_CALL(14:20)='WAVEGEN'
               CALL Create_File('wavegen.tmp')
            ELSE
               CALL Start_Highlite
               CALL Term_Write(24,1,M_INVCOM,L_M_INC)
               CALL Beep(1)
               CALL Wait_Time(3.0)
               CALL Stop_Highlite
               Cont_Read = .TRUE.
            ENDIF
C
         ELSE
            Cont_Read = .TRUE.
         ENDIF
C
         IF (Cont_Read) THEN
C
C           Draw the Boxes
C           --------------
            CALL Screen_Init(1)
         ELSE
            CALL Create_File('exit_ac.tmp')
            IF (Oper_Mode) THEN
               CALL Create_File('formgen.tmp')
            ENDIF
            CALL Start_Highlite
            CALL Term_Write(24,1,M_CALL,L_M_CAL)
            CALL Stop_Highlite
            Xlink_OK = .TRUE. 
         ENDIF
C
C        Read all commands until quit entered
C        ------------------------------------
         DO WHILE(Cont_Read)
            CALL Read_Command(-1,23,1,'AOSUTY > ',9,Command,Length,
     &                                           Read_Status)
C 
C
            IF (Read_Status.EQ.0) THEN
               CALL Parse_Command(Command,Length,15,Item,Which,
     &                                            Read_Status)
               IF (Read_Status.EQ.0) THEN
                  IF (Which.EQ.1) THEN
                     CALL Help(1)
                  ELSE IF (Which.EQ.2) THEN
                     CALL Create_Conf(0,Severe_Error)
                     IF (Severe_Error.NE.0) THEN
                        CALL Create_File('abort.tmp')
                        Cont_Read = .FALSE.
                     ELSE
                        CALL Get_Logicals_Translation
                        CALL Screen_Init(1)
                     ENDIF
                  ELSE IF (Which.EQ.3) THEN
C   
C                    Swap default group from SOUND to AUDIO 
C                    --------------------------------------
                     IF(DSS_In.AND.DAS_In) THEN
                       Default_Group = .NOT.Default_Group
                       Call Disp_Group
                     ENDIF
                 ELSE IF (Which.EQ.4) THEN
                     Oper_Mode = .NOT.Oper_Mode
                     Call Disp_Mode
                  ELSE IF (Which.GE.5.AND.Which.LE.7) THEN
                     Cont_Read=.FALSE.
                     CALL Create_File('exit.tmp')
                  ELSE IF (Which.EQ.8) THEN
                     CALL Screen_Init(1)
                  ELSE IF (Which.EQ.15) THEN
                     Do while(.true.)
                        CALL Term_Write(24,1,'About t try t get SHARK',
     &                                                          23)
                        call wait_Time(100.0)
                        CALL Term_Write(24,1,'Trying harder...',16)
                        do Index=1,25000
                           aa(Index)=bb(Index)*cc
                        enddo
                     enddo 
                  ELSE IF (Which.GE.9) THEN
                     Create_Formgen = Oper_Mode
                     IF (Which.EQ.9) THEN
                        IF (Oper_Mode) THEN
                           CALL Create_File('wavegen.tmp')
                        ENDIF
                        M_CALL(14:20)='HARMONY'
                        CALL Create_File('harmony.tmp')
                     ELSE IF (Which.EQ.10) THEN
                        IF (.NOT.Default_Group) THEN
                           M_CALL(14:20)='FIRGEN '
                           CALL Create_File('firgen.tmp')
                        ELSE
                           CALL Disp_Error(M_OnlyCom,L_M_Onl,1)
                        ENDIF  
                     ELSE IF (Which.EQ.11) THEN
                        M_CALL(14:20)=' TSDGEN'
                        CALL Create_File('tsdgen.tmp')
                     ELSEIF(Which.EQ.12) THEN
                        M_CALL(14:20)=' TMSGEN'
                        CALL Create_File('tmsgen.tmp')
                     ELSE IF (Which.EQ.13) THEN
                        M_CALL(14:20)='WAVEGEN'
                        CALL Create_File('wavegen.tmp')
                     ELSE IF (Which.EQ.14) THEN
                        M_CALL(14:20)='FORMGEN'
                        Create_Formgen = .TRUE.
                     ENDIF
                     IF (Create_Formgen) THEN
                        CALL Create_File('formgen.tmp')
                     ENDIF
                     Xlink_OK = .TRUE. 
                     CALL Term_Write(24,1,M_CALL,L_M_CAL)
                     Cont_Read = .FALSE.
                  ENDIF
               ELSE
C
C                 Print error message when input not found or invalid
C                 ---------------------------------------------------  
                  IF (Status.EQ.1) THEN
                     CALL Disp_Error(M_ERRCOM,41,1)
                  ELSE
                     CALL Disp_Error(M_ERRAMB,43,1)
                  ENDIF
               ENDIF
C
            ENDIF
         ENDDO
C
C        Create Xlink file if requested
C        ------------------------------
         IF (Xlink_OK) THEN
            Call Create_Xlink(Severe_Error)
            IF (Severe_Error.NE.0) THEN
               CALL Create_File('abort.tmp')
            ELSE
C
C              Create files to keep user default upon recall of the utitlity
C              -------------------------------------------------------------
               IF (Default_Group) THEN
                  CALL Create_File('sound.tmp') 
               ENDIF
               IF (.NOT.Oper_Mode) THEN
                  CALL Create_File('standalone.tmp') 
               ENDIF
            ENDIF
         ENDIF
C
      ENDIF
C
 98   CONTINUE
      CALL EXIT
C
      END
C
C
C     =======================================
      SUBROUTINE Check_File(File_Name,Status)
C     =======================================
C
      INCLUDE 'aospar.inc'
C
      LOGICAL*4
     &  Present            ! File presence flag
C
      INTEGER*4
     &  Status            ,! Status of file found
     &  revstat            !
C
      CHARACTER
     &  File_Name*(*)    ,! Name of the file
     &  n_file*80         !
C
      INCLUDE 'aosdat.inc'
C
      CALL rev_curr(File_name,n_file,' ',.FALSE.,1,revstat)
      INQUIRE (FILE=n_file,EXIST=Present) 
C
      IF (Present) THEN
         Status = 1    ! File exist
      ELSE
         Status = 0    ! File not found
      ENDIF
C
      RETURN
      END
C
C
C     =====================================================
      SUBROUTINE Check_Keyboard(Read_Status,Command,Length)
C     =====================================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      LOGICAL*1
     &  Repl_Found        ! One replace key found
C
      INTEGER*2
     &  L_M_Inv/33/,      ! Length of invalid key message
     &  Read_status,      ! Status of key pressed
     &  Length            ! Length of input string
C
      CHARACTER
     &  M_Invalid*33,     ! Invalid key message 
     &  Command*(*)       ! Input string replaced if found 
C
      DATA M_Invalid /'%INPUT-E-KEY, Invalid Key pressed'/
C
      INCLUDE 'aosdat.inc'
C
      IF (Read_Status.NE.0) THEN
         Repl_Found = .FALSE.
         Index = 1
         DO WHILE (Index.LE.REPL_N.AND..NOT.Repl_Found)
            IF (Read_Status.EQ.REPL_CODE(Index)) THEN
C
C              Replace only if found
C              ---------------------
               Command = REPL_STR(Index)
               Length  = REPL_L(Index)
               Repl_Found = .TRUE.
            ENDIF
            Index = Index + 1
         ENDDO
C
C        Print error message if no replace keys found
C        --------------------------------------------
         IF (.NOT.Repl_Found) THEN
            CALL Disp_Error(M_Invalid,L_M_Inv,1)          
         ELSE
            Read_Status = 0 ! Reset status if key found to parse string
         ENDIF  
      ENDIF
C
      RETURN
      END
C
C
C     ===================================
      SUBROUTINE Create_Conf(Type,Status)
C     ===================================
C
C     This routine looks for the AOSUTY.CNF file, build a new one or
C     just read or write to it.
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
       INTEGER*4
     &  Which,                ! Which command parsed
     &  Read_Status,          ! Status of READ_COMMAND routine
     &  Length,               ! Length of filename string
     &  Status,               ! Status of I/O thru filing on CONF
     &  Type                  ! Type of CONFiguration input (1-Menu,0-On entry)
C
       INTEGER*2 
     &  Prompt_Length(16)      ! Length of Question
C
      CHARACTER
     &  Command*80,            ! Command string input by user
     &  M_Interr*48,           ! Internal error message
     &  M_Errcom*41,           ! Invalid command error message
     &  M_Erramb*43,           ! Ambiguous command error message
     &  Prompt_String(16)*34,  ! Question for each logical names
     &  Item(8)*15             ! Selectable item from menu
C
      LOGICAL*1
     &  Generate,              ! Generate CNF file
     &  Cont_Ask               ! Continu to ask question until valid answer
C
      INCLUDE 'aosdat.inc'
C
      DATA M_Erramb /'%PARSE-E-AMBIG, Ambiguous command specified'/
      DATA M_Errcom /'%PARSE-E-INVAL, Invalid command specified'/
      DATA M_Interr /'%CONF-E-FILE, Internal error #      on CNF file '/
      DATA Item / 'HELP           ','BOX            ',
     &            'AUDIO          ','SOUND          ',
     &            'GENERAL        ','X              ',
     &            'EXIT           ','QUIT           '/
C
C     ----------------------------------------------------------------------
C     Stored in CAE_AOS:AOSUTY.CNF (Store the configuration of sound)
C            Conf_String(1) - DIRECTORY: AOS library
C                       (2) - DIRECTORY: Simex
C                       (3) - DIRECTORY: Ship
C                       (4) - DIRECTORY: Spare #1
C                       (5) - DIRECTORY: CDB
C                       (6) - DIRECTORY: Help
C                       (7) - DIRECTORY: Spare #2
C                       (8) - DIRECTORY: Spare #3
C                       (9) - LOGICAL: Ship identification
C                      (10) - LOGICAL: Ship name description
C                      (11) - LOGICAL: DSS installed on site
C                      (12) - LOGICAL: DAS installed on site
C                      (13) - AUDIO: DMC number
C                      (14) - AUDIO: DMC page
C                      (15) - SOUND: DMC number
C                      (16) - SOUND: DMC page
C                      (17) - FLAG: On site
C                      (18) - FLAG: Spare #1
C                      (19) - FLAG: Spare #2
C                      (20) - FLAG: Spare #3
C                      (21) - SOUND: Default name for WAVEGEN data file
C                      (22) - SOUND: Default name for TSDGEN data file
C                      (23) - SOUND: Default name for TMSGEN data file
C                      (24) - SOUND: Default name for XILINX Init load file
C                      (25) - SOUND: Default name for TMS code load file
C                      (26) - AUDIO: Default name for WAVEGEN data file
C                      (27) - AUDIO: Default name for TSDGEN data file
C                      (28) - AUDIO: Default name for TMSGEN data file
C                      (29) - AUDIO: Default name for XILINX Init load file
C                      (30) - AUDIO: Default name for TMS code load file
C                      (31) - Number of simulator configuration for AUDIO
C                      (32) - Number of simulator configuration for SOUND
C                      (33) - Actual simulator configuration (AUDIO)
C                      (34) - Actual simulator configuration (SOUND)
C                   (35-39) - List of all simulator configuration for AUDIO
C                   (39-44) - List of all simulator configuration for SOUND
C     ----------------------------------------------------------------------
C
      Status = 0
C
      IF (Type.EQ.1) THEN
C
C        On entry, all parameters to be configured
C        -----------------------------------------
         CALL Screen_Init(2)
         CALL Enter_Logicals(Generate)
C
         IF (.NOT.Generate) THEN
            Status = 999
         ELSE
C
C           Enter a default for Audio and Sound
C           -----------------------------------
            Conf_String(13) = DMC_Audio_Default
            Conf_Length(13) = 2
            Conf_String(14) = PAGE_Audio_Default
            Conf_Length(14) = 2
            Conf_String(15) = DMC_Sound_Default
            Conf_Length(15) = 2
            Conf_String(16) = PAGE_Sound_Default
            Conf_Length(16) = 2
            Conf_String(21) = 'wavsnc.dat'
            Conf_Length(21) = 10
            Conf_String(22) = 'tsdsnc.dat'
            Conf_Length(22) = 10
            Conf_String(23) = 'tmssnc.dat'
            Conf_Length(23) = 10
            Conf_String(24) = 'tmisnc.lod'
            Conf_Length(24) = 10
            Conf_String(25) = 'tmcsnc.lod'
            Conf_Length(25) = 10
            Conf_String(26) = 'wavrfc.dat'
            Conf_Length(26) = 10
            Conf_String(27) = 'tsdrfc.dat'
            Conf_Length(27) = 10
            Conf_String(28) = 'tmsrfc.dat'
            Conf_Length(28) = 10
            Conf_String(29) = 'tmirfc.lod'
            Conf_Length(29) = 10
            Conf_String(30) = 'tmcrfc.lod'
            Conf_Length(30) = 10
            Audio_Max_Config = 1
            Sound_Max_Config = 1
            Audio_Active = 1
            Sound_Active = 1
            Audio_Letter(1) = Letter_default
            Audio_Desc(1) = 'Default Simulator Configuration'
            Sound_Letter(1) = Letter_default
            Sound_Desc(1) = 'Default Simulator Configuration'
         ENDIF
      ELSE
C
C        Request by user, menu type
C        --------------------------
         CALL Screen_Init(3)
         Generate = .FALSE.
         Cont_Ask = .TRUE.
         DO WHILE (Cont_Ask)
            CALL Read_Command(-1,15,14,'Enter your selection > ',
     &                        23,Command,Length,Read_Status)
C
            IF (Read_Status.EQ.0) THEN 
               CALL Parse_Command(Command,Length,8,Item,Which,
     &                                              Read_Status)
               IF (Read_Status.EQ.0) THEN
                  IF (Which.EQ.1) THEN
                     CALL Help(2)
                  ELSE IF (Which.EQ.2) THEN
C    
C                    Reinitialize the screen
C                    -----------------------
                     CALL Screen_Init(3)
                  ELSE IF (Which.EQ.3) THEN
C
C                    Modify AUDIO specific configuration 
C                    -----------------------------------
                     CALL Modify_Specific(1,Generate)
                  ELSE IF (Which.EQ.4) THEN
C
C                    Modify SOUND specific configuration 
C                    -----------------------------------
                     CALL Modify_Specific(2,Generate)
                  ELSE IF (Which.EQ.5) THEN
C
C                    Enter the new logical names definition
C                    --------------------------------------
                     CALL Screen_Init(2)
                     CALL Modify_General(Generate)
                     CALL Screen_Init(3)
                  ELSE IF (Which.GE.6) THEN
C 
C                    QUIT, EXIT or X 
C                    ---------------
                     Cont_Ask = .FALSE.
                  ENDIF
               ELSE
C
C                 Print error message when input not found or invalid
C                 ---------------------------------------------------  
                  IF (Status.EQ.1) THEN
                     CALL Disp_Error(M_ERRCOM,41,1)
                  ELSE
                     CALL Disp_Error(M_ERRAMB,43,1)
                  ENDIF
               ENDIF
            ENDIF
         ENDDO
      ENDIF
C
      IF (Generate) THEN      
C
         CALL String_Length(Util_Dir,Length) 
         Filename = Util_Dir(1:Length)//'aoslib.cnf'
         CALL File_IO(1,2,Filename,Status,*1000)
C
         IF (Status.EQ.0) THEN    
C
C           If OK, write the parameters to the AOSUTY.CNF file
C           ---------------------------------------------------
            DO Index = 1,30
               WRITE (CNF_UNIT,500,IOSTAT=Status,ERR=1000) 
     &                Conf_Length(Index),
     &                Conf_string(Index)(1:Conf_Length(Index))
            ENDDO 
C
            WRITE(CNF_UNIT,501,IOSTAT=Status,ERR=1000) Audio_Max_Config
            WRITE(CNF_UNIT,501,IOSTAT=Status,ERR=1000) Sound_Max_Config
            WRITE(CNF_UNIT,501,IOSTAT=Status,ERR=1000) Audio_Active
            WRITE(CNF_UNIT,501,IOSTAT=Status,ERR=1000) Sound_Active
            DO Index=1,Audio_Max_Config
               WRITE (CNF_UNIT,503,IOSTAT=Status,ERR=1000) 
     &               Audio_Letter(Index),
     &               Audio_Desc(Index)
            ENDDO
C
            DO Index=1,Sound_Max_Config
               WRITE (CNF_UNIT,503,IOSTAT=Status,ERR=1000) 
     &               Sound_Letter(Index),
     &               Sound_Desc(Index)
            ENDDO
            CALL File_IO(1,3,Filename,Status,*1000)
C
         ENDIF
      ENDIF
C
 1000 IF (Status.NE.0) THEN
C
C        Print error message during operation
C        ------------------------------------
         CALL GET_ERR_STR(Status,M_INTERR(31:35))
         CALL Disp_Error(M_INTERR,48,1)
         CALL Create_File('abort.tmp')
      ENDIF
C
      RETURN
 500  FORMAT(1X,I4,A)
 501  FORMAT(1X,I2)
 502  FORMAT(1X,A1)
 503  FORMAT(1X,A1,1X,A31)
 99   CONTINUE
      END
C
C
C     =================================
      SUBROUTINE Create_File(File_Name)
C     =================================
C
C     This routine creates dummy files for handshake with command level
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER
     &  N_File*80,
     &  File_Name*(*),        ! Name of the file to be created
     &  M_FILTMP*80           ! Error message string
C
      INTEGER*4
     &  revstat,
     &  Status                ! Status of the create file process
C
      INCLUDE 'aosdat.inc'
C
      DATA M_FILTMP /'%CREATE-E-FILE_TMP, Error #      when creating fil
     &e                             '/
C
      CALL rev_next(File_Name,N_File,' ',.FALSE.,1,revstat)
      OPEN (UNIT=99,FILE=N_File,STATUS='NEW',IOSTAT=Status,ERR=1)
C
      GOTO 2
C
C     Leave the program if can't create O/S link file 
C     ----------------------------------------------- 
 1    <USER> <GROUP>(Status,M_FILTMP(28:32))
      M_FILTMP(55:78) = File_Name
      CALL Disp_Error(M_FILTMP,78,1)
      CALL EXIT(2)
 2    CONTINUE
      CLOSE (UNIT=99) 
      RETURN
      END
C
C
C     ===============================
      SUBROUTINE Create_Xlink(Status)
C     ===============================
C
C     aosxlink.inf (Communication with all the utilities)
C           Xlink_String (1) - DIRECTORY: AOSLIB
C                        (2) - DIRECTORY: Simex
C                        (3) - DIRECTORY: Ship
C                        (4) - DIRECTORY: Executable
C                        (5) - DIRECTORY: CDB
C                        (6) - DIRECTORY: Help
C                        (7) - DIRECTORY: Spare #1
C                        (8) - DIRECTORY: Spare #2
C                        (9) - LOGICAL: Ship identification
C                       (10) - LOGICAL: Ship name description
C                       (11) - LOGICAL: Spare #1
C                       (12) - LOGICAL: Spare #2
C                       (13) - DMC number
C                       (14) - DMC page
C                       (15) - Filename letters (3)
C                       (16) - Computer Id
C                       (17) - FLAG: On site
C                       (18) - FLAG: Spare #1
C                       (19) - FLAG: Spare #2
C                       (20) - FLAG: Spare #3
C                       (21) - WAVEGEN: Data File name
C                       (22) - TSDGEN: Data File name
C                       (23) - TMSGEN: Data File name
C                       (24) - TMSGEN: XLINX load File name
C                       (25) - TMSGEN: TMS code load File name
C
C           Xlink_Flags  (1) - COM: UPDATE flag
C                        (2) - COM: DOWNLOAD flag
C                        (3) - COM: WGSIZE flag
C                        (4) - COM: Spare #1
C                        (5) - COM: Spare #2
C                        (6) - COM: Spare #3
C
C     ---------------------------------------------------------------------
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER
     &  VAL*1,                  ! String translation of logical variable
     &  M_INTERR*48,            ! Internal error message
     &  F_String1*2,            ! Format String
     &  F_String*30,            ! Format String
     &  Letters*3,              ! File letters to be transmitted
     &  Name_Lst(4)*14,         ! String to be printed in the file (DMC,PAge)
     &  Name_List(12)*13,       ! String to be printed in the file (Logicals)
     &  File_List(5)*20
C 
      INTEGER*2
     &  Offset,                 ! Offset of index due to AUDIO/SOUND 
     &  II,
     &  Tmp_len,                ! Xlink_length of type integer*2
     &  Length                  ! Length of input from user
C
      INTEGER*4
     &  Status                  ! Status of I/O
C
      INCLUDE 'aosdat.inc'
C
      DATA M_INTERR /'%CONF-E-XLNK, Internal error #      on XLNK file'/
      DATA Name_Lst  /'DMC number  : ',
     &                'PAGE number : ',
     &                'Letters     : ',
     &                'Computer Id : '/
      DATA Name_List /'Library    : ',
     &                'Simex      : ',
     &                'Ship       : ',
     &                'Executable : ',
     &                'CDB        : ',
     &                'Help file  : ',
     &                'Unused     : ',
     &                'Unused     : ',
     &                'Ship ID    : ',
     &                'Ship name  : ',
     &                'Unused     : ',
     &                'Unused     : '/
C
      DATA File_List /'WAVEGEN Data File : ',
     &                'TSDGEN  Data File : ',
     &                'TMSGEN  Data File : ',
     &                'XILINX  Load File : ',
     &                'TMS CODE Load File: '/
C
C     Open a new file aosxlink.inf
C     ----------------------------
      Filename = 'aosxlink.inf'
      CALL File_IO(2,2,Filename,Status,*3000)
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '  **************************************  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '  XLINK file for AUDIO & SOUND utilities  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '  **************************************  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '  A- Logical names for directories ...'
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '     ---------------------------------'
C
C     Write the parameters to the AOSXLINK.INF file
C     ---------------------------------------------
      DO Index = 1,Max_Conf1
         WRITE (F_String1,'(I2)') Xlink_Length(Index)
         IF (Xlink_Length(Index).EQ.0) THEN
            F_String1 = '1'
         ENDIF
         tmp_len = Xlink_Length(Index)
         F_String = '(1X,A10,1X,''L='',I2,'' is '',A'//F_String1//')'
         WRITE (XLK_UNIT,F_String,IOSTAT=Status,ERR=3000) 
     &          Name_List(Index),tmp_Len,Xlink_String(Index)
      ENDDO
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '   B- DMC number, Memory Page Number & File letters'
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '      ---------------------------------------------'
C
      IF (Default_Group) THEN
         Offset = 14
         Letters(1:2) = 'sn'  
         Letters(3:3) = Sound_Letter(Sound_Active)
      ELSE
         Offset = 12
         Letters(1:2) = 'rf'  
         Letters(3:3) = Audio_Letter(Audio_Active)
      ENDIF
C
      DO Index=1,2
         WRITE (XLK_UNIT,'(1X,A13,A2)',IOSTAT=Status,ERR=3000)
     &          Name_Lst(Index),Conf_String(Index+Offset)
      ENDDO
      WRITE (XLK_UNIT,'(1X,A13,A3)',IOSTAT=Status,ERR=3000)
     &          Name_Lst(3),Letters
      WRITE (XLK_UNIT,'(1X,A13,A3)',IOSTAT=Status,ERR=3000)
     &          Name_Lst(4),'1'
C 
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '   C- Multy purpose flags '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '      ------------------- '
C
C     Write the flags
C     ---------------
      IF (Conf_String(17)(1:4).EQ.'TRUE') THEN
         VAL = 'T'
      ELSE
         VAL = 'F'
      ENDIF
      WRITE (XLK_UNIT,'(1X,A19,A1)',IOSTAT=Status,ERR=3000)
     &                ' AOS    : ON SITE =',VAL
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
C
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '   D - Default File Names '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '       -------------------'
      IF (Default_Group) THEN
         Offset = 20
      ELSE
         Offset = 25
      ENDIF
      DO Index = 1,5
         II = Index + Offset
         WRITE(F_String1,'(I2)') Conf_Length(II)
         F_String = '(1X,A20,1X,''L='',I2,'' is '',A'//F_String1//')'
         tmp_len = Conf_Length(II)
         WRITE(XLK_UNIT,F_String,IOSTAT=Status,ERR=3000)
     &         File_List(Index),tmp_len,Conf_String(II)
      ENDDO 
C
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '   E- Flags for x communication between utilities'
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000)
     &    '      -------------------------------------------'
C
C     Write the communication flags
C     -----------------------------
      IF (Oper_Mode) THEN
         VAL = 'T'
      ELSE
         VAL = 'F'
      ENDIF
      WRITE (XLK_UNIT,'(1X,A19,A1)',IOSTAT=Status,ERR=3000)
     &                ' AOS    : UPDATE  =',VAL
C
      WRITE (XLK_UNIT,'(1X,A20)',IOSTAT=Status,ERR=3000)
     &                ' AOS    : DWNLOAD =F'
C
      WRITE (XLK_UNIT,'(1X,A20)',IOSTAT=Status,ERR=3000)
     &                ' AOS    : WGSIZE  =F'
C
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
      WRITE (XLK_UNIT,*,IOSTAT=Status,ERR=3000) ' AOS    : Spare  '
C
C     Close DATA file aosxlink.inf
C     ----------------------------
      CALL File_IO(2,3,Filename,Status,*3000)
C
C     Create the special file for FORMGEN 
C     -----------------------------------
CCC      Filename = 'aosxlink.fmg'
CCC      CALL File_IO(5,1,Filename,Status,*3000)
C
CCC      WRITE (FMG_UNIT,'(A)',IOSTAT=Status,ERR=3000)
CCC     &    '$!  ****************************** '
CCC      WRITE (FMG_UNIT,'(A)',IOSTAT=Status,ERR=3000)
CCC     &    '$!  XLINK file for FORMGEN utility '
CCC      WRITE (FMG_UNIT,'(A)',IOSTAT=Status,ERR=3000)
CCC     &    '$!  ****************************** '
CCC      WRITE (FMG_UNIT,'(A)',IOSTAT=Status,ERR=3000) '$! '
CCC      IF (Default_Group) THEN
CCC         Letters(1:2) = 'sn'  
CCC         Letters(3:3) = Sound_Letter(Sound_Active)
CCC      ELSE
CCC         Letters(1:2) = 'rf'  
CCC         Letters(3:3) = Audio_Letter(Audio_Active)
CCC      ENDIF
CCC      Filename = Xlink_String(2)(1:Xlink_length(2))//
CCC     &           Xlink_String(9)(1:Xlink_length(9))//Letters//
CCC     &           '.DLD'
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ create ',Filename(1:Xlink_Length(2)+Xlink_Length(9)+7)
CCC      Filename = Xlink_String(1)(1:Xlink_length(1))//
CCC     &           Xlink_String(9)(1:Xlink_length(9))//Letters//
CCC     &           'x.DLD'
CCC      Length = Xlink_Length(1)+Xlink_Length(9)+3      
CCC      Filename(Length+1:Length+1) = 't' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ use ',Filename(1:Length+5)
CCC      Filename(Length+1:Length+1) = 's' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ use ',Filename(1:Length+5)
CCC      Filename(Length+1:Length+1) = 'x' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ use ',Filename(1:Length+5)
CCC      Filename(Length+1:Length+1) = 'h' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ use ',Filename(1:Length+5)
CCC      Filename(Length+1:Length+1) = 'w' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC    &    '$ use ',Filename(1:Length+5)
CCC      Filename(Length+1:Length+1) = 'i' 
CCC      WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &    '$ use ',Filename(1:Length+5)
CCC      IF (.NOT.Default_group) THEN
CCC         Filename(Length+1:Length+1) = 'f' 
CCC         WRITE (FMG_UNIT,'(A,A)',IOSTAT=Status,ERR=3000) 
CCC     &           '$ use ',Filename(1:Length+5)
CCC      ENDIF
CCC      WRITE (FMG_UNIT,'(A)',IOSTAT=Status,ERR=3000) '$ end'
C
CCC      CALL File_IO(5,2,Filename,Status,*3000)
C
 3000 IF(Status.NE.0) THEN
C
         CALL GET_ERR_STR(Status,M_INTERR(31:35))
         CALL Disp_Error(M_INTERR,48,1)
         CALL Create_File('abort.tmp')
      ENDIF
C
      RETURN
C
      END
C
C
C     =========================================================
      SUBROUTINE Disp_Current(Which,Change_1,Change_2,Change_3)
C     =========================================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
C
      LOGICAL*1      
     &  Change_1,       ! Modify screen section flag (CURRENT)
     &  Change_2,       ! Modify screen section flag (DESCRIPTION)
     &  Change_3        ! Modify screen section flag (DMC,PAGE)
C
      INTEGER*4
     &  Which           ! Which system (1-Audio,2-Sound)
C
      INCLUDE 'aosdat.inc'
C
      IF (Change_1) THEN
         CALL Clear_Disp(6)
         CALL Start_Underline
         CALL Term_Write(11,5,'CURRENT',7)
         CALL Term_Write(11,15,'LETTER',6)
         CALL Term_Write(11,25,'DESCRIPTION',11)  
         CALL Stop_Underline  
      ENDIF
C
       
      DO Index = 1, Common_Max_Config(Which)
         IF (Change_2) THEN
            IF (Index.EQ.Common_Active(Which)) THEN
               CALL Term_Write(11+Index,8,'*',1)
            ELSE
               CALL Term_Write(11+Index,8,' ',1)
            ENDIF
         ENDIF
         IF (Change_3) THEN
            CALL Term_Write(11+Index,18,Common_Letter(Index,Which),1)
            CALL Term_Write(11+Index,25,Common_Desc(Index,Which),31)
         ENDIF
      ENDDO
C
      RETURN
C
      END
C
C
C     ===================================
      SUBROUTINE Disp_Error(MESS,LEN,NUM)
C     ===================================
      IMPLICIT NONE
C
C -- This subroutine displays an error message MESS of length LEN on
C    the second reverse video mode line in the menu.  It then Beeps
C    the number of times indicated by the absolute value of NUM.  If
C    NUM is less than 0, then it does not return to the statement
C    indicated in *.  Otherwise, the subroutine returns to the label *
C    in the calling program.
C
      INCLUDE 'aospar.inc'
C
      CHARACTER MESS*(*),             ! Message to be displayed
     &             MESS1*78           ! Message with appropriate blanks

      INTEGER*4 NUM,                  ! Number of times to Beep
     &          NNUM,                 ! Absolute value of NUM
     &          LEN,                  ! Length of character string
     &          LLEN                  ! Length of character string
C
      INCLUDE 'aosdat.inc'
C
      NNUM = ABS(NUM)
C
C -- Blank out message string and obtain message to be displayed
C
      LLEN=MIN(LEN,78)
      MESS1 = BLANK(1:78)
      MESS1(2:LLEN+1) = MESS(1:LLEN)
C
C -- Display message, Beep NNUM times, wait 2 seconds and clear
C    message line
C
      CALL Term_Write(24,1,MESS1,78)
      CALL Beep(NNUM)
      CALL Wait_Time(3.0)
      CALL Term_Write(24,1,BLANK,LLEN+1)
C
      RETURN
      END
C
C
C     ====================
      SUBROUTINE Disp_Mode
C     ====================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INCLUDE 'aosdat.inc'
C
      CALL Start_Highlite
      IF (Oper_Mode) THEN
         CALL Term_Write(3,69,'  UPDATE  ',10)
      ELSE
         CALL Term_Write(3,69,'STANDALONE',10)
      ENDIF
      CALL Stop_Highlite
C
      RETURN
C
      END
C
C
C     =====================
      SUBROUTINE Disp_Group
C     =====================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INCLUDE 'aosdat.inc'
C
      CALL Term_Write(6,2,'             A U D I O               ',37)
      CALL Term_Write(6,42,'              S O U N D               ',38)
      CALL Start_Reverse
      IF (Default_Group) THEN
         CALL Term_Write(6,42,'              S O U N D               ',
     &                                               38)
      ELSE
         CALL Term_Write(6,2,'             A U D I O               ',
     &                                               37)
      ENDIF
      CALL Stop_Reverse
C
      RETURN
C
      END
C
C     ==========================================================
      SUBROUTINE Disp_Specific(Which,Change_1,Change_2,Change_3)
C     ==========================================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
      INCLUDE 'aosdat.inc'
C
      LOGICAL*1      
     &  Change_1,       ! Modify screen section flag (TITLE & COMMANDS)
     &  Change_2,       ! Modify screen section flag (PAGE & DMC)
     &  Change_3        ! Modify screen section flag (FILE NAMES)
C
      INTEGER*2
     &  Offset(2)
C
      INTEGER*4
     &  Which
C
      CHARACTER
     &  Grp_Name(2)*5,
     &  Dummy*1,
     &  Title*80
C      
      DATA Grp_Name /'AUDIO','SOUND'/,
     &     Offset   /25,20/
C
      IF (Change_1) THEN
        CALL Clear_disp(5)
        Title = BLANK(1:80)
        Title(26:30) = Grp_Name(Which)
        Title(32:53) = 'SPECIFIC CONFIGURATION'
        CALL Term_Write(5,1,Dummy,0)
        CALL Start_Reverse
        CALL Term_Write(5,1,Title,80)
        CALL Stop_Reverse
C
        CALL Disp_Box(16,1,22,79,1,1,1,1,1,1,1,1,2)
        CALL Term_Write(17,5,'COMMANDS:',9)
        CALL Start_Highlite
        CALL Term_Write(17,15,'PAGE',4)
        CALL Term_Write(18,15,'DMC',3)
        CALL Term_Write(19,15,'CURRENT',7)
        CALL Term_Write(20,15,'FILES',5)
        CALL Term_Write(21,15,'EXIT, X or QUIT',15)
        CALL Stop_Highlite
        CALL Term_Write(17,19,'......To change page number',27)
        CALL Term_Write(18,18,'.......To change DMC number',27)
        CALL Term_Write(19,22,'...To change configuration letter',33)
        CALL Term_Write(20,20,'.....To change default file names',33)
      ENDIF
C
      IF (Change_2) THEN
        CALL Erase_Screen(7,1,6,65)
        CALL Term_Write(7,3,'Memory PAGE:',12)
        CALL Start_Highlite  
        CALL Term_Write(7,16,Conf_String(12+(Which*2)),2)
        CALL Stop_Highlite
C
        CALL Term_Write(8,3,'DMC Number:',11)
        CALL Start_Highlite  
        CALL Term_Write(8,16,Conf_String(11+(Which*2)),2)
        CALL Stop_Highlite
      ENDIF
C
      CALL Term_Write(9,3,'CURRENT Configuration:',22)
      Title(1:3) = '['//Common_Letter(Common_Active(Which),Which)//']'
      Title(5:35) = Common_Desc(Common_Active(Which),Which)    
      CALL Start_Highlite      
      CALL Term_Write(9,26,Title,35)
      CALL Stop_Highlite      
C
      IF (Change_3) THEN
        CALL Term_Write(10,3,'DEFAULT NAMES:',14)
        CALL Term_Write(11,17,'WAVEGEN  DATA FILE:',19)
        CALL Start_Highlite  
        CALL Term_Write(11,37,Conf_String(Offset(Which)+1),
     &                        Conf_Length(Offset(Which)+1))
        CALL Stop_Highlite
        CALL Term_Write(12,17,'TSDGEN   DATA FILE:',19)
        CALL Start_Highlite  
        CALL Term_Write(12,37,Conf_String(Offset(Which)+2),
     &                        Conf_Length(Offset(Which)+2))
        CALL Stop_Highlite
        CALL Term_Write(13,17,'TMSGEN   DATA FILE:',19)
        CALL Start_Highlite  
        CALL Term_Write(13,37,Conf_String(Offset(Which)+3),
     &                        Conf_Length(Offset(Which)+3))
        CALL Stop_Highlite
        CALL Term_Write(14,17,'XILINX   LOAD FILE:',19)
        CALL Start_Highlite  
        CALL Term_Write(14,37,Conf_String(Offset(Which)+4),
     &                        Conf_Length(Offset(Which)+4))
        CALL Stop_Highlite
        CALL Term_Write(15,17,'TMS CODE LOAD FILE:',19)
        CALL Start_Highlite  
        CALL Term_Write(15,37,Conf_String(Offset(Which)+5),
     &                        Conf_Length(Offset(Which)+5))
        CALL Stop_Highlite
      ENDIF
C
      RETURN
      END
C
C     ========================================
      SUBROUTINE Enter_Logicals(Read_Logicals)
C     ========================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INTEGER*2
     &  New_Index,                        ! Revised index for CONF array  
     &  Offset                            ! Offset in question array (SITE/STF)
C
      INTEGER*4
     &  Prompt_Length(Max_Logicals*2)    ,! Length of Question to be ask 
     &  Status                           ,! Status of input request
     &  len                              ,! Temporary length of answer
     &  Length                            ! Length of answer
C
      CHARACTER
     &  Answer*80,                        ! Answer to prompt   
     &  M_Wrong*43,                       ! Wrong input selected error message
     &  M_Wrong2*35,                      !
     &  Prompt_String(Max_Logicals*2)*33  !Question to be ask for each logicals    
C
      LOGICAL*1
     &  Quit_Ask,                         ! Quit configuration screen
     &  On_Site,                          ! On site flag 
     &  Read_Logicals,                    ! Read the logicals
     &  Cont_Ask,                         ! Continu to ask until right answer
     &  First /.TRUE./                    ! First pass flag
C
      INCLUDE 'aosdat.inc'
C
      DATA M_Wrong /'%CONF-E-ONLY: Select STF, SITE or QUIT only'/
      DATA   Prompt_String /'AOSUTY directory [CAE_AOS]?      ', !On site
     &                      'Simex directory [CAE_SIMEX]?     ',
     &                      'Ship directory [CAE_SHIP]?       ',
     &                      'CDB  directory [CAE_CDB]?        ',
     &                      'Help directory [AOS_HELP]?       ',
     &                      'Ship identification[CAE_SHIPNAM]?',
     &                      'Shipname description[CAE_SHIPNM]?',
     &                      'AOSUTY dir                       ', !On STF
     &                      'Simex directory [AOSUTY dir]?    ',
     &                      'Ship directory [AOSUTY dir]?     ',
     &                      'CDB  directory [AOSUTY dir]?     ',
     &                      'Help directory[AOSUTY dir]?      ',
     &                      'Ship identification ?            ',
     &                      'Shipname description ?           '/
      DATA   Prompt_Length/ 28,29,27,26,27,33,33,
     &                      19,29,28,28,27,21,22 /
C
      IF (First) THEN
C
C        Correct question for default library directory
C        ----------------------------------------------
         Call String_Length(Util_Dir,Length)
         IF (Length.GT.80) THEN
         ENDIF
         Prompt_String(8)(12:16+Length) ='['//Util_Dir(1:Length)//'] ?'
         Prompt_Length(8) = 16+Length
         First = .FALSE.
      ENDIF
C
C     Ask the user if on SITE or STF
C     ------------------------------
      Cont_Ask = .TRUE.
      DO WHILE (Cont_Ask)
      CALL Term_Write(11,5,'Configuration for SITE or STF (Quit)? ',38)
         CALL Term_Read(-1,Answer,Length,Status)
         IF(Answer(1:4).EQ.'SITE') THEN
C
C           If on site, ask the user for the directories and the DMC
C           number-page using default value if user enter blank
C           --------------------------------------------------------
            Conf_String(17) = 'TRUE'
            Conf_Length(17) = 4
            Cont_Ask = .FALSE.     ! Valid answer 
            Read_Logicals = .TRUE. 
            Offset = 0
            Quit_Ask = .FALSE.
            On_Site = .TRUE.
C
         ELSE IF (Answer(1:3).EQ.'STF') THEN
C
C           If on STF, just ask the input
C           -----------------------------
            Conf_String(11) = 'TRUE'
            Conf_Length(11) = 4
            Conf_String(12) = 'TRUE'
            Conf_Length(12) = 4
            Conf_String(17) = 'FALSE'
            Conf_Length(17) = 5
            DAS_In = .TRUE.
            DSS_In = .TRUE.
            Cont_Ask = .FALSE.     ! Valid answer 
            Read_Logicals = .TRUE. 
            Offset = Max_Logicals
            Quit_Ask = .FALSE.
            On_Site = .FALSE.
C
         ELSE IF (Answer(1:4).EQ.'QUIT'.OR.
     &            Answer(1:1).EQ.'Q'   .OR.
     &            Answer(1:1).EQ.'q'   .OR.
     &            Answer(1:1).EQ.'x') THEN
            Cont_Ask = .FALSE.     ! Valid answer 
            Read_Logicals = .FALSE.
            Quit_Ask = .TRUE.
         ELSE
            CALL Disp_Error(M_Wrong,43,1)
         ENDIF
      ENDDO
C
      IF (On_Site .AND. .NOT.Quit_Ask) THEN
        Cont_Ask = .TRUE.
        DO WHILE (Cont_Ask)
         CALL Term_Write(12,5,'Is the Digital Sound System installed on
     &this site ? ',53)
         CALL Term_Read(-1,Answer,Length,Status)
         IF(Answer(1:1).EQ.'Y'.OR.Answer(1:1).EQ.'y') THEN
            Default_group = .TRUE.
            Conf_String(11) = 'TRUE'
            Conf_Length(11) = 4
            DSS_In = .TRUE.
            Cont_Ask = .FALSE.     ! Valid answer 
C
         ELSE IF (Answer(1:1).EQ.'N'.OR.Answer(1:1).EQ.'n') THEN
            Conf_String(11) = 'FALSE'
            Conf_Length(11) = 5
            DSS_In = .FALSE.
            Cont_Ask = .FALSE.     ! Valid answer 
C
         ELSE
            CALL Disp_Error(M_Wrong2,35,1)
         ENDIF
        ENDDO
C
        Cont_Ask = .TRUE.
        DO WHILE (Cont_Ask)
         CALL Term_Write(13,5,'Is the Digital Audio System installed on
     &this site ? ',53)
         CALL Term_Read(-1,Answer,Length,Status)
         IF(Answer(1:1).EQ.'Y'.OR.Answer(1:1).EQ.'y') THEN
            Default_group = .FALSE.
            Conf_String(12) = 'TRUE'
            Conf_Length(12) = 4
            DAS_In = .TRUE.
            Cont_Ask = .FALSE.     ! Valid answer 
C
         ELSE IF (Answer(1:1).EQ.'N'.OR.Answer(1:1).EQ.'n') THEN
            Conf_String(12) = 'FALSE'
            Conf_Length(12) = 5
            DAS_In = .FALSE.
            Cont_Ask = .FALSE.     ! Valid answer 
C
         ELSE
            CALL Disp_Error(M_Wrong2,35,1)
         ENDIF
        ENDDO
      ENDIF
C
      IF (Read_Logicals) THEN
C
C        Enter all the directories
C        -------------------------
         DO Index=1,Max_Logicals
            New_Index = Index + Offset
            Array_Index = Array(Index) 
            CALL Term_Write(13+Index,5,Prompt_String(New_Index),
     &                              Prompt_Length(New_Index))
            CALL Term_Read(1,Answer,len,Status)
C
            IF (len.NE.0) THEN
              IF ((Answer(len:len).NE.'/').AND.(Index.LT.5)) THEN  
                Conf_String(Array_Index)(1:60) = Answer(1:len)//'/'
                Conf_Length(Array_Index) = len + 1
              ELSE
                Conf_String(Array_Index)(1:60) = Answer
                Conf_Length(Array_Index) = len
              ENDIF
C
            ELSE
               IF (On_Site) THEN
                  CALL Translate(Logicals(Index),Length_Logicals(Index),
     &                   Conf_String(Array_Index),
     &                   Conf_Length(Array_Index),Status)
                  IF (Status.NE.TL_NORMAL) THEN
C
C                    If fault during translation, ask the user to enter it
C                    -----------------------------------------------------
                     CALL Term_Write(14+Index,5,'Error: Unable to transl
     &ate logical name',39)
                     CALL Term_Write(14+Index,45,
     &                      Logicals(Index)(1:Length_Logicals(Index)),
     &                      Length_Logicals(Index))
                     CALL Term_Write(15+Index,5,'Enter translation > ',
     &                                                           20)
                     CALL Term_Read(1,Answer,Conf_Length(Array_Index),
     &                                       Status)
                     Conf_String(Array_Index) = Answer
                     CALL Term_Write(14+Index,5,Blank,75)
                     CALL Term_Write(15+Index,5,Blank,75)
                     IF (Status.NE.0) THEN
                        Cont_ask = .FALSE. 
                        Read_Logicals = .FALSE. 
                     ENDIF
                  ELSE
C
                     CALL Term_Write(13+Index,5,Blank,75)
                     CALL Term_Write(13+Index,5,'Translated Value : ',
     &                                                     19)
                     CALL Term_Write(13+Index,24,
     &                                  Conf_String(Array_Index),
     &                                  Conf_Length(Array_Index))
C
C                    Default value are LOG: Will be translated when XLINK file
C                    created
C                    ---------------------------------------------------------
                     Conf_String(Array_Index)= 'LOG'
                     Conf_Length(Array_Index) = 3
                  ENDIF
C
               ELSE
C
C                 On STF, just set the AOSUTY directory
C                 -------------------------------------
                  IF (Index.EQ.1) THEN
                     Conf_String(Array_Index) = Util_Dir
                     CALL String_Length(Util_Dir,
     &                       Conf_Length(Array_Index))
                  ELSE IF (Index.GE.9) THEN
                     Conf_String(Array_Index) = ' '
                     Conf_Length(Array_Index) = 1
                  ELSE
                     Conf_String(Array_Index) = Conf_String(1)
                     Conf_Length(Array_Index) = Conf_Length(1)
                  ENDIF
               ENDIF 
            ENDIF
C
         ENDDO
C
         CALL Wait_Time(2.0)
         CALL Erase_Screen(11,2,23,79)
      ENDIF
C
      RETURN
      END
C
C
C     =======================================
      SUBROUTINE GET_ERR_STR(ERR_NUMB,STRING)
C     =======================================
C
      IMPLICIT NONE
C
      INTEGER*4 
     &  ERR_NUMB       ! Error number
      CHARACTER*5 
     &  STRING         ! Error number in characters
C
      IF(ABS(ERR_NUMB).GT.99999) THEN
         ERR_NUMB = 0
         WRITE(STRING,'(I5.5)') ERR_NUMB
      ELSE
         WRITE(STRING,'(I5)') ERR_NUMB
      ENDIF
C
      RETURN
      END
C
C
C     ===================================
      SUBROUTINE Get_Logicals_Translation
C     ===================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      LOGICAL*1
     &  First /.TRUE./        ! First pass flag
C
      INTEGER*4 
     &  Status                ! Status of return routine
C
      INCLUDE 'aosdat.inc'
C
C
      DO Index=1,Max_Logicals
C 
         IF (Conf_String(Array(Index))(1:3).EQ.'LOG') THEN
            CALL Translate(Logicals(Index),Length_Logicals(Index),
     &                     Xlink_String(Array(Index)),
     &                     Xlink_Length(Array(Index)),Status)
            IF (Status.NE.TL_NORMAL) THEN
C
C              If fault during translation, ask the user to enter it
C              -----------------------------------------------------
               IF (First) THEN
                  CALL Clear_Screen
                  CALL Disp_Box(21,1,24,80,1,1,1,1,1,1,1,1,2)
                  First = .FALSE.
               ENDIF
               CALL Term_Write(22,5, 'Error: Unable to translate logical
     & name',39)
               CALL Term_Write(22,45,Logicals(Index)
     &                         (1:Length_Logicals(Index)),
     &                         Length_Logicals(Index))
               CALL Term_Write(23,5,'Enter translation > ',20)
               CALL Term_Read(1,Xlink_String(Array(Index)),
     &                          Xlink_Length(Array(Index)),Status)
            ENDIF
         ELSE
            Xlink_String(Array(Index)) = Conf_String(Array(Index)) 
            Xlink_Length(Array(Index)) = Conf_Length(Array(Index)) 
         ENDIF
C
      ENDDO 
C
      RETURN
      END      
C
C
C     ======================
      SUBROUTINE Help(Which)
C     ======================
C
C     This routine process the Help command
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INTEGER*2 
     &  Start_Xpos/7/,
     &  Help_Number
C
      INTEGER*4
     &  P_Length,
     &  Xpos,
     &  Length,
     &  Status,
     &  L_ERR_HLP/44/,
     &  L_ERR_OP/49/,
     &  Which
C
      LOGICAL*1 
     &  End_Of_File,
     &  Read_All,
     &  Help_Found,
     &  Quit_Pressed
C
      CHARACTER 
     &  Prompt*38, 
     &  Read_Line*80,
     &  ERR_OPEN*49,
     &  ERR_HELP*44
C
      INCLUDE 'aosdat.inc'
C
      DATA Prompt/' ... Press <CR> to continue or Quit ? '/
      DATA P_Length /38/
      DATA ERR_OPEN/'%HELP-E-OPEN, Error when opening HELP file  '/
      DATA ERR_HELP/'%HELP-E-READ : Error when reading HELP file '/
C
C     Check if HELP file exist
C     ------------------------
      CALL String_Length(Util_Dir,Length) 
      Filename = Xlink_String(6)(1:Xlink_Length(6))//'aosuty.hlp'
      CALL Check_File(Filename,Status)
      IF (Status.EQ.1) THEN
C
         CALL File_IO(3,1,Filename,Status,*1000)
C
         CALL Erase_Screen(5,1,24,80)
C
         CALL Start_Highlite 
         CALL Term_Write(5,33,'* AOSUTY HELP *',15)
         CALL Stop_Highlite 
C
         Xpos = Start_Xpos
         Help_Found = .FALSE. 
         End_Of_File = .FALSE.
         Quit_Pressed = .TRUE.
         DO WHILE (.NOT.End_Of_File)
           Read_All = .TRUE.
           DO WHILE (Read_All)
              READ (HLP_UNIT,'(A80)',IOSTAT=Status,ERR=1000,END=10) 
     &                                Read_Line
              Quit_Pressed = .FALSE.
              IF (.NOT.Help_Found) THEN
C
C                Look for special marker in file
C                -------------------------------
                 IF (Read_Line(1:2).EQ.'**') THEN
                    READ (Read_Line(3:3),'(I1)',ERR=2000,IOSTAT=Status) 
     &                                           Help_Number
                    IF (Help_Number.EQ.Which) THEN
                       Help_Found = .TRUE.
                    ENDIF  
                 ENDIF
C
              ELSE
C
C                Read each line belonging to the help
C                ------------------------------------
                 IF (Read_Line(1:2).NE.'**') THEN
                    CALL Term_Write(Xpos,1,Read_Line,80)
                    IF (Xpos.GE.22) THEN
                       CALL Wait_For_A_Key(23,19,Prompt,P_Length,
     &                                     Quit_Pressed)
                       IF (Quit_Pressed) THEN
                          End_Of_File = .TRUE.
                          Read_All = .FALSE.                   
                       ENDIF
                       Xpos = Start_Xpos
                    ELSE
                       Xpos = Xpos + 1
                    ENDIF
                 ELSE
                    End_Of_File = .TRUE.
                    Read_All = .FALSE.                   
                 ENDIF
              ENDIF
           ENDDO
C
C          Wait before leaving the HELP  
C          ----------------------------
           IF (.NOT.Quit_Pressed) THEN
              CALL Erase_Screen(Xpos,1,22,80)
              CALL Wait_For_A_Key(23,19,Prompt,P_Length,Quit_Pressed)
              IF (Quit_Pressed) THEN
                 End_Of_File = .FALSE.
              ENDIF
           ENDIF
C
           CALL File_IO(3,2,Filename,Status,*1000)
C
           GOTO 20
   10      End_Of_File = .TRUE.
C
C          Wait before leaving the HELP  
C          ----------------------------
           IF (.NOT.Quit_Pressed) THEN
              CALL Erase_Screen(Xpos,1,22,80)
              CALL Wait_For_A_Key(23,19,Prompt,P_Length,Quit_Pressed)
              IF (Quit_Pressed) THEN
                 End_Of_File = .FALSE.
              ENDIF
           ENDIF
   20      CONTINUE
         ENDDO
C
         GOTO 9999
 1000    CONTINUE
         CALL Disp_Error(ERR_HELP,L_ERR_HLP,1)
         GOTO 9999
 2000    CONTINUE
         CALL Disp_Error(ERR_HELP,L_ERR_HLP,1)
 9999    CONTINUE
C
         IF (Which .EQ. 1) THEN
            CALL Screen_Init(1)
         ELSE IF (Which .EQ. 2) THEN
            CALL Screen_Init(3)
         ENDIF
C
      ELSE
C
C        Error when opening HELP file
C        ----------------------------
         CALL Disp_Error(ERR_OPEN,L_ERR_OP,1)
      ENDIF
C
      RETURN
      END
C
C
C     =====================
      SUBROUTINE Initialize
C     =====================
C
C     This routine initializes all variables needed for the utility
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER*1   IBLANK(255) /255*' '/
      CHARACTER*255 I2BLANK
C
      INCLUDE 'aosdat.inc'
C
      EQUIVALENCE(IBLANK,I2BLANK)
C
C     Screen functions initialization
C     -------------------------------
      Blank = I2BLANK
C
C     Keyboard replacement entry
C     --------------------------
      REPL_N=15
      REPL_STR(1) = 'HELP'
      REPL_L(1)   = 4
      REPL_CODE(1)= ED_HLP
      REPL_STR(2) = 'BOX '
      REPL_L(2)   = 4
      REPL_CODE(2)= ED_CTW
      REPL_STR(3) = 'CONF'
      REPL_L(3)   = 4
      REPL_CODE(3)= ED_F17
      REPL_STR(4) = 'GROU'
      REPL_L(4)   = 4
      REPL_CODE(4)= ED_F18
      REPL_STR(5) = 'MODE'
      REPL_L(5)   = 4
      REPL_CODE(5)= ED_F19
      REPL_STR(6) = 'EXIT'
      REPL_L(6)   = 4
      REPL_CODE(6)= ED_F20
      REPL_STR(7) = 'HARM'
      REPL_L(7)   = 4
      REPL_CODE(7)= ED_F06
      REPL_STR(8) = 'TMSG'
      REPL_L(8)   = 4
      REPL_CODE(8)= ED_F07
      REPL_STR(9) = 'TSDG'
      REPL_L(9)   = 4
      REPL_CODE(9)= ED_F08
      REPL_STR(10) = 'WAVG'
      REPL_L(10)   = 4
      REPL_CODE(10)= ED_F09
      REPL_STR(11) = 'EXIT'
      REPL_L(11)   = 4
      REPL_CODE(11)= ED_F10
      REPL_STR(12) = 'FIRG'
      REPL_L(12)   = 4
      REPL_CODE(12)= ED_F11
      REPL_STR(13) = 'FORM'
      REPL_L(13)   = 4
      REPL_CODE(13)= ED_F12
      REPL_STR(14) = 'EXIT'
      REPL_L(14)   = 4
      REPL_CODE(14)= ED_EXIT
      REPL_STR(15) = '    '
      REPL_L(15)   = 0
      REPL_CODE(15)= ED_ENT
C
      RETURN
      END
C
C
C     ========================================
      SUBROUTINE Modify_Current(A_S,New_Entry)
C     ========================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER
     &  Item(6)*15, 
     &  Command*80,
     &  Answer*80,
     &  M_Errcom*41,           !
     &  M_Erramb*43,           !
     &  M_ACTNEW*59
C
      CHARACTER 
     &  YESTR*3/'YES'/,
     &  M_INVLET*49,
     &  M_MAX*60,
     &  M_LETMAX*45,
     &  M_DESCMAX*46,
     &  M_DELNF*44,
     &  M_EXIST*52
C
      LOGICAL*1
     &  Cont_Read,
     &  Cont_Ask,
     &  Exist,
     &  New_Entry         !If changes have been made...
C
      INTEGER*2 
     &  Number, 
     &  ASCII_Char,
     &  Del_Index
C
      INTEGER*4
     &  L_Com,
     &  Status,
     &  A_S,
     &  Which,
     &  Length
C
      INCLUDE 'aosdat.inc'
C
      DATA Item /'ADD            ','DELETE        ',
     &           'SET            ','QUIT          ',
     &           'EXIT           ','X             '/
C
      DATA M_Erramb /'%PARSE-E-AMBIG, Ambiguous command specified'/
      DATA M_Errcom /'%PARSE-E-INVAL, Invalid command specified'/
      DATA M_ACTNEW /'%SIM-I-DEL,  Actual configuration is deleted , now
     & set to 1'/
      DATA M_MAX   /'%SIM-W-MAX,  Maximum number of configurations (6) i
     &s reached'/
      DATA M_EXIST  /'%SIM-E-EXIST,This configuration exist, nothing add
     &ed'/
      DATA M_INVLET/'%SIM-E-LET,  Invalid letter, should be 0-9 or A-Z'/
      DATA M_LETMAX /'%SIM-E-MAX,  Maximum number of character is 1'/
      DATA M_DESCMAX/'%SIM-E-MAX,  Maximum number of character is 31'/
      DATA M_DELNF  /'%SIM-E-FIND, Simulator not found in the list'/
C
      New_Entry = .FALSE.
      Cont_Read = .TRUE. 
C
      CALL Disp_Current(A_S,T,T,T) 
      DO WHILE (Cont_Read)
         CALL Read_Command(-1,19,5, 'Add, Delete, Set, Quit ? ',
     &                     25,Command,L_Com,Status)
         CALL Parse_Command(Command,L_Com,6,Item,Which,Status)
C
         IF (Status.EQ.0) THEN
            IF (Which.EQ.1) THEN
C
C              ADD command 
C              -----------
               IF (Common_Max_Config(A_S).EQ.Max_Config) THEN
                  CALL Disp_Error(M_MAX,60,1)
               ELSE
                  IF (Common_Max_Config(A_S).EQ.1 .AND.
     &                Common_Letter(1,A_S).EQ.Letter_default) THEN
                     Common_Max_Config(A_S) = 1
                     Common_Desc(1,A_S) = Blank(1:31)
                  ELSE
                     Common_Max_Config(A_S) = Common_Max_Config(A_S)+1
                  ENDIF
                  Cont_Ask = .TRUE.
                  DO WHILE (Cont_Ask)
                     CALL Term_Write(19,5,
     &                    'ADD: Enter configuration LETTER > ',34)
                     CALL Term_Read(1,Answer,Length,Status)
                     Common_Letter(Common_Max_Config(A_S),A_S) = 
     &                                 Answer(1:1)
                     ASCII_Char = 
     &                 ICHAR(Common_Letter(Common_Max_Config(A_S),A_S))
C
C                    Only ASCII letter or number accepted 
C                    ------------------------------------
                     IF (ASCII_Char.LT.48.OR.ASCII_Char.GT.122.OR.
     &                  (ASCII_Char.GE.58.AND.ASCII_Char.LE.64))THEN
                        CALL Disp_Error(M_INVLET,49,1)
                        CALL Term_Write(19,2,BLANK,63)
                     ELSE
C      
C                       Only one character accepted
C                       ---------------------------
                        IF (Length.GT.1) THEN
                           CALL Disp_Error(M_LETMAX,45,1)
                           CALL Term_Write(19,2,BLANK,63)
                        ELSE
C
C                          Check for already existing letter
C                          ---------------------------------
                           Exist = .FALSE. 
                           Index = 1
                           DO WHILE ( 
     &                         (Index.LE.(Common_Max_Config(A_S)-1))
     &                                       .AND.(.NOT.Exist) )
                              IF (Common_Letter(Index,A_S).EQ.
     &                            Common_Letter(Common_Max_Config(A_S),
     &                            A_S) ) THEN
                                 CALL Disp_Error(M_EXIST,52,1)
                                 CALL Term_Write(19,2,BLANK,63)
C
C                                Remove letter in the list
C                                -------------------------
                                 Common_Desc(Common_Max_Config(A_S),A_S)
     &                                      = Blank(1:31)
                                 Common_Letter(Common_Max_Config(A_S),
     &                                      A_S) = ' '
                                 Common_Max_Config(A_S) = 
     &                                       Common_Max_Config(A_S)-1
                                 Exist = .TRUE.
                              ENDIF
                              Index = Index + 1
                           ENDDO
                           IF (.NOT.Exist) THEN
                              Cont_ask = .FALSE.
                           ENDIF
                        ENDIF 
                     ENDIF
                  ENDDO
C
                  Cont_Ask = .TRUE.
                  DO WHILE (Cont_Ask)
                     CALL Term_Write(20,3,Blank,77)
                     CALL Term_Write(20,5,'ADD: Enter DESCRIPTION > ',
     &                                                      25)
                     CALL Term_Read(-1,Answer,Length,Status)
                     Common_Desc(Common_Max_Config(A_S),A_S) = 
     &                                      Answer(1:31)
                     IF (Length.GT.31) THEN
                        CALL Disp_Error(M_DESCMAX,46,1)
                        CALL Term_Write(20,2,BLANK,63)
                     ELSE
                        Cont_Ask = .FALSE.
                     ENDIF
                  ENDDO
C
                  CALL Disp_Current(A_S,F,F,T) 
C
                  CALL Term_Write(21,5,'ADD: Is new entry OK [Y]? ',26)
                  CALL Term_Read(0,Answer,Length,Status)
                  IF (Length.GT.0.AND.
     &                Answer(1:Length).NE.YESTR(1:Length))THEN
C
C                    Remove the entry
C                    ----------------
                     IF (Common_Max_Config(A_S).GT.1) THEN
                        Common_Desc(Common_Max_Config(A_S),A_S) = 
     &                                 Blank(1:31)
                        Common_Letter(Common_Max_Config(A_S),A_S) = ' '
                        Common_Max_Config(A_S) =Common_Max_Config(A_S)-1
                     ELSE
                        Common_Desc(1,A_S) ='Default Simulator Configura
     &tion'   
                        Common_Letter(1,A_S) = 'C '
                        Common_Max_Config(A_S) = 1
                     ENDIF
                     CALL Disp_Current(A_S,F,T,T)
                  ELSE
                     New_Entry = .TRUE.
                  ENDIF
               ENDIF
C
            ELSE IF (Which.EQ.2) THEN
C
C              DELETE command
C              --------------
               CALL Term_Write(19,5,'DEL: Enter configuration LETTER > '
     &                                         ,34)
               CALL Term_Read(1,Answer,Length,Status)
               IF (Length.GT.1) THEN
                  CALL Disp_Error(M_LETMAX,45,1)
                  CALL Term_Write(19,2,BLANK,63)
               ELSE
                  Exist = .FALSE.
                  Index = 1
                  DO WHILE (Index.LE.Common_Max_Config(A_S).AND.
     &                             .NOT.Exist)
                     IF (Common_Letter(Index,A_S).EQ.Answer(1:1))THEN
                           Exist = .TRUE.
                           Del_Index = Index
                     ENDIF
                     Index = Index + 1
                  ENDDO
C
                  IF (Exist) THEN
                     IF (Common_Active(A_S).EQ.Del_Index) THEN
                        Common_Active(A_S) = 1
                        CALL Disp_Error(M_ACTNEW,59,1)
                     ENDIF
                     DO Index=Del_Index,Common_Max_Config(A_S)-1
                        IF (Common_Active(A_S).EQ.(Index+1)) THEN
                              Common_Active(A_S) = Index
                        ENDIF
                        Common_Letter(Index,A_S) = 
     &                          Common_Letter(Index+1,A_S)
                        Common_Desc(Index,A_S) = 
     &                          Common_Desc(Index+1,A_S)
                     ENDDO
                     Common_Letter(Common_Max_Config(A_S),A_S) = ' '
                     Common_Desc(Common_Max_Config(A_S),A_S) = 
     &                                      Blank(1:31)
                     Common_Max_Config(A_S)=Common_Max_Config(A_S)-1
                     IF (Common_Max_Config(A_S).EQ.0) THEN
                        Common_Max_Config(A_S) = 1
                        Common_Active(A_S) = 1
                        Common_Letter(1,A_S) = Letter_Default
                        Common_Desc(1,A_S) = 'Default simulator configur
     &ation'
                     ENDIF
                     CALL Erase_Screen(12,1,17,70)
                     CALL Disp_Current(A_S,F,T,T) 
                     New_Entry = .TRUE.
                  ELSE
                     CALL Disp_Error(M_DELNF,44,1)
                  ENDIF
               ENDIF
C
            ELSE IF (Which.EQ.3) THEN
C
C              SET command 
C              -----------
               CALL Term_Write(19,5,'SET: Enter configuration LETTER > '
     &                                    ,34)
               CALL Term_Read(1,Answer,Length,Status)
               IF (Length.GT.1) THEN
                  CALL Disp_Error(M_LETMAX,45,1)
                  CALL Term_Write(19,33,BLANK,32)
               ELSE
                  Exist = .FALSE.
                  Index = 1
                  DO WHILE(Index.LE.Common_Max_Config(A_S).AND.
     &                            .NOT.Exist)
                     IF (Common_Letter(Index,A_S).EQ.Answer(1:1))THEN
                        Exist = .TRUE.
                        Common_Active(A_S) = Index
                        New_Entry = .TRUE.
                     ENDIF
                     Index = Index + 1
                  ENDDO
                  IF (.NOT.Exist) THEN
                     CALL Disp_Error(M_DELNF,44,1)
                  ELSE               
                     CALL Disp_Current(A_S,F,T,F)
                  ENDIF
               ENDIF  
C
C
            ELSE IF (Which.GE.4) THEN
C
C              QUIT, EXIT, or X command
C              ------------------------
               Cont_Read = .FALSE.
            ENDIF
            CALL Erase_Screen(19,1,22,80)
         ELSE
C
C           Print error message when input not found or invalid
C           ---------------------------------------------------  
            IF (Status.EQ.1) THEN
               CALL Disp_Error(M_ERRCOM,41,1)
            ELSE
               CALL Disp_Error(M_ERRAMB,43,1)
            ENDIF
         ENDIF
C
      ENDDO
      CALL Clear_Disp(6)
      CALL Disp_Specific(A_S,T,T,T)
C
      RETURN
C
      END
C
C
C     ====================================
      SUBROUTINE Modify_Files(A_S,Changed)
C     ====================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INTEGER*2
     &  Offset(2),
     &  Ind
C
      INTEGER*4
     &  Status,
     &  Pos,
     &  Prompt_L,
     &  Length,
     &  A_S
C
      LOGICAL*1
     &  Changed,
     &  Cont_Read
C
      CHARACTER
     &  Prompt*40,
     &  Question(5)*18,
     &  Answer*80
C
      INCLUDE 'aosdat.inc'
C
      DATA Offset / 25 , 20 /,
     &     Question  /'WAVEGEN  DATA FILE',
     &                'TSDGEN   DATA FILE',
     &                'TMSGEN   DATA FILE',
     &                'XILINX   LOAD FILE',
     &                'TMS CODE LOAD FILE'/
C
      Cont_Read = .TRUE.
      CALL Clear_Disp(6)
      CALL Term_Write(8,20,'Enter names of the input data files',35)
      CALL Term_Write(9,20,'that will be read by the utilities.',35)
      Pos = 11
      DO Ind = 1,5
         Prompt = Question(Ind)(1:18)//'['//Conf_String(Offset(A_S)+
     &            Ind)(1:Conf_Length(Offset(A_S)+Ind))//'] ?'
         Prompt_L = 19 + Conf_Length(Offset(A_S)+Ind) + 3
         CALL Term_Write(Pos,10,Prompt,Prompt_L)
         CALL Term_Read(1,Answer,Length,Status)
         IF (Length.GT.0) THEN
            Conf_String(Offset(A_S)+Ind) = Answer(1:Length)
            Conf_Length(Offset(A_S)+Ind) = Length
            Changed = .TRUE.
         ENDIF
         Pos = Pos + 1
      ENDDO
      CALL Disp_Specific(A_S,T,T,T)
C
      RETURN
      END
C
C
C     ====================================
      SUBROUTINE Modify_General(Changed)
C     ====================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      INTEGER*4
     &  col,
     &  posi,
     &  L_Com,
     &  Which,
     &  New_ConfL(8),
     &  Status
C
      LOGICAL*1
     &  Quit_ask,
     &  Changed,
     &  Cont_Read
C
      CHARACTER
     &  New_ConfS(8)*80,
     &  Command*80,
     &  M_Errcom*41,           !
     &  M_Erramb*43,           !
     &  Item(4)*15
C
      INCLUDE 'aosdat.inc'
C
      DATA M_Erramb /'%PARSE-E-AMBIG, Ambiguous command specified'/
      DATA M_Errcom /'%PARSE-E-INVAL, Invalid command specified'/
C
      DATA Item /'CHANGE         ',
     &           'QUIT           ','EXIT          ',
     &           'X              '/ 
C
      Cont_Read = .TRUE.
      Quit_ask = .FALSE.
C
      DO WHILE(.NOT.Quit_ask)
        CALL Clear_Disp(6)
        CALL Term_Write(8,20,'Type of configuration:',22)
        CALL Term_Write(9,20,'DSS installed:',14)
        CALL Term_Write(10,20,'DAS installed:',14)
        CALL Term_Write(11,20,'AOSUTY directory:',17)
        CALL Term_Write(12,20,'SIMEX  directory:',17)
        CALL Term_Write(13,20,'SHIP   directory:',17)
        CALL Term_Write(14,20,'CDB    directory:',17)
        CALL Term_Write(15,20,'HELP   directory:',17)
        CALL Term_Write(16,20,'Ship ID:',8)
        CALL Term_Write(17,20,'Ship NAME:',10)
C
        CALL Start_Highlite
        IF (Conf_String(17)(1:4) .EQ. 'TRUE') THEN
           CALL Term_Write(8,43,'SITE',4)
        ELSE
           CALL Term_Write(8,43,'STF',3)
        ENDIF
C
        IF (DSS_In) THEN
           CALL Term_Write(9,35,'YES',3)
        ELSE
           CALL Term_Write(9,35,'NO',2)
        ENDIF
C
        IF (DAS_In) THEN
           CALL Term_Write(10,35,'YES',3)
        ELSE
           CALL Term_Write(10,35,'NO',2)
        ENDIF
C
C
C
        DO Index=1,Max_Logicals
C  
           IF (Conf_String(Array(Index))(1:3).EQ.'LOG') THEN
              CALL Translate(Logicals(Index),Length_Logicals(Index),
     &                     New_ConfS(Index),New_ConfL(Index),Status)
           ELSE
              New_ConfS(Index) = Conf_String(Array(Index)) 
              New_ConfL(Index) = Conf_Length(Array(Index)) 
           ENDIF
C
           posi = 10 + Index
           IF (Index .GE. 6) THEN
             col = 31
           ELSE
             col = 38
           ENDIF
           CALL Term_Write(posi,col,New_ConfS(Index),New_ConfL(Index))
        ENDDO 
C
        CALL Stop_Highlite
C
        cont_read = .TRUE.
        DO WHILE (cont_read)
          CALL Read_Command(-1,23,1, 'CHANGE or QUIT >',16,Command,
     &                     L_Com,Status)
          CALL Parse_Command(Command,L_Com,4,Item,Which,Status)
C
          IF (Status.EQ.0) THEN
             cont_read = .FALSE.
             IF (Which.EQ.1) THEN
                CALL Clear_Disp(8)
                CALL Enter_Logicals(Changed)
             ELSE
                Quit_ask = .TRUE.
             ENDIF
          ELSE
C
C           Print error message when input not found or invalid
C           ---------------------------------------------------  
            IF (Status.EQ.1) THEN
               CALL Disp_Error(M_ERRCOM,41,1)
            ELSE
               CALL Disp_Error(M_ERRAMB,43,1)
            ENDIF
          ENDIF
C
        ENDDO
C
      ENDDO
C
C
      RETURN
      END
C
C
C     =========================================
      SUBROUTINE Modify_Specific(A_S,New_Entry)
C     =========================================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER
     &  M_Invalid*30,
     &  M_Noinput*36,
     &  Item(7)*15, 
     &  Command*80,
     &  Answer*80,
     &  M_Errcom*41,           !
     &  M_Erramb*43            !
C
      CHARACTER 
     &  YESTR*3/'YES'/
C
      LOGICAL*1
     &  Cont_Read,
     &  New_Entry         !If changes have been made...
C
      INTEGER*2 
     &  Number
C
      INTEGER*4
     &  L_Com,
     &  Which,
     &  Status,
     &  A_S,
     &  Length
C
      INCLUDE 'aosdat.inc'
C
      DATA Item /'PAGE           ','DMC           ',
     &           'CURRENT        ','FILES         ',
     &           'QUIT           ','EXIT          ',
     &           'X              '/ 
      DATA M_Erramb /'%PARSE-E-AMBIG, Ambiguous command specified'/
      DATA M_Errcom /'%PARSE-E-INVAL, Invalid command specified'/
      DATA M_Invalid/'%CONF-E-NUMBER, Invalid number'/
      DATA M_Noinput/'%CONF-E-NONUMBER, No entry specified'/
C
      New_Entry = .FALSE.
      Cont_Read = .TRUE. 
C
      CALL Disp_Specific(A_S,T,T,T)
      DO WHILE (Cont_Read)
         CALL Erase_Screen(23,1,23,65)
         CALL Read_Command(-1,23,1, 'Enter Command >',15,Command,
     &                     L_Com,Status)
         CALL Parse_Command(Command,L_Com,7,Item,Which,Status)
C
         IF (Status.EQ.0) THEN
            IF (Which.EQ.1) THEN
C
C              PAGE command
C              ------------
               CALL Term_Write(23,1,'PAGE: Enter DMC Page in Hex > '
     &                                        ,30)
               CALL Term_Read(0,Answer,Length,Status)
               IF (Length.EQ.0) THEN
                  CALL Disp_Error(M_Noinput,36,1)
               ELSE
                  IF (Length.EQ.1) THEN
                     Answer(2:2) = Answer(1:1)
                     Answer(1:1) = ' '
                     Length = 2
                  ENDIF
C
C                 Read DMC memory page or DMC number and check for valid
C                 input 
C                 ------------------------------------------------------
                  Read (Answer(1:2),'(Z2)',ERR=91) Number 
                  IF (Number.LT.0.OR.Number.GT.Max_PAGE_Number) THEN
                     CALL Disp_Error(M_Invalid,30,1)
                  ELSE
                     New_Entry = .TRUE.
                     Conf_String(12+(A_S*2))(1:2) = Answer(1:2)
                     Conf_Length(12+(A_S*2)) = Length 
                     CALL Disp_Specific(A_S,F,T,F) 
                  ENDIF
               ENDIF
C
            ELSE IF (Which.EQ.2) THEN
C
C              DMC command
C              -----------
               CALL Term_Write(23,1,'DMC: Enter DMC number in Hex > '
     &                                        ,31)
               CALL Term_Read(0,Answer,Length,Status)
               IF (Length.EQ.0) THEN
                  CALL Disp_Error(M_Noinput,36,1)
               ELSE
                  IF (Length.EQ.1) THEN
                     Answer(2:2) = Answer(1:1)
                     Answer(1:1) ='0'
                     Length = 2
                  ENDIF
C
C                 Read DMC memory page or DMC number and check for valid
C                 input (0 to 15)
C                 ------------------------------------------------------
                  Read (Answer(1:2),'(Z2)',ERR=90) Number 
                  IF (Number.LE.0.OR.Number.GT.Max_DMC_Number) THEN
                     CALL Disp_Error(M_Invalid,30,1)
                  ELSE
                     New_Entry = .TRUE.
                     Conf_String(11+(A_S*2))(1:2) = Answer(1:2)
                     Conf_Length(11+(A_S*2)) = Length 
                     CALL Disp_Specific(A_S,F,T,F) 
                  ENDIF
               ENDIF
C
            ELSE IF (Which.EQ.3) THEN
C
C             CURRENT COMMAND
C             ---------------
              CALL Modify_Current(A_S,New_Entry)
C
            ELSE IF (Which.EQ.4) THEN
C
C             FILE COMMAND
C             ------------
              CALL Modify_Files(A_S,New_Entry)
C
            ELSE IF (Which.GE.5) THEN
C
C              QUIT, EXIT, or X command
C              ------------------------
               Cont_Read = .FALSE.
            ENDIF
         ELSE
C
C           Print error message when input not found or invalid
C           ---------------------------------------------------  
            IF (Status.EQ.1) THEN
               CALL Disp_Error(M_ERRCOM,41,1)
            ELSE
               CALL Disp_Error(M_ERRAMB,43,1)
            ENDIF
         ENDIF
C
      ENDDO
C
      Call Clear_Disp(5)
      CALL Screen_Init(3)
C
      RETURN
C
 90   CONTINUE
 91   CONTINUE
      CALL Disp_Error(M_Invalid,30,1)
      RETURN
C
      END
C
C     ============================
      SUBROUTINE Read_Conf(Status)
C     ============================
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER  
     &  M_Interr*48           !
C
      INTEGER*4
     &  Length,
     &  Status
C
      INCLUDE 'aosdat.inc'
C
      DATA M_Interr /'%CONF-E-FILE, Internal error #      on CNF file '/
C
      CALL String_Length(Util_Dir,Length) 
      Filename = Util_Dir(1:Length)//'aoslib.cnf'
      CALL File_IO(1,1,Filename,Status,*1000)
C
      IF (Status.EQ.0) THEN    
C
C        If OK, read the parameters from the AOSUTY.CNF file
C        ---------------------------------------------------
         DO Index = 1,30
            READ (CNF_UNIT,500,IOSTAT=Status,ERR=1000) 
     &           Conf_Length(Index),
     &           Conf_string(Index)(1:Conf_Length(Index))
         ENDDO
C
         IF (Conf_String(11)(1:4).EQ.'TRUE') THEN
            DSS_In = .TRUE.
         ELSE
            DSS_In = .FALSE.
         ENDIF
C
         IF (Conf_String(12)(1:4).EQ.'TRUE') THEN
            DAS_In = .TRUE.
         ELSE
            DAS_In = .FALSE.
         ENDIF
C
         READ (CNF_UNIT,501,IOSTAT=Status,ERR=1000) Audio_Max_Config
         READ (CNF_UNIT,501,IOSTAT=Status,ERR=1000) Sound_Max_Config
         READ (CNF_UNIT,501,IOSTAT=Status,ERR=1000) Audio_Active
         READ (CNF_UNIT,501,IOSTAT=Status,ERR=1000) Sound_Active
         DO Index=1,Audio_Max_Config
            READ (CNF_UNIT,503,IOSTAT=Status,ERR=1000) 
     &               Audio_Letter(Index),
     &               Audio_Desc(Index)
         ENDDO
C
         DO Index=1,Sound_Max_Config
            READ (CNF_UNIT,503,IOSTAT=Status,ERR=1000) 
     &               Sound_Letter(Index),
     &               Sound_Desc(Index)
         ENDDO
C
         CALL File_IO(1,3,Filename,Status,*1000)
C
      ENDIF
C
 1000 IF (Status.NE.0) THEN
C
C        Print error message during operation
C        ------------------------------------
         CALL GET_ERR_STR(Status,M_INTERR(31:35))
         CALL Disp_Error(M_INTERR,48,1)
      ENDIF
C
      RETURN
C
 500  FORMAT(T2,I4,A)
 501  FORMAT(T2,I2)
 502  FORMAT(T2,A1)
 503  FORMAT(T2,A1,1X,A31)
C
      END
C
C
C     =============================
      SUBROUTINE Screen_Init(Which)
C     =============================
C
C     This routine initialize the screen for AOSUTY
C
      IMPLICIT NONE
C
      INCLUDE 'aospar.inc'
C
      CHARACTER
     &  Shipname*21,
     &  Print_String*80
C
      INTEGER*2 
     &  Length 
C
      INTEGER*4
     &  Which
C
      INCLUDE 'aosdat.inc'
C
C
      IF (Which.EQ.1) THEN
C
C        Draw the main display
C        ---------------------
         CALL Clear_Screen
C
         CALL Disp_Box(1,1,4,67,1,3,3,1,1,1,1,1,1)
         CALL Disp_Box(1,1,4,14,0,3,3,0,0,1,0,0,0)
         CALL Disp_Box(1,67,4,80,0,1,1,0,1,1,1,0,-1)
C
C        Identify the utility
C        --------------------
         CALL Start_Highlite
         Print_String = BLANK
         Print_String(1:6) = 'AOSUTY'
         CALL Term_Write(2,5,Print_String,6)
         Print_String(1:8) = 'Rev     '
         WRITE (Print_String(5:8),'(F4.1)') Rev_Level
         CALL Term_Write(3,4,Print_String,8)
C
         Print_String(1:31) = 'AUDIO & SOUND UTILITIES LIBRARY'
         CALL Term_Write(2,25,Print_String,31)
         CALL Stop_Highlite
C
         Shipname = Xlink_String(10)
         IF (Xlink_Length(10).LT.21) THEN
            Shipname(Xlink_Length(10)+1:21) = 
     &                     Blank(1:21-Xlink_Length(10))
         ENDIF             
         Print_String(1:36) =' Ship Name : ['//Shipname//']'
         CALL Term_Write(3,23,Print_String,36)
C
         CALL Term_Write(2,72,'MODE',4)
         CALL Disp_Mode
C
         CALL Disp_Group
C
         CALL Disp_Box(5,1,20,39,1,1,1,1,1,1,1,1,1)
         CALL Disp_Box(5,41,20,80,1,1,1,1,1,1,1,1,-1)
C
         IF (Conf_String(12)(1:4).EQ.'TRUE') THEN
           CALL Term_Write(8,2,'DMC #         : ',16)
           CALL Term_Write(8,18,Conf_String(13),2)
           CALL Term_Write(9,2,'Memory Page # : ',16)
           CALL Term_Write(9,18,Conf_String(14),2)
           CALL Term_Write(10,2,'Config Type   : [ ]',19)
           CALL Term_Write(10,19,Audio_Letter(Audio_Active),1)
           CALL Term_Write(11,2,'Desc: ',6)
           CALL Term_Write(11,8,Audio_Desc(Audio_Active),31)
           CALL Term_Write(13,11,'HARMONY Rev 2.3',15)
           CALL Term_Write(14,11,'TMSGEN  Rev 1.2',15)
           CALL Term_Write(15,11,'WAVEGEN Rev 1.3',15)
           CALL Term_Write(16,11,'TSDGEN  Rev 1.3',15)
           CALL Term_Write(17,11,'FIRGEN  Rev 1.0',15)
           CALL Term_Write(18,11,'FORMGEN Rev 2.1',15)
         ELSE
           CALL Term_Write(11,9,'DIGITAL AUDIO SYSTEM',20)
           CALL Term_Write(12,11,'NOT INSTALLED ON',16)
           CALL Term_Write(13,12,'THIS SIMULATOR',14)
         ENDIF
C
         IF (Conf_String(11)(1:4).EQ.'TRUE') THEN
           CALL Term_Write(8,42,'DMC #         : ',16)
           CALL Term_Write(8,58,Conf_String(15),2)
           CALL Term_Write(9,42,'Memory Page # : ',16)
           CALL Term_Write(9,58,Conf_String(16),2)
           CALL Term_Write(10,42,'Config Type   : [ ]',19)
           CALL Term_Write(10,59,Sound_Letter(Sound_Active),1)
           CALL Term_Write(11,42,'Desc: ',6)
           CALL Term_Write(11,48,Sound_Desc(Sound_Active),31)
           CALL Term_Write(13,51,'HARMONY Rev 2.3',15)
           CALL Term_Write(14,51,'TMSGEN  Rev 1.2',15)
           CALL Term_Write(15,51,'WAVEGEN Rev 1.3',15)
           CALL Term_Write(16,51,'TSDGEN  Rev 1.3',15)
           CALL Term_Write(18,51,'FORMGEN Rev 2.1',15)
         ELSE
           CALL Term_Write(11,50,'DIGITAL SOUND SYSTEM',20)
           CALL Term_Write(12,52,'NOT INSTALLED ON',16)
           CALL Term_Write(13,53,'THIS SIMULATOR',14)
         ENDIF
C
         CALL Term_Write(22,24,'Type HELP for the list of commands',34)
C
      ELSE IF (Which.EQ.2 .OR. Which.EQ.3) THEN
C
C        This is the configuration screen
C        -------------------------------- 
         CALL Clear_Screen
         CALL Disp_Box(1,1,4,67,1,3,3,1,1,1,1,1,1)
         CALL Disp_Box(1,1,4,14,0,3,3,0,0,1,0,0,0)
         CALL Disp_Box(1,67,4,80,0,1,1,0,1,1,1,0,-1)
         Print_String = BLANK
         CALL Start_Highlite
         Print_String(1:6) = 'AOSUTY'
         CALL Term_Write(2,5,Print_String,6)
         Print_String(1:8) = 'Rev     '
         WRITE (Print_String(5:8),'(F4.1)') Rev_Level
         CALL Term_Write(3,4,Print_String,8)
         Print_String(1:31) = 'AUDIO & SOUND UTILITIES LIBRARY'
         CALL Term_Write(2,25,Print_String,31)
         CALL Start_Reverse
         CALL Term_Write(3,15,Blank,51)
         CALL Term_Write(3,34,'CONFIGURATION',13)
         CALL Stop_Reverse
         CALL Stop_Highlite
         CALL Term_Write(2,72,'MODE',4)
         CALL Disp_Mode
C
         IF (Which.EQ.2) THEN
            CALL Start_Reverse
            CALL Term_Write(5,1,'                               GENERAL
     & CONFIGURATION                            ',80)
            CALL Stop_Reverse
         ENDIF
C
         IF (Which.EQ.3) THEN
           CALL Start_Reverse
           CALL Term_Write(5,1,'                               LIST OF C
     &OMMANDS                                 ',80)
           CALL Stop_Reverse
           CALL Start_Highlite
           CALL Term_Write(7,14,'AUDIO  :',8) 
           CALL Term_Write(8,14,'SOUND  :',8) 
           CALL Term_Write(9,14,'GENERAL:',8) 
           CALL Term_Write(10,14,'HELP, BOX, EXIT, X OR QUIT',26) 
           CALL Stop_Highlite
C
           CALL Term_Write(7,23,'To change AUDIO specific configuration'
     &                                                     ,38)
           CALL Term_Write(8,23,'To change SOUND specific configuration'
     &                                                     ,38)
           CALL Term_Write(9,23,'To change GENERAL configuration',31)
         ENDIF
C
      ENDIF
 99   CONTINUE
      RETURN
      END
