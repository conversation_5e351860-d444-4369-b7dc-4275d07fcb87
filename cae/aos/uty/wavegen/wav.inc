C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                   WG.INC
C
C  This module contains global variables and COMMON declarations for
C  the WAVEGEN utility.
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C  Parameters
C  ----------
C
      INTEGER*2
     & MAXDMC,MAXSLOT,MAXSIZE,MAXSRC,MAXMACRO,MAXEQN,MAXTBL,MAXLAB,
     & MAXCALL,MAXCW,
     & Config_L(12),
     & File_L(5),
     & Comp_Id,
     & L_DATA_DIR,
     & L_INT_DIR
C
      INTEGER*4
     & DATFILID,INFFILID,SIZFILID,DLDFILID,INCFILID
C
      CHARACTER*7
     & DEFNAME
C
      PARAMETER (MAXDMC =    2)         !Maximum number of DMCs
      PARAMETER (MAXSLOT =  24)         !Maximum number of SLOT entries
      PARAMETER (MAXSIZE =  64)         !Maximum number of SIZE entries
      PARAMETER (MAXSRC =   96)         !Maximum number of SOURCE entries
      PARAMETER (MAXMACRO = 20)         !Maximum number of MACRO definitions
                                        !in a DECLARATION block
      PARAMETER (MAXEQN =   40)         !Maximum number of equations
                                        !in a MACRO definition
      PARAMETER (MAXTBL =   MAXEQN/2)   !Maximum number of TBL parameters
                                        !(There is 1 TBL per equation pair)
      PARAMETER (MAXLAB =   MAXEQN*3)   !Maximum number of LABELs
                                        !(There are 3 labels per equation)
      PARAMETER (MAXCALL =  50)         !Maximum number of MACRO calls
C
      PARAMETER (MAXCW =  MAXCALL*MAXEQN) !Maximum number of CWs
C
CC      PARAMETER (MAXCW =    MAXMACRO*(MAXEQN/2)*12) !Maximum number of CWs
C
      PARAMETER (DATFILID = 1)          !.DAT file logical unit number
      PARAMETER (INFFILID = 2)          !.INF file logical unit number
      PARAMETER (SIZFILID = 3)          !.INF file logical unit number
      PARAMETER (DLDFILID = 4)          !.DLD file logical unit number
      PARAMETER (INCFILID = 11)         !.INC file logical unit number
C
C
C
C
C  Character variables
C  -------------------
C
      CHARACTER
     & DATLINE*132,                     !.DAT file line
     & SIZLINE*132,                     !.SIZ file line
     & OPLINE*80,                       !Output file line
     & RECORD*80,                       !Intel Hex record buffer
     & EMPTY*80,                        !Empty line
     & DOLLAR*80,                       !Dollar sign
     & STAR*80,                         !Star
     & AMPER*80,                        !Ampersand
     & COMMENT*80,                      !Comment line
     & PARA_S(20)*255,                  !SOUND.LOG parameters
     & BUFFERF*52,                      !Buffer for file names
     & FILENAME*52,                     !File name with no extension
     & DATFILNAM*255,                    !.DAT file name
     & SIZFILNAM*255,                    !.SIZ file name
     & INFFILNAM*255,                    !.INF file name
     & DLDFILNAM*255,                    !.DLD file name
     & INCFILNAM*255,                    !.INC file name
     & N_DATFILNAM*255,                  !.DAT file name
     & N_SIZFILNAM*255,                  !.SIZ file name
     & N_INFFILNAM*255,                  !.INF file name
     & N_DLDFILNAM*255,                  !.DLD file name
     & N_INCFILNAM*255,                  !.INC file name
     & TITLE*40,                        !TITLE name
     & SHIPNAME*40,                     !Ship name
     & DATA_DIR*40,
     & INT_DIR*40,
     & SHIPDIR*40,                      !Ship directory name
     & OPERAND(12)*12,                  !Operands (12 operands exist)
     & STORELINE(64)*80,           !Store line buffer
     & SYMBOL*12,                       !Symbol (name, sign, number or keyword)
     & SRCNAME(2,24,96)*8, !Name of SOURCE entries
     & MACRONAME(20)*8,           !MACRO definition names
     & TBLNAME(20,40/2)*8,      !TBL parameter names
     & LABNAME(20,40*3)*8,      !LABEL names
     & EQNTBL(20,40/2)*8,       !Equation TBLs
     & CALLTBL(50,40/2)*8,       !TBLs in a CALL
     & CALLLAB(50,40*3)*8,       !LABELs in a CALL
     & CW2(50*40/2)*8,         !List of even CWs names
     & BITPAT*8,                        !Bit pattern function
     & HEXFMT*8,                        !Format for hex number constant
     & ANSWER*1,                        !User y/n response
C
     & DLDTITL1*80,                     !.DLD file strings
     & DLDTITL2*80,
     & EQUALS*80,
     & BRACKETS*80,
     & ENDMESS1*80,
     & ENDMESS2*80,
C
     & INCTITLE*80,                     !.INC file string
C
     & STARS*80,                        !.INF file strings
     & INFTITLE*80,
     & LEGEND*80,
     & DASHES*80,
     & MEMHL*80,
     & ENDMESS*80,
C
     & DMC_Num*2,                      ! XLINK_READ parameters ...
     & Page_Num*2,                     !            
     & Filetters*3,                    !
     & File_N(5)*40,                   !
     & Config_S(12)*80                 !    
C
C
C
C  Integer variables
C  -----------------
C
      INTEGER*4
     & STFREQ,                          !Starting frequency value
     & STAMPL,                          !Starting amplitude value
     & STPHASE,                         !Starting phase value
     & STCTRL,                          !Starting control word
     & STATUS,                          !Status of I/O operations
     & PARA_L(20)                       !SOUND.LOG parameter length
C
      INTEGER*2
     & MODE,                            !Write mode
     & STRLENG,                         !STRLENG function
     & I,J,                             !Loop counters
     & DATLINCNT,                       !.DAT file line count
     & SIZLINCNT,                       !.SIZ file line count
     & IERR,                            !Status of SNDXLINK subroutine
     & POS,                             !Position of a character within a line
     & NUMBER,                          !Multi-purpose variable
     & NUMDMC,                          !Number of DMCs
     & DMCCNT,                          !DMC counter
     & DMC(2),                          !DMC numbers
     & FREQ,                            !Frequency value
     & AMPL,                            !Amplitude value
     & PHASE,                           !Phase value
     & CTRL,                            !Control word
     & NUMSLOT(2),                 !Number of SLOT entries
     & SLOTCNT,                         !SLOT counter
     & SLOT(2,24),            !Value of SLOT entries (1-24)
     & NUMSIZE(2,24),         !Number of SIZE entries
     & SIZE(2,24,64),    !Value of SIZE entries (512-4096)
     & TABLE_NUM(2,24,64),!Value of table entries (1-...)
     & SRC_TAB(2,24,96),  !Value of source table entries (1-...)
     & NUMSRC(2,24),          !Number of SOURCE entries
     & SRC(2,24,96),      !Value of SOURCE entries
     & NUMMACRO,                        !Number of MACRO definitions
     & NUMTBL(20),                !Number of TBL parameters per MACRO
     & NUMLAB(20),                !Number of LABELs per MACRO
     & NUMEQN(20),                !Number of equations per MACRO (even)
     & EQNLAB(20,40,3),       !Equation labels number
     & OPERWGT(12),                     !Weight of each operand
     & NUMCALL,                         !Number of macro CALLs
     & CALLTYP(50),                !Type of CALLs
     & CW(50*40),              !List of all CWs for a SLOT entry
     & CWCNT,                           !Counter for CW list
     & CW2CNT,                          !Counter for CW2 list
     & CNT,                             !Multi-purpose counter
     & CAL,                             !Call counter
     & EQN,                             !Equation counter
     & OP,                              !Operand counter
     & ACCUM,                           !Accumulator
     & ADDCNT,                          !Record address counter
     & LEN,                             !Length
     & LEN2,                            !Another length
     & LASTSLOT,                        !Empty slot marker
     & HIGH,                            !Highest SLOT number
     & LOW,                             !Lowest SLOT number
     & TOP,BOTTOM                       !Used to calculate amount of ext. mem.
C
C
C
C  Logical*1 variables
C  -------------------
C
      LOGICAL*1
     & FLAG(6),                         !WGSIZE flag
     & SUM(50),                    !Value of SUM
     & WRT(50),                    !Value of WRT
     & LAST(50),                   !Value of LAST
     & FOUND,                           !Flag
     & LinkFlag(4),                     !OnSite flag...
     & Com(6),                          !Communication flags in XLINK...
     & Sound_Gr                         !GROUP Flag
C
      LOGICAL*2
     & DOINF,                           !Set to .TRUE. if user wants .INF file
     & DODLD,                           !Set to .TRUE. if user wants .DLD file
     & DOINC,                           !Set to .TRUE. if user wants .INC file
     & EQNOPER(20,MAXEQN/2,12)    !Value of each operand
C
      LOGICAL*4
     & WGSHIP,                          !Set to .TRUE. for ship mode
     & WGSTAT                           !Set to .TRUE. if WAVEGEN exits ok
C
C
C  Common declarations according to data type
C  ------------------------------------------
C
      COMMON /INTEGER2/
     & IERR,
     & L_DATA_DIR,L_INT_DIR,
     & DATLINCNT,SIZLINCNT,
     & POS,
     & NUMDMC,DMCCNT,
     & CNT,
     & CAL,EQN,
     & FREQ,AMPL,PHASE,CTRL,
     & SLOTCNT,
     & CWCNT,CW2CNT,
     & LASTSLOT,
     & HIGH,LOW,
     & TOP,
     & EQNLAB,
     & DMC,
     & NUMSLOT,SLOT,
     & NUMSIZE,SIZE,
     & TABLE_NUM,
     & SRC_TAB,
     & NUMSRC,SRC,
     & NUMMACRO,NUMTBL,NUMLAB,NUMEQN,
     & NUMCALL,CALLTYP,
     & CW,
     & Config_L,
     & Comp_Id,
     & BOTTOM
C
      COMMON /INTEGER4/
     & STFREQ,STAMPL,STPHASE,STCTRL,
     & PARA_L
C
      COMMON /LOGICAL2/
     & DOINF,
     & DODLD,
     & DOINC
C
      COMMON /LOGICAL1/
     & FLAG,
     & SUM,
     & WRT,
     & LAST,
     & LinkFlag,
     & Com
C
      COMMON /SPECIAL/
     & EQNOPER
C
      COMMON /CHARAC132/
     & DATLINE,SIZLINE
C
      COMMON /CHARAC52/
     & DATFILNAM,SIZFILNAM,INFFILNAM,DLDFILNAM,INCFILNAM
C
      COMMON /CHARAC255/
     & PARA_S
C
      COMMON /CHARAC40/
     & SHIPNAME,SHIPDIR,
     & DATA_DIR,INT_DIR,
     & TITLE
C
      COMMON /CHARAC12/
     & SYMBOL
C
      COMMON /CHARAC8/
     & SRCNAME,MACRONAME,TBLNAME,LABNAME,
     & EQNTBL,CALLTBL,CALLLAB,
     & CW2
C
      COMMON /HEXDATA/
     & HEXFMT
C
      COMMON /CHARAC80/
     & STORELINE,
     & Config_S
C
      COMMON /CHARAC2/
     & DMC_Num,
     & Page_Num
C
      COMMON /CHARAC3/
     & Filetters
C
C
C
C  Data initialization
C  -------------------
C
      DATA EMPTY /'                                                    
     &                '/
      DATA DOLLAR /'$'/
      DATA STAR /'*'/
      DATA AMPER /'&'/
      DATA COMMENT /'C'/
C
      DATA OPERAND /'ENA','ENB','ACC','ACC_AM','WEN','WEN_AM','END',
     &              '[ACC*SUM]','[ACC_AM*SUM]',
     &              '[WEN*WRT]','[WEN_AM*WRT]',
     &              '[END*LAST]'/
C
      DATA OPERWGT /1,2,4,8,16,32,64,4,8,16,32,64/
C
      DATA DLDTITL1 /'$           WAVEGEN CONTROLLER'/
      DATA DLDTITL2 /'$             DOWNLOAD FILE'/
      DATA EQUALS /'$=========================================='/
      DATA BRACKETS /'$<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>'/
      DATA ENDMESS1 /'$           WAVEGEN CONTROLLER'/
      DATA ENDMESS2 /'$              END OF FILE'/
C
      DATA INCTITLE /'C      WAVEGEN CONTROLLER INCLUDE FILE'/
C
      DATA STARS /'*****************************************************
     &*******************'/
C
      DATA INFTITLE /'*     WAVEGEN CONTROLLER INFORMATION FILE'/
C
      DATA LEGEND /'*  CONTROL WORD        AMPLITUDE         FREQUENCY
     &       PHASE'/
C
      DATA DASHES /'*-----------------+-----------------+---------------
     &--+-----------------'/
C
      DATA MEMHL /'*  MEM    B&L     |  MEM    LAB     |  MEM    LAB
     & |  MEM    LAB'/
C
      DATA ENDMESS /'***** END OF FILE *********************************
     &*********************'/
C
