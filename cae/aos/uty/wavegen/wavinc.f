C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVINC.FOR
C
C  This module contains WAVEGEN utility's subroutines which write processed
C  information to the .INC file.
C
C  WRTINCHD
C  WRTINCSLT
C  WRTINCAD
C  WRTINCPH
C  WRTINCPO
C  WRTINCSZ
C
C'Revision_History
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                  WRTINCHD
C =============================================================================
C
C  This subroutine writes the .INC file header information.
C
      SUBROUTINE WRTINCHD(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write title
C  -----------
C
      CALL WRITELINE(COMMENT,INCFILID,STATUS)
      CALL WRITELINE(INCTITLE,INCFILID,STATUS)
C
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error writing file')
         CALL SHOWFILE(INCFILNAM)
         RETURN 1
      END IF
C
C  Find low SLOT
C  -------------
C
      LOW = MAXSLOT
      DO I=1,NUMSLOT(DMCCNT)
         IF (SLOT(DMCCNT,I).LT.LOW) LOW = SLOT(DMCCNT,I)
      END DO
C
C  Find high SLOT
C  --------------
C
      HIGH = LOW
      DO I=1,NUMSLOT(DMCCNT)
         IF (SLOT(DMCCNT,I).GT.HIGH) HIGH = SLOT(DMCCNT,I)
      END DO
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTINCSLT
C =============================================================================
C
C  This subroutine writes a .INC file SLOT entry.
C
      SUBROUTINE WRTINCSLT(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
      CALL WRTINCAD(*20)
      CALL WRTINCPH(*20)
      CALL WRTINCPO(*20)
      CALL WRTINCSZ(*20)
C
      RETURN
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  WRTINCAD
C =============================================================================
C
C  This subroutine writes a .INC file addresses.
C
      SUBROUTINE WRTINCAD(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write integer*2
C  ---------------
C
      IF (SLOTCNT.EQ.1) THEN
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
         OPLINE = EMPTY
         OPLINE(7:) = 'INTEGER*2'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
      ENDIF
C
C  Compute number of entries
C  -------------------------
C
      CNT = 0
      DO CAL = 1,NUMCALL
         DO EQN = 1,NUMEQN(CALLTYP(CAL))/2
            CNT = CNT + 1
         END DO
      END DO
C
C  Write variable declaration statement
C  ------------------------------------
C
      IF (SLOTCNT.EQ.1) THEN
         OPLINE = EMPTY
         OPLINE(6:) = 'C DSGHADSO(   ,   :   )'
         WRITE (OPLINE(21:23),'(I3)') LOW
         WRITE (OPLINE(25:27),'(I3)') HIGH
         WRITE (OPLINE(17:19),'(I3)') MAXSRC
         OPLINE(40:) = '!DSG address of each source'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      ENDIF
C
      CALL WRITELINE(COMMENT,INCFILID,STATUS)
      OPLINE = EMPTY
      OPLINE(7:) = 'DATA'
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
C  Write entries
C  -------------
C
      CTRL = STCTRL + 1
C
      DO I=1,MAXSRC-1
C
C        Write value
C        -----------
C
         OPLINE = EMPTY
         OPLINE(6:) = '& DSGHADSO(   ,   )'
         WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
         WRITE(OPLINE(17:19),'(I3)') I
         OPLINE(26:35) = '/        /'
         OPLINE(27:34) = HEXFMT
C
         IF (I.LE.CNT) THEN
            WRITE (OPLINE(29:32),'(Z4.4)') CTRL
         ELSE
            WRITE (OPLINE(29:32),'(Z4.4)') 0
         END IF
C
C        Write comment
C        -------------
C
         OPLINE(40:) = ',!(   )'
         WRITE (OPLINE(43:45),'(I3)') I
         IF (I.LE.CNT) OPLINE(48:) = CW2(I)
         CTRL = CTRL + 2
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      END DO
C
C  Write last entry
C  ----------------
C
      OPLINE = EMPTY
      OPLINE(6:) = '& DSGHADSO(   ,   )'
      WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
      WRITE(OPLINE(17:19),'(I3)') I
      OPLINE(26:35) = '/        /'
      OPLINE(27:34) = HEXFMT
C
      IF (I.LE.CNT) THEN
         WRITE (OPLINE(29:32),'(Z4.4)') CTRL
      ELSE
         WRITE (OPLINE(29:32),'(Z4.4)') 0
      END IF
C
C  Write last comment
C  ------------------
C
      OPLINE(40:) = ' !(   )'
      WRITE (OPLINE(43:45),'(I3)') I
      IF (I.LE.CNT) OPLINE(48:) = CW2(I)
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTINCPH
C =============================================================================
C
C  This subroutine writes a .INC file phases.
C
      SUBROUTINE WRTINCPH(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write integer*2
C  ---------------
C
      IF (SLOTCNT.EQ.1) THEN
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
         OPLINE = EMPTY
         OPLINE(7:) = 'INTEGER*2'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
      ENDIF
C
C  Compute number of entries
C  -------------------------
C
      CNT = 0
      DO CAL = 1,NUMCALL
         DO EQN = 1,NUMEQN(CALLTYP(CAL))/2
            CNT = CNT + 1
         END DO
      END DO
C
C  Write variable declaration statement
C  ------------------------------------
C
      IF (SLOTCNT.EQ.1) THEN
         OPLINE = EMPTY
         OPLINE(6:) = 'C DSGHPHSO(   ,   :   )'
         WRITE (OPLINE(21:23),'(I3)') LOW
         WRITE (OPLINE(25:27),'(I3)') HIGH
         WRITE (OPLINE(17:19),'(I3)') MAXSRC
         OPLINE(40:) = '!DSG phase of each source'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      ENDIF
C
      CALL WRITELINE(COMMENT,INCFILID,STATUS)
      OPLINE = EMPTY
      OPLINE(7:) = 'DATA'
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
C  Write entries
C  -------------
C
      PHASE = STPHASE
C
      DO I=1,MAXSRC-1
C
C        Write value
C        -----------
C
         OPLINE = EMPTY
         OPLINE(6:) = '& DSGHPHSO(   ,   )'
         WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
         WRITE(OPLINE(17:19),'(I3)') I
         OPLINE(26:35) = '/        /'
         OPLINE(27:34) = HEXFMT
C
         IF (I.LE.CNT) THEN
            WRITE (OPLINE(29:32),'(Z4.4)') PHASE
         ELSE
            WRITE (OPLINE(29:32),'(Z4.4)') 0
         END IF
C
C        Write comment
C        -------------
C
         OPLINE(40:) = ',!(   )'
         WRITE (OPLINE(43:45),'(I3)') I
         IF (I.LE.CNT) OPLINE(48:) = CW2(I)
         PHASE = PHASE + 2
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      END DO
C
C  Write last entry
C  ----------------
C
      OPLINE = EMPTY
      OPLINE(6:) = '& DSGHPHSO(   ,   )'
      WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
      WRITE(OPLINE(17:19),'(I3)') I
      OPLINE(26:35) = '/        /'
      OPLINE(27:34) = HEXFMT
C
      IF (I.LE.CNT) THEN
         WRITE (OPLINE(29:32),'(Z4.4)') PHASE
      ELSE
         WRITE (OPLINE(29:32),'(Z4.4)') 0
      END IF
C
C  Write last comment
C  ------------------
C
      OPLINE(40:) = ' !(   )'
      WRITE (OPLINE(43:45),'(I3)') I
      IF (I.LE.CNT) OPLINE(48:) = CW2(I)
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTINCPO
C =============================================================================
C
C  This subroutine writes a .INC file pointers.
C
      SUBROUTINE WRTINCPO(*)
      IMPLICIT NONE
C
      CHARACTER*80 PRINTLINE   
C
      INCLUDE 'wav.inc'
C
C  Write integer*2
C  ---------------
C
      IF (SLOTCNT.EQ.1) THEN
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
         OPLINE = EMPTY
         OPLINE(7:) = 'INTEGER*2'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
      ENDIF
C
C  Write variable declaration statement
C  ------------------------------------
C
      IF (SLOTCNT.EQ.1) THEN
         OPLINE = EMPTY
         OPLINE(6:) = 'C DSGHPOTA(   ,   :   )'
         WRITE (OPLINE(21:23),'(I3)') LOW
         WRITE (OPLINE(25:27),'(I3)') HIGH
         WRITE (OPLINE(17:19),'(I3)') MAXSIZE
         OPLINE(40:) = '!DSG pointer to each table'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      ENDIF
C
      CALL WRITELINE(COMMENT,INCFILID,STATUS)
      OPLINE = EMPTY
      OPLINE(7:) = 'DATA'
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
C  Write entries
C  -------------
C
C     Initialize the line buffer
C     --------------------------
      DO I=1,MAXSIZE
         STORELINE(I) = EMPTY
         STORELINE(I)(6:) = '& DSGHPOTA(   ,   )'
         WRITE(STORELINE(I)(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
         WRITE(STORELINE(I)(17:19),'(I3)') I
         STORELINE(I)(26:35) = '/        /'
         STORELINE(I)(27:34) = HEXFMT
         STORELINE(I)(29:32) = '0000'
         IF (I.EQ.MAXSIZE) THEN
            STORELINE(I)(40:70) =' !Table does not exist         '
         ELSE
            STORELINE(I)(40:70) =',!Table does not exist         '
         ENDIF
      ENDDO
C
      ACCUM = 0
C
      DO I=1,MAXSIZE
C
C        Compute number code
C        -------------------
C
         NUMBER = ACCUM/256
C
         IF (SIZE(DMCCNT,SLOTCNT,I).EQ.512) THEN
C
C           Case Xbbbbbb1
C           -------------
C
            NUMBER = IOR(NUMBER+0,1)
C
         ELSE IF (SIZE(DMCCNT,SLOTCNT,I).EQ.1024) THEN
C
C           Case Xbbbbb10
C           -------------
C
            NUMBER = IOR(NUMBER+0,2)
            NUMBER = IAND(NUMBER+0,NOT(1))
C
         ELSE IF (SIZE(DMCCNT,SLOTCNT,I).EQ.2048) THEN
C
C           Case Xbbbb100
C           -------------
C
            NUMBER = IOR(NUMBER+0,4)
            NUMBER = IAND(NUMBER+0,NOT(3))
C
         ELSE IF (SIZE(DMCCNT,SLOTCNT,I).EQ.4096) THEN
C
C           Case Xbbb1000
C           -------------
C
            NUMBER = IOR(NUMBER+0,8)
            NUMBER = IAND(NUMBER+0,NOT(7))
         END IF
C
         ACCUM = ACCUM + SIZE(DMCCNT,SLOTCNT,I)
C
C        Write size only if table exists...
C        ----------------------------------
C
         OPLINE = EMPTY
         OPLINE(6:) = '& DSGHPOTA(   ,   )'
         WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
         WRITE(OPLINE(17:19),'(I3)') TABLE_NUM(DMCCNT,SLOTCNT,I)
         OPLINE(26:35) = '/        /'
         OPLINE(27:34) = HEXFMT
C
         IF (I.LE.NUMSIZE(DMCCNT,SLOTCNT)) THEN
            WRITE (OPLINE(29:32),'(Z4.4)') NUMBER
         ELSE
            WRITE (OPLINE(29:32),'(Z4.4)') 0
         END IF
C
C        Write comment
C        -------------
C
         IF (I.EQ.MAXSIZE) THEN
            OPLINE(40:) = ' !(   )'
         ELSE
            OPLINE(40:) = ',!(   )'
         ENDIF
C
         WRITE (OPLINE(43:45),'(I3)') I
C   
         IF (I.LE.NUMSIZE(DMCCNT,SLOTCNT)) THEN
            OPLINE(48:) = 'TABLE(   )'
            WRITE (OPLINE(54:56),'(I3)') TABLE_NUM(DMCCNT,SLOTCNT,I)
         END IF
C
C
C        Write into line buffer only if table exist
C        ------------------------------------------
         IF (TABLE_NUM(DMCCNT,SLOTCNT,I).NE.-1) THEN
            STORELINE(TABLE_NUM(DMCCNT,SLOTCNT,I)) = OPLINE
         ENDIF
      END DO
C
C     Write all lines in proper order
C     -------------------------------
      DO I=1,MAXSIZE
         PRINTLINE = STORELINE(I)
         CALL WRITELINE(PRINTLINE,INCFILID,STATUS)
      ENDDO
C
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTINCSZ
C =============================================================================
C
C  This subroutine writes a .INC file size of sources.
C
      SUBROUTINE WRTINCSZ(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write integer*2
C  ---------------
C
      IF (SLOTCNT.EQ.1) THEN
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
         OPLINE = EMPTY
         OPLINE(7:) = 'INTEGER*2'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
         CALL WRITELINE(COMMENT,INCFILID,STATUS)
      ENDIF
C
C  Write variable declaration statement
C  ------------------------------------
C
      IF (SLOTCNT.EQ.1) THEN
         OPLINE = EMPTY
         OPLINE(6:) = 'C DSGHSZSO(   ,   :   )'
         WRITE (OPLINE(21:23),'(I3)') LOW
         WRITE (OPLINE(25:27),'(I3)') HIGH
         WRITE (OPLINE(17:19),'(I3)') MAXSRC
         OPLINE(40:) = ' !DSG size of each source'
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      ENDIF
C
      CALL WRITELINE(COMMENT,INCFILID,STATUS)
      OPLINE = EMPTY
      OPLINE(7:) = 'DATA'
      CALL WRITELINE(OPLINE,INCFILID,STATUS)      
C
C  Write entries
C  -------------
C
      DO I=1,MAXSRC-1
C
C        Write size
C        ----------
C
         OPLINE = EMPTY
         OPLINE(6:) = '& DSGHSZSO(   ,   )'
         WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
         WRITE(OPLINE(17:19),'(I3)') I
         OPLINE(26:32) = '/     /'
C
         IF (I.LE.NUMSRC(DMCCNT,SLOTCNT)) THEN
            WRITE (OPLINE(27:31),'(I5)') SIZE(DMCCNT,
     &                                     SLOTCNT,
     &                                     SRC(DMCCNT,SLOTCNT,I))
         ELSE
            WRITE (OPLINE(27:31),'(I5)') 0
         END IF
C
C        Write comment
C        -------------
C
         OPLINE(40:) = ',!(   )'
         WRITE (OPLINE(43:45),'(I3)') I
C
         IF (I.LE.NUMSRC(DMCCNT,SLOTCNT)) THEN
            OPLINE(48:) = SRCNAME(DMCCNT,SLOTCNT,I)
            OPLINE(56:) = ' = TABLE(   )'
            WRITE (OPLINE(65:67),'(I3)') SRC_TAB(DMCCNT,SLOTCNT,I)
         END IF
C
         CALL WRITELINE(OPLINE,INCFILID,STATUS)
      END DO
C
C  Write last entry
C  ----------------
C
      OPLINE = EMPTY
      OPLINE(6:) = '& DSGHSZSO(   ,   )'
      WRITE(OPLINE(21:23),'(I3)') SLOT(NUMDMC,SLOTCNT)
      WRITE(OPLINE(17:19),'(I3)') I
      OPLINE(26:32) = '/     /'
      IF (I.LE.NUMSRC(DMCCNT,SLOTCNT)) THEN
         WRITE (OPLINE(27:31),'(I5)') SIZE(DMCCNT,
     &                                  SLOTCNT,
     &                                  SRC(DMCCNT,SLOTCNT,I))
      ELSE
         WRITE (OPLINE(27:31),'(I5)') 0
      END IF
C
C  Write last comment
C  ------------------
C
      OPLINE(40:) = ' !(   )'
      WRITE (OPLINE(43:45),'(I3)') I
C
      IF (I.LE.NUMSRC(DMCCNT,SLOTCNT)) THEN
         OPLINE(48:) = SRCNAME(DMCCNT,SLOTCNT,I)
         OPLINE(56:) = ' = TABLE(   )'
         WRITE (OPLINE(65:67),'(I3)') SRC(DMCCNT,SLOTCNT,I)
      END IF
C
      CALL WRITELINE(OPLINE,INCFILID,STATUS)
C
      RETURN
      END
C
