C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVPROC.FOR
C
C  This module contains WAVEGEN utility's subroutines which process the data
C  and computes the control words.
C
C  PROCDAT
C  COMPCW1
C  COMPCW2
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C'Revision_History
C
C =============================================================================
C                                  PROCDAT
C =============================================================================
C
C  This subroutine computes all the control words for a SLOT entry. 
C
      SUBROUTINE PROCDAT(*)              
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Clear CWs
C  ---------
C
      DO I=1,MAXCW
         CW(I) = 0
      END DO
C
C  Reset CW counters
C  -----------------
C
      CWCNT = 1
      CW2CNT = 1
C
C  Do for each MACRO call
C  ----------------------
C
      DO CAL=1,NUMCALL
C
C        Do for each pair of equations in a MACRO call
C        ---------------------------------------------
C
         DO EQN=1,NUMEQN(CALLTYP(CAL))/2
C
C           Compute control word & number code
C           ----------------------------------
C
            CALL COMPCW1(*20)
            CALL COMPCW2(*20)
C
         END DO
      END DO
C
      RETURN
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  COMPCW1
C =============================================================================
C
C  This subroutine computes a control word. 
C
      SUBROUTINE COMPCW1(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Calculate 1st equation's CW value by adding weigth of present operands
C  ----------------------------------------------------------------------
C
      ACCUM = 0
C
      DO OP=1,12
C
         IF (EQNOPER(CALLTYP(CAL),EQN,OP)) THEN              
C
C           Add weigth of operands 8 & 9 only if SUM is present
C           ----------------------------------------------------
C
            IF (OP.EQ.8.OR.OP.EQ.9) THEN
               IF (SUM(CAL)) ACCUM = ACCUM + OPERWGT(OP)
C
C           Add weigth of operands 10 & 11 only if WRT is present
C           ----------------------------------------------------
C
            ELSE IF (OP.EQ.10.OR.OP.EQ.11) THEN
               IF (WRT(CAL)) ACCUM = ACCUM + OPERWGT(OP)
C
C           Add weigth of operands 12 only if LAST is present
C           ----------------------------------------------------
C
            ELSE IF (OP.EQ.12) THEN
               IF (LAST(CAL)) ACCUM = ACCUM + OPERWGT(OP)
C
C           Add weigth of any other operand
C           -------------------------------
C
            ELSE
               ACCUM = ACCUM + OPERWGT(OP)
            END IF
         END IF
      END DO
C
C     Set value of 1st CW
C     -------------------
C
      CW(CWCNT) = ACCUM
      CWCNT = CWCNT + 1
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  COMPCW2
C =============================================================================
C
C  This subroutine sets 2nd equation's CW name and computes its number code.
C
      SUBROUTINE COMPCW2(*)
      IMPLICIT NONE
C
      INTEGER*2
     & TBLIND,          !Table index
     & SRCIND,          !Source index
     & SIZIND           !Size index
C
      INCLUDE 'wav.inc'
C
C  Get index of 2nd equation's TBL in parameter list
C  -------------------------------------------------
C         
      DO I=1,NUMTBL(CALLTYP(CAL))
         IF (TBLNAME(CALLTYP(CAL),I).EQ.EQNTBL(CALLTYP(CAL),EQN)) THEN
            TBLIND = I
         END IF
      END DO
C
C  Get index of corresponding SOURCE entry in SOURCE list 
C  ------------------------------------------------------
C
      DO I=1,NUMSRC(DMCCNT,SLOTCNT)
C
         IF (SRCNAME(DMCCNT,SLOTCNT,I).EQ.CALLTBL(CAL,TBLIND)) THEN
            SRCIND = I
         END IF
C
      END DO
C
C  Set name of 2nd CW
C  ------------------
C
      CW2(CW2CNT) = SRCNAME(DMCCNT,
     &                      SLOTCNT,
     &                      SRCIND)
      CW2CNT = CW2CNT + 1
C
C  Compute pointer to table by adding preceding table sizes
C  --------------------------------------------------------
C
      SIZIND = SRC(DMCCNT,SLOTCNT,SRCIND) 
C
      ACCUM = 0
      DO I=1,SIZIND-1
         ACCUM = ACCUM + SIZE(DMCCNT,SLOTCNT,I)
      END DO
C
C  Compute number code
C  -------------------
C
      NUMBER = ACCUM/256
C
      IF (SIZE(DMCCNT,SLOTCNT,SIZIND).EQ.512) THEN
C
C        Case Xbbbbbb1
C        -------------
C
         NUMBER = IOR(NUMBER+0,1)
C
      ELSE IF (SIZE(DMCCNT,SLOTCNT,SIZIND).EQ.1024) THEN
C
C        Case Xbbbbb10
C        -------------
C
         NUMBER = IOR(NUMBER+0,2)
         NUMBER = IAND(NUMBER+0,NOT(1))
C
      ELSE IF (SIZE(DMCCNT,SLOTCNT,SIZIND).EQ.2048) THEN
C
C        Case Xbbbb100
C        -------------
C
         NUMBER = IOR(NUMBER+0,4)
         NUMBER = IAND(NUMBER+0,NOT(3))
C
      ELSE IF (SIZE(DMCCNT,SLOTCNT,SIZIND).EQ.4096) THEN
C
C        Case Xbbb1000
C        -------------
C
         NUMBER = IOR(NUMBER+0,8)
         NUMBER = IAND(NUMBER+0,NOT(7))
      END IF
C
C     Set value of 2st CW
C     -------------------
C
      CW(CWCNT) = NUMBER
      CWCNT = CWCNT + 1
C
      RETURN
      END                  
C
