C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVMAIN.FOR
C
C  This module contains WAVEGEN utility's main subroutines.
C
C  WAVEGEN
C  PROCDMC
C
C  Author: <PERSON><PERSON>, Dept. 73
C
C  Usage:  New digital sound processing system
C
C  Revision history:  V1.0  Initial release            C.Lafleche  6-APR-88
C                     V1.1  New XINF file structure    G.DeSerre   6-JUN-89
C                     V1.2  Change INC file structure  G.DeSerre   3-MAR-90
C                     V1.3  Modifications for combined
C                           audio & sound utilities    P.Daigle   27-JUL-90
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                   WAVEGEN
C =============================================================================
C
C  The WAVEGEN subroutine takes as input a size file and a data file and
C  creates an information file, a download file and an include file.
C  The WGSHIP parameter tells WAVEGEN whether it runs in stand-alone mode
C  or not. The WGSTAT parameter is set by WAVEGEN and indicates completion
C  status.
C
      SUBROUTINE WAVEGEN(WGSHIP,WGSTAT)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
      CHARACTER
     &  Tmp_Str*80,
     &  N_Tmp_Str*80
C
      INTEGER*4
     &  Writ(3),                           !Write enable for flags
     &  RevStat
C
      CALL Init_Libgd(1,3)                 !Initialize library routines
C
      CALL GETEXTMEM(*20)                  !Get extended memory if Sel
C
      CALL CLEARMEM                        !Clear variables if Sel
C
C  Check if WAVEGEN should run
C  ---------------------------
C
      IF (WGSHIP) THEN
         CALL XLINK_READ(Config_S,Config_L,DMC_Num,Page_Num,
     &        Filetters,Comp_Id,LinkFlag,Com,File_N,File_L,IERR)
C
         IF (IERR.NE.0) THEN
            CALL TERMWRITE('Error reading file')
            CALL SHOWFILE('AOSXLINK.INF')
            GO TO 20
         END IF
C
         IF (Filetters(1:2).EQ.'sn') THEN
            Sound_Gr = .TRUE.
            DATA_DIR = Config_S(1)(1:Config_L(1))//'sound/data/'
            INT_DIR = Config_S(1)(1:Config_L(1))//'sound/inter/'
         ELSE
            Sound_Gr = .FALSE.
            DATA_DIR = Config_S(1)(1:Config_L(1))//'audio/data/'
            INT_DIR = Config_S(1)(1:Config_L(1))//'audio/inter/'
         ENDIF
         L_DATA_DIR = Config_L(1)+11
         L_INT_DIR = Config_L(1)+12
         IF (Com(1)) THEN
            IF (.NOT.Com(3)) THEN
               GO TO 20
            END IF
         END IF
C
      END IF
C
C  Assume an error is present
C  --------------------------
C
      WGSTAT = .FALSE.
C
C  Show header
C  -----------
C
      CALL HEADER
C
C  Get file names
C  --------------
C
      IF (.NOT.Com(1)) THEN                    !In standalone mode
         Tmp_Str = 'Filename? ['//File_N(1)(1:File_L(1))//']'
         CALL TERMREAD(Tmp_Str(1:(12+File_L(1))),FILENAME)
      ELSE                                     !In update mode
         FILENAME = ' '
         Tmp_Str = 'WAVEGEN using '//File_N(1)(1:File_L(1))//
     &' data file'
         CALL TERMWRITE(Tmp_Str)
      ENDIF
C
      IF (FILENAME.EQ.' ') FILENAME = File_N(1)(1:File_L(1))
C
      LEN = STRLENG(FILENAME)
      IF(WGSHIP) THEN
         SIZFILNAM = Config_S(9)(1:Config_L(9))//'w'//
     &               Filetters(1:3)//'.siz'
      ELSE
         SIZFILNAM = FILENAME(1:(LEN-4))//'.siz'
      ENDIF
C
      DATFILNAM = FILENAME(:LEN)  
C                 ! Variable FILENAME includes the extention
C
C     Define output file names
C     ------------------------ 
      INFFILNAM = FILENAME(1:(LEN-4))//'.inf'
      DLDFILNAM = FILENAME(1:(LEN-4))//'.int'
      INCFILNAM = FILENAME(1:(LEN-4))//'.inc'
C
C  Add ship parameters if required
C  -------------------------------
C
      IF (WGSHIP) THEN
         SHIPNAME = Config_S(9)(:Config_L(9))
         SHIPDIR = Config_S(3)(:Config_L(3))
C
         BUFFERF = INT_DIR(1:L_INT_DIR)//SIZFILNAM
         SIZFILNAM = BUFFERF
         BUFFERF = DATA_DIR(1:L_DATA_DIR)//DATFILNAM
         DATFILNAM = BUFFERF
         BUFFERF = INT_DIR(1:L_INT_DIR)//INFFILNAM
         INFFILNAM = BUFFERF
C
         LEN = STRLENG(SHIPDIR)
         LEN2 = STRLENG(SHIPNAME)
         INCFILNAM = SHIPDIR(:LEN)//SHIPNAME(:LEN2)//Filetters(1:3)
     &               //'w.inc'
C
         DLDFILNAM = INT_DIR(1:L_INT_DIR)//SHIPNAME(:LEN2)//
     &               Filetters(1:3)//'w.int'
      END IF
C
C  Get job request
C  ---------------
C
      IF (.NOT.Com(1)) THEN
        CALL TERMWRITE(EMPTY)
        CALL TERMREAD('Do you want the INFORMATION file? [Y]',ANSWER)
        IF (ANSWER.EQ.' '.OR.ANSWER.EQ.'Y') THEN
          DOINF = .TRUE.
        ELSE
          DOINF = .FALSE.
        END IF
C
        CALL TERMREAD('Do you want the DOWNLOAD file? [Y]',ANSWER)
        IF (ANSWER.EQ.' '.OR.ANSWER.EQ.'Y') THEN
           DODLD = .TRUE.
        ELSE
           DODLD = .FALSE.
        END IF
C
        CALL TERMREAD('Do you want the INCLUDE file? [Y]',ANSWER)
        IF (ANSWER.EQ.' '.OR.ANSWER.EQ.'Y') THEN
          DOINC = .TRUE.
        ELSE
          DOINC = .FALSE.
        END IF
C
      ELSE
        DOINF = .FALSE.
        DODLD = .TRUE.
        DOINC = .TRUE.
      ENDIF
C
C  Open files
C  ----------
C
C
      CALL OPNFILR(SIZFILNAM,SIZFILID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(SIZFILNAM)
         GO TO 20
      END IF
C
      CALL OPNFILR(DATFILNAM,DATFILID,STATUS)
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error opening file')
         CALL SHOWFILE(DATFILNAM)
         GO TO 20
      END IF
C
      IF (DOINF) THEN
         CALL OPNFILRW(1,INFFILNAM,INFFILID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(INFFILNAM)
         END IF
      END IF
C
      IF (DODLD) THEN
         CALL OPNFILRW(2,DLDFILNAM,DLDFILID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(DLDFILNAM)
            GO TO 20
         END IF
      END IF
C
      IF (DOINC) THEN
         CALL OPNFILRW(3,INCFILNAM,INCFILID,STATUS)
         IF (STATUS.NE.0) THEN
            CALL TERMWRITE('Error opening file')
            CALL SHOWFILE(INCFILNAM)
            GO TO 20
         END IF
      END IF
C
C  Read and process .SIZ file in one pass
C  --------------------------------------
C
      CALL TERMWRITE(EMPTY)
      CALL TERMWRITE('Reading SIZE file')
      CALL RDSIZFIL(*20)
C
C  Read and process .DAT file & create O/P files
C  ---------------------------------------------
C
      CALL TERMWRITE('Reading DATA file')
      DMCCNT = 0
C
      CALL SEARFILE('TITLE',DATFILID,DATLINE,DATLINCNT,POS)
C
      DO WHILE (POS.NE.0)
C
C        Read and process a DMC entry
C        ----------------------------
C
         DMCCNT = DMCCNT + 1
C
         IF (DMCCNT.GT.NUMDMC) THEN
            CALL TERMWRITE('Too many DMC entries')
            CALL SHOWFILE(DATFILNAM)
            GO TO 20
         END IF
C
         CALL PROCDMC(*20)
C
         POS = 1                                 !Search for next TITLE
         CALL SEARSTR('TITLE',DATLINE,POS)
      END DO
C
      CALL TERMWRITE(EMPTY)
      IF (DMCCNT.NE.NUMDMC) THEN
         CALL TERMWRITE('Too few DMC entries')
         CALL SHOWFILE(DATFILNAM)
         GO TO 20
      END IF
C
      IF (DOINF) CALL TERMWRITE('INFORMATION file created')
      IF (DODLD) CALL TERMWRITE('DOWNLOAD file created')
      IF (DOINC) CALL TERMWRITE('INCLUDE file created')
      IF (.NOT.DOINF.AND..NOT.DODLD.AND..NOT.DOINC) THEN
         CALL TERMWRITE('NO output file created')
      END IF
      CALL TERMWRITE(EMPTY)
C
C  Close files
C  -----------
C
      CALL CLOSEFIL(DATFILID)
      CALL CLOSEFIL(SIZFILID)
      IF (DOINF) CALL CLOSEFIL(INFFILID)
      IF (DODLD) CALL CLOSEFIL(DLDFILID)
      IF (DOINC) CALL CLOSEFIL(INCFILID)
C
C  Check if should set DOWNLOAD flag
C  ---------------------------------
C
      IF (WGSHIP) THEN
         IF (Com(1)) THEN
            IF (Com(3)) THEN
               Writ(2) = 1
               Writ(3) = 1
               Com(2) = .TRUE.
               Com(3) = .FALSE.
               CALL XLINK_WRITE(Writ,Com,IERR)
C
               IF (IERR.NE.0) THEN
                  CALL TERMWRITE('Error writing file')
                  CALL SHOWFILE('AOSXLINK.INF')
                  GO TO 20
               END IF
            END IF
         END IF
      END IF
C
C  Normal exit
C  -----------
C
      CALL TERMWRITE('WAVEGEN successfully terminated')
      CALL RSTWIND
      WGSTAT = .TRUE.           !Indicate WAVEGEN successfully terminated
      CALL Wait_Time(5.0)
      RETURN
C
C  Error occured
C  -------------
C
 20   CALL RSTWIND
      RETURN                    !Leave with WGSTAT unchanged
C
      END
C
C
C
C =============================================================================
C                                  PROCDMC
C =============================================================================
C
C  This subroutine reads and checks a .DAT file DMC entry information and
C  creates O/P files DMC entry.
C
      SUBROUTINE PROCDMC(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for TITLE name & DMC number
C  ----------------------------------
C
      CALL READHEAD(*20)
C
C  Do O/P files header
C  -------------------
C
      IF (DOINF) CALL WRTINFHD(*20)
      IF (DODLD) CALL WRTDLDHD(*20)
      IF (DOINC.AND.DMCCNT.EQ.1) CALL WRTINCHD(*20)
C
C  Do .DAT file SLOT entries
C  -------------------------
C
      SLOTCNT = 0
C
      CALL SEARFILE('SLOT',DATFILID,DATLINE,DATLINCNT,POS)
C
      DO WHILE (POS.NE.0)
C
         SLOTCNT = SLOTCNT + 1
         IF (SLOTCNT.GT.NUMSLOT(DMCCNT)) THEN
            CALL TERMWRITE('Number of SLOT entries mismatch')
            RETURN 1
         END IF
C
C  Read and check all SLOT entry data
C  ----------------------------------
C
         CALL READADDR(*20)
         CALL READMACR(*20)
         CALL READCODE(*20)
C
C  Process data and generate CWs for a SLOT entry
C  ----------------------------------------------
         CALL PROCDAT(*20)
C
C  Create O/P files slot entry
C  ---------------------------
C
         IF (DOINF) CALL WRTINFSLT(*20)
         IF (DODLD) CALL WRTDLDSLT(*20)
         IF (DOINC.AND.DMCCNT.EQ.1) CALL WRTINCSLT(*20)
C
         CALL SEARFILE('SLOT',DATFILID,DATLINE,DATLINCNT,POS)
      END DO
C
      IF (SLOTCNT.LT.NUMSLOT(DMCCNT)) THEN
         CALL TERMWRITE('Number of SLOT entries mismatch')
         RETURN 1
      END IF
C
C  Do O/P files end of file
C  ------------------------
C
      IF (DMCCNT.EQ.NUMDMC) THEN
         IF (DOINF) CALL WRTINFEOF(*20)
         IF (DODLD) CALL WRTDLDEOF(*20)
      END IF
C
      RETURN
 20   RETURN 1
      END
C
