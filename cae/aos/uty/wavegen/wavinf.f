C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVINF.FOR
C
C  This module contains WAVEGEN utility's subroutines which write processed
C  information to the .INF file.
C
C  WRTINFHD
C  WRTINFSLT
C  WRTINFCAL
C  WRTINFEOF
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C'Revision_History
C
C
C =============================================================================
C                                  WRTINFHD
C =============================================================================
C
C  Thks subroutine writes the .INF file header information. 
C
      SUBROUTINE WRTINFHD(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write box 
C  ---------
C
      IF (DMCCNT.EQ.1) THEN
         CALL WRITELINE(STARS,INFFILID,STATUS)
         CALL WRITELINE(STAR,INFFILID,STATUS)
         CALL WRITELINE(INFTITLE,INFFILID,STATUS)
         CALL WRITELINE(STAR,INFFILID,STATUS)
      ELSE
         CALL WRITELINE(STAR,INFFILID,STATUS)
         CALL WRITELINE(STARS,INFFILID,STATUS)
         CALL WRITELINE(STAR,INFFILID,STATUS)
      END IF
C
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error writing file')
         CALL SHOWFILE(INFFILNAM)                 
         RETURN 1
      END IF
C
C  Write title          
C  -----------
C
      OPLINE = STAR
      OPLINE(7:) = TITLE
      CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
C  Write DMC #
C  -----------
C
      OPLINE = STAR
      OPLINE(7:) = 'DMC # '
      WRITE (OPLINE(13:),'(Z2.2)') DMC(DMCCNT) 
      CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTINFSLT
C =============================================================================
C
C  This subroutine writes a .INF file SLOT entry. 
C
      SUBROUTINE WRTINFSLT(*)
      IMPLICIT NONE     
C
      INCLUDE 'wav.inc'                  
C
C  Write SLOT header
C  -----------------
C
      CALL WRITELINE(STAR,INFFILID,STATUS)
      CALL WRITELINE(STARS,INFFILID,STATUS)
      CALL WRITELINE(STAR,INFFILID,STATUS)
C
      OPLINE = STAR
      OPLINE(7:) = 'SLOT # '
      WRITE (OPLINE(14:),'(I2)') SLOT(DMCCNT,SLOTCNT)
      CALL WRITELINE(OPLINE,INFFILID,STATUS)
      CALL WRITELINE(STAR,INFFILID,STATUS)
C
      CALL WRITELINE(LEGEND,INFFILID,STATUS)
      CALL WRITELINE(DASHES,INFFILID,STATUS)
      CALL WRITELINE(MEMHL,INFFILID,STATUS)
      CALL WRITELINE(DASHES,INFFILID,STATUS)
C
C  Set addresses starting values
C  -----------------------------
C
      FREQ = STFREQ
      AMPL = STAMPL
      PHASE = STPHASE
      CTRL = STCTRL
C
C  Reset CW counters
C  -----------------
C
      CWCNT = 1 
      CW2CNT = 1 
C
C  Write a SLOT entry's MACRO calls
C  --------------------------------
C
      DO CAL=1,NUMCALL
         CALL WRTINFCAL(*20)
      END DO
C
C  Check addresses final value
C  ---------------------------
C
      IF (FREQ-STFREQ.GT.511.OR.
     &    AMPL-STAMPL.GT.511.OR.
     &    PHASE-STPHASE.GT.511.OR.
     &    CTRL-STCTRL.GT.511) THEN
         CALL WRITELINE('Address incremented more than 1FFH')
         RETURN 1
      END IF
C
C  Write comment for empty addresses
C  ---------------------------------
C
      CALL WRITELINE(STAR,INFFILID,STATUS)
C
      OPLINE = STAR
      OPLINE(4:) = 'ADDRESSES     -     = 00H'
      WRITE (OPLINE(14:17),'(Z4.4)') CTRL
      WRITE (OPLINE(19:22),'(Z4.4)') STCTRL + 511
      CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
      RETURN
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  WRTINFCAL
C =============================================================================
C
C  This subroutine writes a .INF file CALL entry. 
C
      SUBROUTINE WRTINFCAL(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write an empty line
C  -------------------
C
      CALL WRITELINE(STAR,INFFILID,STATUS)
C
C  Write a line with MACRO CALL's number and name
C  ----------------------------------------------
C
      OPLINE = STAR
      OPLINE(2:) = '>>>'
      WRITE (OPLINE(6:),'(I2)') CAL
      OPLINE(9:) = MACRONAME(CALLTYP(CAL))
      CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
C  Write an information line for each equation
C  -------------------------------------------
C
      DO EQN=1,NUMEQN(CALLTYP(CAL))/2
C
C        Write 1st equation
C        ------------------
C
         OPLINE = EMPTY
C        
C        Set CW
C        ------
C
         WRITE (OPLINE(4:),'(Z4)') CTRL
         CTRL = CTRL + 1
         OPLINE(9:) = '='
         OPLINE(11:) = BITPAT(CW(CWCNT))
         CWCNT = CWCNT + 2
C        
C        Set AMPL
C        --------
C
         WRITE (OPLINE(22:),'(Z4)') AMPL
         AMPL = AMPL + 1
         OPLINE(27:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2-1,1)
         IF (NUMBER.EQ.0) THEN
            OPLINE(29:) = '0'
         ELSE
            OPLINE(29:) = CALLLAB(CAL,NUMBER) 
         END IF
C
C        Set FREQ
C        --------
C
         WRITE (OPLINE(40:),'(Z4)') FREQ
         FREQ = FREQ + 1
         OPLINE(45:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2-1,2)
         IF (NUMBER.EQ.0) THEN
            OPLINE(47:) = '0'
         ELSE
            OPLINE(47:) = CALLLAB(CAL,NUMBER) 
         END IF
C
C        Set PHASE
C        ---------
C
         WRITE (OPLINE(58:),'(Z4)') PHASE
         PHASE = PHASE + 1
         OPLINE(63:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2-1,3)
         IF (NUMBER.EQ.0) THEN
            OPLINE(65:) = '0'
         ELSE
            OPLINE(65:) = CALLLAB(CAL,NUMBER) 
         END IF
C
         CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
C        Write 2nd equation
C        ------------------
C
         OPLINE = EMPTY
C
C        Set CW name
C        -----------
C
         WRITE (OPLINE(4:),'(Z4)') CTRL
         CTRL = CTRL + 1
         OPLINE(9:) = '='
         OPLINE(11:) = CW2(CW2CNT)
         CW2CNT = CW2CNT + 1
C
C        Set AMPL
C        --------
C
         WRITE (OPLINE(22:),'(Z4)') AMPL
         AMPL = AMPL + 1
         OPLINE(27:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2,1)
         IF (NUMBER.EQ.0) THEN
            OPLINE(29:) = '0'
         ELSE
            OPLINE(29:) = CALLLAB(CAL,NUMBER) 
         END IF
C
C        Set FREQ
C        --------
C
         WRITE (OPLINE(40:),'(Z4)') FREQ
         FREQ = FREQ + 1
         OPLINE(45:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2,2)
         IF (NUMBER.EQ.0) THEN
            OPLINE(47:) = '0'
         ELSE
            OPLINE(47:) = CALLLAB(CAL,NUMBER) 
         END IF
C
C        Set PHASE
C        ---------
C
         WRITE (OPLINE(58:),'(Z4)') PHASE
         PHASE = PHASE + 1
         OPLINE(63:) = '='
         NUMBER = EQNLAB(CALLTYP(CAL),EQN*2,3)
         IF (NUMBER.EQ.0) THEN
            OPLINE(65:) = '0'
         ELSE
            OPLINE(65:) = CALLLAB(CAL,NUMBER) 
         END IF
C
         CALL WRITELINE(OPLINE,INFFILID,STATUS)
C
      END DO
C
      RETURN
      END
C
C
C
C =============================================================================
C                                 WRTINFEOF
C =============================================================================
C
C  This subroutine writes the .INF file end of file message.
C
      SUBROUTINE WRTINFEOF(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write message
C  -------------
C
      CALL WRITELINE(STAR,INFFILID,STATUS)
      CALL WRITELINE(ENDMESS,INFFILID,STATUS)
C
      RETURN
      END
C
