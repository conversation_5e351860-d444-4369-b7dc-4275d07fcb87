C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVCODE.FOR
C  
C  This module contains WAVEGEN utility's subroutines which read and check
C  information in the CODE block of the .DAT file
C
C  READCODE
C  READPVAL
C  READLVAL
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C'Revision_History
C
C =============================================================================
C                                  READCODE
C =============================================================================
C
C  This subroutine reads the MACRO CALLs.
C
      SUBROUTINE READCODE(*) 
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for CODE_START
C  ---------------------
C
      CALL SEARFILE('CODE_START',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing CODE_START keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
C  Read MACRO CALLs until CODE_END found
C  -------------------------------------
C
      NUMCALL = 0
      CALL SEARFILE('CODE_END',DATFILID,DATLINE,DATLINCNT,POS)
C
      DO WHILE (POS.EQ.0.AND.INDEX(DATLINE,'(').NE.0)
C
         NUMCALL = NUMCALL + 1
         IF (NUMCALL.GT.MAXCALL) THEN
            CALL TERMWRITE('Too many MACRO calls')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
C        Search for line number
C        ----------------------
C
         POS = 1
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         READ (SYMBOL,'(BN,I2)',ERR=10) NUMBER
         IF (NUMBER.NE.NUMCALL) THEN 
            CALL TERMWRITE('Wrong MACRO call number')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
C        Search for <macroname>
C        ----------------------
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         FOUND = .FALSE.
         DO I=1,NUMMACRO
            IF (SYMBOL.EQ.MACRONAME(I)) THEN
               CALLTYP(NUMCALL) = I
               FOUND = .TRUE.
            END IF
         END DO
C
         IF (.NOT.FOUND) THEN 
            CALL TERMWRITE('Wrong MACRO name')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
C        Search for parameter values
C        ---------------------------
C
         CALL READPVAL(*20)
C
C        Search for label values
C        -----------------------
C
         CALL READLVAL(*20)
C
         CALL SEARFILE('CODE_END',DATFILID,DATLINE,DATLINCNT,POS)
      END DO
C
C  Verify CODE_END was found
C  -------------------------
C
      POS = 1
      CALL SEARSTR('CODE_END',DATLINE,POS)
      IF (POS.EQ.0) THEN 
         CALL TERMWRITE('Missing CODE_END keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(DATFILNAM,DATLINCNT)
      CALL TERMWRITE(DATLINE)
 20   RETURN 1 
C
      END
C
C
C
C =============================================================================
C                                  READPVAL
C =============================================================================
C
C  This subroutine reads the parameter values of a MACRO call.
C
      SUBROUTINE READPVAL(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for { ( } 
C  ----------------
C
      CALL SEARSTR('(',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / ( / in MACRO call')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
C  Search for TBL values
C  ---------------------
C
      DO I=1,NUMTBL(CALLTYP(NUMCALL))
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
C        Check if TBL value is in the SOURCE list
C        ----------------------------------------
C
         FOUND = .FALSE.
         J = 1
         DO WHILE (J.LE.NUMSRC(DMCCNT,SLOTCNT).AND..NOT.FOUND)
            IF (SYMBOL.EQ.SRCNAME(DMCCNT,SLOTCNT,J)) FOUND = .TRUE.
            J = J + 1
         END DO
C
         IF (.NOT.FOUND) THEN
            CALL TERMWRITE('Undefined TBL entry in MACRO call')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
         CALLTBL(NUMCALL,I) = SYMBOL
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         IF (SYMBOL.NE.',') THEN
            CALL TERMWRITE('Missing / , / in MACRO call')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
      END DO
C
C  Search for SUM value
C  --------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.EQ.'+') THEN
         SUM(NUMCALL) = .TRUE.
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         IF (SYMBOL.NE.',') THEN
            CALL TERMWRITE('Missing / , / in MACRO call')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
      ELSE IF (SYMBOL.EQ.',') THEN
         SUM(NUMCALL) = .FALSE.
C
      ELSE IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Invalid SUM value in MACRO call')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF         
C
C  Search for WRT value
C  --------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.EQ.'=') THEN
         WRT(NUMCALL) = .TRUE.
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         IF (SYMBOL.NE.',') THEN
            CALL TERMWRITE('Missing / , / in MACRO call')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
      ELSE IF (SYMBOL.EQ.',') THEN
         WRT(NUMCALL) = .FALSE.
C
      ELSE IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Invalid WRT value in MACRO call')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF         
C
C  Search for LAST value
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.EQ.'#') THEN
         LAST(NUMCALL) = .TRUE.
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         IF (SYMBOL.NE.')') THEN
            CALL TERMWRITE('Missing / ) / in MACRO call')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
      ELSE IF (SYMBOL.EQ.')') THEN
         LAST(NUMCALL) = .FALSE.
C
      ELSE IF (SYMBOL.NE.')') THEN
         CALL TERMWRITE('Invalid LAST value in MACRO call')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF         
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  READLVAL
C =============================================================================
C
C  This subroutine reads the label values of a MACRO call.
C
      SUBROUTINE READLVAL(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for LABEL values if needed
C  ---------------------------------
C
      IF (NUMLAB(CALLTYP(NUMCALL)).GT.0) THEN
C
         IF (DATLINE(POS:).EQ.' ') THEN
            CALL SEARFILE('DUMMY',DATFILID,DATLINE,DATLINCNT,POS)
            POS = 1
         END IF
C
         DO I=1,NUMLAB(CALLTYP(NUMCALL))-1
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
            CALLLAB(NUMCALL,I) = SYMBOL
C
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
            IF (SYMBOL.NE.',') THEN
               CALL TERMWRITE('Missing / , / in MACRO call')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1 
            END IF
         END DO
C
C        Search for last label value
C        ---------------------------
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         CALLLAB(NUMCALL,I) = SYMBOL
C
      END IF
C
      RETURN
      END
C
