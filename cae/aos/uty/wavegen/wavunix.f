C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                WGIO_VAX.FOR
C
C  This module contains all the subroutines which perform I/O to the files
C  and the terminal. All the files are formatted and use sequential access.
C  This is the version for VAX computers.
C
C  READLINE
C  WRITELINE
C  OPNFILR
C  OPNFILRW
C  CLOSEFIL
C  SHOWLINE
C  SHOWFILE
C  TERMWRITE
C  TERMREAD
C  HEADER
C  RSTWIND
C  HEXBLKDAT
C  SNDRDXLK
C  SNDWRXLK
C  GETEXTMEM
C  CLEARMEM
C  BITPAT
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                  READLINE
C =============================================================================
C
C  This subroutine reads a line from the file whose file ID is specified
C  and returns the status of the operation. The status is 0 if the read
C  was successful, 1 if the end of file is encountered or -1 if there was
C  an error reading the file.
C
      SUBROUTINE READLINE(LINE,FILEID,STAT)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & LINE
C
      INTEGER*4
     & FILEID
C
      INTEGER*2
     & STAT
C
      READ(FILEID,100,ERR=200,END=300) LINE
 100  FORMAT(A)
      STAT = 0
      RETURN
C
 200  STAT = -1
      RETURN
C
 300  STAT = 1
      RETURN
      END
C
C
C
C =============================================================================
C                                 WRITELINE
C =============================================================================
C
C  This subroutine writes a line to the file whose file ID is specified
C  and returns the status of the operation. The status is 0 if the write
C  was successful, -1 if there was an error writing the file.
C
      SUBROUTINE WRITELINE(LINE,FILEID,STAT)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & LINE
C
      INTEGER*4
     & FILEID
C
      INTEGER*2
     & STAT
C
      WRITE(FILEID,400,ERR=500) LINE(1:80)
 400  FORMAT(A)
      STAT = 0
      RETURN
C
 500  STAT = -1
      RETURN
      END
C
C
C
C =============================================================================
C                                  OPNFILR
C =============================================================================
C
C  This subroutine opens an existing file for read-only access. A status
C  is returned with value 0 if the operation is successful, with a non-zero
C  value otherwise.
C
      SUBROUTINE OPNFILR(FILENAME,FILEID,STAT)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & FILENAME
C
      CHARACTER
     &  N_FILENAME*80
C
      INTEGER*4
     & FILEID,
     & revstat,
     & STAT
C
      CALL rev_curr(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
      OPEN(UNIT=FILEID,FILE=N_FILENAME,STATUS='OLD',IOSTAT=STAT)
      RETURN
      END
C
C
C
C =============================================================================
C                                  OPNFILRW
C =============================================================================
C
C  This subroutine opens an unexisting file for read/write access. A status
C  is returned with value 0 if the operation is successful, with a non-zero
C  value otherwise.
C
      SUBROUTINE OPNFILRW(TYPE,FILENAME,FILEID,STAT)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & FILENAME
C
      CHARACTER
     & N_FILENAME*80
C
      INTEGER*2
     & TYPE       !This is a dummy to be compatible with Sel version
C
      INTEGER*4
     & revstat,
     & FILEID,
     & STAT
C
      CALL rev_next(FILENAME,N_FILENAME,' ',.FALSE.,1,revstat)
      OPEN(UNIT=FILEID,FILE=N_FILENAME,STATUS='NEW',ACCESS='SEQUENTIAL',
     &     FORM='FORMATTED',IOSTAT=STAT)
      RETURN
      END
C
C
C
C =============================================================================
C                                  CLOSEFIL
C =============================================================================
C
C  This subroutine closes any file.
C
      SUBROUTINE CLOSEFIL(FILEID)
      IMPLICIT NONE
C
      INTEGER*4
     & FILEID
C
      CLOSE(UNIT=FILEID)
      RETURN
      END
C
C
C
C =============================================================================
C                                  SHOWLINE
C =============================================================================
C
C  This subroutine displays a file name and line on the terminal.
C
      SUBROUTINE SHOWLINE(FILENAME,LINENUM)
      IMPLICIT NONE
C
      CHARACTER
     & FILENAME*(*),
     & BUFFER*80,
     & LINBUF*4
C
      INTEGER*2
     & LINENUM
C
      WRITE (LINBUF,'(I4)') LINENUM
      BUFFER = 'File: '//FILENAME//' Line: '//LINBUF
C
      CALL TERMWRITE(BUFFER)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  SHOWFILE
C =============================================================================
C
C  This subroutine displays a file name on the terminal.
C
      SUBROUTINE SHOWFILE(FILENAME)
      IMPLICIT NONE
C
      CHARACTER
     & FILENAME*(*),
     & BUFFER*80
C
      BUFFER = 'File: '//FILENAME
C
      CALL TERMWRITE(BUFFER)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  TERMWRITE
C =============================================================================
C
C  This subroutine outputs a line of message to the terminal.
C
      SUBROUTINE TERMWRITE(TEXT)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & TEXT
C
      WRITE(6,600) TEXT
 600  FORMAT(' ',A)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  TERMREAD
C =============================================================================
C
C  This subroutine writes a message to the terminal without <CRLF> and reads
C  back another message. The input message is converted to uppercase.
C
      SUBROUTINE TERMREAD(OPMESS,IPMESS)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & OPMESS,
     & IPMESS
C
      WRITE(6,700) OPMESS
 700  FORMAT(' ',A,$)
C
      READ(5,800) IPMESS
 800  FORMAT (A)
      CALL TO_UPPER(IPMESS)
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  HEADER
C =============================================================================
C
C  This subroutine writes a header on the terminal.
C
      SUBROUTINE HEADER
      IMPLICIT NONE
C
      CHARACTER
     & DDATE*11,
     & DTIME*11,
     & ALIGN*2,
     & ESCAPE*2
C
      ESCAPE = CHAR(27)//'['
C
      CALL CDATE(DDATE,DTIME)
C
      WRITE(6,1)ESCAPE,ESCAPE,ESCAPE,DDATE      !Write header & date
      WRITE(6,2)ESCAPE                          !Write Status line
      WRITE(6,3)ESCAPE                          !Set scrolling region
      WRITE(6,4)ESCAPE                          !Set cursor position
      RETURN
C
 1    FORMAT(A2,'2J',A2,'4;27H',29('*'),/,
     &       26X,'*',T35,'W A V E G E N',T55,'*',/,
     &       26X,'*    C O N T R O L L E R    *',/,
     &       26X,'*',T35,'U T I L I T Y',T55,'*',/,
     &       26X,29('*'),A2,'10;35H','VERSION 1.3',/,
     &       10X,/,
     &       27X,'DATE OF THIS RUN: ',A)
C
 2    FORMAT(' ',A2,'15;1H',35('='),'<STATUS>',36('='))
C
 3    FORMAT(' ',A2,'17;24r')
C
 4    FORMAT(' ',A2,'16;1H')
C
      END
C
C
C
C =============================================================================
C                                  RSTWIND
C =============================================================================
C
C  This subroutine resets the terminal's window.
C
      SUBROUTINE RSTWIND
      IMPLICIT NONE
C
      CHARACTER
     & ESCAPE*2
C
      ESCAPE = CHAR(27)//'['
C
      WRITE(6,1)ESCAPE,ESCAPE
      RETURN
C
 1    FORMAT(' ',A2,'01;24r',A2,'23;1H')
C
      END
C
C
C
C =============================================================================
C                                 HEXBLKDAT
C =============================================================================
C
C  This BLOCK DATA contains the character representation of the hexadecimal
C  constant format for VAX and GOULD FORTRAN. Choose one by commenting out
C  the other.
C
      BLOCK DATA HEXBLKDAT
C
      CHARACTER*8
     & HEXFMT
C
      COMMON /HEXDATA/ HEXFMT
C
      DATA HEXFMT /'X\'    \' '/        !This is the GOULD format
CC    DATA HEXFMT /' ''    ''X'/        !This is the VAX format
C
      END
C
C
C
C =============================================================================
C                                 GETEXTMEM
C =============================================================================
C
C  This subroutine reserves necessary memory for the EXTMEM common block
C  in the case of Sel computers.
C
      SUBROUTINE GETEXTMEM(*)
      IMPLICIT NONE
C
      INTEGER*4
     & NASK,
     & NGET,
     & ERRET
C
      INCLUDE 'wav.inc'
C
      RETURN          !Comment this statement for a Sel.
C
C      NASK = (ADDR(BOTTOM)-ADDR(TOP))/8192 + 1   !Comment these for VAX
C      CALL X:GDSPCE(NASK,NGET,ERRET,)
C      IF(ERRET.NE.0) THEN
C         CALL TERMWRITE('Error: can not get extended memory')
C         RETURN 1
C      END IF
C
C      RETURN
      END
C
C
C
C =============================================================================
C                                 CHKFILNAM
C =============================================================================
C
C  This subroutine reads a filename and returns 0 if it contains only a
C  name, 1 if it contains also a directory name or 2 if it contains a full
C  pathname.
C
      SUBROUTINE CHKFILNAM(FILENAME,TYPE)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & FILENAME
C
      INTEGER*4
     & TYPE
C
      IF (INDEX(FILENAME,'@').NE.0) THEN
         TYPE = 2
      ELSE IF (INDEX(FILENAME,'(').NE.0) THEN
         TYPE = 1
      ELSE
         TYPE = 0
      END IF
C
      RETURN
      END
C
C
C
C =============================================================================
C                                 CLEARMEM
C =============================================================================
C
C  This subroutine clears initialization sensitive variables.
C  This subroutine is optional for Vax.
C
      SUBROUTINE CLEARMEM
      IMPLICIT NONE
C
      INTEGER*4
     & I1,I2,I3
C
      INCLUDE 'wav.inc'
C
      DO I1=1,MAXMACRO
         DO I2=1,MAXEQN/2
            DO I3=1,12
               EQNOPER(I1,I2,I3) = .FALSE.
            END DO
         END DO
      END DO
C
      DO I1=1,MAXCALL
         SUM(I1) = .FALSE.
         WRT(I1) = .FALSE.
         LAST(I1) = .FALSE.
         CALLTYP(I1) = 0
      END DO
C
      DO I1=1,MAXMACRO
         NUMTBL(I1) = 0
         NUMLAB(I1) = 0
         NUMEQN(I1) = 0
      END DO
C
      RETURN
      END
C
C
C
C =============================================================================
C                                   BITPAT
C =============================================================================
C
C  This function returns a string containing the bit pattern of the low
C  byte of the given integer*2.
C
      CHARACTER*8 FUNCTION BITPAT(NUMBER)
      IMPLICIT NONE
C
CCC      EXTENDED DUMMY NUMBER  !This statement to be commented out for Vax
C
      INTEGER*2
     & NUMBER,
     & I
C
      DO I=7,0,-1
C
         IF (BTEST(NUMBER,I)) THEN
            BITPAT(8-I:8-I) = '1'
         ELSE
            BITPAT(8-I:8-I) = '0'
         END IF
C
      END DO
C
      RETURN
      END
