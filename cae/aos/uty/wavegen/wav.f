C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 
C       
C                                   WAV.FOR  
C       
C  This program is used to invoke the WAVEGEN utility.  
C       
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 
C       
C  Set SHIPMODE to .TRUE. in order to run in a ship environment, .FALSE.
C  otherwise. WAVEGEN returns with STATUS set to .TRUE. if no error was 
C  encountered, .FALSE. otherwise.      
C       
C       
      PROGRAM WG
C       
      LOGICAL*4
     & SHIPMODE,
     & STATUS   
C       
      SHIPMODE = .TRUE.        !Run in a site   
C      SHIPMODE = .FALSE.       !Stand-alone mode   
C       
      CALL WAVEGEN(SHIPMODE,STATUS) 
      CALL EXIT 
      END   
C       
