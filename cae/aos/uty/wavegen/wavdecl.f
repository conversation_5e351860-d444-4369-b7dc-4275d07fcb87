C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                WAVDECL.FOR
C
C  This module contains WAVEGEN utility's subroutines which read and check
C  information in the header and the DECLARATION block of the .DAT file.
C
C  READHEAD
C  READADDR
C  READMACR
C  READPARA
C  READLAB
C  READEQN1
C  READEQN2
C  READEQNL
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                  READHEAD
C =============================================================================
C
C  This subroutine reads and checks header information.
C
      SUBROUTINE READHEAD(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
      DATLINCNT = 0
C
C  Search for TITLE name
C  ---------------------
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing TITLE name')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      TITLE = DATLINE(POS:)
C
C  Search for DMC = <number>
C  -------------------------
C
      CALL SEARFILE('DMC',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing DMC keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C             
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing DMC number')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      READ (DATLINE(POS:POS+2),'(BN,Z2)',ERR=10) NUMBER
      IF (NUMBER.NE.DMC(DMCCNT)) THEN
         CALL TERMWRITE('DMC number mismatch')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(DATFILNAM,DATLINCNT)
      CALL TERMWRITE(DATLINE)
      RETURN 1 
C
      END
C
C
C
C =============================================================================
C                                  READADDR
C =============================================================================
C
C  This subroutine reads and checks ADDRESS information.
C
      SUBROUTINE READADDR(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for SLOT number
C  ----------------------
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SLOT number')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      READ (DATLINE(POS:POS+2),'(BN,I2)',ERR=10) NUMBER
      IF (NUMBER.NE.SLOT(DMCCNT,SLOTCNT)) THEN
         CALL TERMWRITE('SLOT number mismatch')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search for DECLA_START
C  ----------------------
C
      CALL SEARFILE('DECLA_START',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing DECLA_START keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
C  Search for ADDRESS
C  ------------------
C
      CALL SEARFILE('ADDRESS',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing ADDRESS keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
C  Search for FREQ = <address>
C  ---------------------------
C
      CALL SEARFILE('FREQ',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing FREQ keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing FREQ value')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      READ (DATLINE(POS:POS+4),'(BN,Z4)',ERR=10) STFREQ
C
C  Search for AMPL = <address>
C  ---------------------------
C
      CALL SEARFILE('AMPL',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing AMPL keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing AMPL value')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      READ (DATLINE(POS:POS+4),'(BN,Z4)',ERR=10) STAMPL
C
C  Search for PHASE = <address>
C  ---------------------------
C
      CALL SEARFILE('PHASE',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing PHASE keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing PHASE value')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C                        
      READ (DATLINE(POS:POS+5),'(BN,Z4)',ERR=10) STPHASE
C
C  Search for CTRL = <address>
C  ---------------------------
C
      CALL SEARFILE('CTRL',DATFILID,DATLINE,DATLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing CTRL keyword')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SEARSTR('=',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing CTRL value')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      READ (DATLINE(POS:POS+4),'(BN,Z4)',ERR=10) STCTRL
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(DATFILNAM,DATLINCNT)
      CALL TERMWRITE(DATLINE)
      RETURN 1 
C
      END
C
C
C
C =============================================================================
C                                  READMACR
C =============================================================================
C
C  This subroutine reads and checks MACRO information.
C
      SUBROUTINE READMACR(*)
      IMPLICIT NONE
C
      INTEGER*2
     & II,III
C
      INCLUDE 'wav.inc'
C
C  Initialize logical operand values
C  ---------------------------------
      DO I=1,MAXMACRO
         DO II=1,MAXEQN/2
           DO III=1,12
             EQNOPER(I,II,III) = .FALSE.
           END DO
         END DO
      END DO
C
C  Read MACROs until DECLA_END found
C  ---------------------------------
C
      NUMMACRO = 0
      CALL SEARFILE('DECLA_END',DATFILID,DATLINE,DATLINCNT,POS)
C        
      DO WHILE (POS.EQ.0)
C
C        Search for MACRO
C        ----------------
C
         POS = 1                               
         CALL SEARSTR('MACRO',DATLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing MACRO keyword')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
C
C        Search for <macroname>
C        ----------------------
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         NUMMACRO = NUMMACRO + 1
C
         IF (NUMMACRO.GT.MAXMACRO) THEN
            CALL TERMWRITE('Too many MACRO definitions')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
C
C        Check for duplicate <macroname>
C        -------------------------------
C
         DO I=1,NUMMACRO-1
            IF (SYMBOL.EQ.MACRONAME(I)) THEN
               CALL TERMWRITE('Duplicate MACRO name')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
         END DO
C
         MACRONAME(NUMMACRO) = SYMBOL
C
C        Search for parameters and labels
C        --------------------------------
C
         CALL READPARA(*20)
         CALL READLAB(*20)
C
C        Search for equations
C        --------------------
C     
         NUMEQN(NUMMACRO) = 0
         CALL SEARFILE('CW',DATFILID,DATLINE,DATLINCNT,POS)
C
         DO WHILE (POS.NE.0)
C
            NUMEQN(NUMMACRO) = NUMEQN(NUMMACRO) + 1
            IF (NUMEQN(NUMMACRO).GT.MAXEQN) THEN
               CALL TERMWRITE('Too many equations in MACRO definition')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
C
            CALL READEQN1(*20)
C
            CALL SEARFILE('CW',DATFILID,DATLINE,DATLINCNT,POS)
            NUMEQN(NUMMACRO) = NUMEQN(NUMMACRO) + 1
C
            CALL READEQN2(*20)
C
            CALL SEARFILE('CW',DATFILID,DATLINE,DATLINCNT,POS)
         END DO
C
C        Search for ENDM <macroname>
C        ---------------------------
C
         POS = 1
         CALL SEARSTR('ENDM',DATLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing ENDM keyword')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)               
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         IF (SYMBOL.NE.MACRONAME(NUMMACRO)) THEN
            CALL TERMWRITE('Wrong MACRO name after ENDM')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
C
         CALL SEARFILE('DECLA_END',DATFILID,DATLINE,DATLINCNT,POS)
      END DO
C
      RETURN
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  READPARA
C =============================================================================
C
C  This subroutine reads the parameters of a MACRO definition.
C
      SUBROUTINE READPARA(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for { ( } 
C  ----------------
C
      CALL SEARSTR('(',DATLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / ( / in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1 
      END IF
C
      NUMTBL(NUMMACRO) = 0
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C         
      DO WHILE (SYMBOL.NE.'SUM'.AND.POS.NE.0)
C
C        Search for TBL entries up to SUM
C        --------------------------------
C
         DO I=1,NUMTBL(NUMMACRO)-1
            IF (SYMBOL.EQ.TBLNAME(NUMMACRO,I)) THEN
               CALL TERMWRITE('Duplicate TBL names')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
         END DO
C
         NUMTBL(NUMMACRO) = NUMTBL(NUMMACRO) + 1
         IF (NUMTBL(NUMMACRO).GT.MAXTBL) THEN 
            CALL TERMWRITE('Too many TBL entries in MACRO definition')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
C
         TBLNAME(NUMMACRO,NUMTBL(NUMMACRO)) = SYMBOL
C
C        Search for { , }
C        ----------------
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / , / in TBL list')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1 
         END IF
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      END DO
C
C  Search for SUM,WRT,LAST
C  -----------------------
C
      IF (SYMBOL.NE.'SUM') THEN
         CALL TERMWRITE('Missing SUM keyword in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1                     
      END IF
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Missing / , / in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.'WRT') THEN
         CALL TERMWRITE('Missing WRT keyword in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Missing / , / in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.'LAST') THEN
         CALL TERMWRITE('Missing LAST keyword in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search for { ) }
C  ----------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.')') THEN
         CALL TERMWRITE('Missing / ) / in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  READLAB
C =============================================================================
C
C  This subroutine reads the labels of a MACRO definition.
C
      SUBROUTINE READLAB(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for labels if present
C  ----------------------------
C
      NUMLAB(NUMMACRO) = 0
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      IF (POS.NE.0) THEN      
C
         NUMLAB(NUMMACRO) = NUMLAB(NUMMACRO) + 1
         LABNAME(NUMMACRO,NUMLAB(NUMMACRO)) = SYMBOL
C
         CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
         DO WHILE (POS.NE.0)
C
C           Search for { , }
C           ----------------
C
            IF (SYMBOL.NE.',') THEN
               CALL TERMWRITE('Missing / , / in MACRO definition')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
C
C           Search for next label
C           ---------------------
C
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
            NUMLAB(NUMMACRO) = NUMLAB(NUMMACRO) + 1
            IF (NUMLAB(NUMMACRO).GT.MAXLAB) THEN
               CALL TERMWRITE('Too many labels in MACRO definition')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
            LABNAME(NUMMACRO,NUMLAB(NUMMACRO)) = SYMBOL
C
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
         END DO
C
      END IF
C
      RETURN
      END
C                                                
C
C
C =============================================================================
C                                  READEQN1
C =============================================================================
C
C  This subroutine reads the odd equations of a MACRO definition.
C
      SUBROUTINE READEQN1(*)
      IMPLICIT NONE                
C
      INCLUDE 'wav.inc'
C
C  Verify equation number
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      READ (SYMBOL,'(BN,I2)',ERR=10) NUMBER
      IF (NUMBER.NE.NUMEQN(NUMMACRO)) THEN
         CALL TERMWRITE('Wrong equation number in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search for { = }
C  ---------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.'=') THEN
         CALL TERMWRITE('Missing / = / in MACRO definition equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search operands until { / } found
C  ---------------------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      DO WHILE (SYMBOL.NE.'/'.AND.POS.NE.0)
C
C        Identify operand
C        ----------------
C                  
         IF (SYMBOL.EQ.'+'.OR.SYMBOL.EQ.',') THEN
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
         ELSE 
            FOUND = .FALSE.
            DO I=1,12
               IF (SYMBOL.EQ.OPERAND(I)) THEN
                  EQNOPER(NUMMACRO,NUMEQN(NUMMACRO)/2+1,I) = .TRUE.
                  FOUND = .TRUE.
               END IF
            END DO
C
            IF (.NOT.FOUND) THEN
               CALL TERMWRITE('Unreckognized equation operand')
               CALL SHOWLINE(DATFILNAM,DATLINCNT)
               CALL TERMWRITE(DATLINE)
               RETURN 1
            END IF
            CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
         END IF
      END DO
C
C  Search for 3 equation labels
C  ----------------------------
C
      IF (POS.NE.0) THEN
         CALL READEQNL(*20)
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(DATFILNAM,DATLINCNT)
      CALL TERMWRITE(DATLINE)
 20   RETURN 1 
C
      END                          
C
C
C
C =============================================================================
C                                  READEQN2
C =============================================================================
C
C  This subroutine reads the even equations of a MACRO definition.
C
      SUBROUTINE READEQN2(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Verify equation number
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      READ (SYMBOL,'(BN,I2)',ERR=10) NUMBER
      IF (NUMBER.NE.NUMEQN(NUMMACRO)) THEN
         CALL TERMWRITE('Wrong equation number in MACRO definition')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search for { = }
C  ---------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.'=') THEN
         CALL TERMWRITE('Missing / = / in MACRO definition equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Search for TBL entry 
C  --------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      FOUND = .FALSE.
      DO I=1,NUMTBL(NUMMACRO)
         IF (SYMBOL.EQ.TBLNAME(NUMMACRO,I)) FOUND = .TRUE.                  
      END DO   
C
      IF (.NOT.FOUND) THEN
         CALL TERMWRITE('Wrong TBL entry in equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
      EQNTBL(NUMMACRO,NUMEQN(NUMMACRO)/2) = SYMBOL
C                                  
C  Search for { / }
C  ----------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
C  Search for 3 equation labels
C  ----------------------------
C
      IF (POS.NE.0) THEN
         CALL READEQNL(*20)
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(DATFILNAM,DATLINCNT)
      CALL TERMWRITE(DATLINE)
 20   RETURN 1 
C
      END
C
C
C
C =============================================================================
C                                  READEQNL
C =============================================================================
C
C  This subroutine reads the 3 labels of an equation.
C
      SUBROUTINE READEQNL(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Verify { / } was found
C  ----------------------
C          
      IF (SYMBOL.NE.'/') THEN
         CALL TERMWRITE('Missing / / / in equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Find 1st label number 
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      IF (SYMBOL.EQ.'0') THEN
         EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),1) = 0
C
      ELSE
         FOUND = .FALSE.
         DO I=1,NUMLAB(NUMMACRO)
            IF (SYMBOL.EQ.LABNAME(NUMMACRO,I)) THEN
               EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),1) = I
               FOUND = .TRUE.
            END IF
         END DO
C
         IF (.NOT.FOUND) THEN
            CALL TERMWRITE('Wrong equation label')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF                                
      END IF
C
C  Search for { , }
C  ----------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Missing / , / in equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Find 2nd label number 
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      IF (SYMBOL.EQ.'0') THEN
         EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),2) = 0
C
      ELSE
         FOUND = .FALSE.
         DO I=1,NUMLAB(NUMMACRO)
            IF (SYMBOL.EQ.LABNAME(NUMMACRO,I)) THEN
               EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),2) = I
               FOUND = .TRUE.
            END IF
         END DO
C
         IF (.NOT.FOUND) THEN
            CALL TERMWRITE('Wrong equation label')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
      END IF
C
C  Search for { , }
C  ----------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
      IF (SYMBOL.NE.',') THEN
         CALL TERMWRITE('Missing / , / in equation')
         CALL SHOWLINE(DATFILNAM,DATLINCNT)
         CALL TERMWRITE(DATLINE)
         RETURN 1
      END IF
C
C  Find 3rd label number 
C  ---------------------
C
      CALL GETSYMBL(SYMBOL,DATFILID,DATLINE,DATLINCNT,POS)
C
      IF (SYMBOL.EQ.'0') THEN
         EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),3) = 0
C
      ELSE
         FOUND = .FALSE.
         DO I=1,NUMLAB(NUMMACRO)
            IF (SYMBOL.EQ.LABNAME(NUMMACRO,I)) THEN
               EQNLAB(NUMMACRO,NUMEQN(NUMMACRO),3) = I
               FOUND = .TRUE.
            END IF
         END DO
C
         IF (.NOT.FOUND) THEN
            CALL TERMWRITE('Wrong equation label')
            CALL SHOWLINE(DATFILNAM,DATLINCNT)
            CALL TERMWRITE(DATLINE)
            RETURN 1
         END IF
      END IF
C
      RETURN
      END
C
