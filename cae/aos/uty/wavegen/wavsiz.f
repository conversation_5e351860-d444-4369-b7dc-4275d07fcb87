C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVSIZ.FOR
C
C  This module contains WAVEGEN utility's subroutines which read and
C  check information of the .SIZ file.
C
C  RDSIZFIL
C  READDMC
C  READSIZE 
C  READSRC
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C'Revision_History
C
C
C =============================================================================
C                   "              RDSIZFIL
C =============================================================================
C
C  This subroutine reads and checks .SIZ file information.
C
      SUBROUTINE RDSIZFIL(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Read and check DMC entries while TITLE keywords found
C  -----------------------------------------------------
C
      NUMDMC = 0
      CALL SEARFILE('TITLE',SIZFILID,SIZLINE,SIZLINCNT,POS)
C
      DO WHILE (POS.NE.0)
C
C        Search for DMC = <number>
C        -------------------------
C
         CALL SEARFILE('DMC',SIZFILID,SIZLINE,SIZLINCNT,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing DMC keyword')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1 
         END IF
C
         NUMDMC = NUMDMC + 1
         IF (NUMDMC.GT.MAXDMC) THEN
            CALL TERMWRITE('Too many DMC entries')
            CALL SHOWFILE(SIZFILNAM)
            RETURN 1
         END IF
C
         CALL SEARSTR('=',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / = /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1 
         END IF
C   
         CALL SCANSTR(SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing DMC number')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1 
         END IF
C
         READ (SIZLINE(POS:POS+2),'(BN,Z2)',ERR=10) DMC(NUMDMC)
C
C        Read and check DMC entries
C        --------------------------
C
         CALL READDMC(*20)
C
C        Check for another TITLE keyword
C        -------------------------------
C
         POS = 1
         CALL SEARSTR('TITLE',SIZLINE,POS)
      END DO
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
      CALL TERMWRITE(SIZLINE)
C
 20   RETURN 1 
C
      END
C
C        
C
C =============================================================================
C                                  READDMC
C =============================================================================
C
C  This subroutine reads and checks a DMC entry information.
C
      SUBROUTINE READDMC(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Read and process SLOT entries while SLOT keywords found
C  -------------------------------------------------------
C
      NUMSLOT(NUMDMC) = 0
      CALL SEARFILE('SLOT',SIZFILID,SIZLINE,SIZLINCNT,POS)
C
      DO WHILE (POS.NE.0)
C
         NUMSLOT(NUMDMC) = NUMSLOT(NUMDMC) + 1
         IF (NUMSLOT(NUMDMC).GT.MAXSLOT) THEN
            CALL TERMWRITE('Too many SLOT entries')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Read and check all SLOT entry data
C        ----------------------------------
C
         CALL READSIZE(*20)
         CALL READSRC(*20)
C
         CALL SEARFILE('SLOT',SIZFILID,SIZLINE,SIZLINCNT,POS)
      END DO
C
      RETURN
C
 20   RETURN 1
      END
C
C
C
C =============================================================================
C                                  READSIZE
C =============================================================================
C  
C  This subroutine reads and checks SIZE information.
C
      SUBROUTINE READSIZE(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C     Initialize all table number buffer to table does not exist,
C     it will be set only if table exist
C     -----------------------------------------------------------
      DO I=1,MAXSIZE
         TABLE_NUM(NUMDMC,NUMSLOT(NUMDMC),I) = -1
      ENDDO
C
C  Search for SLOT number
C  ----------------------
C
      CALL SEARSTR('=',SIZLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing / = /')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1 
      END IF
C
      CALL SCANSTR(SIZLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SLOT number')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1
      END IF
C
      READ (SIZLINE(POS:POS+2),'(BN,I2)',ERR=10)
     & SLOT(NUMDMC,NUMSLOT(NUMDMC))
      IF (SLOT(NUMDMC,NUMSLOT(NUMDMC)).LT.1.OR.
     &    SLOT(NUMDMC,NUMSLOT(NUMDMC)).GT.MAXSLOT) THEN
         CALL TERMWRITE('Invalid SLOT number')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1
      END IF
C
C  Search for SIZE_START
C  ---------------------
C
      CALL SEARFILE('SIZE_START',SIZFILID,SIZLINE,SIZLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SIZE_START keyword')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1 
      END IF
C
C  Search for SIZE(i) = <value> until SIZE_END found
C  -------------------------------------------------
C                     
      NUMSIZE(NUMDMC,NUMSLOT(NUMDMC)) = 0
      CALL SEARFILE('SIZE_END',SIZFILID,SIZLINE,SIZLINCNT,POS)
C
      DO WHILE (POS.EQ.0.AND.INDEX(SIZLINE,'SIZE').NE.0)
C
         NUMSIZE(NUMDMC,NUMSLOT(NUMDMC)) =    
     &   NUMSIZE(NUMDMC,NUMSLOT(NUMDMC)) + 1
C
         IF (NUMSIZE(NUMDMC,NUMSLOT(NUMDMC)).GT.MAXSIZE) THEN
            CALL TERMWRITE('Too many SIZE entries')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Search for SIZE
C        ---------------
C
         POS = 1
         CALL SEARSTR('SIZE',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing SIZE keyword')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Check SIZE index
C        ----------------
C
         CALL SEARSTR('(',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / ( /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C                      
         CALL GETSYMBL(SYMBOL,SIZFILID,SIZLINE,SIZLINCNT,POS)
         READ (SYMBOL,'(BN,I2)',ERR=10) NUMBER
         IF (NUMBER.NE.NUMSIZE(NUMDMC,NUMSLOT(NUMDMC))) THEN
            CALL TERMWRITE('Wrong SIZE index')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF 
C
         CALL SEARSTR(')',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / ) /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Search for { = }
C        ----------------
C
         CALL SEARSTR('=',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / = /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF  
C
         CALL SCANSTR(SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing SIZE value')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Get <value>
C        -----------
C
         READ (SIZLINE(POS:POS+4),'(BN,I4)',ERR=10) NUMBER
         IF (NUMBER.EQ.512.OR.
     &      NUMBER.EQ.1024.OR.
     &      NUMBER.EQ.2048.OR.
     &      NUMBER.EQ.4096) THEN                                
            SIZE(NUMDMC,
     &           NUMSLOT(NUMDMC),
     &           NUMSIZE(NUMDMC,NUMSLOT(NUMDMC))) = NUMBER
         ELSE
            CALL TERMWRITE('Invalid SIZE value')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1                 
         END IF
C
C        Get table number field
C        ----------------------
CC         CALL SCANSTR(SIZLINE,POS)
CC         IF (POS.EQ.0) THEN
CC            CALL TERMWRITE('Missing TABLE number')
CC            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
CC            CALL TERMWRITE(SIZLINE)
CC            RETURN 1
CC         END IF
C
C        Get <value>
C        -----------
C
         READ (SIZLINE(34:35),'(BN,I2)',ERR=10) NUMBER
         IF (NUMBER.GT.0) THEN
            TABLE_NUM(NUMDMC,
     &           NUMSLOT(NUMDMC),
     &           NUMSIZE(NUMDMC,NUMSLOT(NUMDMC))) = NUMBER
         ELSE
            CALL TERMWRITE('Zero table found: Invalid')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1                 
         END IF
C
         CALL SEARFILE('SIZE_END',SIZFILID,SIZLINE,SIZLINCNT,POS)
      END DO
C
C  Verify SIZE_END was found
C  -------------------------
C
      POS = 1
      CALL SEARSTR('SIZE_END',SIZLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SIZE_END keyword')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1 
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
      CALL TERMWRITE(SIZLINE)
      RETURN 1 
C
      END
C
C
C        
C =============================================================================
C                                  READSRC
C =============================================================================
C
C  This subroutine reads and checks SOURCE information.
C
      SUBROUTINE READSRC(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Search for SOURCE_START
C  -----------------------
C
      CALL SEARFILE('SOURCE_START',SIZFILID,SIZLINE,SIZLINCNT,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SOURCE_START keyword')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1 
      END IF
C
C  Search for <sourcename>nn = SIZE(i) until SOURCE_END found
C  ----------------------------------------------------------
C
      NUMSRC(NUMDMC,NUMSLOT(NUMDMC)) = 0
      CALL SEARFILE('SOURCE_END',SIZFILID,SIZLINE,SIZLINCNT,POS)
C
      DO WHILE (POS.EQ.0)
C
         IF (INDEX(SIZLINE,'SIZE').EQ.0) THEN
            CALL TERMWRITE('Missing SIZE keyword in SOURCE list')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
         NUMSRC(NUMDMC,NUMSLOT(NUMDMC)) =
     &   NUMSRC(NUMDMC,NUMSLOT(NUMDMC)) + 1
         IF (NUMSRC(NUMDMC,NUMSLOT(NUMDMC)).GT.MAXSRC) THEN
            CALL TERMWRITE('Too many SOURCE entries')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
C        Get <sourcename>
C        ----------------
C
         POS = 1
         CALL GETSYMBL(SYMBOL,SIZFILID,SIZLINE,SIZLINCNT,POS)
C
         SRCNAME(NUMDMC,
     &           NUMSLOT(NUMDMC),
     &           NUMSRC(NUMDMC,NUMSLOT(NUMDMC))) = SYMBOL
C
C        Search for SIZE keyword
C        -----------------------
C
         CALL SEARSTR('=',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / = /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
         CALL SEARSTR('SIZE',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing SIZE keyword')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C         
C        Get SIZE index 
C        --------------
C
         CALL SEARSTR('(',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / ( /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
         CALL GETSYMBL(SYMBOL,SIZFILID,SIZLINE,SIZLINCNT,POS)
         READ (SYMBOL,'(BN,I2)',ERR=10) NUMBER
         IF (NUMBER.GT.NUMSIZE(NUMDMC,NUMSLOT(NUMDMC))) THEN
            CALL TERMWRITE('Wrong SIZE index')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF 
C
         SRC(NUMDMC,
     &       NUMSLOT(NUMDMC),
     &       NUMSRC(NUMDMC,NUMSLOT(NUMDMC))) = NUMBER
C
         SRC_TAB(NUMDMC,
     &       NUMSLOT(NUMDMC),
     &       NUMSRC(NUMDMC,NUMSLOT(NUMDMC))) = TABLE_NUM(NUMDMC,
     &                                           NUMSLOT(NUMDMC),
     &                                           NUMBER)
C
         CALL SEARSTR(')',SIZLINE,POS)
         IF (POS.EQ.0) THEN
            CALL TERMWRITE('Missing / ) /')
            CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
            CALL TERMWRITE(SIZLINE)
            RETURN 1
         END IF
C
         CALL SEARFILE('SOURCE_END',SIZFILID,SIZLINE,SIZLINCNT,POS)
      END DO
C
C  Verify SOURCE_END was found
C  ---------------------------
C
      POS = 1
      CALL SEARSTR('SOURCE_END',SIZLINE,POS)
      IF (POS.EQ.0) THEN
         CALL TERMWRITE('Missing SOURCE_END keyword')
         CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
         CALL TERMWRITE(SIZLINE)
         RETURN 1
      END IF
C
      RETURN
C
 10   CALL TERMWRITE('Input conversion error')
      CALL SHOWLINE(SIZFILNAM,SIZLINCNT)
      CALL TERMWRITE(SIZLINE)
      RETURN 1 
C
      END
C
