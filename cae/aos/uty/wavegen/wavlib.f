C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVLIB.FOR
C
C  This module contains character data handling subroutines.
C
C  SEARFILE
C  SEARSTR  
C  GETSYMBL
C  SCANSTR  
C  EXTRNAME
C  STRLENG
C  ISVALID
C  ISNUM
C  TO_UPPER     
C  ADDCHKSUM
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C
C
C =============================================================================
C                                  SEARFILE
C ==?==========================================================================
C
C  This subroutine searches for a specific keyword in the file whose file
C  ID is given. The keyword to find must be the next keyword in the file.
C  The subroutine returns the line found containing it and the position
C  of the next character following it. If the keyword is absent from the
C  file, or if it is not the first keyword found, a 0 is returned as the
C  position. The routine starts its search by reading a new line. The line
C  returned is converted to uppercase. If a line is read successfully, the
C  line number is incremented. If the parameter given as keyword contains
C  blanks, they will not be considered as part of the keyword.
C
      SUBROUTINE SEARFILE(KEY,FILEID,LINE,LINENUM,POS)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & LINE,
     & KEY
C
      INTEGER*2
     & LINENUM,
     & POS,     
     & STRLENG,
     & STATUS,
     & KEYLEN
C
      INTEGER*4
     & FILEID
C                    
      POS = 0
C
C  Find next non-empty line
C  ------------------------
C
      DO WHILE (POS.EQ.0)
         CALL READLINE(LINE,FILEID,STATUS)      
         IF (STATUS) 300,100,200 
 100     LINENUM = LINENUM + 1
         POS = 1
         CALL SCANSTR(LINE,POS)
      END DO
C            
      CALL TO_UPPER(LINE)
C
C  Return position of next char or 0 if wrong keyword
C  --------------------------------------------------
C
      KEYLEN = STRLENG(KEY)
      IF (INDEX(LINE(POS:),KEY(:KEYLEN)).EQ.1) THEN
         POS = POS + KEYLEN
      ELSE
         POS = 0
      END IF
      RETURN
C
C  End of file encountered
C  -----------------------
C
 200  POS = 0
      RETURN
C
C  Error reading file
C  ------------------
C
 300  POS = 0
      CALL TERMWRITE('Error reading file')
      RETURN
      END
C
C
C
C =============================================================================
C                                  SEARSTR
C =============================================================================
C
C  This subroutine searches a string for a specific keyword. The keyword
C  to find must be the next keyword in the string. The subroutine returns
C  the position of the next character following it. If the keyword is absent
C  from the string, or if it is not the first keyword found, a 0 is returned
C  as the position. If the parameter given as keyword contains blanks,
C  they will not be considered as part of the keyword.
C
      SUBROUTINE SEARSTR(KEY,STRING,POS)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & KEY,
     & STRING
C
      INTEGER*2
     & POS,
     & STRLENG,
     & KEYLEN,
     & NXTPOS
C
C  Get position of key in string
C  -----------------------------
C
      KEYLEN = STRLENG(KEY)
      NXTPOS = INDEX(STRING,KEY(:KEYLEN))
C
C  If key found, verify it is the 1st keyword
C  ------------------------------------------
C
      IF (NXTPOS.NE.0) THEN
         IF (STRLENG(STRING(POS:NXTPOS-1)).EQ.0) THEN
            POS = NXTPOS + KEYLEN
         ELSE
            POS = 0
         END IF
C
      ELSE
         POS = 0
      END IF
C
      RETURN      
      END
C                          
C
C
C =============================================================================
C                                  GETSYMBL
C =============================================================================
C
C  This subroutine returns the next symbol found in file whose file ID is
C  given, starting at the line and position given. It returns also the line
C  where the symbol was found and the position of the next character after
C  it. The possible symbols are: { ( | ) | = | + | / | # | , | <name> }.
C  A name must contain only valid name characters (see function ISVALID)
C  and only the first 12 characters of the name are returned.
C  If a { , } is found ending a line, the next line is searched. If no symbol
C  is found, the position returned is 0. The line returned is converted
C  to uppercase.
C
      SUBROUTINE GETSYMBL(SYMBOL,FILEID,LINE,LINENUM,POS)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & SYMBOL,
     & LINE
C
      CHARACTER*12
     & EXTRNAME
C
      CHARACTER*1
     & C
C
      INTEGER*2
     & LINENUM,
     & POS,
     & STRLENG,
     & REM,
     & STATUS
C
      INTEGER*4
     & FILEID
C
      LOGICAL*1
     & ISVALID
C
C  Return now if line empty
C  ------------------------
C
      CALL SCANSTR(LINE,POS)
      IF (POS.NE.0) THEN
C
C        Get first character of symbol
C        -----------------------------
C
         C = LINE(POS:POS)
C
C        Return if character is one operation sign
C        -----------------------------------------
C
         IF (C.EQ.'('.OR.C.EQ.')'.OR.C.EQ.'='.OR.
     &       C.EQ.'+'.OR.C.EQ.'/'.OR.C.EQ.'#') THEN
            SYMBOL = C
            POS = POS + 1
C
C        If character is { , } check remainder of line 
C        ---------------------------------------------
C
         ELSE IF (C.EQ.',') THEN
            SYMBOL = C
            REM = POS + 1
            CALL SCANSTR(LINE,REM)
C
C           If { , } ends a line, return with continuation line 
C           ---------------------------------------------------
C
            IF (REM.EQ.0) THEN
               CALL READLINE(LINE,FILEID,STATUS)
               IF (STATUS) 700,500,600 
 500           LINENUM = LINENUM + 1
               CALL TO_UPPER(LINE)
               POS = 1
            ELSE
C
C              Return { , } as symbol
C              ----------------------
C
               POS = POS + 1
            END IF 
C
C        If character not valid, return it as is
C        ---------------------------------------
C
         ELSE IF (.NOT.ISVALID(C)) THEN
            SYMBOL = C
            POS = POS + 1
C
C        The character is part of a name, return the name
C        ------------------------------------------------
C
         ELSE
            SYMBOL = EXTRNAME(LINE(POS:))
            POS = POS + STRLENG(SYMBOL)
         END IF
C
      END IF
      RETURN
C
C  End of file encountered
C  -----------------------
C
 600  POS = 0
      RETURN
C
C  Error reading file
C  ------------------
C
 700  POS = 0
      CALL TERMWRITE('Error reading file')
      RETURN
      END
C
C
C
C =============================================================================
C                                  SCANSTR
C =============================================================================
C
C  This subroutine scans a string starting at the position given and returns
C  the position of the 1st character which is not *, !, <space> or <tab>.
C  A 0 is returned if no other character is found. The subroutine stops
C  scanning as soon as * or ! is found.
C                                
      SUBROUTINE SCANSTR(STRING,POS)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & STRING
C
      INTEGER*2 
     & POS,      
     & LENGTH
C
      LENGTH = LEN(STRING)
C
C  Find first non-white character
C  ------------------------------
C
      DO WHILE (POS.LE.LENGTH.AND.
     &          (STRING(POS:POS).EQ.' '.OR.
     &           STRING(POS:POS).EQ.CHAR(9)))
         POS = POS + 1
      END DO
C
C  Return 0 if string empty or comment line
C  ----------------------------------------
C
      IF (POS.GT.LENGTH.OR.
     &    STRING(POS:POS).EQ.'*'.OR.
     &    STRING(POS:POS).EQ.'!') POS = 0
C
      RETURN
      END
C
C                                              
C
C =============================================================================
C                                EXTRNAME
C =============================================================================
C
C  This function returns a name extracted from a string. The name must start
C  at the beginning of the string and must contain only valid name characters.
C  See the list of name characters specified in the ISVALID function.
C
      CHARACTER*(*) FUNCTION EXTRNAME(STRING)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & STRING
C
      CHARACTER*1
     & C
C                                
      INTEGER*2                                 
     & I,
     & STRLENG,
     & LENGTH
C
      LOGICAL*1
     & ISVALID
C
      LENGTH = STRLENG(STRING)
      I = 1
      C = STRING(I:I)
C
      DO WHILE (I.LE.LENGTH.AND.ISVALID(C))
         I = I + 1
         C = STRING(I:I)
      END DO                            
C
      EXTRNAME = STRING(:I-1)
C
      RETURN                      
      END                                      
C
C
C
C =============================================================================
C                                   STRLENG
C =============================================================================
C
C  This function returns the real length of a string without its left
C  blanks or tabs.
C                                
      INTEGER FUNCTION STRLENG(STRING)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & STRING
C
      INTEGER*2
     & LENGTH,
     & POS
C
      LENGTH = LEN(STRING)
C
      POS = 1
      DO WHILE (POS.LE.LENGTH.AND.
     &          STRING(POS:POS).NE.' '.AND.
     &          STRING(POS:POS).NE.CHAR(9))
         POS = POS + 1
      END DO
C    
      STRLENG = POS - 1
C
      RETURN
      END
C
C
C                                   
C =============================================================================
C                                  ISVALID
C =============================================================================
C
C  This function returns a .TRUE. if the character given is valid, .FALSE.
C  otherwise. These are the valid characters:
C  { A-Z | a-z | 0-9 | [ | ] | _ | $ | * }. Any name must not include
C  characters other than these.
C
      LOGICAL FUNCTION ISVALID(C)
      IMPLICIT NONE
C
      CHARACTER*1
     & C
C
      ISVALID = .FALSE.
C
      IF (C.GE.'A'.AND.C.LE.'Z'.OR.
     &    C.GE.'a'.AND.C.LE.'z'.OR.
     &    C.GE.'0'.AND.C.LE.'9'.OR.
     &    C.EQ.'['.OR.
     &    C.EQ.']'.OR.
     &    C.EQ.'_'.OR.
     &    C.EQ.'$'.OR.
     &    C.EQ.'*') ISVALID = .TRUE.
C
      RETURN
      END
C
C
C                                   
C =============================================================================
C                                  ISNUM
C =============================================================================
C
C  This function returns a .TRUE. if the character given is in { 0-9 },
C  .FALSE. otherwise.
C
      LOGICAL FUNCTION ISNUM(C)
      IMPLICIT NONE
C
      CHARACTER*1
     & C
C
      IF (C.GE.'0'.AND.C.LE.'9') THEN
         ISNUM = .TRUE.
      ELSE
         ISNUM = .FALSE.
      END IF
C
      RETURN
      END
C
C        
C
C =============================================================================
C                                   TO_UPPER
C =============================================================================
C
C  This subroutine converts a string to uppercase.
C
      SUBROUTINE TO_UPPER(STRING)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & STRING
C
      INTEGER*2
     & I,
     & DELTA
C
      DELTA = ICHAR('a') - ICHAR('A')      
C
      I = 1
      DO WHILE (I.LE.LEN(STRING))
         IF (STRING(I:I).GE.'a'.AND.STRING(I:I).LE.'z') THEN
            STRING(I:I) = CHAR(ICHAR(STRING(I:I)) - DELTA)
         END IF
         I = I + 1
      END DO
C
      RETURN
      END
C
C        
C
C =============================================================================
C                                 ADDCHKSUM
C =============================================================================
C
C  This subroutine adds the checksum to an Intel Hex record.
C
      SUBROUTINE ADDCHKSUM(REC)
      IMPLICIT NONE
C
      CHARACTER*(*)
     & REC
C
      INTEGER*2
     & I,
     & LENGTH,
     & STRLENG,
     & CHECKSUM,
     & ABYTE
C
      LENGTH = STRLENG(REC)      
      CHECKSUM = 0
C
      DO I=2,LENGTH-1,2
         READ (REC(I:I+1),'(Z2)') ABYTE
         CHECKSUM = MOD(CHECKSUM + ABYTE+0,256)
      END DO
C
      IF (CHECKSUM.NE.0) CHECKSUM = MOD(32767 - CHECKSUM + 1,256)
C
      WRITE (REC(LENGTH+1:LENGTH+2),'(Z2.2)') CHECKSUM
C
      RETURN
      END
C
