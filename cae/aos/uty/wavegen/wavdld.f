C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C                                 WAVDLD.FOR
C
C  This module contains WAVEGEN utility's subroutines which write processed
C  information to the .DLD file.
C
C  WRTDLDHD
C  WRTDLDSLT
C  WRTDLDDAT
C  WRTDLDEOF
C
C >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
C
C'Revision_History
C
C
C =============================================================================
C                                  WRTDLDHD
C =============================================================================
C
C  This subroutine writes the .DLD file header information. 
C
      SUBROUTINE WRTDLDHD(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C                 
C  Write lines for empty SLOTs
C  ---------------------------
C
C      IF (DMCCNT.GT.1) THEN
C         DO I=LASTSLOT+1,MAXSLOT     
C            OPLINE = BRACKETS
C            OPLINE(10:34) = ' NO WAVEGEN ON SLOT #    '
C            WRITE (OPLINE(32:33),'(I2.2)') I
C            CALL WRITELINE(OPLINE,DLDFILID,STATUS)
C         END DO
C      END IF
C
C  Write box 
C  ---------
C
      IF (DMCCNT.EQ.1) THEN
         CALL WRITELINE(DOLLAR,DLDFILID,STATUS)
         CALL WRITELINE(DLDTITL1,DLDFILID,STATUS)
         CALL WRITELINE(DLDTITL2,DLDFILID,STATUS)
         CALL WRITELINE(DOLLAR,DLDFILID,STATUS)
      ELSE
         CALL WRITELINE(DOLLAR,DLDFILID,STATUS)
      END IF
C
      IF (STATUS.NE.0) THEN
         CALL TERMWRITE('Error writing file')
         CALL SHOWFILE(DLDFILNAM)                 
         RETURN 1 
      END IF
C
C  Write chassis # & DMC #
C  -----------------------
C
      OPLINE = EQUALS
      OPLINE(8:20) = ' CHASSIS #   '
      WRITE (OPLINE(19:19),'(I1)') DMCCNT
C
      OPLINE(26:35) = ' DMC #    '
      WRITE (OPLINE(33:34),'(Z2.2)') DMC(DMCCNT)
C
      CALL WRITELINE(OPLINE,DLDFILID,STATUS)
C
C  Initialize empty SLOT marker
C  ----------------------------
C
      LASTSLOT = 0
C
      RETURN
      END
C
C
C
C =============================================================================
C                                  WRTDLDSLT
C =============================================================================
C
C  This subroutine writes a .DLD file SLOT entry. 
C
      SUBROUTINE WRTDLDSLT(*)
      IMPLICIT NONE     
C
      INCLUDE 'wav.inc'                  
C
C  Write lines for empty SLOTs
C  ---------------------------
C
      DO I=LASTSLOT+1,SLOT(DMCCNT,SLOTCNT)-1     
         OPLINE = BRACKETS
         OPLINE(10:34) = ' NO WAVEGEN ON SLOT #    '
         WRITE (OPLINE(32:33),'(I2.2)') I
         CALL WRITELINE(OPLINE,DLDFILID,STATUS)
      END DO
C
      LASTSLOT = SLOT(DMCCNT,SLOTCNT)
C
C  Write SLOT header
C  -----------------
C
      OPLINE = BRACKETS
      OPLINE(17:27) = ' SLOT #    '
      WRITE (OPLINE(25:26),'(I2.2)') SLOT(DMCCNT,SLOTCNT)
      CALL WRITELINE(OPLINE,DLDFILID,STATUS)
C
C  Write header record
C  -------------------
C
      OPLINE = AMPER      
      WRITE (OPLINE(2:3),'(Z2.2)') DMC(DMCCNT)
      OPLINE(4:5) = 'HW'
      WRITE (OPLINE(6:7),'(Z2.2)') SLOT(DMCCNT,SLOTCNT)
      CALL WRITELINE(OPLINE,DLDFILID,STATUS)
C
C  Write extended address record
C  -----------------------------
C
      RECORD = ':020000021400'
      CALL ADDCHKSUM(RECORD)
      CALL WRITELINE(RECORD,DLDFILID,STATUS)
C
C  Write data records
C  ------------------
C
      CALL WRTDLDDAT(*20)
C
C  Write end of file record
C  ------------------------
C
      RECORD = ':00000001'
      CALL ADDCHKSUM(RECORD)
      CALL WRITELINE(RECORD,DLDFILID,STATUS)
C                 
C  Write lines for empty SLOTs
C  ---------------------------
C
      IF (SLOTCNT.EQ.NUMSLOT(DMCCNT)) THEN
         DO I=LASTSLOT+1,MAXSLOT
            OPLINE = BRACKETS
            OPLINE(10:34) = ' NO WAVEGEN ON SLOT #    '
           WRITE (OPLINE(32:33),'(I2.2)') I
           CALL WRITELINE(OPLINE,DLDFILID,STATUS)
         END DO
      END IF
C
      RETURN
 20   RETURN 1
      END
C
C
C                         
C =============================================================================
C                                  WRTDLDDAT
C =============================================================================
C
C  This subroutine writes the data records of a SLOT entry,
C
      SUBROUTINE WRTDLDDAT(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Reset CW counter
C  ----------------
C
      CWCNT = 1 
C
C  Write 512 CWs, 8 in each record
C  -------------------------------
C
      ADDCNT = 0
C
      DO I=1,64
C
C        Write a full record
C        -------------------
C
         RECORD = ':1000000000000000000000000000000000000000'
         WRITE (RECORD(4:7),'(Z4.4)') ADDCNT
         ADDCNT = ADDCNT + 16
C
         DO J=10,34,8
C
C           Write control word
C           ------------------
C
            WRITE (RECORD(J:J+1),'(Z2.2)') CW(CWCNT)
            CWCNT = CWCNT + 1
C
C           Write number code
C           -----------------
C
            WRITE (RECORD(J+4:J+5),'(Z2.2)') CW(CWCNT)
            CWCNT = CWCNT + 1
         END DO
C
         CALL ADDCHKSUM(RECORD)
         CALL WRITELINE(RECORD,DLDFILID,STATUS)
C
      END DO
C
      RETURN
      END
C
C
C
C =============================================================================
C                                 WRTDLDEOF
C =============================================================================
C
C  This subroutine writes the .DLD file end of file message.
C
      SUBROUTINE WRTDLDEOF(*)
      IMPLICIT NONE
C
      INCLUDE 'wav.inc'
C
C  Write EOF message
C  -----------------
C
      CALL WRITELINE(DOLLAR,DLDFILID,STATUS)
      CALL WRITELINE(ENDMESS1,DLDFILID,STATUS)
      CALL WRITELINE(ENDMESS2,DLDFILID,STATUS)
      CALL WRITELINE(DOLLAR,DLDFILID,STATUS)
C
      RETURN
      END
C
