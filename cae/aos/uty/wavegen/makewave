INCLUDE = wav.inc
LIBDIR = $(aos_disk)/aos/uty/library
EXEDIR = $(aos_disk)/aos/uty/exec
CAELIB = /cae/lib
#
wavegen: wav.o wavcode.o wavdecl.o wavdld.o wavunix.o wavinc.o wavinf.o \
wavlib.o wavsiz.o wavmain.o wavproc.o $(CAELIB)/libcae.a $(LIBDIR)/libaos.a
#
#
	xlf -C -qcharlen=1024 wav.o wavcode.o wavdecl.o wavdld.o wavunix.o \
wavinc.o wavinf.o wavlib.o wavsiz.o wavmain.o wavproc.o \
-L$(CAELIB) -lcae -lc -L$(LIBDIR) -laos -o $(EXEDIR)/wavegen
#
#
wav.o: wav.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wav.f
#
wavcode.o: wavcode.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavcode.f
#
wavdecl.o: wavdecl.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavdecl.f
#
wavdld.o: wavdld.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavdld.f
#
wavunix.o: wavunix.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavunix.f
#
wavinc.o: wavinc.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavinc.f
#
wavinf.o: wavinf.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavinf.f
#
wavlib.o: wavlib.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavlib.f
#
wavsiz.o: wavsiz.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavsiz.f
#
wavmain.o: wavmain.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavmain.f
#
wavproc.o: wavproc.f $(INCLUDE)
	xlf -O -qcharlen=1024 -c wavproc.f
