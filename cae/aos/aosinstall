#==============================================================================
#                          AOSINSTALL  for  IBM  
#
# Version 1.0                Pierre Daigle             Date: 30 April 1991  
#==============================================================================
#! /bin/csh -f
#
# Set environment to run on SITE computer
#
source aos_env
#
#
onintr INTERRUPT
clear
echo -n '[2;14H'
echo "[1m*****    AOSINSTALL  Version 1.0 - April 91   *****[0m"
echo " "
echo " "
#
set AOSDISK = "`printenv aos_disk`"
set AOSDIR = "${AOSDISK}/aos/"
set USER_DIR = "`pwd`"
#
#
cd $AOSDIR
echo "Creating  [1m/uty  /sound  /audio [0mdirectories ..."
echo " "
mkdir uty
mkdir sound
mkdir audio
#
cd uty
echo "Creating utilities sub-directories ..."
echo "   mkdir formgen"
mkdir formgen
echo "   mkdir harmony"
mkdir harmony
echo "   mkdir help"
mkdir help
echo "   mkdir library"
mkdir library
echo "   mkdir main"
mkdir main
echo "   mkdir tmsgen"
mkdir tmsgen
echo "   mkdir tsdgen"
mkdir tsdgen
echo "   mkdir wavegen"
mkdir wavegen
echo "   mkdir exec"
mkdir exec
#
echo " "
cd ../sound
echo "Creating sound sub-directories ..."
echo "   mkdir data"
mkdir data
echo "   mkdir inter"
mkdir inter
#
goto END
#
#
INTERRUPT:
echo " "
echo " AOSINSTALL Interrupted ... "
#
#
END:
echo " "
echo "Creation of directory structure completed..."
cd $USER_DIR
exit
