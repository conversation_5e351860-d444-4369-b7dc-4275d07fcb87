 DEF TESTNAME "M7_2E4B  "
@INIT .CTS
JOURNAL INPUT OUTPUT TO M7_2E4B.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : DIRECTIONAL CONTROL - ASSYM REV THRUST
!H  Ref  : H15402E4
!H  Test : 7.2.e.4.b 
!H
!
!
FLAP                    35      
GEAR                     1      
GROSS                31326.87
IXX                 164537.36 
IYY                 264289.84 
IZZ                 399975.66 
IXZ                  25504.55 
XCG                    400.10 
ZCG                    154.87 
D VHHSET              1046.882
ENG1      -83.6
ENG2      -1904.2
!
D HELV 0
D HYWCMD 3.8
D VPSI0                  ((RXMISHDG(3)+HYWCMD)/57.296)
D HWDIROFF               (RXMISHDG(3) - 0.) !Assume flt test rwy approx. 0.
TEST FOR 30 IT
ENG1      -83.6
ENG2      -1904.2
D VTRIM 1
D HFLY T
D HYAW T
D HROLL T
D HAILG -15
D HAILTOL 15
D VAILCON T
D HCNMODE 5
D HRUDTOL 40
D HRUDG 40
D HYWCMD 3.8
D VPSI0                  ((RXMISHDG(3)+HYWCMD)/57.296)
D HWDIROFF               (RXMISHDG(3) - 0.) !Assume flt test rwy approx. 0.
D VAILCON                T
!
D TCFHDG F
TEST FOR 4 DRIVE RAMP VUG 125 $3.9
D HSPDCHK T
D HDECEL T
D HTARGET 64.
TEST FOR 20 UNTIL (VVE <= 64.) 
@STOP.CTS
D TCFIAS T
TEST FOR 10
D TCFIAS F
D HRUDO 0. !(VRUD - 33.78) 
D HRUDTOL 10
D HRUDG 10
!
! INITIAL VALUES FOR PLOTS
!
D VRUD 33.78 
D VPSIDG 3.8
D HHDG 3.8
D VR 0
!
DEF RAMP1 "DRIVE -
                 RAMP HNWIND    -3 $10. -
                 RAMP HWDIR     (HHDG)-
 "
DEF PLOT1 "COLLECT -
 VVE      VPSIDG HHDG VEFN(1)  VEFN(2)  VRUD     VELVR    -
 ABPASV12  ABPASV14 VTHETADG VNWS "
!
D VPSI0                  ((RXMISHDG(3)+HYWCMD)/57.296)
d vr -.015
!
@TSTARTM.CTS
!
TEST/LIM=200 WHEN (HSTART) RAMP1 PLOT1 COND IN TESTNAME FOR  10
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =   30.0 TESTNAME
!
@OFF.CTS
