      PROGRAM VSARAMP
C
C     REVISION HISTORY:
C
C     1. Added subroutine 'TIMEconv'to put a leading zero to
C        the left of the decimal place for all time values
C        between 0.0 and 0.9999999. i.e '.1' now output as 'O.1'
C
C        R. KISIL   OCT. 92
C     
C     2. Added 'REV_CURR' and 'REV_NEXT' to handle files with
C        revision numbers.
C        
C        R. KISIL   OCT. 92
C
      IMPLICIT NONE
C
C  Local declarations.
C
      INTEGER*4
     &     I,M       ! Indices
     &,    IOPT      ! Option number
     &,    EXIT      ! GOTO assignment for exit
     &,    MENU      ! GOTO assignment for menu
     &,    NPTS      ! Number of points in the ramp
     &,    LENIN     ! Input filename length
     &,    LENOUT    ! Output filename length
     &,    STRLEN    ! Function
C
      REAL*4
     &     TIME      ! Time value in VSA ramp file.
     &,    TIMESHF   ! Time shift value.
     &,    OFFSET    ! Ramp offset
     &,    MULT      ! Ramp multiplier
     &,    PCRED     ! Percent reduction in ramp size
     &,    ABSTOL    ! Absolute tolerance
C
      REAL*4
     &     VALUE     ! Ramp value
     &,    TIM(8000) ! Array of time values
     &,    VAL(8000) ! Array of ramp values
C
      LOGICAL*1
     &     LOOP/.TRUE./     ! Loop flag to read ramp values
     &,    FILT             ! Filter the ramp
C
      CHARACTER*79  LINE    ! Input line.
      CHARACTER*40  RAMPIN  ! Input file name.
      CHARACTER*40  RAMPOUT ! Output file name.
      CHARACTER*10  CTIME   ! Time value in char format
      CHARACTER*6   FRMT    ! Format string.
      CHARACTER*3   ANS     ! Y/N prompt
C
C Initializations
C
      ASSIGN 999 TO EXIT
      ASSIGN 100 TO MENU
      IOPT = 100
      WRITE (6,1)                                      ! Header
C
C Prompt for option
C
100   DO WHILE (IOPT.GT.6 .OR. IOPT.LT.0)              ! Invalid inputs
C100   DO WHILE (IOPT.GT.5 .OR. IOPT.LT.0)              ! Invalid inputs
        WRITE (6,2)                                    ! Option list
        READ  (6,22) IOPT                              ! Option selected
        IF (IOPT .EQ. 0) GOTO EXIT                     ! Quit
      ENDDO
C
C  Get input file.
C
      WRITE (6,10)
      READ (5,3,ERR=1001) RAMPIN
      LENIN = STRLEN(RAMPIN)
      IF (LENIN .EQ. 1) THEN
        WRITE(6,'(A)') ' Invalid input filename.'
        GOTO 70 ! End program
      ENDIF
      CALL REV_CURR(RAMPIN)    ! ibm only
      OPEN (UNIT=3,FILE=RAMPIN,STATUS='OLD',ERR=1002)
C
C Create output file.
C
      WRITE (6,13) RAMPIN
      READ (5,3,ERR=1003) RAMPOUT
      LENOUT = STRLEN(RAMPOUT)
      IF (LENOUT .EQ. 1) THEN
c      IF (LENOUT .EQ. 0 .OR. RAMPIN .EQ. RAMPOUT) THEN
        RAMPOUT = RAMPIN
        CALL REV_NEXT(RAMPOUT)
C        WRITE(6,'(A)') ' Invalid output filename.'
C        GOTO 70 ! End program
      ENDIF
      OPEN (UNIT=4,FILE=RAMPOUT,STATUS='NEW',ERR=1004)
C
C Selected option.
C
      IF (IOPT .EQ. 1) THEN
        WRITE (6,11)
        READ (5,*) TIMESHF
      ELSEIF (IOPT .EQ. 2) THEN
        WRITE (6,16)
        READ (5,*) OFFSET
      ELSEIF (IOPT .EQ. 3) THEN
        WRITE (6,17)
        READ (5,*) MULT
      ELSEIF (IOPT .EQ. 4) THEN
        FILT = .TRUE.
      ELSEIF (IOPT .EQ. 5) THEN
        WRITE (6,51)
        READ (5,3) ANS
        I = STRLEN(ANS)
        IF (I .EQ. 0) THEN
          FILT = .FALSE.
        ELSE
          IF (ANS(1:1).EQ.'Y' .OR. ANS(1:1).EQ.'y') FILT = .TRUE.
        ENDIF
        WRITE (6,54)
        READ (5,*) ABSTOL
      ELSEIF (IOPT .EQ. 6) THEN
        WRITE (6,18)
        READ (5,*) MULT
      ENDIF
C
C  Read first line of input file.
C
      READ (3,14) LINE
      IF (LINE(2:2) .LT. 'A') GOTO 200
      WRITE (4,'(A)') LINE
C
C  Read header of input file and write to new file
C  if line is an alpha numeric string
C
      DO WHILE (LINE(2:2) .GE. 'A')
        READ (3,14) LINE
        IF (LINE(2:2) .LT. 'A') GOTO 200
        WRITE (4,'(A)') LINE
      ENDDO
C
C  If line is numeric then perform time shift and write
C  to new time shifted file.
C
200   BACKSPACE 3
      I = 1
C
C Time shift the ramp
C
      DO WHILE (LOOP)
        READ (3,*,END=60) TIME,VALUE
        IF (IOPT .EQ. 1) THEN
          TIME = TIME + TIMESHF
          IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
             CALL TIMEconv(TIME,CTIME)
             WRITE (4,'(1X,A10,1X,F15.6)') CTIME,VALUE
          ELSE
             WRITE (4,15) TIME,VALUE
          ENDIF
C
C Offset the ramp
C
        ELSEIF (IOPT .EQ. 2) THEN
          VALUE = VALUE + OFFSET
          IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
             CALL TIMEconv(TIME,CTIME)
             WRITE (4,'(1X,A10,1X,F15.6)') CTIME,VALUE
          ELSE
             WRITE (4,15) TIME,VALUE
          ENDIF
C
C Multiply the ramp by a constant
C
        ELSEIF (IOPT .EQ. 3) THEN
          VALUE = VALUE * MULT
          IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
             CALL TIMEconv(TIME,CTIME)
             WRITE (4,'(1X,A10,1X,F15.6)') CTIME,VALUE
          ELSE
             WRITE (4,15) TIME,VALUE
          ENDIF
C
C Filter the ramp
C
        ELSEIF (IOPT.EQ.4 .OR. IOPT.EQ.5) THEN
          TIM(I) = TIME
          VAL(I) = VALUE
          I = I + 1
C
C Multiply time by a constant
C
        ELSEIF (IOPT .EQ. 6) THEN
          TIME = TIME * MULT
          IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
             CALL TIMEconv(TIME,CTIME)
             WRITE (4,'(1X,A10,1X,F15.6)') CTIME,VALUE
          ELSE
             WRITE (4,15) TIME,VALUE
          ENDIF
        ENDIF
      ENDDO
C
C Reduce the ramp
C
 60   CONTINUE
      IF (IOPT.EQ.4 .OR. IOPT.EQ.5) THEN
        NPTS = I - 1
        IF (FILT) CALL FILTER(VAL,NPTS)
        IF (IOPT .EQ. 5) THEN
          CALL REDUCE(TIM,VAL,NPTS,PCRED,ABSTOL)
          WRITE (6,52) PCRED
        ENDIF
        DO I = 1,NPTS
C          WRITE(6,53) I
           TIME = TIM(I)
           IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
              CALL TIMEconv(TIME,CTIME)
              WRITE (4,'(1X,A10,1X,F15.6)') CTIME,VAL(I)
           ELSE
              WRITE(4,15) TIM(I),VAL(I)
           ENDIF
        ENDDO
      ENDIF
C
C File successfully written
C
      WRITE (6,21) RAMPOUT
      CLOSE (UNIT=3)
      CLOSE (UNIT=4)
      IOPT = 100
      FILT = .FALSE.
      GOTO MENU
C
C  Format statements.
C
 1    FORMAT (//' VSA Ramp Utility ')
 2    FORMAT (/' Options available: '//
     &         '    0. Exit'/
     &         '    1. Time shift a ramp'/
     &         '    2. Offset a ramp by a constant'/
     &         '    3. Multiply a ramp by a constant'/
     &         '    4. Filter a ramp'/
     &         '    5. Reduce a VSA ramp file'/
     &         '    6. Multiply time by a constant'//
     &         ' Option number ? ',$)
 3    FORMAT (A)
 10   FORMAT (' Enter input ramp file name : ',$)
 13   FORMAT (' Enter output ramp file name or <cr> for same : ',$)
 11   FORMAT (' Time shift in seconds : ',$)
 16   FORMAT (' Ramp offset           : ',$)
 17   FORMAT (' Ramp multiplier       : ',$)
 18   FORMAT (' Time multiplier       : ',$)
 51   FORMAT (' Filter the ramp ? <N> : ',$)
 52   FORMAT (' Percent reduction     : ',F5.1/)
 53   FORMAT ('+Writing record        : ',I4)
 54   FORMAT (' Absolute tolerance    : ',$)
 12   FORMAT (F9.3)
 22   FORMAT (I1)
 14   FORMAT (A)
 15   FORMAT (1X,F10.4,1X,F15.6)
 23   FORMAT (/)
 21   FORMAT (' Ramp output file was successfully written')
 30   FORMAT (' ERROR : Reading file ',A,/)
 40   FORMAT (' ERROR : Cannot open file ',A,/)
C
C  Error in input/output.
C
 1001 WRITE(6,30) RAMPIN
      GOTO 70
 1002 WRITE(6,40) RAMPIN
      GOTO 70
 1003 WRITE(6,30) RAMPOUT
      GOTO 70
 1004 WRITE(6,40) RAMPOUT
C
  70  CLOSE (UNIT = 3)
      CLOSE (UNIT = 4)
C
 999  WRITE (6,23)
      END
C
C------------------------------------------------------------------------------
C
C This subroutine filters a ramp. The filtered ramp overwrites the previous
C ramp.
C
C------------------------------------------------------------------------------
C
      SUBROUTINE FILTER(VAL,NPTS)
      IMPLICIT  NONE
      REAL*4    VAL(*),KFILT/0.9/
      INTEGER*4 NPTS,N
C
      DO N = 2,NPTS
        VAL(N) = KFILT * VAL(N-1) + (1. - KFILT) * VAL(N)
      ENDDO
C
      RETURN
      END
C
C------------------------------------------------------------------------------
C
C This subroutine reduces a ramp. The reduced ramp overwrites the previous
C ramp and the number of points is returned in NPTS.
C
C------------------------------------------------------------------------------
C
      SUBROUTINE REDUCE(TIM,VAL,NPTS,PCRED,ABSTOL)
      IMPLICIT  NONE
      REAL*4    TIM(*),VAL(*),BASE,NEWVAL(8000),NEWTIM(8000),
     &          ABSERROR,ABSTOL
      REAL*4    PCERROR,PCTOL1/0.1/,PCRED,MINVAL,MAXVAL,
     &          PCTOL
      INTEGER*4 NPTS,I,J,FLAG/0/
C
C Determine an absolute tolerance based on the min and max values of
C the ramp.
C
C      CALL EXCURSION(VAL,NPTS,MINVAL,MAXVAL)
C      ABSTOL = (MAXVAL - MINVAL) / 100. * PCTOL1
C
      NEWVAL(1) = VAL(1)
      NEWTIM(1) = TIM(1)
      BASE      = VAL(1)
      J = 2
      DO I = 2,NPTS-1
C        PCERROR = ABS((VAL(I+1) - BASE)/(BASE) * 100.0
C        PCTOL = AMAX1(PCTOL1,ABSTOL/VAL(I)*100.)
C        IF (PCERROR .GT. PCTOL) THEN
         ABSERROR = ABS(VAL(I+1) - BASE)
         IF (ABSERROR .GT. ABSTOL) THEN
           BASE = VAL(I)
           NEWVAL(J) = VAL(I)
           NEWTIM(J) = TIM(I)
           J = J + 1
         ENDIF
      ENDDO
      NEWVAL(J) = VAL(NPTS)
      NEWTIM(J) = TIM(NPTS)
      J = J + 1
      PCRED = FLOAT(NPTS - J)/NPTS * 100.
      NPTS = J - 1
      DO I = 1,NPTS
        TIM(I) = NEWTIM(I)
        VAL(I) = NEWVAL(I)
      ENDDO
C
      RETURN
      END
C
C------------------------------------------------------------------------------
C
C This subroutine determines the min and max values in a ramp.
C
C------------------------------------------------------------------------------
C
      SUBROUTINE EXCURSION(VAL,NPTS,MINVAL,MAXVAL)
      IMPLICIT  NONE
      REAL*4    VAL(*),MINVAL,MAXVAL
      INTEGER*4 NPTS,I
C
      MINVAL = VAL(1)
      MAXVAL = VAL(1)
      DO I = 2,NPTS
        IF (VAL(I) .LT. MINVAL) MINVAL = VAL(I)
        IF (VAL(I) .GT. MAXVAL) MAXVAL = VAL(I)
      ENDDO
C
      RETURN
      END
C
C------------------------------------------------------------------------------
C
C This subroutine determines if time values are between .0 and .9999 and adds
C a zero to the left of the decimal point so that GR or OVP can correctly
C handle the time values in this range.
C
C------------------------------------------------------------------------------
C
C
      SUBROUTINE TIMEconv(TIME,CTIME)
      IMPLICIT  NONE
C
      CHARACTER*10 CTIME
C
      INTEGER      I
C
      REAL         TIME
C
      IF (TIME .GE. 0.0 .AND. TIME .LT. 1.0) THEN
         WRITE(CTIME,'(F10.4)') TIME
         I = INDEX(CTIME,'.')
         CTIME = '0'//CTIME(I:)
      ENDIF
      RETURN
      END
C *
C ************************** STRLEN *******************************
C *
      FUNCTION STRLEN(STRING)
      INTEGER*4 STRLEN,LEN
      CHARACTER*(*) STRING
      STRLEN = LEN(STRING)
      DO WHILE (STRING(STRLEN:STRLEN).EQ.' '.AND.STRLEN.GT.1)
        STRLEN = STRLEN - 1
      ENDDO
      IF (STRLEN .LT. 1) STRLEN = 1
      RETURN
      END      
C *
C ************************* FORMATTER *****************************
C *
      SUBROUTINE FORMATTER(STRING,LENGTH)
C
      IMPLICIT NONE
C
      CHARACTER*(*)  STRING
      INTEGER*4      LENGTH
C
      STRING(1:3) = '(A)' ! Default
      IF (LENGTH.GT.0.AND.LENGTH.LT.10) THEN
         WRITE(STRING,'(A2,I1,A1)') '(A',LENGTH,')'
      ELSE IF (LENGTH.GT.9.AND.LENGTH.LT.100) THEN
            WRITE(STRING,'(A2,I2,A1)') '(A',LENGTH,')'
      ELSE IF (LENGTH.GT.99.AND.LENGTH.LT.1000) THEN
            WRITE(STRING,'(A2,I3,A1)') '(A',LENGTH,')'
      ENDIF
      RETURN
      END
C *
C *************************** REV_CURR ************************************
C *
      SUBROUTINE REV_CURR(FILENAME)
C
      IMPLICIT NONE
C
      CHARACTER*(*) FILENAME
      CHARACTER*100 HOLD_NAME
      CHARACTER*4   FRMT
      INTEGER*4     RSTRLEN,I,J,REVNO,DIGITS
      LOGICAL*4     FILE_FOUND
C
      REVNO = 99
      HOLD_NAME(1:LEN(FILENAME)) = FILENAME
      FILE_FOUND = .FALSE.
      DO WHILE (.NOT.FILE_FOUND.AND.REVNO.GE.1)
         I = RSTRLEN(FILENAME)
         IF (ICHAR(FILENAME(I:I)).GE.48.AND.
     *       ICHAR(FILENAME(I:I)).LE.57) THEN ! Does filename have rev. level?
            J = I
            DO WHILE (FILENAME(J:J).NE.'.'.OR.J.EQ.1) ! Find begining of rev no
               J = J - 1
            ENDDO
            IF (J.EQ.1) THEN                  ! Was begining of rev no found?
               FILENAME(I+1:I+3) = '.99'       ! No, add '.900' to FILENAME.
c               FILENAME(I+1:I+2) = '.900'       ! No, add '.900' to FILENAME.
            ELSE
               READ(FILENAME(J+1:J+4),'(I4)') REVNO  ! Yes, decode rev no
               REVNO = REVNO - 1                     ! Add -1 one to REVNO
               IF (REVNO.LT.10) THEN
                  DIGITS = 1
               ELSE IF (REVNO.GT.9.AND.REVNO.LT.100) THEN
                     DIGITS = 2
               ELSE
                     DIGITS = 3
               ENDIF
               WRITE(FRMT,'(A2,I1,A1)') '(I',DIGITS,')'
               WRITE(FILENAME(J+1:J+4),'(A4)') '    '
               WRITE(FILENAME(J+1:J+DIGITS),FRMT) REVNO
            ENDIF
         ELSE
            FILENAME(I+1:I+3) = '.99'          ! No, add '.900' to FILENAME.
c            FILENAME(I+1:I+2) = '.900'          ! No, add '.900' to FILENAME.
         ENDIF
         INQUIRE(FILE=FILENAME,EXIST=FILE_FOUND) ! Does file exist?
      ENDDO
      IF (REVNO.EQ.0) FILENAME = HOLD_NAME(1:LEN(FILENAME))
      RETURN
      END
C *
C *************************** REV_NEXT ************************************
C *
      SUBROUTINE REV_NEXT(FILENAME)
C
      IMPLICIT NONE
C
      CHARACTER*(*) FILENAME
      CHARACTER*4   FRMT
      INTEGER*4     RSTRLEN,I,J,REVNO,DIGITS
      LOGICAL*4     FILENAME_EXISTS
C
      FILENAME_EXISTS = .TRUE.
      DO WHILE (FILENAME_EXISTS)
         I = RSTRLEN(FILENAME)
         IF (ICHAR(FILENAME(I:I)).GE.48.AND.
     *       ICHAR(FILENAME(I:I)).LE.57) THEN ! Does filename have rev. level?
            J = I
            DO WHILE (FILENAME(J:J).NE.'.'.OR.J.EQ.1) ! Find begining of rev no
               J = J - 1
            ENDDO
            IF (J.EQ.1) THEN                  ! Was begining of rev no found?
               FILENAME(I+1:I+2) = '.1'       ! No, add '.1' to FILENAME.
            ELSE
               READ(FILENAME(J+1:J+4),'(I4)') REVNO  ! Yes, decode rev no
               REVNO = REVNO + 1                     ! add one to REVNO
               IF (REVNO.LT.10) THEN
                  DIGITS = 1
               ELSE IF (REVNO.GT.9.AND.REVNO.LT.100) THEN
                     DIGITS = 2
               ELSE
                     DIGITS = 3
               ENDIF
               WRITE(FRMT,'(A2,I1,A1)') '(I',DIGITS,')'
               WRITE(FILENAME(J+1:J+4),'(A4)') '    '
               WRITE(FILENAME(J+1:J+DIGITS),FRMT) REVNO
            ENDIF
         ELSE
            FILENAME(I+1:I+2) = '.1'          ! No, add '.1' to FILENAME.
         ENDIF
         INQUIRE(FILE=FILENAME,EXIST=FILENAME_EXISTS) ! Does file exist?
      ENDDO
      RETURN
      END
C *
C ********************** RSTRLEN ***********************
C *   FINDS THE LAST NON-BLANK CHARACTER IN A STRING   *
C ******************************************************
C *
      FUNCTION RSTRLEN(STRING)
      INTEGER*4 RSTRLEN,LEN
      CHARACTER*(*) STRING
C
      RSTRLEN = LEN(STRING)
      DO WHILE (STRING(RSTRLEN:RSTRLEN).EQ.' ' .AND. 
     *   RSTRLEN .GT. 0)
         RSTRLEN = RSTRLEN - 1
      ENDDO
      IF (RSTRLEN .LT. 1) RSTRLEN = 1
      RETURN
      END
