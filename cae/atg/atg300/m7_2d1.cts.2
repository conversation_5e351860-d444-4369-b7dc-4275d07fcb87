DEF TESTNAME "M7_2D1  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D1.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : VMCA
!H  Ref  : H151196A 
!H  Test : 7.2.d.1
!H
TRIM
STKFREE
JAX
FLAP                  35      
GEAR                   1      
GROSS                32160.22 
IXX                 177640.97 
IYY                 264456.16 
IZZ                 412889.44 
IXZ                  25537.97 
XCG                    400.30 
ZCG                    155.64 
!DEGC                   11.5653
EAS                     90.6163
ALT                   8174.271
ALTP                  8174.271
D VENGF(1)               T
CLIMB                   -0.252
!ENG1                 -168.2
!ENG2                 4628.7
!
!BANKA                   (1.7133*deg_rad)
D VPSI0                  (-7.3192*deg_rad)
!D HWDIROFF              (VPSIDG+7.3912)
D HCWTRM                 T
UDOT                    -0.3525
VDOT                    -3.6564
WDOT                    -1.1748
!D HVWIND                0.1027
PRATE                    0.4153
QRATE                   -0.2699
QDOT                     0.20
RRATE                    0.9218
RDOT                    -0.60
!BETA                    0.7954
AY                      -2.1102
D VAILCON                T
!ETRIM                 -29.663
!D HCSPLO                1.4218
!D HCSPLI                1.9734
!D HCSPRI               15.2084
!D HCSPRO               15.5478
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE 1
D HCSMODE 1
D HCNMODE 1
D HELVO     (VELVR     +      4.0490)
D HAILO     (VAIL      -      5.6982)
D HRUDO     (VRUD      +     29.2251)
D HECMDO(1) (VEFN(1)   +    168.1636)
D HECMDO(2) (VEFN(2)   -   4628.7012)
!
D HFLY                    T
!
D HPITCH                  T
D HELVG                   2.
D HELVTOL                 2.
!
D HYAW                    T
D HRUDG                   7.5
D HRUDTOL                 3.5
!
D HROLL                   T
D HAILTOL                10.
D HAILG                  -5.
D HAILE                   0.
!
DEF RAMP1 "DRIVE -
		 RAMP HPICMDO   0.  $10 .5 $10 -
                 RAMP HRUDTOL   7.5 $10  0 $10 -
                 RAMP HYWCMD    FILE=A7_2D1DR.VIS -
                 RAMP HPICMD    FILE=A7_2D1GR.VIS - 
                 RAMP HRLCMD    FILE=A7_2D1ER.VIS -
                 RAMP HELVE     FILE=A7_2D1CR.VIS -
                 RAMP HRUDE     FILE=A7_2D1FR.VIS -
                 RAMP HAILE     FILE=A7_2D1AR.VIS -
                 RAMP HECMD(1)  FILE=A7_2D1HR.VIS -
                 RAMP HECMD(2)  FILE=A7_2D1IR.VIS -
 "
DEF PLOT1 "COLLECT VVE VPSIDG VRUD VEFN(1) VEFN(2) VBETA VPHIDG - 
                   VTHETADG VELVR VAIL VALPHA -
 "
!
@TSTARTM.CTS
!
TEST/lim=200 WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  48
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    0.0 TESTNAME
!
D VENGF(1)               F
@OFF.CTS
