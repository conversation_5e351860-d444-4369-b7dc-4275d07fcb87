sp0c0
JOURNAL OUTPUT TO ESSG4_300.HAR
!
SET DEF TEST/WAIT
SET VERIFY ON
!
! D VM 0.0
! D VHH 0.0
@ESSG1.POS
@ESSBL1
!
SAY"*************************************************************"
SAY"*  PW123 ENGINES ON DASH-8                                  *"
SAY"*                                                           *"
SAY"*  TABLE 2.6: Engine Parameters vs CLA - Forward            *"
SAY"*             ( ISA, SLS, No Bleed, No Load, PLA @ 80. )    *"
SAY"*************************************************************"
SAY"    >> Set up A/C as follows :                       "
SAY"                    Altitude          :  SLS         "
SAY"                    Airspeed          :  0.0         "
SAY"                    Atmosphere        :  Standard    "
SAY"                    Power lever (2)   :  66. Degrees "
SAY"                    Cond. lever (2)   :  55. Degrees "
SAY"                    Engines (2)       :  Running     "
SAY"                    Parking brakes    :  Set         "
SAY"                    External Power    :  ON          "
SAY"                    Generators (2)    :  OFF         "
SAY"                    Engine BLEED sws  :  OFF         "
SAY"                    ECU mode          :  ON          "
SAY" "
SAY"    >> Type  CONT  when ready to start test  "
! STOP
!
IDESTOP  = F
IDESMCP  = F 
IDESMCL  = F 
IDESMCR  = F 
IDESNORM = T 
IDESCUM  [2 =  F 
IDESCUO  [2 =  T
TCREGT = T
!
EPLAD [2 =  32.7
ECLAD [2 =  27.0
TEST FOR 40
SAY" "
SAY" "
EPLA  /    66.0   0.5
ECLA  /    32.7   0.5
ENPR  /   905.4   6.0
EQI   /    77.3   1.0
ENH   /    91.7   0.5        
EITT  /   604.0   5.0        
EWF   /   766.0   50.0       
EFNT  /  3931.0   100.0      
SAY" "
ECLAD [2 =  90.0
TEST FOR 40
!                
EPLA  /    66.0  0.5
ECLA  /    84.1   0.5
ENPR  /  1106.0   6.0
EQI   /    59.8   1.0
ENH   /    91.7   0.5         
EITT  /   604.6   5.0         
EWF   /   766.4   50.0         
EFNT  /  5152.1   100.0      
!
ECLAD [2 =  0.0
EPLAD [2 =  0.0
TCREGT = F
TEST FOR 20
!
@ESSBL2
JOURNAL CLOSE
