@egacc.pos
! d vm 0.
! d vhh 0.
!
DEFINE OUTPUT "PRINT"
SET DEF TEST/WAIT
SET VERIFY ON
!
SAY"******************************************************************"
SAY"*  ATM CHECK 2.1.3                                               *"
SAY"*       ATA - 71 POWERPLANTS ATM  : Engine ground accel-decel    *"
SAY"*                                                                *"
SAY"*             USAIR DASH8 300 FLIGHT SIMULATOR                   *"
SAY"*                   ACCEPTANCE TEST MANUAL                       *"
SAY"******************************************************************"    
SAY"                                                                  "
SAY"    >> SET UP A/C AS FOLLOWS :                                    "
SAY"                                                                  " 
SAY"         OUTSIDE AIR TEMPERATURE    ISA                           "
SAY"         ALTITUDE                   SEA LEVEL                     "
SAY"         AIRSPEED                   0.0                           "
SAY"         ENGINES                    ON                            "
SAY"         GEN sws                    ON                            "
SAY"         HYD PWR pumps              ON                            "
SAY"         ECU MODE pb                ON                            "
SAY"         ECU ROTARY sw              NORM                          "
SAY"         BLEED SWS                  ON                            "
SAY"         POWER LEVERS               FLT/IDLE                      "
SAY"         CONDITION LEVERS           MAX                           "
SAY"                                                                  "
SAY" Type   CONT   when ready to continue                             "
!
! STOP
!
MON EFLM,ENH,ENP,EWF,EQI,EITT
!
D ECLAD 100.0
D EPLAD 0.1
TEST FOR 20
!
TEST  -
       DRIVE RAMP EPLAD 0.1, $10; 50., $5; 50., $20; 0.1, $5; 0.1, $20; -
       COLLECT ECLA(1),EPLA(1),ENPR(1),EQI(1),ENH(1),EWFI(1),EITT(1) -
       INITIALLY VM VHH VTEMP UWCAS ECLA EPLA EFECU EFECUM  -
       IDESNORM IDESTOP            -
       FOR 60
!
PUT/BIN/ALL RESULT A_EGACC_300
! @egacc.plo
!
EPLAD = 0.
ECLAD = 0.
