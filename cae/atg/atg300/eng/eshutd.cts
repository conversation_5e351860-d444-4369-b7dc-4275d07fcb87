@eshutd.pos
! d vm 0
! d vhh 0
!
DEFINE OUTPUT "PRINT"
SET DEF TEST/WAIT
SET VERIFY ON
!
SAY"******************************************************************"
SAY"*  ATM CHECK 2.1.3                                               *"
SAY"*       ATA - 71 POWERPLANTS ATM  : Engine ground shutdown       *"
SAY"*                                                                *"
SAY"*             USAIR DASH8 100 FLIGHT SIMULATOR                   *"
SAY"*                   ACCEPTANCE TEST MANUAL                       *"
SAY"******************************************************************"    
SAY"                                                                  "
SAY"    >> SET UP A/C AS FOLLOWS :                                    "
SAY"                                                                  " 
SAY"         OUTSIDE AIR TEMPERATURE    ISA                           "
SAY"         ALTITUDE                   SEA LEVEL                     "
SAY"         AIRSPEED                   0.0                           "
SAY"         ENGINES                    ON                            "
SAY"         BATTERIE SWS               ON                            "
SAY"         GEN sws                    ON                            "
SAY"         EXT PWR                    ON                            "
SAY"         HYD PWR pumps              ON                            "
SAY"         ECU MODE pb                ON                            "
SAY"         ECU ROTARY sw              NORM                          "
SAY"         BLEED SWS                  OFF                           "
SAY"         POWER LEVERS               FLT/IDLE                      "
SAY"         CONDITION LEVERS           MAX                           "
SAY"                                                                  "
SAY" Type   CONT   when ready to continue                             "
SAY"                                                                  "
!
! STOP
!
MON EFLM,ENH,ENP,EWFI,EQI,EITT
!
EPLAD[2=0.1
ECLAD[2=90
TEST FOR 20
!
TEST DRIVE RAMP ECLAD 90.,$10; 0.1,$2; 0.1,$10; -26.4,$2; -
                      -26.4,$20; -44.6,$2; -44.6,$40;     -
           COLLECT ECLA(1),EPLA(1),ENPR(1),EQI(1),ENH(1),EWFI(1),EITT(1) -
           INITIALLY VM VHH VTEMP ECLA EPLA EFECU EFECUM IDESNORM -
           IDESTOP -
           FOR 110.0
!
PUT/BIN/ALL RESULT A_ESHUTD
! @ESHUTD.PLO
!
ECLAD[2 = 0.
EPLAD[2 = 0.
! 
