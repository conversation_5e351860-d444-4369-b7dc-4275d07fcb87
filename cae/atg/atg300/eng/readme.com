The tape contain the following files:
	- USD8ENG.DAT
	- *.CTS
	- *.VIS

Please compile the data file and introduce it into SIMEX.
	1) copy data file in work
	2) Type FGEN USD8ENG
	3) Enter USD8ENG.DAT into SIMEX.

All the CTS files have exactly the same tests for the 
Dash-8/100A and the Dash-8/300A. Each CTS file includes
open files. One is for the reposition ( .POS ) and the
two other one are for removing bleeds ( ESSBL1.CTS ) at the
beginning of the test and one for restoring all bleed
effects as normal at the end of the test ( ESSBL2.CTS ).

The reposition file is often the same name even if it is
different tests. I try to reduce the number of POS files
by using the same file if the reposition was the same. The
position of the ECU switch can be ignored. The CTS files
take care of it.

All the CTS files have the same name. The differentiation
between both A/C is done with the extension "_300" for the 
300 version.

Here is the list of all the CTS files with their particular setup.

Version 100	Version 300	Target & Reposition

ESSG1.CTS	ESSG1_300.CTS	Engine vs PLA; ECU ON; ISA,SLS
ESSG2.CTS	ESSG2_300.CTS	Engine vs PLA; ECU OFF; ISA,SLS
ESSG3.CTS	ESSG3_300.CTS	Engine vs PLA; ECU ON; ISA+20,SLS
ESSG4.CTS	ESSG4_300.CTS	Engine vs CLA; ECU ON; ISA,SLS
ESSG5.CTS	ESSG5_300.CTS	Engine vs PLA in reverse; ECU ON; ISA,SLS

ESSA1.CTS	ESSA1_300.CTS	Engine vs PLA; ECU ON; 10000Ft, 0.25Mn
ESSA2.CTS	ESSA2_300.CTS	Engine vs PLA; ECU ON; 15000Ft, 0.30Mn
ESSA3.CTS	ESSA3_300.CTS	Engine vs PLA; ECU ON; 20000Ft, 0.35Mn


Corresponding to each CTS file and keeping the same name, you have all the
VIS files. Here again the "_300" extension is used. For each of the VIS files,
the parameter order is always the same :

*AM.VIS:	CLA
*BM.VIS:	PLA
*CM.VIS:	ENP
*DM.VIS:	EQI
*EM.VIS:	ENH
*FM.VIS:	EWFI
*GM.VIS:	EITT

If there is any problem, do not hesitate to call me.

Louis Demers
Ext: 3050
