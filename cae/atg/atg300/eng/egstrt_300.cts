@ESHUTD.POS
! D VM 0
! D VHH 0
!
DEFINE OUTPUT "PRINT"
SET DEF TEST/WAIT
SET VERIFY ON
!
SAY"******************************************************************"
SAY"*  ATM CHECK 2.1.3                                               *"
SAY"*       ATA - 71 POWERPLANTS ATM  : Engine ground start          *"
SAY"*                                                                *"
SAY"*             USAIR DASH8 300 FLIGHT SIMULATOR                   *"
SAY"*                   ACCEPTANCE TEST MANUAL                       *"
SAY"******************************************************************"    
SAY"                                                                  "
SAY"    >> SET UP A/C AS FOLLOWS :                                    "
SAY"                                                                  " 
SAY"         OUTSIDE AIR TEMPERATURE    ISA                           "
SAY"         ALTITUDE                   SEA LEVEL                     "
SAY"         AIRSPEED                   0.0                           "
SAY"         ENGINES                    OFF                           "
SAY"         BATTERIE SWS               ON                            "
SAY"         GEN sws                    ON                            "
SAY"         EXT PWR                    ON                            "
SAY"         HYD PWR pumps              ON                            "
SAY"         ECU MODE pb                ON                            "
SAY"         ECU ROTARY sw              TOP                           "
SAY"         BLEED SWS                  ON                            "
SAY"         POWER LEVERS               FLIGHT IDLE                   "
SAY"         CONDITION LEVERS           FUEL OFF                      "
SAY"                                                                  "
SAY" Type   CONT   when ready to continue                             "
!
! STOP
!
MON EFLM ENH ENP EWF EQI EITT
!               
aevdrmn = 26.
aerdk1  = T
eclad = 0.1
test for 10
idesel1 = t
idesi1 = t
!
TEST DRIVE RAMP IDESS1  f $5; T $0.2;   -
           RAMP ECLAD 0.1 $20; 20.0,$0.2; 20.0,$50; 40.0,10; -
           COLLECT ECLA(1),EPLA(1),ENPR(1),EQI(1),ENH(1),EWFI(1),EITT -
           INITIALLY VM VHH VTEMP ECLA EPLA EFECU EFECUM IDESNORM -
           IDESTOP -
           FOR 100.0
!
PUT/BIN/ALL RESULT A_EGSTRT_300
! @EGSTRT_300.PLO
! 
IDESEL1 = F
IDESS1  = F
IDESI1  = F
AEVDRMN = 0.
AERDK1  = F
!
ECLAD[2 = 0.
EPLAD[2 = 0.
!
