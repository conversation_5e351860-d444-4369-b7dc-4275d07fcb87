sp0c0
JOURNAL OUTPUT TO ESSG4.HAR
!
SET DEF TEST/WAIT
SET VERIFY ON
!
! D VM 0.0
! D VHH 0.0
@ESSG1.POS
@ESSBL1
!
SAY"*************************************************************"
SAY"*  PW120 ENGINES ON DASH-8                                  *"
SAY"*                                                           *"
SAY"*  TABLE 2.6: Engine Parameters vs CLA - Forward            *"
SAY"*             ( ISA, SLS, No Bleed, No Load, PLA @ 80. )    *"
SAY"*************************************************************"
SAY"    >> Set up A/C as follows :                       "
SAY"                    Altitude          :  SLS         "
SAY"                    Airspeed          :  0.0         "
SAY"                    Atmosphere        :  Standard    "
SAY"                    Power lever (2)   :  66. Degrees "
SAY"                    Cond. lever (2)   :  55. Degrees "
SAY"                    Engines (2)       :  Running     "
SAY"                    Parking brakes    :  Set         "
SAY"                    External Power    :  ON          "
SAY"                    Generators (2)    :  OFF         "
SAY"                    Engine BLEED sws  :  OFF         "
SAY"                    ECU mode          :  ON          "
SAY" "
SAY"    >> Type  CONT  when ready to start test  "
! STOP
!
IDESTOP  = F
IDESMCP  = F 
IDESMCL  = F 
IDESMCR  = F 
IDESNORM = T 
IDESCUM  [2 =  F 
IDESCUO  [2 =  T
!
EPLAD [2 =  28.2
ECLAD [2 =  12.22
TCREGT = T
TEST FOR 40
SAY" "
SAY" "
EPLA  /   66.   0.5
ECLA  /   55.   0.5
ENPR  /  1003.  6.0
EQI   /  92.4   1.0
ENH   /  93.1   0.5        
EITT  /  657.   5.0        
EWF   /  834.   50.0       
EFNT  /  4810.  100.0      
SAY" "
ECLAD [2 =  90.0
TEST FOR 40
!
EPLA  /   66.   0.5
ECLA  /   97.4  0.5
ENPR  / 1200.   6.0
EQI   /  75.0   1.0
ENH   /  93.1   0.5         
EITT  /  657.   5.0         
EWF   /  834.  50.0         
EFNT  /  6030.  100.0      
!
ECLAD [2 =  0.0
EPLAD [2 =  0.0
TCREGT = F
TEST FOR 20
!
@ESSBL2
JOURNAL CLOSE
            
