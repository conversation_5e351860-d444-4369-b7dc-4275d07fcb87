DEF TESTNAME "M7_2D7A  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D7A.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : DUTCH ROLL                   
!H  Ref  : E036146H 
!H  Test : 7.2.d.7.a
!H
TRIM
STKFREE
JAX
FLAP                   0      
GEAR                   0      
GROSS                32072.42 
IXX                 172630.45 
IYY                 264053.63 
IZZ                 410136.69 
IXZ                  26020.55 
XCG                    401.28 
ZCG                    157.71 
!DEGC                    5.4028
EAS                    173.9719
ALT                  10704.503
!CLIMB                  -7.278
ENG1                  1181.8
ENG2                  1192.8
!
BANKA                    (0.5434*deg_rad)
D VPSI0                  (25.5546*deg_rad)
UDOT                    -0.6948
VDOT                     0.0361
WDOT                    -1.1953
!D HVWIND               -0.6894
PRATE                    0.56
QRATE                   -0.3529
RRATE                    0.0
PDOT                     0.0
RDOT                    -0.01
!BETA                    0.0059
!AY                     -0.6817
D VAILCON                F
D HCSMODE                1
!RUD                     0.0
ALTP                 10704.503
!ETRIM                 -26.456
D HCSPLO                 0.1357
D HCSPLI                 0.0771
D HCSPRI                 0.8914
D HCSPRO                -0.2671
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCAMODE                1
D HCSMODE                1
D HCRMODE                1
D HELVO     (VELVR     -      1.2390)
D HAILO     (VAIL      -      0.9469)
D HRUDO     (VRUD      +      0.7756)
D HCSPLIO   (VCSPLI    -      0.0771)
D HCSPLOO   (VCSPLO    -      0.1357)
D HCSPRIO   (VCSPRI    -      0.8914)
D HCSPROO   (VCSPRO    +      0.2671)
D HECMDO(1) (VEFN(1)   -   1181.7639)
D HECMDO(2) (VEFN(2)   -   1192.7747)
D HPICMDO   (VTHETADG  -      2.6411)
!
D VNC                  2
D VINIT                7
D VBETAOSC             T
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D7AMR.VIS-
                 RAMP HRUD      FILE=A7_2D7ANR.VIS-
                 RAMP HAIL      FILE=A7_2D7AOR.VIS-
                 RAMP HCSPLI    FILE=A7_2D7APR.VIS-
                 RAMP HCSPLO    FILE=A7_2D7AQR.VIS-
                 RAMP HCSPRI    FILE=A7_2D7ARR.VIS-
                 RAMP HCSPRO    FILE=A7_2D7ASR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D7ATR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D7AUR.VIS-
 "
DEF PLOT1 "COLLECT VVE VPHIDG VPSIDG VRUD VAIL HP HR VBETA VHH VEFN(1) -
                   VEFN(2) VELVR "
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  20
!
SAY " "
SHOW INITIAL
!
!R Dutch Roll Cruise Results
!
VT      / 4.43   0.5  10%
VDRATIO / 0.2233 0.02
VTHALF  / 2.13        10%
VPHASE  / 0.73   1.   20%
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    0.0 TESTNAME
!
@OFF.CTS
