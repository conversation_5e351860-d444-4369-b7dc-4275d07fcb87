DEF TESTNAME "M7_2D7B  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D7B.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : DUTCH ROLL                   
!H  Ref  : F132416H 
!H  Test : 7.2.d.7.b
!H
TRIM
STKFREE
JAX
FLAP                  15      
GEAR                   1      
GROSS                30970.94 
IXX                 152213.42 
IYY                 264277.25 
IZZ                 387792.81 
IXZ                  25544.55 
XCG                    399.61 
ZCG                    153.72 
!DEGC                   13.1441
EAS                    110.9839
ALT                   8021.403
ALTP                  8021.403
!CLIMB                  -1.700
ENG1                  1537.6
ENG2                  1578.2
!
!BANKA                   (0.2860*deg_rad)
D VPSI0                  (-1.3400*deg_rad)
UDOT                    -0.2072
VDOT                     1.4661
WDOT                     0.0753
!D HVWIND               -0.0373
PRATE                    0.38     !0.2730
QRATE                   -0.0213
RRATE                   -0.35     !-0.3900
PDOT                    -0.25
RDOT                    -0.50
!BETA                    0.1712
!AY                     -0.4530
D VAILCON                T
RUD                     -2.0
!ETRIM                 -28.796
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCAMODE                1
D HCRMODE                1
D HELVO     (VELVR     +      1.6752)
D HAILO     (VAIL      -      0.2750)
D HRUDO     (VRUD      +      1.9827)
D HECMDO(1) (VEFN(1)   -   1537.6173)
D HECMDO(2) (VEFN(2)   -   1578.2174)
D HPICMDO   (VTHETADG  -      2.4179)
!
D HFLY                 T
D HPITCH               T
D HELVTOL              0.5
D HELVG                0.5
!
D VNC                  3
D VINIT                6.5
D VBETAOSC             T
D VADOSC               T
!
DEF RAMP1 "DRIVE -
                 RAMP HELVE     FILE=A7_2D7BMR.VIS-
                 RAMP HPICMD    FILE=A7_2D7BCR.VIS-
                 RAMP HRUD      FILE=A7_2D7BNR.VIS-
                 RAMP HAIL      FILE=A7_2D7BOR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D7BTR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D7BUR.VIS-
 "
DEF PLOT1 "COLLECT VVE VPHIDG VPSIDG VRUD VAIL HP HR VBETA VHH VEFN(1) -
                   VEFN(2) VELVR "
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  25
!
SAY " "
SHOW INITIAL
!
!R Dutch Roll Approach Results
!
VT      / 5.72   0.5  10%
VDRATIO / 0.198  0.02
VTHALF  / 3.12        10%
VPHASE  / 1.19   1.   20%
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    0.0 TESTNAME
!
@OFF.CTS
