DEF TESTNAME "M7_2D3  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D3.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : ROLL OVERSHOOT               
!H  Ref  : H056446C 
!H  Test : 7.2.d.3
!H
TRIM
STKFREE
JAX
FLAP                  35      
GEAR                   1      
GROSS                31063.39 
IXX                 153751.98 
IYY                 267064.44 
IZZ                 392073.56 
IXZ                  25886.38 
XCG                    398.25 
ZCG                    153.82 
!DEGC                    7.0089
EAS                     86.7484
ALT                   9881.862
ALTP                  9881.862
!CLIMB                  -0.554
ENG1                  2306.1
ENG2                  2269.7
!
!BANKA                   (0.7434*deg_rad)
D VPSI0                  (-94.5585*deg_rad)
UDOT                     0.3874
VDOT                     0.1904
WDOT                    -1.5474
!D HVWIND               -0.1255
PRATE                   -0.2222
QRATE                   -0.4757
RRATE                    0.0042
!BETA                    0.3251 
!AY                     -0.4047
D VAILCON                F
D HCSMODE                1
D VDRSET                 T
D HRUD                  -7.5
!D HBYGAIN               T
!ETRIM                 -30.106
D HCSPLO                 1.4642
D HCSPLI                 3.0843
D HCSPRI                 1.7069
D HCSPRO                 1.0969
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCSMODE                1
D HCAMODE                1
D HCRMODE                1
D HELVO     (VELVR     +      4.9752)
D HAILO     (VAIL      -      3.1396 + 3.3)
D HRUDO     (VRUD      +      7.0303)
D HCSPLIO   (VCSPLI    -      3.0843)
D HCSPLOO   (VCSPLO    -      1.4642)
D HCSPRIO   (VCSPRI    -      1.7069)
D HCSPROO   (VCSPRO    -      1.0969)
D HECMDO(1) (VEFN(1)   -   2306.0981)
D HECMDO(2) (VEFN(2)   -   2269.7073)
D HPICMDO   (VTHETADG  -      2.0812)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D3MR.VIS-
                 RAMP HRUD      FILE=A7_2D3NR.VIS-
                 RAMP HAIL      FILE=A7_2D3OR.VIS-
                 RAMP HCSPLI    FILE=A7_2D3PR.VIS-
                 RAMP HCSPLO    FILE=A7_2D3QR.VIS-
                 RAMP HCSPRI    FILE=A7_2D3RR.VIS-
                 RAMP HCSPRO    FILE=A7_2D3SR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D3TR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D3UR.VIS-
 "
DEF PLOT1 "COLLECT VAIL VPHIDG HP VVE VPSIDG VTHETADG VRUD VELVR -
                   VCSPLI VCSPLO VCSPRI VCSPRO VEFN(1) VEFN(2) VBETA  -
"
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  8
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =   10.0 TESTNAME
!
@OFF.CTS
