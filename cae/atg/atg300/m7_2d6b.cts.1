DEF TESTNAME "M7_2D6B  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D6B.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : RUDDER RESPONSE              
!H  Ref  : A7_2D6B 
!H  Test : 7.2.d.6.b
TRIM
STKFREE
FLAP                  15      
GEAR                   1      
GROSS                32164.05 
IXX                 172056.97 
IYY                 267316.06 
IZZ                 410108.28 
IXZ                  25946.79 
XCG                    398.59 
ZCG                    154.92 
DEGC        2.5107
EAS      143.2663
ALT      11018.
ALTP     11018.
!CLIMB   -2.020
ENG1      1877.3
ENG2      1849.6
!
BANKA   (-1.1587*deg_rad)
D VPSI0 (-0.7981*deg_rad)
UDOT  -0.4164
VDOT   0.0613
WDOT  -0.0429
PRATE   -0.402
QRATE  -0.0436
RRATE  -0.1  !-0.2130
PDOT  .0
RDOT  0.
D VAILCON T
!
TRIM
TT
!
@STOP.CTS
!
SET VERIFY
D HCEMODE 1
D HCAMODE 1
D HCSMODE 1
D HCRMODE 2
D HYAWON    T
!
D HELVO     (VELVR     -      0.7898)
D HAILO     (VAIL      +      0.10155)
D HECMDO(1) (VEFN(1)   -   1877.3372)
D HECMDO(2) (VEFN(2)   -   1849.5903)
D HPICMDO   (VTHETADG  +      0.8663)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D6BMR.VIS-
                 RAMP HAIL      FILE=A7_2D6BOR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D6BTR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D6BUR.VIS-
                 RAMP HPEDAL    FILE=A7_2D6BYR.VIS-
 "
DEF PLOT1 "COLLECT - 
 HR    HP   VRUD  VPHIDG      VPSIDG  VBETA  -
 VAIL       VELVR      HPEDAL    CIRQPOS "
!
@TSTARTM.CTS
!
TEST/LIM=200 WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  10
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
!D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    5.0 TESTNAME
!
@OFF.CTS
