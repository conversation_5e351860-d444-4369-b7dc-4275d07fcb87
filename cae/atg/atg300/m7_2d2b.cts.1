DEF TESTNAME "M7_2D2B  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D2B.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : ROLL RATE RESPONSE           
!H  Ref  : H053326B 
!H  Test : 7.2.d.2.b
!H
TRIM
STKFREE
JAX
FLAP                  15      
GEAR                   1      
GROSS                31636.49 
IXX                 163285.50 
IYY                 267197.19 
IZZ                 401463.59 
IXZ                  25918.36 
XCG                    398.43 
ZCG                    154.40 
!DEGC                    6.1718
EAS                    132.6069
ALT                  10065.252
ALTP                 10065.252
!CLIMB                  -2.286 
ENG1                  1792.8
ENG2                  1792.7
!
!BANKA                   (1.1443*deg_rad)
D VPSI0                  (-1.5*deg_rad)
UDOT                    -0.4988
VDOT                     0.4931
WDOT                    -0.3161
!D HVWIND               -0.0033
PRATE                    0.3871
QRATE                   -0.0346
RRATE                   -0.1047
!BETA                    0.1662
!AY                     -0.8756
D VAILCON                F
D HCSMODE                1
D VDRSET                 T
D HRUD                  -1.25
D HBYGAIN                T
!ETRIM                 -27.319
D HCSPLO                 1.5515
D HCSPLI                 1.9023
D HCSPRI                 2.9762
D HCSPRO                 1.4260
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCAMODE                1
D HCRMODE                1
D HCSMODE                1
D HELVO     (VELVR     -      0.0841)
D HAILO     (VAIL      -      0.7469)
D HRUDO     (VRUD      +      1.0492)
D HCSPLIO   (VCSPLI    -      1.9023)
D HCSPLOO   (VCSPLO    -      1.5515)
D HCSPRIO   (VCSPRI    -      2.9762)
D HCSPROO   (VCSPRO    -      1.4260)
D HECMDO(1) (VEFN(1)   -   1792.8466)
D HECMDO(2) (VEFN(2)   -   1792.6563)
D HPICMDO   (VTHETADG  -      0.2667)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D2BMR.VIS-
                 RAMP HRUD      FILE=A7_2D2BNR.VIS-
                 RAMP HAIL      FILE=A7_2D2BOR.VIS-
                 RAMP HCSPLI    FILE=A7_2D2BPR.VIS-
                 RAMP HCSPLO    FILE=A7_2D2BQR.VIS-
                 RAMP HCSPRI    FILE=A7_2D2BRR.VIS-
                 RAMP HCSPRO    FILE=A7_2D2BSR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D2BTR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D2BUR.VIS-
 "
DEF PLOT1 "COLLECT VAIL VPHIDG HP VVE VPSIDG VTHETADG VRUD VELVR -
                   VCSPLI VCSPLO VCSPRI VCSPRO VEFN(1) VEFN(2) VBETA  -
 "
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR   9
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    5.0 TESTNAME
!
@OFF.CTS
