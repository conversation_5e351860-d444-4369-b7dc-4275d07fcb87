DEF TESTNAME "M7_2D2A  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D2A.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : ROLL RATE RESPONSE           
!H  Ref  : H053116B 
!H  Test : 7.2.d.2.a
!H
TRIM
STKFREE
JAX
FLAP                   0      
GEAR                   0      
GROSS                32459.88 
IXX                 175653.59 
IYY                 267258.38 
IZZ                 416152.47 
IXZ                  26477.38 
XCG                    399.63 
ZCG                    157.61 
!DEGC                    6.4252
EAS                    200.3638
ALT                  10017.308
ALTP                 10017.308
!CLIMB                   0.823
ENG1                  1507.1
ENG2                  1508.2
!
!BANKA                   (1.4597*deg_rad)
D VPSI0                  (3.5*deg_rad)
UDOT                    -0.1642
VDOT                     2.1515
WDOT                     0.0652
!D HVWIND                0.2647
PRATE                   -0.27
QRATE                    0.0590
RRATE                   -0.2245
!BETA                    0.5
!AY                     -0.4916
D VAILCON                F
D HCSMODE                1
RUD                      0
D HBYGAIN                T
!ETRIM                 -25.066
D HCSPLO                 0.4246
D HCSPLI                 0.4489
D HCSPRI                 0.7517
D HCSPRO                 0.2681
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCSMODE                1
D HCAMODE                1
D HCRMODE                1
D HELVO     (VELVR     -      1.4778)
D HAILO     (VAIL      -      0.4302)
D HRUDO     (VRUD      +      0.0289)
D HCSPLIO   (VCSPLI    -      0.4489)
D HCSPLOO   (VCSPLO    -      0.4246)
D HCSPRIO   (VCSPRI    -      0.7517)
D HCSPROO   (VCSPRO    -      0.2681)
D HECMDO(1) (VEFN(1)   -   1507.1080)
D HECMDO(2) (VEFN(2)   -   1508.2474)
D HPICMDO   (VTHETADG  -      0.9347)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D2AMR.VIS-
                 RAMP HRUD      FILE=A7_2D2ANR.VIS-
                 RAMP HAIL      FILE=A7_2D2AOR.VIS-
                 RAMP HCSPLI    FILE=A7_2D2APR.VIS-
                 RAMP HCSPLO    FILE=A7_2D2AQR.VIS-
                 RAMP HCSPRI    FILE=A7_2D2ARR.VIS-
                 RAMP HCSPRO    FILE=A7_2D2ASR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D2ATR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D2AUR.VIS-
 "
DEF PLOT1 "COLLECT VAIL VPHIDG HP VVE VPSIDG VTHETADG VRUD VELVR -
                   VCSPLI VCSPLO VCSPRI VCSPRO VEFN(1) VEFN(2) VBETA  -
 "
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  14
!
SHOW INITIAL
!
JOURNAL CLOSE
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    6.0 TESTNAME
!
@OFF.CTS
