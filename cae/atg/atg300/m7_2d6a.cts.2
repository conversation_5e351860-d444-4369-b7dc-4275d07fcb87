DEF TESTNAME "M7_2D6A  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D6A.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : RUDDER RESPONSE              
!H  Ref  : G046286F 
!H  Test : 7.2.d.6.a
!H
TRIM
STKFREE
JAX
FLAP                  15      
GEAR                   1      
GROSS                32163.43 
IXX                 172047.20 
IYY                 267315.97 
IZZ                 410098.63 
IXZ                  25946.76 
XCG                    398.59 
ZCG                    154.92 
!DEGC                    3.2472
EAS                    138.8037
ALT                  11080.538
ALTP                 11080.538
!CLIMB                  -2.467
ENG1                  1889.3
ENG2                  1879.6
!
!BANKA                   (-0.2320*deg_rad)
D VPSI0                  (-8.11*deg_rad)
UDOT                     0.0689
VDOT                     1.2049
WDOT                     0.1925
!D HVWIND               -0.0861
PRATE                    0.1385
QRATE                   -0.0227
RRATE                   -0.2912
PDOT                    -1.95
RDOT                    -1.0
!BETA                   -0.2423
!AY                     -0.2702
D VAILCON                F
D HCSMODE                1
RUD                     -1.0
!ETRIM                 -26.217
D HCSPLO                 1.6217
D HCSPLI                 4.1448
D HCSPRI                 2.0158
D HCSPRO                 1.1621
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCAMODE                1
D HCSMODE                1
D HCRMODE                1
D HELVO     (VELVR     -      0.4167)
D HAILO     (VAIL      +      0.0816)
D HRUDO     (VRUD      +      0.3470)
D HCSPLIO   (VCSPLI    -      4.1448)
D HCSPLOO   (VCSPLO    -      1.6217)
D HCSPRIO   (VCSPRI    -      2.0158)
D HCSPROO   (VCSPRO    -      1.1621)
D HECMDO(1) (VEFN(1)   -   1889.2692)
D HECMDO(2) (VEFN(2)   -   1879.5782)
D HPICMDO   (VTHETADG  +      0.9951)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D6AMR.VIS-
                 RAMP HRUD      FILE=A7_2D6ANR.VIS-
                 RAMP HAIL      FILE=A7_2D6AOR.VIS-
                 RAMP HCSPLI    FILE=A7_2D6APR.VIS-
                 RAMP HCSPLO    FILE=A7_2D6AQR.VIS-
                 RAMP HCSPRI    FILE=A7_2D6ARR.VIS-
                 RAMP HCSPRO    FILE=A7_2D6ASR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D6ATR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D6AUR.VIS-
 "
DEF PLOT1 "COLLECT HR HP VRUD VPHIDG VPSIDG VBETA VAIL VELVR VVE VEFN(1) -
                   VEFN(2) "
!
@TSTARTM.CTS
!
TEST/lim=120 WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  12
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    3.0 TESTNAME
!
@OFF.CTS
