DEF TESTNAME "M7_2E1  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2E1.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : NORMAL LANDING 
!H  Ref  : F061027A 
!H  Test : 7.2.e.1
!H
TRIM
STKFREE
FLAP                  35      
GEAR                   1      
JAX
GROSS                30879.42 
IXX                 154116.22 
IYY                 263916.06 
IZZ                 389398.53 
IXZ                  25453.98 
XCG                    400.02 
ZCG                    154.08 
!DEGC                   24.4894
EAS                     96.4094
ALT                      (278.+VHG)!(277.951+VHG)
D VHHSET              1159.951
!ENG1                  100.1
!ENG2                  146.6
CLIMB                   16.0    ! 17.578
!BANKA                   (0.9332*deg_rad) 
D HYWCMD                 4.803
D VPSI0                  ((RXMISHDG(3)-2.+HYWCMD)*deg_rad)
D HWDIROFF               (RXMISHDG(3)-2.) !Assume flt test rwy approx. 5.
D HCWTRM                 T
UDOT                    -.6 ! -0.4644
VDOT                     2.3584
WDOT                    -1.7270
PRATE                    0.3988
QRATE                   -0.8003
RRATE                   -0.3558
BETA                    -4.2844
!AY                      0.5072
D VAILCON                T
!ETRIM                 -29.492
!
TRIM
test for 10 it
d hnwind  6
d hwdir (hhdg)
!
TT
!
d hxoff -3000.
d hyoff -30.0
d hldpos t
!
D HFLY                   T
!
D HROLL                  T
D HRLCMD                 (VPHIDG)
D HAILTOL                5. 
D HAILG                 -5.  
!
D HYAW                   T
D HYWCMD                 4.803
D HRUDG                 10.
D HRUDTOL               10.
!
D HPITCH                 T
!D HALT                  T
!D HALG                  2
D HELVG                  2
D HELVTOL                5.
!
D HCEMODE                1
!
DEF RAMP1 "DRIVE -
                 RAMP HNWIND    6 @5 5. @ 20. -
                 RAMP HWDIR     (hhdg) -
 "
DEF PLOT1 "COLLECT -
  VVE	   VTHETADG  VALPHA   VHH   VPHIDG  HHDG  VPSIDG -
  VEFN(1)  VEFN(2)   VELVR    HROC  VEE(2)  -
  "
!
@TSTARTM.CTS
!
TEST/limit = 150 WHEN (HSTART) RAMP1 PLOT1 COND IN TESTNAME FOR  22
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    0.0 TESTNAME
@OFF.CTS
