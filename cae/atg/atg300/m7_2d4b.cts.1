DEF TESTNAME "M7_2D4B  "
@INIT.CTS
JOURNAL INPUT OUTPUT TO M7_2D4B.HAR
SET VERIFY
!H
!H  DHC-8-300A           
!H  ACCEPTANCE TEST GUIDE
!H  LEVEL C              
!H  Case : SPIRAL STABILITY 
!H  Ref  : E033036D 
!H  Test : 7.2.d.4.b
!H
TRIM
STKFREE
JAX
FLAP                   0      
GEAR                   0      
GROSS                32724.48 
IXX                 183466.59 
IYY                 264189.63 
IZZ                 420817.00 
IXZ                  26047.54 
XCG                    401.42 
ZCG                    158.27 
!DEGC                    6.9446
EAS                    115.5841
ALT                   9692.999
!CLIMB                  -0.284
ENG1                  1135.6
ENG2                  1044.8
!
!BANKA                   (-0.3874*deg_rad)
D VPSI0                  (-2.2274*deg_rad)
UDOT                     0.0152
VDOT                     0.1234
WDOT                     0.2607
!D HVWIND               -0.0035
PRATE                    0.1378
QRATE                    0.0309
RRATE                   -0.1217
!BETA                    0.4273
!AY                     -0.4227
D VAILCON                T
ALTP                  9692.999
!ETRIM                 -30.541
!
TRIM
TT
@STOP.CTS
!
SET VERIFY
D HCEMODE                1
D HCAMODE                1
D HCRMODE                1
D HELVO     (VELVR     +      3.8014)
D HAILO     (VAIL      -      0.2267)
D HRUDO     (VRUD      +      0.1318)
D HECMDO(1) (VEFN(1)   -   1135.5991)
D HECMDO(2) (VEFN(2)   -   1044.8079)
!
DEF RAMP1 "DRIVE -
                 RAMP HELV      FILE=A7_2D4BMR.VIS-
                 RAMP HRUD      FILE=A7_2D4BNR.VIS-
                 RAMP HAIL      FILE=A7_2D4BOR.VIS-
                 RAMP HECMD(1)  FILE=A7_2D4BTR.VIS-
                 RAMP HECMD(2)  FILE=A7_2D4BUR.VIS-
		 RAMP HAILO -.27 $8 0.2 $3 -.27 $11 
 "
DEF PLOT1 "COLLECT VAIL VPHIDG HP VVE VPSIDG VTHETADG VRUD VELVR -
                   VCSPLI VCSPLO VCSPRI VCSPRO VEFN(1) VEFN(2) VBETA  -
"
!
@TSTARTM.CTS
!
TEST WHEN (HSTART) PLOT1 COND IN TESTNAME FOR  40
!
SHOW INITIAL
!
JOURNAL CLOSE
!
@TEND.CTS
!
D TCFTOT T
PUT/BIN/ALL/TIME_SHIFT =    0.0 TESTNAME
!
@OFF.CTS
