#define cls()              printf("\033[2J")
#define cursoron()         printf("\033[?25h")
#define cursoroff()        printf("\033[?25l")
/************************ extern functions for rk.c **************************/
extern int readkey();
/********************* extern functions for vt240_new.c **********************/
extern void start_pixel(int x, int y);
extern void draw_line(int x, int y);
extern void graphics_on(void);
extern void graphics_off(void);
extern void change_color(HLS_color *foreground);
extern void set_line_pattern(char *pattern);
extern void set_font_size(char *size);
extern void pixel_printxy(int xpix,int ypix,char *string);
/********************** extern functions from gr.c ***************************/
extern void attributes_on(int item);
extern void get_xy_decimal(void);
extern void update_bar_menu(int letter, char direction);
extern void build_label_menu(char direction);
extern void clear_line(int line_no,int no_of_blanks);
extern void printxy(int col, int row, char *string, short int mode);
extern void refresh_bar_menu(int show_item, int show_all);
extern void erase_graph(void);
extern void erase_bar_menu(void);
extern float y_pixel(float func_val);
extern float y_value(float pix_val);
extern float x_pixel(float func_val);
extern float x_value(float pix_val);
extern float xarrays(int page,int pt);
extern float yarrays(int page,int pt);
extern int draw_labels(void);
extern int curve_found(int curve_ty);
extern int same_color(HLS_color *color1, HLS_color *color2);
extern int refresh_graph(int curve_only);
extern int draw_graph();
extern int toggle_menu_file_labels(int check, char *item_id);
extern char *read_value(Ipt location,char type[7],int length,char *result_ptr,
                      int *control); /* Reads a character or numeric string */
/********************** extern variables from gr.c ***************************/
extern short int NIL;
extern short int UP_LEFT_CORNER_X;
extern short int UP_LEFT_CORNER_Y;
extern short int LO_RIGHT_CORNER_X;
extern short int LO_RIGHT_CORNER_Y;
extern short int curve_count;
extern int zoom_status;
extern int curve_no;
extern int current_menu_item[2];
extern Ipt menu_pos[2][100];
extern char menu_item[2][100][80], menu_label[2][100][80];
extern HLS_color grid_color,master_color,cts_color;
extern HLS_color text_color,curve_color;
extern HLS_color menu_color[2][100];
/******************* extern variables for menuing system *********************/
extern Ipt  menu_pos[2][100];
extern char menu_item[2][100][80], menu_label[2][100][80];
extern HLS_color menu_color[2][100];
extern int  current_menu_item[2], starting_item[2];
extern int  ending_item[2], menu_in_use;
extern char menu_file_record[100][80];
extern short int menu_file_size;
/****************** extern variables from scale_prompt() *********************/
extern float sxmin, sxmax, sxgrid, symin, symax, sygrid;
/***************** extern variables from draw_tol_band() *********************/
extern int tol_band;
/******************* extern variables from bar_menu() ************************/
extern short int COMMAND_MENU;
extern short int LABEL_MENU;
extern int overlay, fast_plot, graph_on_screen, xplot;
/****************** extern variables from draw_graph() ***********************/
extern short int ALL_CURVES;
extern short int CTS_CURVE;
extern short int AUX_CURVE;
extern short int HOLD_CURVE;
extern short int MASTER_CURVE;
extern float y_sections, y_coarse_g;
extern float x_sections, x_coarse_g;
extern float new_xmin, new_xmax, new_ymin, new_ymax;
extern int y_decimal, x_decimal;
extern int array_size, curve_type, aux_curves,
           array_count, master_curves, cts_curves, no_of_ic;
extern short int aux_file_type, aux_file_loaded;
/******************* extern variables from gr_bvs_load **********************/
extern Master  info_master[];
extern Page    *info_page[];
extern int     nb_parameters,
               nb_pages,
               nb_initial;
extern int     init_cond_page_number;
extern short int init_cond_page_ready;
extern short int ic_out;
extern char cts_ylabel[MAX_MASTER][LABEL_LEN+1];
