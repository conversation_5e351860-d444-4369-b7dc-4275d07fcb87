/**********************************************************************/
/*                        INCLUDE FILE OVP_VAR.H                      */
/*                                                                    */
/*  Author : <PERSON>                                             */
/*  Date :   Sept. 1990                                               */
/*                                                                    */
/*  Note's                                                            */
/*         Just before include this file, you must define OVPEXTR like*/
/*         extern or nothing depending of the storage class of this   */
/*         variables. (extern or declaration respectivly)             */
/**********************************************************************/

#ifndef MAX_PAGE
#   define MAX_PAGE 1
#endif

#ifndef MAX_MASTER
#   define MAX_MASTER 1
#endif

#ifndef MAX_INIT_COND
#   define MAX_INIT_COND 1
#endif

#ifndef INIT_COND_LENGTH
#   define INIT_COND_LENGTH 1
#endif

#ifndef OVPEXTR
#   define OVPEXTR extern
#endif

/* Global variables     */
OVPEXTR int    master_curves, cts_curves, no_of_ic;
OVPEXTR char   cts_ylabel[MAX_MASTER][LABEL_LEN+1];
OVPEXTR Page   *info_page [MAX_PAGE];
OVPEXTR Master info_master [MAX_MASTER];
OVPEXTR char   initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];
OVPEXTR int    nb_initial;
OVPEXTR int    nb_parameters,   /* number of elements used in info_master */
               nb_pages;        /* number of elements used in info_page array*/

OVPEXTR int    aux_curves, no_of_aux_ic;
OVPEXTR char   cts_aux_ylabel[MAX_MASTER][LABEL_LEN+1];
OVPEXTR Page   *aux_page [MAX_PAGE];
OVPEXTR Auxiliary aux_master [MAX_MASTER];
OVPEXTR char   aux_initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];
OVPEXTR int    aux_nb_initial;
OVPEXTR int    aux_nb_parameters,/* number of elements used in aux_master */
               aux_nb_pages;     /* number of elements used in aux_page array*/

OVPEXTR FILE   *visain,         /* keyboard             */
               *visaout;        /* graphic output       */

OVPEXTR unsigned int keyboard_id; /* identification for the virtual keyboard */

OVPEXTR int    init_cond_page_number;/*page number of initial conditions page*/

OVPEXTR short int init_cond_page_ready;
                                /* flag ready for initial conditions page */
