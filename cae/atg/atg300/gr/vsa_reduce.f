C
C------------------------------------------------------------------------------
C
C This subroutine reduces a ramp. The reduced ramp overwrites the previous
C ramp and the number of points is returned in NPTS.
C
C------------------------------------------------------------------------------
C
      SUBROUTINE VSA_REDUCE(TIM,VAL,NPTS,PCRED,RES,TOLABS,TOLPC)
      IMPLICIT  NONE
      REAL*4    TIM(*)        ! Array of X Values
     &,         VAL(*)        ! Array of Y Values
     &,         RES           ! Resolution (size of Y error to be ignored)
     &,         TOLABS        ! Absolute tolerance field (for master files)
     &,         TOLPC         ! Percent tolerance field (for master files)
     &,         PCRED         ! Percent reduction of array
     &,         ERROR         ! Y error if this point is ignored
     &,         SLOPE         ! Slope of a new line from last saved point
C                             ! to point now in question.
     &,         SLOPE_DENOM   ! Denominator in slope calculation
C
      INTEGER*4 NPTS          ! Number of points in input array
     &,         I,J,K,L       ! Do loop indexes
C
      LOGICAL   TolFlag       ! Flag indicates both a percent and absolute
C                             ! tolerance field are set in a master file
     &,         PerCentDomain ! Percent tolerance is active (now largest)
     &,         PrevDomain    ! Previous value of PerCentDomain
     &,         Invert        ! Invert X and Y axes to in error calculation
C
C Save first point in reduced array.
C
      J = 2
      I = 2
C
C Check to see if both percent and absolute tolerance fields are present.
C If they are the reduction routine must be careful not to reduce away
C the points where one tolerance mode takes over from the other. 
C Otherwise the tolerance bands cannot be correctly graphed.
C
      TOLFLAG = .FALSE.
      IF ((TOLABS*TOLPC) .GT. 0.)TOLFLAG = .TRUE.
C
C Loop throught the array saving points where the error incurred by 
C removing points is less than the resolution desired. (Or the 
C tolerance domain switches)
C
      DO WHILE( I.LT.NPTS)
         DO K = I+1,NPTS
           IF (TOLFLAG) PerCentDomain=(ABS(VAL(K)*TOLPC).GT.TOLABS)
CRW           IF (PerCentDomain .NE. PrevDomain) GOTO 1221
           IF ((.NOT.PerCentDomain .AND. PrevDomain) .OR.
     &         (PerCentDomain .AND. .NOT.PrevDomain)) GOTO 1221
C
C If a vertical line is encountered switch the axis to before calculating
C slope used for resolution check.
C
           SLOPE_DENOM = TIM(K)-TIM(J-1)
           IF (SLOPE_DENOM .EQ. 0. ) THEN
             SLOPE_DENOM= .00000000001  ! AVOID DEVIDE BY ZERO  
             SLOPE = (VAL(K)-VAL(J-1))/SLOPE_DENOM
           ELSE
             SLOPE = (VAL(K)-VAL(J-1))/SLOPE_DENOM
           ENDIF
C
C This loop checks all the points between the last saved point
C and the present point to see that no error is larger than the
C desired resolution.
C
           DO L = I,K-1
             ERROR=ABS(VAL(L)-(VAL(J-1)+(TIM(L)-TIM(J-1))*SLOPE))
             IF (ERROR .GT. RES)GOTO 1221
           ENDDO
         ENDDO
C
C Store point found needed to preserve desired resolution.
C
1221     I = K - 1
         VAL(J) = VAL(I)
         TIM(J) = TIM(I)
         J = J + 1
         I = I + 1
         PrevDomain = PerCentDomain
      ENDDO
      VAL(J) = VAL(NPTS)
      TIM(J) = TIM(NPTS)
      J = J + 1
      PCRED = FLOAT(NPTS - J)/NPTS * 100.
      NPTS = J - 1
C
      RETURN
      END
C
