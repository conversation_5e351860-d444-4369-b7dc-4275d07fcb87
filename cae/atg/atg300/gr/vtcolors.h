/***** Global variables for setvt() function. */
#define   NUMBER_OF_COLORS    49
/* Note: 'color_no' should be initialized with the same colors as GR. */
short int menu_no=0;
char color_name[NUMBER_OF_COLORS][50] = {
                                  "AQUAMARINE               ",  /*  0 */
                                  "AQUAMARINE_MEDIUM        ",  /*  1 */
                                  "BLACK                    ",  /*  2 */
                                  "BLUE                     ",  /*  3 */
                                  "BLUE_CADET               ",  /*  4 */
                                  "BLUE_CORNFLOWER          ",  /*  5 */
                                  "BLUE_DARK_SLATE          ",  /*  6 */
                                  "BLUE_LIGHT               ",  /*  7 */
                                  "BLUE_LIGHT_STEEL         ",  /*  8 */
                                  "BLUE_MEDIUM_SLATE        ",  /*  9 */
                                  "BLUE_MIDNIGHT            ",  /* 10 */
                                  "BLUE_NAVY                ",  /* 11 */
                                  "BLUE_SKY                 ",  /* 12 */
                                  "BLUE_SLATE               ",  /* 13 */
                                  "BLUE_STEEL               ",  /* 14 */
                                  "CORAL                    ",  /* 15 */
                                  "<PERSON>Y<PERSON>                     ",  /* 16 */
                                  "FIREBRICK                ",  /* 17 */
                                  "GOLD                     ",  /* 18 */
                                  "GRE<PERSON>                    ",  /* 19 */
                                  "G<PERSON>EN_DARK               ",  /* 20 */
                                  "GREEN_FOREST             ",  /* 21 */
                                  "GRAY_LIGHT               ",  /* 22 */
                                  "GRAY_DARK_SLATE          ",  /* 23 */
                                  "KHAKI                    ",  /* 24 */
                                  "MAGENTA                  ",  /* 25 */
                                  "MAROON                   ",  /* 26 */
                                  "ORANGE                   ",  /* 27 */
                                  "ORCHID                   ",  /* 28 */
                                  "ORCHID_DARK              ",  /* 29 */
                                  "ORCHID_MEDIUM            ",  /* 30 */
                                  "PINK                     ",  /* 31 */
                                  "PLUM                     ",  /* 32 */
                                  "RED                      ",  /* 33 */
                                  "RED_INDIAN               ",  /* 34 */
                                  "RED_MEDIUM_VIOLET        ",  /* 35 */
                                  "RED_ORANGE               ",  /* 36 */
                                  "RED_VIOLET               ",  /* 37 */
                                  "SALMON                   ",  /* 38 */
                                  "SIENNA                   ",  /* 39 */
                                  "TAN                      ",  /* 40 */
                                  "THISTLE                  ",  /* 41 */
                                  "TURQUOISE                ",  /* 42 */
                                  "TURQUOISE_DARK           ",  /* 43 */
                                  "WHEAT                    ",  /* 44 */
                                  "WHITE                    ",  /* 45 */
                                  "YELLOW                   ",  /* 46 */
                                  "YELLOW_BRIGHT            ",  /* 47 */
                                  "YELLOW_GREEN             "}; /* 48 */

HLS_color color_code[NUMBER_OF_COLORS] = {
                                   {255, 65, 60},
                                   {280, 50, 60},
                                   {  0,  0,  0},
                                   {  0, 50, 60},
                                   {300, 50, 25},
                                   {  0, 35, 25},
                                   {  0, 50,100},
                                   {300, 80, 25},
                                   {  0, 65, 25},
                                   { 30, 50,100},
                                   {  0, 25, 25},
                                   {  0, 35, 60},
                                   {310, 80,100},
                                   {330, 50,100},
                                   {320, 35, 60},
                                   {150, 50,100},
                                   {300, 50, 60},
                                   {120, 35, 60},
                                   {150, 50, 60},
                                   {240, 50, 60},
                                   {240, 25, 25},
                                   {240, 35, 60},
                                   {  0, 70,  0},
                                   {300, 25, 25},
                                   {180, 50, 25},
                                   { 60, 50, 60},
                                   { 80, 35, 60},
                                   {120, 50, 60},
                                   { 60, 65, 60},
                                   { 40, 50, 60},
                                   { 20, 65, 60},
                                   {120, 65, 25},
                                   { 60, 80, 60},
                                   {120, 46, 72},
                                   {120, 25, 25},
                                   {100, 65, 60},
                                   { 90, 50,100},
                                   { 80, 50, 60},
                                   {120, 35, 25},
                                   {160, 35, 60},
                                   {140, 65, 60},
                                   { 60, 80, 25},
                                   {300, 80, 60},
                                   {340, 65, 60},
                                   {180, 80, 25},
                                   {  0, 99,  0},
                                   {180, 50, 60},
                                   {180, 60,100},
                                   {220, 65, 60}};
char pattern_name[10][30] = {
                                  "INVISIBLE                ",  /*  0 */
                                  "SOLID LINE PATTERN       ",  /*  1 */
                                  "DASH PATTERN             ",  /*  2 */
                                  "DASH DOT PATTERN         ",  /*  3 */
                                  "DOT PATTERN              ",  /*  4 */
                                  "DASH DOT-DOT PATTERN     ",  /*  5 */
                                  "SPARSE DOT PATTERN       ",  /*  6 */
                                  "ASYM SPARSE DOT PATTERN  ",  /*  7 */
                                  "SPARSE DASH-DOT PATTERN  ",  /*  8 */
                                  "SPARSE DOT-DASH PATTERN  "}; /*  9 */
char pattern_code[10][3] = {
                                   {"P0"},
                                   {"P1"},
                                   {"P2"},
                                   {"P3"},
                                   {"P4"},
                                   {"P5"},
                                   {"P6"},
                                   {"P7"},
                                   {"P8"},
                                   {"P9"}};
