       SUBROUTINE AUTOSCALE(Min,Max,GridC,MinGrid,NumGrid,Mode)
C
       IMPLICIT NONE
C
C      Inputs
C
       REAL Min           ! Minimum function value 
       REAL Max           ! Maximum function value 
       REAL MinGrid       ! Sets minimum coarse grid size for
C                         ! this plot (not active if = 0.0)       
       Integer NumGrid    ! Number of coarse grids in plot
       Integer Mode       ! Mode of autoscale program
C                         ! 0 = Keep number of grids constant
C                         ! 1 = Reduce number of grids area of data
C
C      Outputs
C
C      REAL Min           ! Minimum function value for graphing
C      REAL Max           ! Maximum function value for graphing
       REAL GridC         ! Size of coarse grid
C      Integer NumGrid    ! Number of coarse grids in plot
C
C
C      Internal variables
C
       INTEGER Itemp      ! Scratch Pad     
       INTEGER Itemp2     ! Scratch Pad     
       INTEGER J          ! Do loop index
       INTEGER SCALE(6)   ! Permitted scale values
       REAL Factor        ! Scaling factor
       REAL Temp          ! Temporary coarse grid value
       REAL Tmax          ! Temporary maximum value of function
       REAL MGrid         ! Local value of minimum grid size
C
C      These are the scales permitted (raised to any power of 10)
C
       DATA SCALE /1,2,4,5,10,20/
C
C      Find nearest integral scale to satisfy page requirements,
C      and have graph min be an integer multiple of the coarse grid.
C
       Temp = (Max-Min)/NumGrid
       Tmax = Max
C
C      Check for max = min
C
       MGrid = MinGrid
       if (Temp.eq.0.) then
        if (MinGrid .eq. 0.) then
          Temp = 1.
          MGrid = 1.
        else
          Temp = MinGrid
        endif
       endif
C
C      Multipy temporary grid by a power of ten so that it becomes
C      a number between 1. and 10.
C
       Factor = 1.
       Do while (temp .LE. 1.0)
            temp = temp * 10.
            factor = factor * 10.
       enddo
       Do while (temp .GT. 10.0)
            temp = temp * 0.1
            factor = factor * 0.1
       enddo
       j=1
C
C      Choose the scale size (from the array of acceptable choices)
C      that is equal to or larger than the minimum required.
C
       do while(temp.gt.scale(j))
         itemp = scale(j+1)
         j=j+1
       enddo
C
C      Find the number of grids required to display min and max
C      on the same plot. If this is more than the number of grids
C      requested, choose the next largest grid size.
C
       Gridc = AMAX1(MGrid,(itemp/factor))      
       itemp = Min/GridC - .999*(.5-SIGN(.5,Min))
       itemp2 = Max/GridC + .999*(.5+SIGN(.5,Max))
       IF ((itemp2-itemp).gt.NumGrid)then
            GridC = scale(j+1)/factor
            itemp = Min/GridC - .999*(.5-SIGN(.5,Min))
       ENDIF
C
C      Calculate the graphic min and max.
C
       If (mode .eq. 0) Then
         Min = Itemp * GridC
         Max = Min + NumGrid * GridC
       Elseif (mode .eq. 1) Then
         Min = Itemp * GridC
         NumGrid = (Tmax-Min)/Gridc + .999
         Max = Min + NumGrid * GridC
       Endif
C
C      Convert min and max back to nearest integer if a roundoff
C      error has occured
C
       Temp = ABS(GRIDC) 
       If (Temp .GT.1.) Then
         if (min.ge.0.)iTemp = Min+.5
         if (min.lt.0.)iTemp = Min-.5
         Min = iTemp
         if (max.ge.0.)iTemp = max+.5
         if (max.lt.0.)iTemp = max-.5
         Max = iTemp
         if (GridC.ge.0.)iTemp = GridC+.5
         if (GridC.lt.0.)iTemp = GridC-.5
         GridC = iTemp
       Endif
C
       Return
       End
C
