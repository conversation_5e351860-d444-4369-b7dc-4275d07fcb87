/************** Global variables used in 'ovp_tab_main.c':
These variable are needed because 'gr' uses the OVP routines that read '.bvs'
files.
*/
#include "ovp.h"
/*#include "ovp_hcp.h"*/
#include "graph.h"
#include "ovp_stru.h"
/*#include "screen.h"*/

#define TRUE   1
#define FALSE  0

char    cts_ylabel[MAX_MASTER][LABEL_LEN + 1];
Master  info_master [MAX_MASTER];
Page    *info_page  [MAX_PAGE];
int     nb_parameters,
        nb_pages,
        nb_initial;
int     init_cond_page_number;
short   int init_cond_page_ready;
char    initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];

char    cts_aux_ylabel[MAX_MASTER][LABEL_LEN + 1];
Auxiliary aux_master [MAX_MASTER];
Page    *aux_page  [MAX_PAGE];
int     aux_nb_parameters,
        aux_nb_pages,
        aux_nb_initial;
char    aux_initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];
