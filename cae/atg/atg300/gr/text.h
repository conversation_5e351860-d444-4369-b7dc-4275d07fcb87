/***********************************************************************/
/*    Author: <PERSON>                                            */
/*    Date:   86/04/29                                                 */
/*    Decription: text240.h is the library of text attributes          */
/*    for a DEC VT-200 series of display terminal.                     */
/*                                                                     */
/* The upper left character would be row 1, column 1 or (1,1), all     */
/* addresses would be passed as IPT structure as define in the Visa.h  */
/* include file.                                                       */
/*                                                                     */
/* Revision History:                                                   */
/*   <PERSON>  August 14, 1989                                     */
/*     Added definition of NUL to comply with GNU C                    */
/***********************************************************************/



#define CLL                 "\033[2K"                         
#define CLS                 "\033[2J" 
#define WIDTH80             "\033?3l"  
#define WIDTH132            "\033?3h" 
#define APPLICATION         "\033="
#define NUMERIC             "\033>"

#define CURSOR_ON           "\033[?25h"  
#define CURSOR_OFF          "\033[?25l"
#define LOCATE              "\033[%d;%dH%s%s%s"
#define REVERSE             "\033[7m"
#define NOT_REVERSE         "\033[27m"
#define HIGHLITE            "\033[1m"
#define NOT_HIGHLITE        "\033[22m"    

#define BLINKING            "\033[5m"
#define NOT_BLINKING        "\033[25m"
#define BOLD_UNDERLINE      "\033[1;4m"
#define NOT_BOLD_UNDERLINE  "\033[0m"
#define BOLD_INV_UNDERLINE  "\033[1;4;7m"
#define NOT_BOLD_INV_UNDERLINE "\033[0m"
#define UNDERLINE           "\033[4m" 
#define NOT_UNDERLINE       "\033[24m" 
#define INBOLD              "\033[7;1m"
#define NOT_INBOLD          "\033[27;22m"
#define NUL                 ""
                        
/* VT200 7 bit control set up string */

#define  SETUP_MODE    "\033[62;1\"p"                            

#define TEXTOUT stdout     /* Text output port */


/***********************************************************************/
/* This Macro clear the screen                                         */
/***********************************************************************/

#define cls()              fprintf(TEXTOUT,CLS)         


/***********************************************************************/
/* This Macro changes the terminal setup to VT200 mode                 */
/***********************************************************************/

#define setup()            fprintf(TEXTOUT,SETUP_MODE)  

               
/***********************************************************************/
/* This Macro set the screen width to 80 character                     */
/***********************************************************************/

#define width80()          fprintf(TEXTOUT,WIDTH80)
                                         

/***********************************************************************/
/* This Macro set the screen width to 132 character                     */
/***********************************************************************/

#define width132()         fprintf(TEXTOUT,WIDTH132)


/***********************************************************************/
/* This Macro changes the terminal setup to application keypad         */
/***********************************************************************/

#define application()      fprintf(TEXTOUT,APPLICATION)
                                                                         

/***********************************************************************/
/* This Macro changes the terminal setup to numeric keypad             */
/***********************************************************************/

#define numeric()          fprintf(TEXTOUT,NUMERIC)               


/***********************************************************************/
/* This Macro puts a visible cursor on the screen                      */
/***********************************************************************/
                                          
#define cursoron()         fprintf(TEXTOUT,CURSOR_ON)


/***********************************************************************/
/* This Macro removes the visible cursor from the screen               */
/***********************************************************************/

#define cursoroff()        fprintf(TEXTOUT,CURSOR_OFF)


/***********************************************************************/
/* This macro is use to define the following Macro                     */
/***********************************************************************/

#define PRINT(XY,DO,STR,UNDO) fprintf(TEXTOUT,LOCATE,XY.x,XY.y,DO,STR,UNDO)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure.                                                      */
/***********************************************************************/

#define locate(XY)         PRINT(XY,&NUL,&NUL,&NUL)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string pointed to by the PTR parameter. */
/***********************************************************************/

#define stringat(XY,STR)    PRINT(XY,&NUL,STR,&NUL)
                                                                       

/***********************************************************************/
/* This Macro  clears the line                                         */
/***********************************************************************/

#define cll(XY)             PRINT(XY,&NUL,CLL,&NUL)

       
/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in reverse video pointed to by   */
/* the PTR parameter.                                                  */
/***********************************************************************/

#define reverse(XY,STR)    PRINT(XY,REVERSE,STR,NOT_REVERSE)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in highlited letters pointed to  */
/* by the PTR parameter.                                               */
/***********************************************************************/

#define highlite(XY,STR)   PRINT(XY,HIGHLITE,STR,NOT_HIGHLITE)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in blinking letters pointed to   */
/* by the PTR parameter.                                               */
/***********************************************************************/

#define blinking(XY,STR)   PRINT(XY,BLINKING,STR,NOT_BLINKING)        

/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in underlined letters pointed to */
/* by the PTR parameter.                                               */
/***********************************************************************/

#define underline(XY,STR)  PRINT(XY,UNDERLINE,STR,NOT_UNDERLINE)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in reverse bold letters pointed  */
/* to by the PTR parameter.                                            */
/***********************************************************************/


#define inbold(XY,STR)     PRINT(XY,INBOLD,STR,NOT_INBOLD)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in reverse bold letters pointed  */
/* to by the PTR parameter.                                            */
/***********************************************************************/


#define bold_underline(XY,STR)  PRINT(XY,BOLD_UNDERLINE,STR,NOT_BOLD_UNDERLINE)


/***********************************************************************/
/* This Macro changes the cursor position to the coordinate specify in */
/* IPT structure and print the string in reverse bold letters pointed  */
/* to by the PTR parameter.                                            */
/***********************************************************************/


#define bold_inv_underline(XY,STR) PRINT(XY,BOLD_INV_UNDERLINE, \
                                        STR,NOT_BOLD_INV_UNDERLINE)
