/************************************************************************/
/*									*/
/*			INCLUDE FILE SCALE				*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/*   Structure use to store the scaling factor                  	*/
/************************************************************************/

struct scaling_struct
   	{ 
/* x */
   	double a;
   	double b;
   	double c;
/* y */
   	double d;
   	double e;
   	double f;
 	};

typedef struct scaling_struct Scale;

                            /* Functions declaration */
Scale scalefind();
Scale scaling();
Pt scale();
Pt scalerel();
Pt orient();

/* Scale X or Y macro                              */

#define SCALX(FAC,X,Y)          (X*FAC.a+Y*FAC.b+FAC.c)
#define SCALY(FAC,X,Y)          (X*FAC.d+Y*FAC.e+FAC.f)

#define PSCALX(FAC,X,Y)          (X*FAC->a+Y*FAC->b+FAC->c)
#define PSCALY(FAC,X,Y)          (X*FAC->d+Y*FAC->e+FAC->f)


/* Finds the angle of rotation                                     */

#define ROTATION(AA)   atan(AA.y/AA.x)

