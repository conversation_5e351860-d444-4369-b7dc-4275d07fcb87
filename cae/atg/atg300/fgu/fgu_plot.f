      Subroutine PLOTTER (X,Y,N,NPLOTS,PLOTS,XI,NYI,XMIN,XMAX,YMIN,YMAX,
     *                    TITLE,REF,GRID,INTERP,XAXIS,YAXIS,XINCH,YINCH,
     *                    CIRCLE)
C
      INCLUDE 'inout.inc'
!
!     Written by <PERSON><PERSON>
!     Revised by <PERSON><PERSON>       January 16  1981
!     Revised by <PERSON><PERSON>       July 1 1981
!     Revised by <PERSON><PERSON>         November 1986 : Modified to be compatible
!                                               with the CAEPLOT utility
!
! ==== description of passed arguments ===================================
!
!         X       -   array of x axis plot values
!         Y       -   array of y axis plot values
!         N       -   number of x axis plot values
!         NPLOTS  -   number of plots to be overlaid
!         PLOTS   -   array of plot numbers to be overlaid
!         XI      -   size of 1 x axis interval
!         NYI     -   number of y axis intervals
!         XMIN    -   minimum x value
!         XMAX    -   maximum x value
!         YMIN    -   minimum y value
!         YMAX    -   maximum y value
!         TITLE   -   plot title string
!         REF     -   plot reference string
!         GRID    -   grid flag
!         INTERP  -   interpolation flag
!         XAXIS   -   array of x variable descriptor strings
!         YAXIS   -   array of y variable descriptor strings
!         XINCH   -   size in inches of Maxplot x axis intervals
!         YINCH   -   size in inches of Maxplot y axis intervals
!         CIRCLE  -   circle flag (plotted points are circled)
!
! ========================================================================
!
! ---- declarations ------------------------------------------------------
!         
      Integer    Maxplot
      Parameter  (Maxplot = 50)

      Character  REF*80, TITLE*80, INITCOND(20)*42
      Character  XAXIS(Maxplot+1)*10, YAXIS(Maxplot+1)*10
      Real*4     X(Maxplot,1500), Y(Maxplot,1500)
      Real*4     XMIN(Maxplot),XMAX(Maxplot),YMIN(Maxplot),YMAX(Maxplot)
      Integer*4  PLOTS(Maxplot),N(Maxplot)
      Logical*4  GRID, INTERP, CIRCLE
!
      INTEGER    LN
      Character  YFTEST1*25, YFTEST2*25, FMT*25, VARFMT*25
      Integer*4  ITAB(50), IYLOC(50), TABLE(6), UNIT
      Integer*2  SP0, SP1, SP2
      Real*4     INCH
      Logical*4  XFLAG, YFLAG, MINUSX, MINUSY
      Byte       M(1600,150), DUMMY, FONT(8), PLOTMODE, SP3
!
      Equivalence (SP2,SP3)
!
      Common  /PLOTBUF/  M  ! share space for buffer with GRAPH subroutine
!
! ---- initializations ---------------------------------------------------
!
      Data  TABLE    / '41'X, '42'X, '44'X, '48'X, '50'X, 'A0'X /
      Data  PLOTMODE / '5'X /, DUMMY / '40'X /
!
! ---- reset these variables at the beginning of each plot ---------------
!
      XFLAG = .false.
      YFLAG = .false.
      SWITCHX = 1.0
      SWITCHY = 1.0
      NYD = 0
      YYD = 0.
      IY  = 0
      NXD = 0
      XXD = 0.
      IX  = 1
!
! ---- transfer axis scaling ----------------------------------------------
!
      XL = XMIN(PLOTS(1))
      XR = XMAX(PLOTS(1))
      YT = YMAX(PLOTS(1))
      YB = YMIN(PLOTS(1))
!
! ---- increase size of plot to include all data points ------------------
!
      Do 10 I = 1,N(1)
        If (XFLAG) then
          XL = AMAX1(XL,X(PLOTS(1),I))
          XR = AMIN1(XR,X(PLOTS(1),I))
        Else
          XL = AMIN1(XL,X(PLOTS(1),I))
          XR = AMAX1(XR,X(PLOTS(1),I))
        End if
        Do 9 J = 1,NPLOTS
          If (YFLAG) then
            YB = AMAX1(YB,Y(PLOTS(J),I))
            YT = AMIN1(YT,Y(PLOTS(J),I))
          Else
            YB = AMIN1(YB,Y(PLOTS(J),I))
            YT = AMAX1(YT,Y(PLOTS(J),I))
          End IF
    9   Continue
   10 Continue
!
! ---- determine if final sign of axes is positive of negative -----------
! ---- after sign flip ---------------------------------------------------
!
      MINUSX = (XL.lt.0.or.XR.lt.0)
      MINUSY = (YB.lt.0.or.YT.lt.0)
!
! ---- set switch flags if maximun value is less than the minimum value --
! 
      If (XR.lt.XL) then
        XFLAG = .true.
        SWITCHX = -1.0
        XR = -XR
        XL = -XL
      End if
!
      If (YT.lt.YB) then
        YFLAG = .true.
        SWITCHY = -1.0
        YT = -YT
        YB = -YB
      End if
!
! ---- determine number of y interval required ---------------------------
!
      DYD = ( YMAX(PLOTS(1)) - YMIN(PLOTS(1)) ) / NYI
      If (DYD.eq.0.0) then
        IYB = YB
        If (YB.lt.0.0) IYB = IYB - 1
        IYT = YT + 0.999
        IYT = IYT + NYI - mod(IYT-IYB,NYI)
        YT = IYT
        YB = IYB
      Else
        If (YT-YB.gt.YMAX(PLOTS(1))-YMIN(PLOTS(1))) then
          If (YB.lt.YMIN(PLOTS(1))) then
            IYB = (YMIN(PLOTS(1))-YB)/DYD + 0.999
            YB  = YMIN(PLOTS(1)) - IYB*DYD
          End if
          NYI = (YT-YB)/DYD + 0.999
          YT  = YB+NYI*DYD
        End if
      End if
      NY  = (NYI*YINCH/10.)*72.
      DYD = NY/(1.0*NYI)
      DYB = (YT-YB)/NYI
!
! ---- determine size of y axis format -----------------------------------
!
      IPT_SIGN = 1
      IDIGIT = jint(alog10(max(abs(YT),abs(YB),.9))) + 1
      If (MINUSY) IPT_SIGN = IPT_SIGN + 1
      Do 15 JDIGIT = 2, 25-IDIGIT-IPT_SIGN
        IJDIGIT = IDIGIT + JDIGIT + IPT_SIGN
        Do 13 I = 1,NYI
          Fmt = Varfmt(IJDIGIT,JDIGIT,LN)
          WRITE (YFTEST1,FMT(1:LN),Err=15) (YT-(I-1)*DYB)*SWITCHY
          write (YFTEST2,fmt(1:ln),Err=15) (YT-I*DYB)*SWITCHY
          If (YFTEST1.eq.YFTEST2) goto 15
13      Continue
        Goto 16
15    Continue
!
16    Continue
      ISTART = IJDIGIT + 2  ! 1 space before & after number
!
! ---- compute number of x intervals required by test --------------------
!
      XINT  = (XR-XL)/XI + 0.999
      NXINT = XINT
      NX    = (NXINT*XINCH/10.)*60.+.5
      NXSIZE= NX/6+ISTART+5
      DXD   = NX/(1.0*NXINT)
      XR    = XL + NXINT*XI
      DXL   = (XR-XL)/NXINT
      If (NXSIZE.gt.130.or.NY.gt.1590) then
        TMP1 = NXSIZE/10.
        TMP2 = NY/72.
        Write (6,742) TMP1,TMP2,XL,XR,YB,YT,XI,NYI,XINCH,YINCH
 742    Format(' %PLOTTER: ** Error ** plot specified is too large.'/
     &  ' -% X-axis size (max=13.00 in.)         => ' 1PG15.5/
     &  ' -% Y-axis size (max=22.08 in.)         => ' 1PG15.5/
     &  ' -% Xmin,Xmax                           => ',2(1PG15.5,1X)/
     &  ' -% Ymin,Ymax                           => ',2(1PG15.5,1X)/
     &  ' -% Size of 1 X-axis interval           => ',1PG15.5/
     &  ' -% Number of Y-axis intervals          => ',I8/
     &  ' -% Size of 10 intervals (in.) X,Y-axis => ',2(1PG15.5,1X))
!
! ---- output title and descriptor message -------------------------------
!
        Write (uPlot,1) TITLE,REF
        Write (uPlot,3) YAXIS(PLOTS(1)),XAXIS(PLOTS(1))
        Write (uPlot,742) TMP1,TMP2,XL,XR,YB,YT,XI,NYI,XINCH,YINCH
        Return
      End if
!
! ---- clear plot output buffer ------------------------------------------
!
30    DO 40 I=1,NY+8
        DO 41 J=1,NXSIZE
          M(I,J)=DUMMY
41      CONTINUE
!
! ---- set up vertical line for y axis -----------------------------------
!
        IF (I.LT.NY+1) THEN
          M(I,ISTART)='20'X
          IF (I-1.EQ.NYD) THEN
            YYD=YYD+DYD
            NYD=YYD
            M(I,ISTART)='B8'X
            IF(GRID) THEN
              DO 399 K=ISTART+1,NXSIZE
399             M(I,K)='52'X
            ENDIF
            IY=IY+1
            IYLOC(IY)=I
          ENDIF
        ENDIF
  40  CONTINUE
      IYLOC(IY+1)=NY
!
! ---- set up horizontal line for x axis ---------------------------------
!
      DO 43 I=ISTART+1,NXSIZE
        M(NY,I)='FF'X
43    CONTINUE
!
      ITAB(1)=ISTART
      DO 44 I=0,NX
        IF(I.EQ.NXD) THEN
          XXD=XXD+DXD
          NXD=XXD
          IBYTE=(I+5)/6.
          IBIT=I-(IBYTE-1)*6.
          SP0=64
          SP1=TABLE(IBIT)
          SP2=IOR(SP0,SP1)
          M(NY+1,IBYTE+ISTART)=SP3
          M(NY+2,IBYTE+ISTART)=SP3
          IF(GRID.AND.I.NE.0) THEN
            DO 440 J=1,NY,5
              SP0=M(J,IBYTE+ISTART)
              SP2=IOR(SP0,SP1)
              M(J,IBYTE+ISTART)=SP3
440         CONTINUE
          ENDIF
          IX=IX+1
          ITAB(IX)=ISTART+NXD/6
        ENDIF
44    CONTINUE
      ITAB(IX+1)=ISTART+NX/6
!
! ---- calculate scale factors -------------------------------------------
!
      XF=(NX-1.)/(XR-XL)
      YF=(NY-1.)/(YT-YB)
!
! ---- fill printronics plot buffer --------------------------------------
!
      DO 60 I=1,NPLOTS
        DO 60 J=1,N(I)-1
          XI=(X(PLOTS(1),J)-XL)*XF+1.
          YJ=(YT-Y(PLOTS(I),J))*YF+1.
          XI1=(X(PLOTS(1),MIN(N(I),J+1))-XL)*XF+1.
          YJ1=(YT-Y(PLOTS(I),MIN(N(I),J+1)))*YF+1.
          IF (CIRCLE) THEN
            IC=XI+.5
            JC=YJ+.5
            DO JB=JC-1,JC+1
              IB=IC-2
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
              IB=IC+2
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
            END DO
!
            DO IB=IC-1,IC+1
              JB=JC-2
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
              JB=JC+2
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
            END DO
          END IF
!
          DX=XI1-XI
          DY=YJ1-YJ
          IF((.NOT.INTERP).OR.(DX.EQ.0..AND.DY.EQ.0.)) THEN
            IB=XI+.5
            JB=YJ+.5
            IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 60
            IBYTE=(IB+5)/6
            IBIT=IB-(IBYTE-1)*6
            SP0=M(JB,IBYTE+ISTART)
            SP1=TABLE(IBIT)
            SP2=IOR(SP0,SP1)
            M(JB,IBYTE+ISTART)=SP3
          ELSEIF(ABS(DY).GT.ABS(DX)) THEN
            S=DX/DY
            B=XI1-S*YJ1
            JY=MIN(YJ,YJ1)+.5
            JY1=MAX(YJ,YJ1)+.5
            DO 45 JB=JY,JY1
              IB=S*JB+B+.5
              IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 45
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
45          CONTINUE
          ELSE
            S=DY/DX
            B=YJ1-S*XI1
            IX=MIN(XI,XI1)+.5
            IX1=MAX(XI,XI1)+.5
            DO 48 IB=IX,IX1
              JB=S*IB+B+.5
              IF(IB.GT.NX.OR.JB.GT.NY.OR.IB.LT.1.OR.JB.LT.1) GO TO 48
              IBYTE=(IB+5)/6
              IBIT=IB-(IBYTE-1)*6
              SP0=M(JB,IBYTE+ISTART)
              SP1=TABLE(IBIT)
              SP2=IOR(SP0,SP1)
              M(JB,IBYTE+ISTART)=SP3
48          CONTINUE
          ENDIF
60    CONTINUE
!
! ---- output title and descriptor message -------------------------------
!
80    WRITE(uPlot,1)TITLE,REF
1     FORMAT('1',15X,A/15X,A)
      WRITE(uPlot,3) YAXIS(PLOTS(1)),XAXIS(PLOTS(1))
3     FORMAT(2X,A,' -VS- ',A)
!
! ---- generate y axis digit font ----------------------------------------
!
      DO 5 I=1,NYI+1
        Fmt = Varfmt(IJDIGIT,JDIGIT,LN)
        WRITE(YFTEST1,FMT(1:LN)) YT*SWITCHY
        DO 7 J=1,IJDIGIT
          CALL FONTGEN(YFTEST1(J:J),FONT)
          DO 8 K=1,8
            M(IYLOC(I)+K-1,J+1)=FONT(K)  !j+1 because of 1 sp in front of no.
8         CONTINUE
7       CONTINUE
        YT=YT-DYB
5     CONTINUE
!
! ---- output buffer removing trailling blanks ---------------------------
!
      DO 90 I=1,NY+8
        DO 88 K=NXSIZE,1,-1
          IF(M(I,K).EQ.DUMMY) GO TO 88
          GO TO 89
88      CONTINUE
89      WRITE(uPlot,2)(M(I,J),J=1,K),PLOTMODE
2       FORMAT(131A1)
  90  CONTINUE
!
! --- determine size of x axis format ------------------------------------
!
      IPT_SIGN=1
      IDIGIT=JINT(ALOG10(MAX(ABS(XR),ABS(XL),.9)))+1
      IF(MINUSX) IPT_SIGN=IPT_SIGN+1
      DO 100 JDIGIT=2,25-IDIGIT-IPT_SIGN
        IJDIGIT=IDIGIT+JDIGIT+IPT_SIGN
        DO I=1,NXINT
          Fmt = Varfmt(IJDIGIT,JDIGIT,LN)
          WRITE(YFTEST1,FMT(1:LN),ERR=101) (XL+(I-1)*DXL)*SWITCHX
          WRITE(YFTEST2,FMT(1:LN),ERR=101) (XL+I*DXL)*SWITCHX
          IF(YFTEST1.EQ.YFTEST2) GO TO 100
        END DO
        GO TO 101
100   CONTINUE
!
101   CONTINUE
!
! ---- output x axis numbers ---------------------------------------------
!
      N1=NXINT+1
      DO J=2,N1
        IF(ITAB(J)-ITAB(1) .GT. IJDIGIT) GO TO 106
      END DO
!
106   WRITE(uPlot,91)
      DO I=1,N1,J-1
        WRITE(uPlot,92) (XL+(I-1)*DXL)*SWITCHX
      END DO
!
91    Format(/)
92    Format('+',T<ITAB(I)-IJDIGIT/2+1>,F<IJDIGIT>.<JDIGIT>)
!
      Return
      END
C
C
C     FONTGENS
C     A STEVE POTTER PROGRAM
C
      SUBROUTINE FONTGENS
C 
      CHARACTER*1 SYMBOL,LEGAL(13)*1
      BYTE        FONT(8),CHARGEN(13,8)
C
C     INITIALIZE
C
      DATA LEGAL /'0','1','2','3','4','5','6','7','8','9',
     +            '-','.','!'/
      DATA CHARGEN /
     + 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 72,
     + 92, 72, 92, 92, 80, 62, 80, 62, 92, 92, 64, 64, 72,
     + 34, 76, 34, 34, 88, 66, 72, 32, 34, 34, 64, 64, 72,
     + 34, 72, 32, 32, 84, 94, 68, 32, 34, 34, 64, 64, 72, 
     + 34, 72, 80, 88, 82, 32, 94, 80, 92, 60, 94, 64, 72,
     + 34, 72, 72, 32, 62, 32, 34, 72, 34, 80, 64, 64, 72,
     + 34, 72, 68, 34, 80, 34, 34, 68, 34, 72, 64, 64, 72,
     + 92, 92, 62, 92, 80, 92, 92, 66, 92, 68, 64, 66, 72/
C
C     LOCATE LETTER
C
      ENTRY FONTGEN(SYMBOL,FONT)
C
      DO 10 I=1,13
         IF (SYMBOL.EQ.LEGAL(I)) GOTO 20
  10  CONTINUE
C
C     STORE A BLANK
C
      DO 15 I=1,8
         FONT(I)=64
  15  CONTINUE
      RETURN
C
C     STORE FONT
C
  20  DO 30 J=1,8
         FONT(J)=CHARGEN(I,J)
  30  CONTINUE
      RETURN
      END
C
      SUBROUTINE VarFmt(IJ,J,Ln)
      IMPLICIT NONE
      INTEGER IJ,J,Ln
      CHARACTER*25 VarFmt
      VarFmt(1) = 'F'
      IF (IJ .LT. 10) THEN
        WRITE(VarFmt(2:2),I1) IJ
        VarFmt(3) = '.'
        WRITE(VarFmt(4:4),I1) J
        Ln = 4
      ELSEIF (IJ .GE. 10) THEN
        WRITE(VarFmt(2:3),I2) IJ
        VarFmt(4) = '.'
        IF (J .LT. 10) THEN
          WRITE(VarFmt(5:5),I1) J
          Ln = 5
        ELSE
          WRITE(VarFmt(5:6),I2) J
          Ln = 6
        ENDIF
      ENDIF
      RETURN
      END
