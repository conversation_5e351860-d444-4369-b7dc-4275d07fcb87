C
C===========================================================================
C The subroutine ReadInput accepts commands from unit number uRead which
C may be the keyboard or a batch file. An input line terminating with
C a hyphen (-) indicates a continuation line and the subroutine reads input
C until a line without a continuation mark is encountered. The resulting
C entire command (including continuation lines) in stored in "Input".
C===========================================================================
C
C
      SUBROUTINE ReadInput(Prompt,Input)
      IMPLICIT NONE
      CHARACTER*(*) Prompt,Input,Line*80,Blank*1/' '/
      CHARACTER InputLines*1000,Prompt2*5
      INTEGER Length,I,J,LEN,Start,End,Rstrln
      LOGICAL First,Once,Multicmd/.FALSE./
      INCLUDE 'inout.inc'
C
      IF (LEN(Input) .GT. LEN(InputLines)) THEN
        CALL Mess(' Error: Internal error in subroutine ReadInput')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Stop reading from a file if an error was encountered
C
      IF (Batch) THEN
        IF (Error .OR. StopProcess) THEN
          IF (.NOT. Once) CALL Mess(' Type CONTINUE to resume processing
     & command file or ABORT to abort')
          Once = .TRUE.
          uRead = Keyboard
        ELSE
          Once = .FALSE.
          uRead = uBatch
        ENDIF
      ENDIF
C
      IF (.NOT. Multicmd) THEN
        Prompt2 = Prompt(1:LEN(Prompt))
        WRITE (Screen,'(A,$)') '\n'//Prompt2(1:LEN(Prompt))
        CALL Clear(Input)
        CALL Clear(InputLines)
        CALL Clear(Line)
        First = .TRUE.
        Start = 1
        Length = 1
C
C Read the first line or continue reading after a continuation mark
C
        DO WHILE (Line(Length:Length).EQ.'-' .OR. First)
          READ(uRead,'(A)',END=10,ERR=20) Line
          Length = Rstrln(Line)
C
C Line length limited to 80
C
          IF (Length .GT. 80) THEN
            CALL Mess(' Error in ReadInput: Command line too long')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
C
C Echo results to screen and/or journal file
C
          IF (First) THEN
            IF (Batch.AND..NOT.Error.AND..NOT.StopProcess)
     &        CALL Mess('+'//Line)
            IF (Journal .AND. .NOT.Batch) CALL Echo('+'//Line)
          ELSE
            IF (Batch.AND..NOT.Error.AND..NOT.StopProcess)
     &        CALL Mess(' '//Line)
            IF (Journal .AND. .NOT.Batch) CALL Echo(' '//Line)
          ENDIF
C
C Get comments at the end of the line
C
          I = INDEX(Line,'!')
          IF (I .GT. 0) Length = I - 1
C
C Eliminate trailing blanks
C
          IF (Length .GT. 0) THEN
            DO WHILE (Line(Length:Length) .EQ. Blank)
              Length = Length - 1
              IF (Length .LT. 1) GOTO 5
            ENDDO
          ENDIF
5         CONTINUE
C
C Check that total length (including continuation lines) is not greater
C than the length of "Input" which must hold the result.
C
          IF (Length .GT. 0) THEN
            End = Start + Length
            IF (End .GT. LEN(InputLines)) THEN
              CALL Mess(' Error in ReadInput: Command line too long')
              Error = .TRUE.
              CALL Clear(InputLines)
              RETURN
            ENDIF
C
C Merge continuation lines
C
            IF (Line(Length:Length) .EQ. '-') THEN
              IF (Length .GT. 1) THEN
                InputLines(Start:End) = Line(1:Length-1)//' '
              ELSE
                InputLines(Start:End) = ' '
              ENDIF
            ELSE
              InputLines(Start:End) = Line(1:Length)
            ENDIF
            Start = End
          ELSE
            Start = 1
            End = 1
            Length = 1
            Line(1:1) = Blank
          ENDIF
          First = .FALSE.
        ENDDO
C
C Replace "," and "=" with blanks for Getcmd to work
C
        DO I = 1,End
          IF (InputLines(I:I).EQ.'=' .OR. InputLines(I:I).EQ.',') THEN
            InputLines(I:I) = ' '
          ENDIF
        ENDDO
      ENDIF
C
C Check for multiple commands per line
C
      CALL Clear(Input)
      J = INDEX(InputLines,';')
      IF (J .GT. 0) THEN
        Input = InputLines(1:J-1)
        InputLines = InputLines(J+1:)
        MultiCmd = .TRUE.
      ELSE
        Input = InputLines
        Multicmd = .FALSE.
      ENDIF
C
      RETURN
C
C End of file received; close batch file and reassign input to keyboard
C
10    Batch = .FALSE.
      CLOSE (uBatch)
      uRead = Keyboard
      RETURN
C
C Error reading input line; ignore it and go on
C
20    CALL Mess(' Error in ReadInput: Input line ignored')
      Error = .TRUE.
      CALL Clear(Input)
      RETURN
      END
C
C=========================================================================
C
C Open a file for write access
C
      FUNCTION openW(Unit,File,Status,Access,Form,Recl)
      IMPLICIT NONE
      LOGICAL openW
      INTEGER Unit,Recl
      CHARACTER*(*) File,Status,Access,Form
      IF (Access .EQ. 'DIRECT') THEN
        OPEN(UNIT=Unit,FILE=File,STATUS=Status,ACCESS=Access,
     &       FORM=Form,RECL=Recl,ERR=10)
      ELSE
        OPEN(UNIT=Unit,FILE=File,STATUS=Status,ERR=10)
      ENDIF
      openW = .TRUE.
      RETURN
 10   openW = .FALSE.
      RETURN
      END
C
C Opens a file for read access
C
      FUNCTION openR(Unit,File,Status,Access,Form,Recl)
      IMPLICIT NONE
      LOGICAL openR
      INTEGER Unit,Recl
      CHARACTER*(*) File,Status,Access,Form
      IF (Access .EQ. 'DIRECT') THEN
        OPEN(UNIT=Unit,FILE=File,STATUS=Status,ACCESS=Access,
     &       FORM=Form,RECL=Recl,ERR=10)
      ELSE
        OPEN(UNIT=Unit,FILE=File,STATUS=Status,ERR=10)
      ENDIF
      openR = .TRUE.
      RETURN
 10   openR = .FALSE.
      RETURN
      END
C
C Converts to upper case
C
      SUBROUTINE Upper(Input)
      IMPLICIT NONE
      CHARACTER Input*(*)
      INTEGER I,K
      DO I = 1,LEN(Input)
        K = ICHAR(Input(I:I))
        IF (K.GE.97 .AND. K.LE.122) Input(I:I) = CHAR(K-32)
      ENDDO
      RETURN
      END
C
C Gets a command line parameter
C
      SUBROUTINE GetParam(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input,Param*60
      CALL GETARG(1,Param)
      Input = Param
      RETURN
      END
C
C============================================================================
C This subroutine writes messages to the screen and/or journal file. String 
C length is limited to 80 characters.
C============================================================================
C
      SUBROUTINE Mess(String)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      CHARACTER String*(*)
      INTEGER   I,Rstrln

      IF (String(1:1) .EQ. ' ') THEN
         String(1:1) = '\n'
      ELSE IF (String(1:1) .EQ. '1') THEN
         String(1:1) = CHAR(12)
         DO I=1,23
            WRITE (Screen,'(A)') ' '
         ENDDO
      ELSE IF (String(1:1) .EQ. '+') THEN
         String(1:1) = CHAR(13)
      ENDIF
      WRITE (Screen,'(A,$)') String(1:MIN0(80,LEN(String)))
      ENTRY Echo(String)
      IF (Journal) WRITE(uJournal,'(A)')
     &             String(2:MIN0(80,Rstrln(String)))
      RETURN
      END
C
C===========================================================================
C This subroutine performs the SYS command. The input string following
C the command verb "sys" is sent to the operating system for processing.
C Once the system command has been executed, control returns to FGU
C automatically.
C===========================================================================
C
      SUBROUTINE SysCmd(Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input
      CHARACTER*30 Command/'                              '/
      LOGICAL Getcmd
      IF (Getcmd(Command,Input)) THEN
        CALL system(Command//Input(1:75)//'\0')
        CALL Clear(Input)
      ELSE
        CALL Mess(' Sys Error: Operating system command not specified')
        Error = .TRUE.
      ENDIF
      RETURN
      END
C
      SUBROUTINE VAXC$CRTL_INIT
      RETURN
      END
C
      SUBROUTINE PLOTTER
      RETURN
      END
