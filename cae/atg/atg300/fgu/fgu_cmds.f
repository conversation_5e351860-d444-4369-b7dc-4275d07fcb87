      SUBROUTINE ContinueCmd
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      StopProcess = .FALSE.
      Error = .FALSE.
      RETURN
      END
C
      SUBROUTINE AbortCmd
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      Error = .FALSE.
      CLOSE(UNIT=uBatch)
      Batch = .FALSE.
      uRead = Keyboard
      RETURN
      END
C
C===========================================================================
C This subroutine performs the JOURNAL command. It expects a file name
C from the user to which all input to FGU and output generated by FGU
C is saved. The journal file remains open until you exit FGU.
C===========================================================================
C
      SUBROUTINE JournalCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input
      CHARACTER*30 Journal_file
      LOGICAL Getcmd
      INCLUDE 'inout.inc'
      IF (.NOT. Journal) THEN
        IF (Getcmd(Journal_file,Input)) THEN
          IF (.NOT. openW(uJournal,Journal_file,'NEW',' ',' ',0)) THEN
            CALL Mess(' Journal Error: Cannot open journal file')
            Error = .TRUE.
            RETURN
          ENDIF
          Journal = .TRUE.
        ELSE
          CALL Mess(' Journal Error: Journal file not specified')
          Error = .TRUE.
        ENDIF
      ELSE
        CALL Mess(' Journal Error: Journal file already open')
        Error = .TRUE.
        CALL Clear(Input)
      ENDIF
      RETURN
      END
C
C===========================================================================
C This subroutine performs the DO command. It expects a file name
C from the user, opens it if it exists and then assigns the correct
C unit number for subsequent reads. FGU commands will then be read from
C the file instead of the keyboard until an end-of-file is encountered.
C It is not possible to abort execution of the command file without
C aborting FGU.
C===========================================================================
C
      SUBROUTINE DoCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input
      CHARACTER*30 Batch_file/'                             '/
      LOGICAL Getcmd,FnF
      INTEGER strln
      INCLUDE 'inout.inc'
C
      Error = .FALSE.
      IF (Batch) THEN
        CALL Mess(' Do Error: Cannot specify nested command files')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Check for batch file
C
      IF (Getcmd(Batch_file,Input)) THEN
        IF (FnF(Batch_file)) THEN
          CALL Mess(' Do Error: Command file '//
     &               Batch_file(1:Strln(Batch_file))//' not found')
          Error = .TRUE.
          CALL Clear(Batch_file)
C
C Open batch file, reassign unit number for read
C
        ELSE
          Batch = .TRUE.
          uRead = uBatch
          OPEN (UNIT=uBatch,FILE=Batch_file,STATUS='OLD')
        ENDIF
      ELSE
        CALL Mess(' Do Error: Input file not specified')
        Error = .TRUE.
      ENDIF
      RETURN
      END
C
C============================================================================
C This subroutine performs the LOAD command. It expects a data file name
C which it opens for sequential access and then uses it to generate a
C random access file which is used thereafter. While the random access
C file is being generated, it is connected to uScratch. The sequential
C access file is connected to uData. Once the random access file is
C generated, both files are closed and the random access file is reopened
C under uData.
C===========================================================================
C
      SUBROUTINE LoadCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input
      CHARACTER Data_file*40,Line*80
      LOGICAL Getcmd,FnF
      INTEGER strln
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CALL Clear(Data_file)
      IF (Getcmd(Data_file,Input)) THEN
        IF (FnF(Data_file)) THEN
          CALL Mess(' Load Error: Data file '//
     &              Data_file(1:Strln(Data_file))//' not found')
          Error = .TRUE.
          CALL Clear(Data_file)
          CALL Clear(Input)
C
C Open data files
C
        ELSE
          IF (Data) THEN
            CLOSE (UNIT=uData,STATUS='DELETE')
            Data = .FALSE.
            CLOSE (UNIT=uScratch,STATUS='DELETE')
            Scratch = .FALSE.
            CLOSE (UNIT=uProcess)
            Process = .FALSE.
            Select = .FALSE.
          ENDIF
          IF (.NOT. openR(uData,Data_file,'OLD',' ',' ',0)) THEN
            CALL Mess(' Load Error: Cannot open file '//
     &                Data_file(1:Strln(Data_file)))
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          IF (.NOT. openW(uScratch,'fgu1.tmp','NEW','DIRECT',
     &        'FORMATTED',80)) THEN
            CALL Mess(' Load Error: Cannot open temporary file')
            Error = .TRUE.
            CLOSE(uData)
            Data = .FALSE.
            CALL Clear(Input)
            RETURN
          ENDIF
          Data = .TRUE.
C
C Copy variable length records from uData to fixed length records in uScratch
C
          Nrecords = 0
          DO WHILE (.TRUE.)
            CALL Clear(Line)
            READ(uData,'(A)',END=10,ERR=20) Line
            Nrecords = Nrecords + 1
            WRITE(uScratch,'(A80)',REC=Nrecords,ERR=40) Line
          ENDDO
10        CONTINUE
          CLOSE(uData)
          CLOSE(uScratch)
C
C Reopen new data file under unit number uData
C
          IF (.NOT. openW(uData,'fgu1.tmp','OLD','DIRECT',
     &        'FORMATTED',80)) THEN
            CALL Mess(' Load Error: Cannot open temporary file')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          CALL RdFuncNames
        ENDIF
      ELSE
        CALL Mess(' Load Error: Data file not specified')
        Error = .TRUE.
      ENDIF
      RETURN
20    CALL Mess(' Load Error: Encountered while reading data file')
      Error = .TRUE.
      RETURN
40    CALL Mess(' Load Error: Encountered while writing to temporary fil
     &e fgu1.tmp')
      CALL Mess('             Check disk space')
      CLOSE(uScratch)
      CLOSE(uData)
      Data = .FALSE.
      Scratch = .FALSE.
      Error = .TRUE.
      RETURN
      END
C
C===========================================================================
C This subroutine performs the SHOW command. If no qualifiers are specified,
C the current selected function is shown. If parses the command line
C following SHOW for a qualifiers and sends it to ShowOutput for processing.
C===========================================================================
C
      SUBROUTINE ShowCmd(Input)
      IMPLICIT NONE
      INTEGER Ncommand
      PARAMETER (Ncommand = 11)
      CHARACTER*(*) Input
      CHARACTER*30 Show_noun/'                              '/
      CHARACTER Showcmds(Ncommand)*13
      LOGICAL Getcmd,SearchList
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      DATA Showcmds
     &/ 'BREAKPOINTS  ','VARIABLE     ','SCHEDULE     ','FUNCTION     '
     &, 'DIMENSION    ','MULTIPLICITY ','CLASS        ','ZONE         '
     &, 'LIMITS       ','SIZE         ','MODIFICATIONS'/
C
      IF (.NOT. Data) THEN
        CALL Mess(' Show Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        IF (Select) THEN
C
C Get command qualifier
C
          IF (Getcmd(Show_noun,Input)) THEN
            IF (SearchList(Show_noun,13,Showcmds,Ncommand)) THEN
              CALL ShowOutput(Show_noun,Input)
            ELSE
              CALL Mess(' Show Error: Unknown SHOW command qualifier')
              Error = .TRUE.
            ENDIF
C
C No command qualifier, show selected function
C
          ELSE
            CALL Mess(' Selected function: '//Function)
          ENDIF
        ELSE
          CALL Mess(' Show Error: Function not selected')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C===========================================================================
C This subroutine performs the SELECT command. It gets the function name,
C verifies that the function exists and loads complete header information
C and function values.
C===========================================================================
C
      SUBROUTINE SelectCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input,Fselect*12
      INTEGER Strln,Index
      LOGICAL Getcmd,FindFunc
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
C
      CALL Clear(Fselect)
      IF (.NOT. Data) THEN
        CALL Mess(' Select Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSEIF (Getcmd(Fselect,Input)) THEN
C
C Function name found. Save the previous function if it has been modified
C and load new function information.
C
        IF (FindFunc(Fselect,Index)) THEN
          IF (Select .AND. Funit(Findex).EQ.uScratch) CALL SaveFunc
          Findex = Index
          Select = .TRUE.
          Function = Fselect
          CALL LoadFuncInfo(.TRUE.)
C
C Function not found
C
        ELSE
          CALL Mess(' Select Error: Function not found <'//
     &                Fselect(1:Strln(Fselect))//'>')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ELSE
        CALL Mess(' Select Error: Function not specified')
        Error = .TRUE.
        CALL Clear(Input)
      ENDIF
      RETURN
      END
C
C===========================================================================
C This subroutine performs the WRITE command. It reads each function from
C the appropriate file (from uData if it hasn't been modified and from
C uScratch if it has been modified) and writes them to the new file
C specified by the user.
C===========================================================================
C
      SUBROUTINE WriteCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input,File*40,Line*80
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      INTEGER I
      LOGICAL Getcmd
C
      CALL Clear(File)
      IF (.NOT. Data) THEN
        CALL Mess(' Write Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
C
C Open the new data file. Save the last function if it has been modified.
C For each function, load the function information from either of the
C two random access files and copy it to the new sequential file.
C
      ELSEIF (Getcmd(File,Input)) THEN
        IF (.NOT. openW(uProcess,File,'NEW',' ',' ',0)) THEN
          CALL Mess(' Write Error: Cannot open output file')
          Error = .TRUE.
          RETURN
        ENDIF
        IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
        Process = .TRUE.
        DO Findex = 1,Nfunction
          IF (.NOT. Fdelete(Findex)) THEN
            CALL LoadFuncInfo(.TRUE.)
            WRITE(uProcess,'(A)') '/FUNC'
            CALL SaveFunc
          ENDIF
        ENDDO
        Process = .FALSE.
        CLOSE(uProcess)
      ELSE
        CALL Mess(' Write Error: Output file not specified')
        Error = .TRUE.
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE SwitchCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input,Param1*1,Param2*1
      INTEGER Dim1,Dim2
      LOGICAL Getcmd
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      Dim1 = 0
      Dim2 = 0
      IF (Data) THEN
        IF (Select) THEN
          IF (Getcmd(Param1,Input)) THEN
            IF (Getcmd(Param2,Input)) THEN
              CALL Upper(Param1)
              CALL Upper(Param2)
              IF (Param1 .EQ. 'X') Dim1 = 1
              IF (Param1 .EQ. 'Y') Dim1 = 2
              IF (Param1 .EQ. 'Z') Dim1 = 3
              IF (Param1 .EQ. 'T') Dim1 = 4
              IF (Param2 .EQ. 'X') Dim2 = 1
              IF (Param2 .EQ. 'Y') Dim2 = 2
              IF (Param2 .EQ. 'Z') Dim2 = 3
              IF (Param2 .EQ. 'T') Dim2 = 4
              IF (Dim1.EQ.0 .OR. Dim2.EQ.0 .OR. Dim1.GE.Dim2 .OR.
     &            Dim1.GT.Dimension .OR. Dim2.GT.Dimension) THEN
                CALL Mess(' Switch Error: Invalid parameters')
                Error = .TRUE.
              ELSE
                CALL Switch(Dim1,Dim2)
              ENDIF
            ELSE
              CALL Mess(' Switch Error: Missing command parameter')
              Error = .TRUE.
            ENDIF
          ELSE
            CALL Mess(' Switch Error: Missing command parameters')
            Error = .TRUE.
          ENDIF
        ELSE
          CALL Mess(' Switch Error: Function not selected')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ELSE
        CALL Mess(' Switch Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE HelpCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input
      CHARACTER HelpPar*12,Line*80,Output(25)*80
      CHARACTER ESC*1,BRV*4,ERV*4,CLS*4
      INTEGER I,J,strln
      LOGICAL FnF,Getcmd
      INCLUDE 'inout.inc'
      IF (.NOT. Help) THEN
        IF (FnF(HelpFile)) THEN
          CALL Mess(' Help Error: Help file '//HelpFile//' not found')
          Error = .TRUE.
        ELSE
          IF (.NOT. openR(uHelp,HelpFile,'OLD',' ',' ',0)) THEN
            CALL Mess(' Help Error: Cannot open help file')
            Error = .TRUE.
            RETURN
          ENDIF
          Help = .TRUE.
        ENDIF
        ESC = char('1B'x)
        BRV = ESC//'[7m'    ! begin reverse video
        ERV = ESC//'[0m'    ! end reverse video
        CLS = ESC//'[2J'    ! clear screen
      ENDIF
      IF (Help) THEN
        IF (.NOT. Getcmd(HelpPar,Input)) HelpPar = 'HELP'
        REWIND(uHelp)
        CALL Clear(Line)
        DO WHILE (Line .NE. '/'//HelpPar(1:strln(HelpPar))//'/')
          READ(uHelp,'(A)',END=10) Line
        ENDDO
        CALL Mess(' '//CLS)
        CALL Mess(' '//ESC//'[0;0H')
        CALL Mess('+'//BRV//HelpPar(1:Strln(HelpPar))//ERV)
        READ(uHelp,'(A)',END=20) Line
        DO WHILE (Line(1:1) .NE. '/')
          CALL Mess(' '//Line)
          READ(uHelp,'(A)',END=10) Line
        ENDDO
20      RETURN
10      CALL Mess(' Help Error: Help command not found')
        Error = .TRUE.
      ENDIF
      RETURN
      END
C
C
C
      SUBROUTINE AddMultCmd(Input,Option)
      IMPLICIT NONE
      CHARACTER Input*(*),Value*10,Option*(*)
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      INTEGER istat,Start(4),End(4),BPno(4),I,J,K,L,Ptr
      INTEGER ParseBrkpt,Strln,ReadReal
      LOGICAL Getcmd
      REAL    Factor
C
      IF (.NOT. Data) THEN
        CALL Mess(' Add/Multiply Error: Data file not loaded')
        Error = .TRUE.
      ELSE
        IF (Select) THEN
          IF (Getcmd(Value,Input)) THEN
            IF (ReadReal(Value,Factor) .EQ. 0) THEN
              CALL Mess(' Add/Multiply Error: Cannot decode add/multiply
     & constant value')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ELSE
            CALL Mess(' Add/Multiply Error: Missing value for add/multip
     &ly constant')
            Error = .TRUE.
            RETURN
          ENDIF
          istat = ParseBrkpt(Input,BPno)
          IF (istat .EQ. 0) THEN
            DO I = 1,4
              Start(I) = BPno(I)
              End(I) = BPno(I)
            ENDDO
          ELSEIF (istat .EQ. 1) THEN
            DO I = 1,4
              IF (BPno(I) .EQ. 0) THEN
                Start(I) = 1
                End(I) = Schedule_size(I)
              ELSE
                Start(I) = BPno(I)
                End(I) = BPno(I)
              ENDIF
            ENDDO
          ELSE
            CALL Mess(' Add/Multiply Error: Incorrect dimension specifie
     &r')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          DO L = Start(4),End(4)
            DO K = Start(3),End(3)
              DO J = Start(2),End(2)
                DO I = Start(1),End(1)
                  IF (Option .EQ. '*') THEN
                    Fvalues(Ptr(I,J,K,L)) = Fvalues(Ptr(I,J,K,L))*Factor
                  ELSEIF (Option .EQ. '+') THEN
                    Fvalues(Ptr(I,J,K,L)) = Fvalues(Ptr(I,J,K,L))+Factor
                  ENDIF
                ENDDO
              ENDDO
            ENDDO
          ENDDO
          Funit(Findex) = uScratch
        ELSE
          CALL Mess(' Add/Multiply Error: Function not selected')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE DepositCmd(Input)
      IMPLICIT NONE
      CHARACTER Value*15,Input*(*)
      INTEGER I,J,K,L,Ptr,ParseBrkpt,BPno(4)
      INTEGER Start(4),End(4),istat,ReadReal
      LOGICAL Getcmd
      REAL Newval
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
C
      CALL Clear(Value)
      IF (Data) THEN
        IF (Select) THEN
          IF (Getcmd(Value,Input)) THEN
            IF (ReadReal(Value,Newval) .EQ. 0) THEN
              CALL Mess(' Deposit Error: Cannot decode value')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ELSE
              istat = ParseBrkpt(Input,BPno)
              IF (istat .EQ. 0) THEN
                DO I = 1,4
                  Start(I) = BPno(I)
                  End(I) = BPno(I)
                ENDDO
              ELSEIF (istat .EQ. 1) THEN
                DO I = 1,4
                  IF (BPno(I) .EQ. 0) THEN
                    Start(I) = 1
                    End(I) = Schedule_size(I)
                  ELSE
                    Start(I) = BPno(I)
                    End(I) = BPno(I)
                  ENDIF
                ENDDO
              ELSE
                CALL Mess(' Deposit Error: Incorrect dimension specifier
     &')
                Error = .TRUE.
                CALL Clear(Input)
                RETURN
              ENDIF
              DO L = Start(4),End(4)
                DO K = Start(3),End(3)
                  DO J = Start(2),End(2)
                    DO I = Start(1),End(1)
                      Fvalues(Ptr(I,J,K,L)) = Newval
                    ENDDO
                  ENDDO
                ENDDO
              ENDDO
              Funit(Findex) = uScratch
            ENDIF
          ELSE
            CALL Mess(' Deposit Error: Value not specified')
            Error = .TRUE.
          ENDIF
        ELSE
          CALL Mess(' Deposit Error: Function not selected')
          Error = .TRUE.
        ENDIF
      ELSE
        CALL Mess(' Deposit Error: Function not selected')
        Error = .TRUE.
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE SetCmd(Input)
      IMPLICIT NONE
      INTEGER Ncmd
      PARAMETER (Ncmd = 6)
      CHARACTER Input*(*),NewZone*2,NewClass*2,NewFunction*12,Multno*2
      CHARACTER NewVariable*12,NewSchedule*12,Dimen*1
      CHARACTER*12 Set_noun/'          '/,Setcmds(Ncmd)
      CHARACTER Dimspec(4)*1/'X','Y','Z','T'/
      INTEGER Multndx,Dimndx,I,J
      LOGICAL Getcmd,ZoneCheck,ClassCheck,SearchList
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      DATA Setcmds
     &/ 'VARIABLE    ','SCHEDULE    ','FUNCTION    ','MULTIPLICITY',
     &  'CLASS       ','ZONE        '/
      IF (.NOT. Data) THEN
        CALL Mess(' Set Error: Data file not loaded')
        Error = .TRUE.
      ELSE
        IF (Select) THEN
          CALL Clear(Set_noun)
          IF (Getcmd(Set_noun,Input)) THEN
            IF (SearchList(Set_noun,12,Setcmds,Ncmd)) THEN
C
C Set function zone
C
              IF (Set_noun .EQ. 'ZONE') THEN
                CALL Clear(NewZone)
                IF (Getcmd(NewZone,Input)) THEN
                  CALL Upper(NewZone)
                  IF (ZoneCheck(NewZone)) THEN
                    Zone = NewZone
                    Funit(Findex) = uScratch
                  ELSE
                    CALL Mess(' Set Error: Invalid zone')
                    Error = .TRUE.
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Zone not specified')
                  Error = .TRUE.
                ENDIF
C
C Set function class
C
              ELSEIF (Set_noun .EQ. 'CLASS') THEN
                CALL Clear(NewClass)
                IF (Getcmd(NewClass,Input)) THEN
                  IF (ClassCheck(NewClass)) THEN
                    Class = NewClass
                    Funit(Findex) = uScratch
                  ELSE
                    CALL Mess(' Set Error: Invalid class')
                    Error = .TRUE.
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Class not specified')
                  Error = .TRUE.
                ENDIF
C
C Set function name
C
              ELSEIF (Set_noun .EQ. 'FUNCTION') THEN
                CALL Clear(NewFunction)
                IF (Getcmd(NewFunction,Input)) THEN
                  CALL Upper(NewFunction)
                  IF (Mult .GE. 2) THEN
                    IF (Getcmd(Multno,Input)) THEN
                      READ(Multno,'(BN,I2)',ERR=10) Multndx
                      IF (Multndx.LT.1 .OR. Multndx.GT.Mult) THEN
                        CALL Mess
     &                  (' Set Error: Incorrect multiplicity index')
                        Error = .TRUE.
                      ELSE
                        Variable_name(Multndx,0) = NewFunction
                        Funit(Findex) = uScratch
                      ENDIF
                    ELSE
                      DO Multndx = 1,Mult
                        Variable_name(Multndx,0) = NewFunction
                      ENDDO
                    ENDIF
                  ELSE
                    Variable_name(1,0) = NewFunction
                    Funit(Findex) = uScratch
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Function name not specified')
                  Error = .TRUE.
                ENDIF
C
C Set variable name
C
              ELSEIF (Set_noun .EQ. 'VARIABLE') THEN
                CALL Clear(NewVariable)
                IF (Getcmd(NewVariable,Input)) THEN
                  CALL Upper(NewVariable)
                  IF (Getcmd(Dimen,Input)) THEN
                    CALL Upper(Dimen)
                    Dimndx = 0
                    DO I = 1,Dimension
                      IF (Dimen .EQ. Dimspec(I)) Dimndx = I
                    ENDDO
                    IF (Dimndx .EQ. 0) THEN
                      CALL Mess(' Set Error: Incorrect dimension specifi
     &er')
                      Error = .TRUE.
                    ELSE
                      IF (Mult .GE. 2) THEN
                        IF (Getcmd(Multno,Input)) THEN
                          READ(Multno,'(BN,I2)',ERR=10) Multndx
                          IF (Multndx.LT.1 .OR. Multndx.GT.Mult) THEN
                            CALL Mess(' Set Error: Incorrect multiplicit
     &y index')
                            Error = .TRUE.
                          ELSE
                            Variable_name(Multndx,Dimndx) = NewVariable
                            Funit(Findex) = uScratch
                          ENDIF
                        ELSE
                          DO Multndx = 1,Mult
                            Variable_name(Multndx,Dimndx) = NewVariable
                          ENDDO
                        ENDIF
                      ELSE
                        Variable_name(1,Dimndx) = NewVariable
                        Funit(Findex) = uScratch
                      ENDIF
                    ENDIF
                  ELSE
                    CALL Mess(' Set Error: Dimension not specified')
                    Error = .TRUE.
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Variable name not specified')
                  Error = .TRUE.
                ENDIF
C
C Set schedule name
C
              ELSEIF (Set_noun .EQ. 'SCHEDULE') THEN
                CALL Clear(NewSchedule)
                IF (Getcmd(NewSchedule,Input)) THEN
                  CALL Upper(NewSchedule)
                  IF (Getcmd(Dimen,Input)) THEN
                    CALL Upper(Dimen)
                    Dimndx = 0
                    DO I = 1,Dimension
                      IF (Dimen .EQ. Dimspec(I)) Dimndx = I
                    ENDDO
                    IF (Dimndx .EQ. 0) THEN
                      CALL Mess(' Set Error: Incorrect dimension specifi
     &er')
                      Error = .TRUE.
                    ELSE
                      Schedule_name(Dimndx) = NewSchedule
                      Funit(Findex) = uScratch
                    ENDIF
                  ELSE
                    CALL Mess(' Set Error: Dimension not specified')
                    Error = .TRUE.
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Schedule name not specified')
                  Error = .TRUE.
                ENDIF
C
C Set function multiplicity
C
              ELSEIF (Set_noun .EQ. 'MULTIPLICITY') THEN
                IF (Getcmd(Multno,Input)) THEN
                  READ(Multno,'(BN,I2)',ERR=10) Multndx
                  IF (Multndx .LE. 0) THEN
                    CALL Mess(' Set Error: Multiplicity must be >= 1')
                    Error = .TRUE.
                  ELSEIF (Multndx .GT. Maxmult) THEN
                    CALL Mess(' Set Error: Multiplicity limit exceeded')
                    Error = .TRUE.
                  ELSE
                    IF (Multndx .GT. Mult) THEN
                      DO I = Mult+1,Multndx
                        DO J = 0,Dimension
                          Variable_name(I,J) = Variable_name(Mult,J)
                        ENDDO
                      ENDDO
                      Mult = Multndx
                    ELSEIF (Multndx .LT. Mult) THEN
                      Mult = Multndx
                    ENDIF
                    Funit(Findex) = uScratch
                  ENDIF
                ELSE
                  CALL Mess(' Set Error: Multiplicity not specified')
                  Error = .TRUE.
                ENDIF
              ENDIF
C
            ELSE
              CALL Mess(' Set Error: Unknown SET command qualifier')
              Error = .TRUE.
            ENDIF
          ELSE
            CALL Mess(' Set Error: Missing SET command qualifier')
            Error = .TRUE.
          ENDIF
        ELSE
          CALL Mess(' Set Error: Function not selected')
          Error = .TRUE.
        ENDIF
      ENDIF
      RETURN
 10   CALL Mess(' Set Error: Unable to decode multiplicity index')
      Error = .TRUE.
      RETURN
      END
C
C
      SUBROUTINE ListCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CHARACTER Output*80,List_qual*12,List_noun*12/'            '/
      CHARACTER List1(5)*12,List2(2)*12
      CHARACTER Flist(Maxfunc)*12
      LOGICAL Getcmd,SearchList,ListFull,ListAlpha
      LOGICAL Pattern
      INTEGER Remainder,No_of_lines,I,J,K,Dim1,Dim2,Dim3,Dim4
      INTEGER FlistSize
      DATA List1/'FUNCTIONS   ','ZONES       ','CLASSES     ',
     &           'SCHEDULES   ','DIMENSIONS  '/
      DATA List2/'FULL        ','ALPHABETICAL'/
C
      ListFull = .FALSE.
      ListAlpha = .FALSE.
      Pattern = .FALSE.
C
      IF (.NOT. Data) THEN
        CALL Mess(' List Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        IF (Getcmd(List_noun,Input)) THEN
          IF (SearchList(List_noun,12,List1,5)) THEN
C
C List functions
C
            IF (List_noun .EQ. 'FUNCTIONS') THEN
              DO WHILE (Getcmd(List_noun,Input))
                IF (SearchList(List_noun,12,List2,2)) THEN
                  IF (List_noun .EQ. 'FULL') THEN
                    ListFull = .TRUE.
                  ELSEIF (List_noun .EQ. 'ALPHABETICAL') THEN
                    ListAlpha = .TRUE.
                  ENDIF
                ELSE
                  Pattern = .TRUE.
                  CALL ListFunc(List_noun,Flist,FlistSize)
                  IF (FlistSize .EQ. 0) THEN
                    CALL Mess(' List Error: Unknown command qualifier or
     & no functions match the given pattern')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ENDIF
                CALL Clear(List_noun)
              ENDDO
              IF (.NOT.Pattern) CALL ListFunc(' ',Flist,FlistSize)
              IF (ListAlpha) CALL ShellSort(Flist,FlistSize,12)
              IF (ListFull) THEN
                DO I = 1,FlistSize
                  WRITE(Output,100) Flist(I),Fdim(I),Fmult(I),
     &                              Fzone(I),Fclass(I)
                  CALL Mess(Output)
100               FORMAT(' ',A12,'  Dimension: ',I2,'  Multiplicity: ',
     &                   I2,'  Zone: ',A2,'  Class: ',A2)
                  IF (MOD(I,23) .EQ. 0) THEN
                    CALL ReadInput('RETURN to continue ...',Input)
                  ENDIF
                ENDDO
              ELSE
                No_of_lines = FlistSize / 5
                Remainder = FlistSize - No_of_lines * 5
                K = -5
                IF (No_of_lines .NE. 0) THEN
                  DO I = 1,No_of_lines
                    K = K + 5
                    WRITE(Output,'(5A16)') (Flist(K+J),J=1,5)
                    CALL Mess(' '//Output)
                    IF (MOD(I,23) .EQ. 0) THEN
                      CALL ReadInput('RETURN to continue ...',Input)
                    ENDIF
                  ENDDO
                ENDIF
                IF (Remainder .NE. 0) THEN
                  K = K + 5
                  WRITE (Output,'(5A16)') (Flist(K+J),J=1,Remainder)
                  CALL Mess(' '//Output)
                ENDIF
              ENDIF
C
C List zones
C
            ELSEIF (List_noun .EQ. 'ZONES') THEN
              CALL Mess(' LIST ZONE command not implemented yet')
C
C List classes
C
            ELSEIF (List_noun .EQ. 'CLASSES') THEN
              CALL Mess(' LIST CLASS command not implemented yet')
C
C List schedules
C
            ELSEIF (List_noun .EQ. 'SCHEDULES') THEN
              CALL ListSched
C
C List dimensions
C
            ELSEIF (List_noun .EQ. 'DIMENSIONS') THEN
              Dim1 = 0
              Dim2 = 0
              Dim3 = 0
              Dim4 = 0
              DO I = 1,Nfunction
                IF (Fdim(I) .EQ. 1) Dim1 = Dim1 + 1
                IF (Fdim(I) .EQ. 2) Dim2 = Dim2 + 1
                IF (Fdim(I) .EQ. 3) Dim3 = Dim3 + 1
                IF (Fdim(I) .EQ. 4) Dim4 = Dim4 + 1
              ENDDO
              WRITE(Output,102) Dim1+Dim2+Dim3+Dim4
102           FORMAT(' ','Total: ',I3)
              CALL Mess(Output)
              WRITE(Output,101) Dim1,Dim2,Dim3,Dim4
              CALL Mess(Output)
101           FORMAT(' ','1d: ',I3,'    2d: ',I3,'    3d: ',I3,
     &             '    4d: ',I3)
            ENDIF
          ELSE
            CALL Mess(' List Error: Unknown command qualifier')
            Error = .TRUE.
            CALL Clear(Input)
          ENDIF
        ELSE
          CALL Mess(' List Error: Missing command qualifier')
          Error = .TRUE.
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
      SUBROUTINE PlotCmd(Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CHARACTER*(*) Input
      INTEGER I,Strln,ReadReal
      CHARACTER PlotSpec*12,Temp*15,Ref*80,Plotcmds(12)*12
      LOGICAL Xgrid_set,Ygrid_set,Xmin_set,Ymin_set,Xmax_set,Ymax_set
      LOGICAL Xlen_set,Ylen_set,Getcmd,SearchList
      LOGICAL*4 CIRCLE,GRID,INTERP
      REAL Xgrid,Ygrid,Xmin,Ymin,Xmax,Ymax,Xlen,Ylen
      DATA Plotcmds/
     &  'XGRID       ','YGRID       ','XMIN        ','YMIN        ' ! 4
     &, 'XMAX        ','YMAX        ','XLEN        ','YLEN        ' ! 8
     &, 'REFERENCE   ','INTERP      ','GRID        ','CIRCLE      '/! 12
C
      CALL Clear(Ref)
      Xgrid_set = .FALSE.
      Ygrid_set = .FALSE.
      Xmin_set  = .FALSE.
      Ymin_set  = .FALSE.
      Xmax_set  = .FALSE.
      Ymax_set  = .FALSE.
      Xlen_set  = .FALSE.
      Ylen_set  = .FALSE.
C
      IF (.NOT. Data) THEN
        CALL Mess(' Plot Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        IF (Select) THEN
          CIRCLE = .FALSE.
          GRID   = .FALSE.
          INTERP = .FALSE.
          DO WHILE (Getcmd(PlotSpec,Input))
            IF (SearchList(PlotSpec,12,Plotcmds,12)) THEN
              CALL Clear(Temp)
C
C Get Xgrid value
C
              IF (PlotSpec .EQ. 'XGRID') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Xgrid) .NE. 0) THEN
                    Xgrid_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode XGRID value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing XGRID value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Ygrid value
C
              ELSEIF (PlotSpec .EQ. 'YGRID') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Ygrid) .NE. 0) THEN
                    Ygrid_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode YGRID value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing YGRID value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Xmin value
C
              ELSEIF (PlotSpec .EQ. 'XMIN') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Xmin) .NE. 0) THEN
                    Xmin_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode XMIN value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing XMIN value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Ymin value
C
              ELSEIF (PlotSpec .EQ. 'YMIN') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Ymin) .NE. 0) THEN
                    Ymin_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode YMIN value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing YMIN value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Xmax value
C
              ELSEIF (PlotSpec .EQ. 'XMAX') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Xmax) .NE. 0) THEN
                    Xmax_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode XMAX value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing XMAX value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Ymax value
C
              ELSEIF (PlotSpec .EQ. 'YMAX') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Ymax) .NE. 0) THEN
                    Ymax_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode YMAX value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing YMAX value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Xlen value
C
              ELSEIF (PlotSpec .EQ. 'XLEN') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Xlen) .NE. 0) THEN
                    Xlen_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode XLEN value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing XLEN value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get Ylen value
C
              ELSEIF (PlotSpec .EQ. 'YLEN') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (ReadReal(Temp,Ylen) .NE. 0) THEN
                    Ylen_set = .TRUE.
                  ELSE
                    CALL Mess(' Plot Error: Cannot decode YLEN value')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing YLEN value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get reference
C
              ELSEIF (PlotSpec .EQ. 'REFERENCE') THEN
                IF (Getcmd(Temp,Input)) THEN
                  IF (Temp(1:1) .EQ. '"') THEN
                    I = INDEX(Input,'"')
                    IF (I .GT. 0) THEN
                      Ref = Temp(2:)//Input(1:I-1)
                      Input = Input(I+1:)
                    ELSE
                      CALL Mess(' Plot Error: Missing end " for referenc
     &e string')
                      Error = .TRUE.
                      CALL Clear(Input)
                    ENDIF
                  ELSE
                    CALL Mess(' Plot Error: Missing starting " for refer
     &ence string')
                    Error = .TRUE.
                    CALL Clear(Input)
                  ENDIF
                ELSE
                  CALL Mess(' Plot Error: Missing plot reference')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
C
C Get CIRCLE flag
C
              ELSEIF (PlotSpec .EQ. 'CIRCLE') THEN
                IF (CIRCLE) THEN
                  CALL Mess(' Plot Warning: CIRCLE flag already set')
                ELSE
                  CIRCLE = .TRUE.
                ENDIF
C
C Get interp flag
C
              ELSEIF (PlotSpec .EQ. 'INTERP') THEN
                IF (INTERP) THEN
                  CALL Mess(' Plot Warning: INTERP flag already set')
                ELSE
                  INTERP = .TRUE.
                ENDIF
C
C Get GRID flag
C
              ELSEIF (PlotSpec .EQ. 'GRID') THEN
                IF (GRID) THEN
                  CALL Mess(' Plot Warning: GRID flag already set')
                ELSE
                  GRID = .TRUE.
                ENDIF
              ENDIF
C
C Unknown command qualifier
C
            ELSE
              CALL Mess(' Plot Error: Unknown command qualifier')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ENDDO
          IF (Xgrid_set .AND. Ygrid_set .AND. Xmin_set .AND.
     &        Ymin_set  .AND. Xmax_set  .AND. Ymax_set .AND.
     &        Xlen_set  .AND. Ylen_set) THEN
            IF (.NOT. Plot) THEN
              IF (.NOT. openW(uPlot,'fgu.plt','NEW',' ',' ',0)) THEN
                CALL Mess(' Plot Error: Cannot open plot file')
                Error = .TRUE.
                RETURN
              ENDIF
              Plot = .TRUE.
            ENDIF
            CALL PlotSetup(Xgrid,Ygrid,Xmin,Ymin,Xmax,Ymax,Xlen,Ylen,
     &                     Ref,CIRCLE,GRID,INTERP)
          ELSE
            CALL Mess(' Plot Error: Missing command qualifier(s)')
            Error = .TRUE.
            CALL Clear(Input)
          ENDIF
        ELSE
          CALL Mess(' Plot Error: Function not selected')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C
C
      SUBROUTINE SubmitCmd(Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input
      LOGICAL Getcmd
      IF (.NOT. Data) THEN
        CALL Mess(' Submit Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        IF (Plot) THEN
          CLOSE (UNIT=uPlot)
          CALL SysCmd('PRINT/NOFEED/QUEUE=GRAPHICS fgu.plt')
          Plot = .FALSE.
        ELSE
          CALL Mess(' Submit Error: Nothing to plot !')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE ExamineCmd(Input)
      IMPLICIT NONE
      CHARACTER*(*) Input,Output*80
      INTEGER I,J,K,L,M,BPno(4),Ptr
      INTEGER Strln,ParseBrkpt,Start(4),End(4),N(4),istat
      LOGICAL Getcmd
      EQUIVALENCE (I,N(1)),(J,N(2)),(K,N(3)),(L,N(4))
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      IF (.NOT. Data) THEN
        CALL Mess(' Examine Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        IF (Select) THEN
          istat = ParseBrkpt(Input,BPno)
          IF (istat .EQ. 0) THEN
            DO I = 1,4
              Start(I) = BPno(I)
              End(I) = BPno(I)
            ENDDO
          ELSEIF (istat .EQ. 1) THEN
            DO I = 1,4
              IF (BPno(I) .EQ. 0) THEN
                Start(I) = 1
                End(I) = Schedule_size(I)
              ELSE
                Start(I) = BPno(I)
                End(I) = BPno(I)
              ENDIF
            ENDDO
          ELSE
            CALL Mess(' Examine Error: Missing dimension specifier')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          DO L = Start(4),End(4)
            DO K = Start(3),End(3)
              DO J = Start(2),End(2)
                DO I = Start(1),End(1)
                  IF (Dimension .LE. 2) THEN
                    WRITE(Output,'(G12.5,5X,2(A,1X,G12.5,2X))')
     &              Fvalues(Ptr(I,J,K,L)),
     &              (Variable_name(1,M)(1:Strln(Variable_name(1,M)))//
     &              '=',Brkpt_Schedule(M,N(M)),M=1,Dimension)
                    CALL Mess(' '//Output)
                  ELSE
                    WRITE(Output,'(G12.5,5X,2(A,1X,G12.5,2X))')
     &              Fvalues(Ptr(I,J,K,L)),
     &              (Variable_name(1,M)(1:Strln(Variable_name(1,M)))//
     &              '=',Brkpt_Schedule(M,N(M)),M=1,2)
                    CALL Mess(' '//Output)
                    CALL Clear(Output)
                    WRITE(Output,'(17X,2(A,1X,G12.5,2X))')
     &              (Variable_name(1,M)(1:Strln(Variable_name(1,M)))//
     &              '=',Brkpt_Schedule(M,N(M)),M=3,Dimension)
                    CALL Mess(' '//Output)
                  ENDIF
                ENDDO
              ENDDO
            ENDDO
          ENDDO
        ELSE
          CALL Mess(' Examine Error: Function not selected')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE GraphCmd(Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      INTEGER I,J,K,L,Ptr,BPno(4),ParseBrkpt,Strln,N,Return_code,Ncurves
      INTEGER istat,fgu_gr,update_yarray
      INTEGER Start(4),End(4),Starting_page
      LOGICAL SearchList,Getcmd
      CHARACTER*(*) Input,Word*12
C
      CALL VAXC$CRTL_INIT
C
      IF (.NOT. Data) THEN
        CALL Mess(' Graph Error: Data file not loaded')
        Error = .TRUE.
      ELSE IF (.NOT. Select) THEN
        CALL Mess(' Graph Error: Function not selected')
        Error = .TRUE.
C
C Get curves to plot
C
      ELSE
        istat = ParseBrkpt(Input,BPno)
C
C Plot only some curves
C
        IF (istat .EQ. 0) THEN
          DO I = 1,4
            Start(I) = BPno(I)
            End(I) = BPno(I)
          ENDDO
C
C Plot all curves
C
        ELSEIF (istat .EQ. 1) THEN
          DO I = 1,4
            IF (BPno(I) .EQ. 0) THEN
              Start(I) = 1
              End(I) = Schedule_size(I)
            ELSE
              Start(I) = BPno(I)
              End(I) = BPno(I)
            ENDIF
          ENDDO
C
C Curves have not been specified properly
C
        ELSE
          CALL Mess(' Graph Error: Incorrect dimension specifier')
          Error = .TRUE.
          RETURN
        ENDIF
C
C Number of curves per screen
C
        Ncurves = End(2) - Start(2) + 1
C
C Pass function information to fgu_gr module.
C
        CALL Send_Function_Info
C
C Call plot routine
C
        Starting_page = Start(3) + (Start(4)-1) * Schedule_size(3)
C
        IF (Ncurves .GT. 1) THEN
          istat = fgu_gr(0,Starting_page)
        ELSE
          istat = fgu_gr(Start(2),Starting_page)
        ENDIF
C
C Error handling
C
        IF (istat .EQ. 32) RETURN     ! graphing is done
        IF (istat .EQ. 38) THEN       ! Edits were done
          Funit(Findex) = uScratch    ! Save function
          CALL SaveFunc
        ENDIF
        IF (istat .LE. 0) THEN
          IF (istat .EQ. 0) THEN
            CALL Mess(' Graph Error: Size of y-axis grid is too sm
     &all')
            Error = .TRUE.
          ELSEIF (istat .EQ. -1) THEN
            CALL Mess(' Graph Error: Size of x-axis grid is too sm
     &all')
            Error = .TRUE.
          ELSEIF (istat .EQ. -2) THEN
            CALL Mess(' Graph Error: Numbers for the y-axis grid d
     &o not fit on screen')
            Error = .TRUE.
          ELSEIF (istat .EQ. -3) THEN
            CALL Mess(' Graph Error: Numbers for the x-axis grid d
     &o not fit on screen')
            Error = .TRUE.
          ELSEIF (istat .EQ. -4) THEN
            CALL Mess(' Graph Error: Cannot calculate y-axis grid'
     &)
            Error = .TRUE.
          ELSEIF (istat .EQ. -5) THEN
            CALL Mess(' Graph Error: Cannot calculate x-axis grid'
     &)
            Error = .TRUE.
          ELSE
            CALL Mess(' Graph Error: Unknown error in fgu_gr')
            Error = .TRUE.
          ENDIF
          CALL ReadInput('Press RETURN to continue ...',Input)
        ENDIF
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE Send_Graph_Edits(Page,Curve,Yarray)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      REAL     Yarray(Maxbp)
      INTEGER  I
      INTEGER  Page,Curve,Point,Ptr,X_index,Y_index,Z_index,T_index
C
      DO I=1,Schedule_size(1)
        X_index = I
        Y_index = 1
        Z_index = 1
        T_index = 1
        IF (Dimension .GE. 2) THEN
          IF (Dimension .GE. 3) THEN
            IF (Dimension .EQ. 4) THEN
              T_index = INT(FLOAT(Page)/FLOAT(Schedule_Size(3)) + 0.999)
              Z_index = Page - (T_index-1)*Schedule_size(3)
            ELSE
              Z_index = Page
            ENDIF
          ENDIF
          Y_index = Curve
        ENDIF
        Fvalues(Ptr(X_index,Y_index,Z_index,T_index)) = Yarray(I)
      ENDDO
C
      Funit(Findex) = uScratch
C
      RETURN
      END
C
C****************************** Get_Menu_Label() ******************************
C    This subroutine is called from the C module 'fgu_lib.c'.  It retrieves
C the table name indicated by 'sub' from the Fortran array 'Flist'.
C
      SUBROUTINE   Get_Menu_Label(sub, String, sort)
      IMPLICIT     NONE
      INCLUDE      'finfo.inc'
      INTEGER      sub, sort
      CHARACTER*20 String
      CHARACTER*12 Flist(Maxfunc)
      INTEGER      FlistSize
C
      COMMON /FGU_LIB/  Flist, FlistSize
C
      IF (sort .NE. 0) THEN
         CALL ListFunc(' ',Flist,FlistSize)
         CALL ShellSort(Flist,FlistSize,12)
      ENDIF
      String = Flist(sub)
C
      RETURN
      END
C
C**************************** Select_Function() ******************************
C    This function is called from the C module 'fgu_gr.c'.  It calls the
C Fortran routines required to select the function indicated by 'table_no'.
C If the function cannot be selected 0 (FALSE) is returned.
C
      FUNCTION     Select_Function(table_no)
      IMPLICIT     NONE
      INCLUDE      'finfo.inc'
      INTEGER      Select_Function, table_no
      CHARACTER*12 Flist(Maxfunc)
      CHARACTER*80 Input
      INTEGER      FlistSize
C
      COMMON /FGU_LIB/  Flist, FlistSize
C
      IF (table_no .GT. FlistSize-1) THEN
         Select_Function = 0
      ELSE
         Select_Function = 1
      ENDIF
C
      Input = Flist(table_no+1)
      CALL SelectCmd(Input)
C
      CALL Send_Function_Info
C
      RETURN
      END
C
C************************** Send_Function_Info() ******************************
C    This function calls the C function 'receive_function_info' passing the
C required information about the current function back to GR.
C
      SUBROUTINE   Send_Function_info
      IMPLICIT     NONE
      INCLUDE      'finfo.inc'
      INTEGER      receive_function_info   ! C function
      INTEGER      I, N, istat, strln, ParseBrkpt
      INTEGER      Ncurves, Start(4), End(4), BPno(4)
      CHARACTER*20 Flabel,Xlabel,Ylabel,Zlabel,Tlabel
      CHARACTER*70 Title, Input/' '/
      LOGICAL      FnF
C
      istat = ParseBrkpt(Input,BPno)
C
      DO I = 1,4
        IF (BPno(I) .EQ. 0) THEN
          Start(I) = 1
          End(I) = Schedule_size(I)
        ELSE
          Start(I) = BPno(I)
          End(I) = BPno(I)
        ENDIF
      ENDDO
C
C Number of curves per screen
C
      Ncurves = End(2) - Start(2) + 1
C
C Create title
C
      CALL Clear(Title)
      IF (Dimension .EQ. 1) THEN
        Title =  Variable_name(1,0)(1:Strln(Variable_name(1,0)))
     &        // ' = f('
     &        // Variable_name(1,1)(1:Strln(Variable_name(1,1)))
     &        //')'//CHAR(0)
      ELSEIF (Dimension .EQ. 2) THEN
        Title =  Variable_name(1,0)(1:Strln(Variable_name(1,0)))
     &        // ' = f('
     &        // Variable_name(1,1)(1:Strln(Variable_name(1,1)))
     &        //','
     &        // Variable_name(1,2)(1:Strln(Variable_name(1,2)))
     &        //')'//CHAR(0)
      ELSEIF (Dimension .EQ. 3) THEN
        Title =  Variable_name(1,0)(1:Strln(Variable_name(1,0)))
     &        // ' = f('
     &        // Variable_name(1,1)(1:Strln(Variable_name(1,1)))
     &        //','
     &        // Variable_name(1,2)(1:Strln(Variable_name(1,2)))
     &        //','
     &        // Variable_name(1,3)(1:Strln(Variable_name(1,3)))
     &        //')'//CHAR(0)
       ELSEIF (Dimension .EQ. 4) THEN
        Title =  Variable_name(1,0)(1:Strln(Variable_name(1,0)))
     &        // ' = f('
     &        // Variable_name(1,1)(1:Strln(Variable_name(1,1)))
     &        //','
     &        // Variable_name(1,2)(1:Strln(Variable_name(1,2)))
     &        //','
     &        // Variable_name(1,3)(1:Strln(Variable_name(1,3)))
     &        //','
     &        // Variable_name(1,4)(1:Strln(Variable_name(1,4)))
     &        //')'//CHAR(0)
      ENDIF
C
C Create axis labels
C
      CALL Clear(Flabel)
      Flabel = Variable_name(1,0)(1:Strln(Variable_name(1,0)))//CHAR(0)
      CALL Clear(Xlabel)
      Xlabel = Variable_name(1,1)(1:Strln(Variable_name(1,1)))//CHAR(0)
C
C Display values for Z and T breakpoints
C
      CALL Clear(Ylabel)
      CALL Clear(Zlabel)
      CALL Clear(Tlabel)
      IF (Dimension .GT. 2) THEN
        Zlabel=Variable_name(1,3)(1:Strln(Variable_name(1,3)))
        N = Strln(Zlabel)
        WRITE(Zlabel(N+1:N+8),'(A,F7.2)',ERR=20) '=',
     &        Brkpt_schedule(3,Start(3))
20      CONTINUE
        Zlabel(N+9:) = CHAR(0)
        IF (Dimension .GT. 3) THEN
          Tlabel = Variable_name(1,4)
     &            (1:Strln(Variable_name(1,4)))
          N = Strln(Tlabel)
          WRITE(Tlabel(N+1:N+8),'(A,F7.2)',ERR=30) '=',
     &         Brkpt_schedule(4,Start(4))
30        CONTINUE
          Tlabel(N+9:) = CHAR(0)
        ELSE
          Tlabel = CHAR(0)
        ENDIF
      ELSE
        Zlabel = CHAR(0)
        Tlabel = CHAR(0)
      ENDIF
C
C Display the range of Y breakpoints
C
      IF (Dimension  .GT. 1) THEN
        Ylabel = Variable_name(1,2)
     &           (1:Strln(Variable_name(1,2)))
        N = Strln(Ylabel)
        IF (Ncurves .GT. 1) THEN
          WRITE(Ylabel(N+1:N+8),'(A)') '=ALL  '
        ELSE
          WRITE(Ylabel(N+1:N+8),'(A,F7.2)',ERR=10) '=',
     &                   Brkpt_schedule(2,Start(2))
10        CONTINUE
        ENDIF
        Ylabel(N+9:) = CHAR(0)
      ELSE
        Ylabel = CHAR(0)
      ENDIF
C
C Call C function sending information about current function.
C
      istat = receive_function_info(Schedule_size(1),
     &                              Schedule_size(2),
     &                              Schedule_size(3)*Schedule_size(4),
     &                              Nfunction,
     &                              Title,Flabel,Xlabel,
     &                              Ylabel,Zlabel,Tlabel,Function)
C
      RETURN
      END
C
C
      SUBROUTINE ModifyCmd(Input)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      LOGICAL Getcmd,SearchList,FindFunc
      INTEGER Dim,Size,List(Maxbp),istat,BreakptList(4,Maxbp),Count
      INTEGER ListSize(4),ReadSchedule,I,Index,L
      CHARACTER*10 List1(2),List2(4),List3(4),Word,Option,Input*(*)
      CHARACTER Table*12,Word2*12
      DATA List1/'BREAKPOINT','FUNCTION  '/
      DATA List2/'EXTRACT   ','MERGE     ','OFFSET    ','EVALUATE  '/
      DATA List3/'ADD       ','DELETE    ','USE       ','SCALE     '/
C
      IF (.NOT. Data) THEN
        CALL Mess(' Modify Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        CALL Clear(Word)
        CALL Clear(Option)
        IF (Getcmd(Word,Input)) THEN
          IF (SearchList(Word,10,List1,2)) THEN
C
C Modify a function table
C
            IF (Word .EQ. 'FUNCTION') THEN
              IF (Getcmd(Option,Input)) THEN
                IF (SearchList(Option,10,List2,4)) THEN
C
C Extract a portion of a function
C
                   IF (Option .EQ. 'EXTRACT') THEN
                     IF (.NOT. Select) THEN
                       CALL Mess(' Modify Error: Function not selected')
                       Error = .TRUE.
                       CALL Clear(Input)
                       RETURN
                     ENDIF
                     DO I = 1,4
                       ListSize(I) = 0
                     ENDDO
                     CALL Clear(Table)
                     CALL Clear(Word2)
                     Count = 0
C
C Get the new table name
C
                     IF (Getcmd(Table,Input)) THEN
                       IF (FindFunc(Table,Index)) THEN
                         CALL Mess(' Modify Error: Function table name a
     &lready exists')
                         Error = .TRUE.
                         CALL Clear(Input)
                         RETURN
                       ENDIF
                     ELSE
                       CALL Mess(' Modify Error: Missing table name for
     &function to extract')
                       Error = .TRUE.
                       RETURN
                     ENDIF
C
C Process an input of the form ".modify function extract vflaps=5,10,20
C vbeta=-10,0,20 vflaps=20"
C
                     istat = 0
                     DO WHILE (Getcmd(Word2,Input))
                       Count = Count + 1
                       DO WHILE (istat .EQ. 0)
                         istat = ReadSchedule(Word2,Input,Dim,List,Size)
                         IF (istat .EQ. 0) THEN
                           ListSize(Dim) = Size
                           DO I = 1,Size
                             BreakptList(Dim,I) = List(I)
                           ENDDO
                         ENDIF
                       ENDDO
                     ENDDO
C
C Error handling
C
                     IF (istat.GT.0 .AND. istat.LT.4) THEN
                       IF (istat .EQ. 1) THEN
                         CALL Mess(' Modify Error: Incorrect variable na
     &me')
                         Error = .TRUE.
                       ELSEIF (istat .EQ. 2) THEN
                         CALL Mess(' Modify Error: Cannot decode breakpo
     &int value or breakpoint non existent')
                         Error = .TRUE.
                       ELSEIF (istat .EQ. 3) THEN
                         CALL Mess(' Error in ExtractFunc: Missing break
     &point value')
                         Error = .TRUE.
                       ENDIF
                       CALL Clear(Input)
                       RETURN
                     ENDIF
C
C Use all breakpoints for schedules which were not specified
C
                     DO I = 1,4
                       IF (ListSize(I) .EQ. 0) THEN
                         ListSize(I) = Schedule_size(I)
                         DO L = 1,Schedule_size(I)
                           BreakptList(I,L) = L
                         ENDDO
                       ENDIF
                     ENDDO
                     IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
                     CALL ExtractFunc(ListSize,BreakptList,Table)
C
C Merge 2 or more functions
C
                   ELSEIF (Option .EQ. 'MERGE') THEN
                     CALL MergeFunc(Input)
C
C Add/subtract function tables together
C
                   ELSEIF (Option .EQ. 'EVALUATE') THEN
                     CALL EvaluateFunc(Input)
C
C Offset a function curve
C
                   ELSEIF (Option .EQ. 'OFFSET') THEN
                     IF (.NOT. Select) THEN
                       CALL Mess(' Modify Error: Function not selected')
                       Error = .TRUE.
                       CALL Clear(Input)
                     ELSE
                       CALL OffsetFunc(Input)
                     ENDIF
                   ENDIF
                ELSE
                  CALL Clear(Input)
                  CALL Mess(' Modify Error: Unknown option for MODIFY FU
     &NCTION command')
                  Error = .TRUE.
                ENDIF
              ELSE
                CALL Mess(' Modify Error: Missing option for MODIFY FUNC
     &TION command')
                Error = .TRUE.
              ENDIF
C
C Modify a breakpoint schedule
C
            ELSEIF (Word .EQ. 'BREAKPOINT') THEN
              IF (.NOT. Select) THEN
                CALL Mess(' Modify Error: Function not selected')
                Error = .TRUE.
                CALL Clear(Input)
              ELSE
                IF (Getcmd(Option,Input)) THEN
                  IF (SearchList(Option,10,List3,4)) THEN
C
C Add a breakpoint
C
                    IF (Option .EQ. 'ADD') THEN
                      CALL ModBreakpt(Input,'ADD')
C
C Delete a breakpoint
C
                    ELSEIF (Option .EQ. 'DELETE') THEN
                      CALL ModBreakpt(Input,'DELETE')
C
C Use the specified breakpoint schedule
C
                    ELSEIF (Option .EQ. 'USE') THEN
                      CALL ModBreakpt(Input,'USE')
C
C Scale the breakpoints by a value but do not alter the function values
C
                    ELSEIF (Option .EQ. 'SCALE') THEN
                      CALL ModBreakpt(Input,'SCALE')
C
                    ENDIF
                  ELSE
                    CALL Clear(Input)
                    CALL Mess(' Modify Error: Unknown option for MODIFY
     &BREAKPOINT command')
                    Error = .TRUE.
                  ENDIF
                ELSE
                  CALL Mess(' Modify Error: Missing option for MODIFY BR
     &EAKPOINT command')
                  Error = .TRUE.
                ENDIF
              ENDIF
            ENDIF
          ELSE
            CALL Mess(' Modify Error: Unknown command qualifier')
            Error = .TRUE.
            CALL Clear(Input)
          ENDIF
        ELSE
          CALL Mess(' Modify Error: Missing command qualifier')
          Error = .TRUE.
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
      SUBROUTINE DeleteCmd(Input,Cmd)
      IMPLICIT NONE
      CHARACTER*(*) Input,Cmd,Tname*12
      INTEGER Strln,Index,Count,I
      LOGICAL Getcmd,FindFunc,All
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      Count = 0
      All = .FALSE.
      IF (.NOT. Data) THEN
        CALL Mess(' Delete/Undelete Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        DO WHILE (Getcmd(Tname,Input))
          IF (FindFunc(Tname,Index)) THEN
            IF (Cmd .EQ. 'DELETE') THEN
              Fdelete(Index) = .TRUE.
            ELSE
              Fdelete(Index) = .FALSE.
            ENDIF
            Count = Count + 1
          ELSEIF (Tname .EQ. 'ALL') THEN
            All = .TRUE.
            IF (Cmd .EQ. 'DELETE') THEN
              DO I = 1,Nfunction
                Fdelete(I) = .TRUE.
              ENDDO
            ELSE
              DO I = 1,Nfunction
                Fdelete(I) = .FALSE.
              ENDDO
            ENDIF
          ELSE
            CALL Mess(' Delete/Undelete Error: Function not found <'//
     &                  Tname(1:Strln(Tname))//'>')
            Error = .TRUE.
          ENDIF
        ENDDO
C
        IF (Count.EQ.0 .AND. .NOT.All) THEN
          CALL Mess(' Delete/Undelete Error: Missing function name(s)')
          Error = .TRUE.
        ENDIF
      ENDIF
      RETURN
      END
C
C===========================================================================
C The subroutine ModBreakpt modifies breakpoint schedules. It can add or
C delete breakpoints from a schedule.
C===========================================================================
C
      SUBROUTINE ModBreakpt(Input,Option)
      IMPLICIT NONE
      REAL Tolperc
      PARAMETER (TolPerc = 0.1)
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input,Option
      CHARACTER Word*14,List*14/'EXTRAPOLATE   '/
      CHARACTER Dimspec(4)*1/'X','Y','Z','T'/,Dimen*1
      INTEGER I,Strln,istat,ParseBrkpt,BPno(4),Dim,Size
      INTEGER Dimndx,Status,ReadReal
      LOGICAL Getcmd,SearchList,Extrapolate
      REAL Schedule(Maxbp),Value,Err,Scale_factor
      Extrapolate = .FALSE.
C
      IF (Option .EQ. 'ADD') THEN
C
C Get the variable name of the breakpoint schedule
C
        IF (Getcmd(Word,Input)) THEN
          CALL Upper(Word)
          I = 1
          DO WHILE (Word.NE.Variable_name(1,I) .AND. I.LE.Dimension)
            I = I + 1
          ENDDO
        ELSE
          CALL Mess(' Modify Error: Missing variable name')
          Error = .TRUE.
          RETURN
        ENDIF
C
C Incorrect variable name
C
        IF (I .EQ. Dimension+1) THEN
          CALL Mess(' Modify Error: Incorrect variable name')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ELSE
          Dim = I
        ENDIF
C
C Get new breakpoint value
C
        IF (Getcmd(Word,Input)) THEN
          IF (ReadReal(Word,Value) .EQ. 0) THEN
            CALL Mess(' Modify Error: Cannot decode breakpoint value')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ELSE
          CALL Mess(' Modify Error: Missing breakpoint value')
          Error = .TRUE.
          RETURN
        ENDIF
C
C Check that new breakpoint value doesn't already exist
C
        DO I = 1,Schedule_size(Dim)
          Err = (Brkpt_schedule(Dim,I) - Value)
          IF (Brkpt_schedule(Dim,I) .NE. 0.0) THEN
            Err = Err / Brkpt_schedule(Dim,I)
          ENDIF
          IF (ABS(Err)*100 .LT. TolPerc) THEN
            CALL Mess(' Modify Error: Breakpoint value already exists')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ENDDO
C
C Get extrapolate option (if specified)
C
        IF (Getcmd(Word,Input)) THEN
          IF (SearchList(Word,14,List,1)) THEN
            IF (Word .EQ. 'EXTRAPOLATE') THEN
              Extrapolate = .TRUE.
            ENDIF
          ELSE
            CALL Mess(' Modify Error: Unknown command option')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ENDIF
C
C Add the breakpoint
C
        CALL AddBreakpt(Dim,Value,Extrapolate,Status)
        IF (Status .EQ. 0) Funit(Findex) = uScratch
C
C Delete a breakpoint
C
      ELSEIF (Option .EQ. 'DELETE') THEN
        istat = ParseBrkpt(Input,BPno)
C
        IF (istat .EQ. 2) THEN
          CALL Mess(' Modify Error: Incorrect dimension specifier')
          Error = .TRUE.
          RETURN
        ENDIF
C
        CALL DelBreakpt(BPno,Status)
        IF (Status .EQ. 0) Funit(Findex) = uScratch
C
C Use the specified breakpoint schedule
C
      ELSEIF (Option .EQ. 'USE') THEN
C
C Get the variable name of the breakpoint schedule
C
        IF (Getcmd(Word,Input)) THEN
          CALL Upper(Word)
          I = 1
          DO WHILE (Word.NE.Variable_name(1,I) .AND. I.LE.Dimension)
            I = I + 1
          ENDDO
        ELSE
          CALL Mess(' Modify Error: Missing variable name')
          Error = .TRUE.
          RETURN
        ENDIF
C
C Incorrect variable name
C
        IF (I .EQ. Dimension+1) THEN
          CALL Mess(' Modify Error: Incorrect variable name')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ELSE
          Dim = I
        ENDIF
C
C Get breakpoint values
C
        CALL Clear(Word)
        Size = 0
        DO WHILE (Getcmd(Word,Input))
          IF (ReadReal(Word,Value) .EQ. 0) THEN
C
C Get extrapolate option (if specified)
C
CRW
C Extrapolating is needed when the 'USE' option is performed on a linear
C function (straight line).  Without the use of 'EXTRAPOLATE' the first and
C last breakpoints may be removed resulting with an incorrect angle at the
C begining and end of the function.
CRW
C
            IF (SearchList(Word,14,List,1)) THEN
              IF (Word .EQ. 'EXTRAPOLATE') THEN
                Extrapolate = .TRUE.
              ENDIF
            ELSE
              CALL Mess(' Modify Error: Cannot decode breakpoint value')
              CALL Mess(' Modify Error: Unknown command option')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ENDIF
          Size = Size + 1
          IF (Size .GT. Maxbp) THEN
            CALL Mess(' Error in ModBreakpt: Breakpoint schedule size
     &too large')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          Schedule(Size) = Value
        ENDDO
C
C Check that the schedule has at least 2 values
C
        IF (Size .LT. 2) THEN
          CALL Mess(' Modify Error: Specified schedule must have at leas
     &t 2 breakpoints')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Check that schedule is in ascending order
C
        DO I = 1,Size-1
          IF (Schedule(I) .GT. Schedule(I+1)) THEN
            CALL Mess(' Error in ModBreakpt: Breakpoint schedule is not
     &in ascending order')
            Error = .TRUE.
            RETURN
          ENDIF
        ENDDO
C
C Modify the schedule
C
        CALL SetSchedule(Dim,Size,Schedule,Extrapolate,Status)
        IF (Status .EQ. 0) Funit(Findex) = uScratch
C
C Scale a breakpoint schedule by a constant
C
      ELSEIF (Option .EQ. 'SCALE') THEN
        IF (Getcmd(Dimen,Input)) THEN
          CALL Upper(Dimen)
          Dimndx = 0
          DO I = 1,Dimension
            IF (Dimen .EQ. Dimspec(I)) Dimndx = I
          ENDDO
          IF (Dimndx .EQ. 0) THEN
            CALL Mess
     &           (' Error in ModBreakpt: Incorrect dimension specifier')
            Error = .TRUE.
            CALL Clear(Input)
          ELSE
            IF (Getcmd(Word,Input)) THEN
              IF (ReadReal(Word,Scale_factor) .EQ. 0) THEN
                CALL Mess
     &              (' Error in ModBreakpt: Cannot decode scale factor')
                Error = .TRUE.
              ELSE
                DO I = 1,Schedule_size(Dimndx)
                  Brkpt_schedule(Dimndx,I) = Brkpt_schedule(Dimndx,I)
     &                                     * Scale_factor
                ENDDO
              ENDIF
            ELSE
              CALL Mess(' Error in ModBreakpt: Missing scale factor')
              Error = .TRUE.
            ENDIF
          ENDIF
        ELSE
          CALL Mess(' Error in ModBreakpt: Missing dimension specifier')
          Error = .TRUE.
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
      SUBROUTINE ConvertCmd(Input)
      IMPLICIT NONE
      INTEGER Nformat
      PARAMETER (Nformat = 6)
      INCLUDE 'inout.inc'
      LOGICAL SearchList,Getcmd
      CHARACTER Formats(Nformat)*12,Format*12,Input*(*)
      CHARACTER  Infile*30/'                              '/
      CHARACTER Outfile*30/'                              '/
      CHARACTER Auxfile*30/'                              '/
      DATA Formats/
     &     'AEROSPATIALE','BOEING      ','DOUGLAS     ','FOKKER      '
     &,    'SAAB        ','CANADAIR    '/
C
C Make sure data file is not loaded
C
      IF (Data) THEN
        CALL Mess(' Convert Error: Data file already loaded')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
C
C Get the file type based on manufacturer
C
      ELSE
        IF (.NOT. Getcmd(Format,Input)) THEN
          CALL Mess(' Convert Error: File type not specified')
          Error = .TRUE.
          RETURN
        ENDIF
C
C Aerospatiale data format
C
        IF (SearchList(Format,14,Formats,Nformat)) THEN
          IF (Format .EQ. 'AEROSPATIALE') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
C
C Boeing data format
C
          ELSEIF (Format .EQ. 'BOEING') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
C
C Fokker data format
C
          ELSEIF (Format .EQ. 'FOKKER') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
C
C Douglas data format
C
          ELSEIF (Format .EQ. 'DOUGLAS') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
C
C SAAB data format
C
          ELSEIF (Format .EQ. 'SAAB') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
C
C Canadair data format
C
          ELSEIF (Format .EQ. 'CANADAIR') THEN
            IF (Getcmd(Infile,Input)) THEN
              IF (Getcmd(Outfile,Input)) THEN
                IF (Getcmd(Auxfile,Input)) THEN
                  CONTINUE
                ELSE
                  CALL Mess(' Convert Error: Missing map file name')
                  Error = .TRUE.
                  RETURN
                ENDIF
              ELSE
                CALL Mess(' Convert Error: Missing output file name')
                Error = .TRUE.
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Convert Error: Missing input file name')
              Error = .TRUE.
              RETURN
            ENDIF
          ENDIF
C
C Unknown data format
C
        ELSE
          CALL Mess(' Convert Error: Unknown data format')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
      SUBROUTINE CommonizeCmd(Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      CHARACTER*11 Options(2)/'BREAKPOINTS','SCHEDULES  '/,Option
      CHARACTER Input*(*)
      LOGICAL Getcmd,SearchList
C
C Make sure data file is loaded
C
      IF (.NOT.Data) THEN
        CALL Mess(' Commonize Error: Data file not loaded')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Commonize schedule names or breakpoints ?
C
      IF (Getcmd(Option,Input)) THEN
        IF (SearchList(Option,11,Options,2)) THEN
          IF (Option .EQ. 'SCHEDULES') THEN
            CALL CommonSchedName
          ELSEIF (Option .EQ. 'BREAKPOINTS') THEN
            CALL CommonBreakpt(Input)
          ENDIF
        ELSE
          CALL Mess(' Commonize Error: Unknown command option')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Missing command option
C
      ELSE
        CALL Mess(' Commonize Error: Missing command option')
        Error = .TRUE.
        RETURN
      ENDIF
C
      RETURN
      END
