      INTEGER Maxfunc,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>_Maxbp,<PERSON>gen_Maxval
C
      PARAMETER  (Maxfunc = 1000)  ! Maximum number of function names
      PARAMETER  (Maxval  = 80000) ! Max array capacity for function values
      PARAMETER  (Maxbp   = 2000)  ! Max array capacity for breakpt schedule size
      PARAMETER  (Maxmult = 20)    ! Maximum multiplicity
      PARAMETER  (Maxvar  = 100)   ! Maximum number of independent variables
      PARAMETER  (Fgen_Maxbp  = 150)   ! Fgen limit for a schedule size
      PARAMETER  (Fgen_Maxval = 15000) ! Fgen limit for number of func values
C
      INTEGER
     &     Nfunction         ! Total number of functions
     &,    Nfuncp            ! Number of functions first loaded
     &,    Dimension         ! Dimension of selected function
     &,    Mult              ! Multiplicity of selected function
     &,    Schedule_size(4)  ! Breakpoint schedule sizes for selected function
     &,    Nvalues           ! Number of function values
     &,    Findex            ! Index of selected function
     &,    NextRec           ! Next record in file
     &,    Nrecords          ! Number of records in random access data file
     &,    Fdim(Maxfunc)     ! Function dimensions
     &,    Fmult(Maxfunc)    ! Function multiplicities
     &,    Frec(Maxfunc)     ! Function record index
     &,    Funit(Maxfunc)    ! Function unit number
C
      CHARACTER 
     &     Class*2                  ! Class of selected function
     &,    Fclass(Maxfunc)*2        ! Function classes
     &,    Zone*2                   ! Zone of selected function
     &,    Fzone(Maxfunc)*2         ! Function zones
     &,    Function*12              ! Selected function name
     &,    Fname(Maxfunc)*12        ! Function names 
     &,    Schedule_name(4)*12      ! Schedule names of selected function
     &,    Variable_name(Maxmult,0:4)*12 ! Variable names of selected function
C
      REAL
     &     Brkpt_schedule(4,Maxbp)  ! Breakpoint schedules of selected function
     &,    Fvalues(Maxval)          ! Function values
     &,    Ftemp(Maxval)            ! Temporary storage for function values
C
      LOGICAL
     &     Select                   ! A valid function has been selected
     &,    Fdelete(Maxfunc)         ! Function marked for deletion
C
      COMMON /Finfo/ Function,Nfunction,Dimension,Fname,Select,Findex,
     &               Mult,Schedule_name,Variable_name,Class,Zone,
     &               Brkpt_schedule,Schedule_size,Nvalues,Fvalues,
     &               NextRec,Fmult,Fzone,Fdim,Fclass,Fdelete,Frec,
     &               Funit,Nrecords,Ftemp,Nfuncp
