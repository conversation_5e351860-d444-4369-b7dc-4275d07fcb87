/************************************************************************/
/*									*/
/*			INCLUDE FILE LOG_NAME 				*/
/*									*/
/* Author : Pierre <PERSON> 						*/
/* Date :   April 1991 							*/
/*									*/
/* Logical names definitions						*/
/************************************************************************/

#ifdef VAXHOST
#  define OVP_C_DIR	"OVP$CTS"
#  define OVP_M_DIR	"OVP$MASTER"

#  define VSA_C_DIR	"VISA$CTS"
#  define VSA_M_DIR	"VISA$MASTER"

#  define CMP_VIS_DIR	"CMP$VISFILE"
#  define CMP_BVS_DIR	"CMP$BVSFILE"

#  define OWNER		"OVP$SIMOWNER"
#  define PRINTER	"VSA$PRINTER"

#  define REF_DATA      "OVP$REFDATA"
#  define SIM_DATA      "OVP$SIMDATA"
#endif


#ifdef UNIXHOST
#  define OVP_C_DIR	"ovp_cts"
#  define OVP_M_DIR	"ovp_master"

#  define VSA_C_DIR	"visa_cts"
#  define VSA_M_DIR	"visa_master"

#  define CMP_VIS_DIR	"cmp_visfile"
#  define CMP_BVS_DIR	"cmp_bvsfile"

#  define OWNER		"ovp_simowner"
#  define PRINTER	"vsa_printer"

#  define REF_DATA      "ovp_refdata"
#  define SIM_DATA      "ovp_simdata"

#  define VSA_HPQ       "vsa_hpq"
#endif
