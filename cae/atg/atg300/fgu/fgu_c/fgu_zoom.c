/***********************************************************************/
/*    Author: <PERSON> - <PERSON>. 24                                */
/*    Date:   92/05/27                                                 */
/*    File:   fgu_zoom.c                                               */
/*    Decription:                                                      */
/*                                                                     */
/*    Revision history:                                                */
/***********************************************************************/
#include "ovp_prec.h"
#ifdef VAXHOST
#   include <descrip.h>
#   include <ssdef.h>
#endif
#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include <math.h>
#include <stdlib.h>
#include "ovp.h"
#include "graph.h"
#include "ovp_stru.h"
#include "screen.h"
#include "fgu_extern.h"  /*** define external variables and functions. ***/

/***** aline_overlay() global variables: *****/
float zoom_area_xmin, zoom_area_xmax, zoom_area_ymin, zoom_area_ymax,
      zoom_area_xsections, zoom_area_ysections;
short int init_zoom;
Ipt cursor;

/*************************** zoom_prompt() ***********************************/
int zoom_prompt(void)
{
int enter_zoom(void);
int plot_zoom(void);
   int letter;
   static short int option;

   clear_line(23,65);
   change_color(&text_color);

   letter = 0;

   if (zoom_status) {
      printxy(21,23,"Use auto scaling? (y/n)",2);
      while ((letter=readkey())!=313 && letter!=89 && letter!=121 &&
              letter!=78 && letter!=110);
      if (letter==89 || letter==121) { /* Was y or Y pressed? */
         zoom_status=0;
         letter=refresh_graph(0);
         return(letter);
      }
      else
         clear_line(23,65);
   }

   while (letter!=314 && letter!=13 && letter!=313) {
      if (option) {
         printxy(33,23,"PLOT_ZOOM",2);
         printxy(22,23,"ENTER_ZOOM",1);
      }
      else {
         printxy(22,23,"ENTER_ZOOM",2);
         printxy(33,23,"PLOT_ZOOM",1);
      }
      letter=readkey();
      if (letter==277 || letter==276) {
         if (option)
            option=0;
         else
            option=1;
      }
   }

   if (letter==313) {
      clear_line(23,65);
#     ifdef UNIXHOST
         fflush(stdout);
#     endif
   }
   else if (option)
      enter_zoom();
   else
      plot_zoom();
}

/**************************** plot_zoom() ************************************/
int plot_zoom(void)
{
void show_position(int x_pix, int y_pix);
int draw_zoom(void);
extern int do_autoscale(short int fix_graph);
   extern float xmax, xmin, ymax, ymin; /* From gr.c */
   Pts line;
   Pt  gr_cursor, zoom_corner[2];
   float grid_step_x, grid_step_y, num;
   short int loop=0;
   int i, j, k, l, status;

   clear_line(23,65);

/* Start the crosshair cursor in the middle of the graph.
   gr_cursor.x = (LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/2+UP_LEFT_CORNER_X;
   gr_cursor.y = (UP_LEFT_CORNER_Y-LO_RIGHT_CORNER_Y)/2+LO_RIGHT_CORNER_Y;
*/
/* Start the crosshair cursor on the upper left corner of the graph. */
   gr_cursor.x = UP_LEFT_CORNER_X;
   gr_cursor.y = UP_LEFT_CORNER_Y;

/* Set the crosshair cursor stepping with the coarse grids. */
   grid_step_x = (x_pixel(new_xmax)-x_pixel(new_xmin))/
                 ((new_xmax-new_xmin)/x_coarse_g);
   grid_step_y = (y_pixel(new_ymax)-y_pixel(new_ymin))/
                 ((new_ymax-new_ymin)/y_coarse_g);

   graphics_on();
   change_color(&text_color);
   clear_line(23,65);
   printxy(37,23,"Plot 1st corner of zoom area.",0);

   status=0;
   while (status!=313 && loop<2) { /* Loop while Remove key not pressed. */

      /* Draw the crosshair cursor. */
      show_position((int)gr_cursor.x,(int)gr_cursor.y);
      graphics_on();
      start_pixel((int)gr_cursor.x,(int)gr_cursor.y);
      vtgcurseon();

      status=readkey();  /* Read a key stroke. */
      /* Return, Select or Insert key pressed? */
      if (status==13 || status==314 || status==312) {
         if (zoom_corner[0].x != gr_cursor.x &&
             zoom_corner[0].y != gr_cursor.y) {
            clear_line(23,65);
            if (loop==0)
               printxy(37,23,"Plot 2nd corner of zoom area.",0);
            graphics_on();
            change_color(&text_color);
            if (gr_cursor.x>=0 && gr_cursor.x<=659) {
               start_pixel((int)gr_cursor.x, 42);
               draw_line((int)gr_cursor.x, 479);
            }
            if (gr_cursor.y>=0 && gr_cursor.y<=479) {
               start_pixel(0, (int)gr_cursor.y);
               draw_line(659, (int)gr_cursor.y);
            }
            change_color(&curve_color);
            zoom_corner[loop].x = gr_cursor.x;
            zoom_corner[loop].y = gr_cursor.y;
            loop++;
         }
      }
      if (status==277)      /* Right arrow key pressed? */
         gr_cursor.x += grid_step_x;
      else if (status==276) /* Left arrow key pressed? */
         gr_cursor.x -= grid_step_x;
      else if (status==274) /* Up arrow key pressed? */
         gr_cursor.y += grid_step_y;
      else if (status==275) /* Down arrow key pressed? */
         gr_cursor.y -= grid_step_y;
     /* Number pressed, or +, - pressed? */
      else if ((status>=48 && status<=57) || 
               status=='+' || status=='=' || status=='-' || status=='_') {
        /* Pressing a number divides stepping speed by the number pressed. */
        /* Pressing the '+' key multiplies the stepping speed by 2.        */
        /* Pressing the '-' key divides the stepping speed by 2.           */
         if (status>=48 && status<=57) {
           /* Set the crosshair cursor stepping with the coarse grids. */
            grid_step_x = (x_pixel(new_xmax)-x_pixel(new_xmin))/
                          ((new_xmax-new_xmin)/x_coarse_g);
            grid_step_y = (y_pixel(new_ymax)-y_pixel(new_ymin))/
                          ((new_ymax-new_ymin)/y_coarse_g);
            if (status==48) /* Zero pressed? */
               status = 58;
            grid_step_x = grid_step_x / (status-48);
            grid_step_y = grid_step_y / (status-48);
         }
         else if (status=='+' || status=='=') {
            grid_step_x = grid_step_x*2;
            grid_step_y = grid_step_y*2;
         }
         else {
            grid_step_x = grid_step_x/2;
            grid_step_y = grid_step_y/2;
         }
         if (grid_step_x <= 0) /* Stepping cannot be less than one pixel. */
            grid_step_x = 1;
         if (grid_step_y <= 0)
            grid_step_y = 1;
      }
   }

   if (status != 313) {
     /* Autoscale the zoom values. */
      zoom_status = 2;
      if (y_value(zoom_corner[0].y) > y_value(zoom_corner[1].y)) {
         symax = y_value(zoom_corner[0].y);
         symin = y_value(zoom_corner[1].y);
      }
      else {
         symin = y_value(zoom_corner[0].y);
         symax = y_value(zoom_corner[1].y);
      }
      if (x_value(zoom_corner[0].x) > x_value(zoom_corner[1].x)) {
         sxmax = x_value(zoom_corner[0].x);
         sxmin = x_value(zoom_corner[1].x);
      }
      else {
         sxmin = x_value(zoom_corner[0].x);
         sxmax = x_value(zoom_corner[1].x);
      }
      sygrid = (symax-symin)/4;
      sxgrid = (sxmax-sxmin)/4;
      graphics_on();
      vtgcurseoff();
      draw_zoom();
   }
   else
      clear_line(23,65);
   graphics_on();
   vtgcurseoff();
}

/****************************** show_position() *******************************
     Displays the X and Y values where the crosshair cursor is currently 
located.
*/
void show_position(int x_pix, int y_pix)
{
   fprintf(stdout,"\033[23;2HX=%-5.6f Y=%-5.6f       \033[1;1H",
           x_value((float)x_pix), y_value((float)y_pix));
}

/**************************** enter_zoom() ***********************************/
int enter_zoom(void)
{
int draw_zoom(void);
   char float_str[13] = {"            "}, result[30];
   int length, menu_item=1, j, letter=1;

   clear_line(23,65);

   cursoron();
   length = 12;    /* Maximum length of string that will be accepted */

   while (menu_item!=7) {
      switch(menu_item) {
         case 1:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter minimum x-axis value: ",2);
         sprintf (float_str,"%-12f",sxmin);
         /* Print current value for this string */
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&sxmin);
         if (letter==274)      /* Was UP arrow pressed? */
            menu_item = 6;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 2;
         break;

         case 2:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter maximum x-axis value: ",2);
         sprintf (float_str,"%-12f",sxmax);
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&sxmax);
         if (letter==274)     /* Was UP arrow pressed? */
            menu_item = 1;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 3;
         break;

         case 3:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter x-axis coarse grid  : ",2);
         sprintf (float_str,"%-12f",sxgrid);
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&sxgrid);
         if (letter==274)     /* Was UP arrow pressed? */
            menu_item = 2;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 4;
         break;

         case 4:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter minimum y-axis value: ",2);
         sprintf (float_str,"%-12f",symin);
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&symin);
         if (letter==274)     /* Was UP arrow pressed? */
            menu_item = 3;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 5;
         break;

         case 5:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter maximum y-axis value: ",2);
         sprintf (float_str,"%-12f",symax);
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&symax);
         if (letter==274)     /* Was UP arrow pressed? */
            menu_item = 4;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 6;
         break;

         case 6:
         cursor.x = 40;  /* Corrdinates where cursor will appear */
         cursor.y = 23;
         printxy(12,23,"Enter y-axis coarse grid  : ",2);
         sprintf (float_str,"%-12f",sygrid);
         printxy(cursor.x,cursor.y,float_str,1); 
         read_value(cursor,"NUMERIC",length,result,&letter);
         sscanf(result,"%f",&sygrid);
         if (letter==274)     /* Was UP arrow pressed? */
            menu_item = 5;
         else if (letter==313) /* Was Remove key pressed? */
            menu_item = 7;    /* Yes, exit while loop.   */
         else
            menu_item = 7;
         break;
      }
   }
   clear_line(23,65);

   cursoroff();
   if (letter!=313) {
      /* Start validating values. */
      if (sxmin >= sxmax)
         printxy(15,23,"X-axis min/max values are incorrect.",2);
      else if (symin >= symax)
         printxy(15,23,"Y-axis min/max values are incorrect.",2);
      else if ((sxmax-sxmin)/sxgrid < 2)
         printxy(8,23,"X-axis coarse grid too great or divsion not even.",2);
      else if ((symax-symin)/sygrid < 2)
         printxy(8,23,"Y-axis coarse grid too great or divsion not even.",2);
      else if ((sxmax-sxmin)/sxgrid > 24)
         printxy(8,23,"X-axis coarse grid too small or divsion not even.",2);
      else if ((symax-symin)/sygrid > 24)
         printxy(8,23,"Y-axis coarse grid too small or divsion not even.",2);
      else {
         zoom_status=1;   /* Set zoom_status flag to TRUE. */
         draw_zoom();
      }
   }
#  ifdef UNIXHOST
      fflush(stdout);
#  endif
}

/**************************** draw_zoom() ************************************/
int draw_zoom(void)
{
   extern short int both_have_master, both_have_cts;
   short int i;
   short int current_menu;
   int letter;

   init_zoom = 1;

   new_xmin=sxmin;
   new_xmax=sxmax;
   x_coarse_g=sxgrid;
   x_sections=(sxmax-sxmin)/sxgrid;

   new_ymin=symin;
   new_ymax=symax;
   y_coarse_g=sygrid;
   y_sections=(symax-symin)/sygrid;

   letter=refresh_graph(0);
#  ifdef UNIXHOST
      fflush(stdout);
#  endif
   return(letter);
}

/*************************** aline_zoom() ************************************/
void aline_zoom(void)
{
/*   extern short int xzoom_setup, yzoom_setup;*/ /* From gr_setup.c */
   short int xzoom_setup=0, yzoom_setup=0;
   float hold_xmin, hold_xmax, hold_xgrid;
   float hold_ymin, hold_ymax, hold_ygrid;
   short int i;

   if (init_zoom) {
      if (xzoom_setup) { /************** Is zoom on x-axis relative? */
         zoom_area_xmin = x_pixel(sxmin); /* Yes */
         zoom_area_xmax = x_pixel(sxmax);
      }
      zoom_area_xsections = (sxmax - sxmin)/sxgrid;
      if (yzoom_setup) { /************** Is zoom on y-axis relative? */
         zoom_area_ymin = y_pixel(symin); /* Yes */
         zoom_area_ymax = y_pixel(symax);
      }
      zoom_area_ysections = (symax - symin)/sygrid;
      init_zoom = 0;
   }

   if (xzoom_setup) {
      hold_xmin = x_value(zoom_area_xmin);
      hold_xmax = x_value(zoom_area_xmax);
      hold_xgrid = (hold_xmax - hold_xmin) / zoom_area_xsections;
   }
   else
      hold_xgrid = (sxmax - sxmin) / zoom_area_xsections;

   if (yzoom_setup) {
      hold_ymin = y_value(zoom_area_ymin);
      hold_ymax = y_value(zoom_area_ymax);
      hold_ygrid = (hold_ymax - hold_ymin) / zoom_area_ysections;
   }
   else
      hold_ygrid = (symax - symin) / zoom_area_ysections;

   if (xzoom_setup) {
      new_xmin = hold_xmin;
      new_xmax = hold_xmax;
   }
   else {
      new_xmin = sxmin;
      new_xmax = sxmax;
   }
   x_coarse_g = hold_xgrid;
   x_sections = zoom_area_xsections;

   if (yzoom_setup) {
      new_ymin = hold_ymin;
      new_ymax = hold_ymax;
   }
   else {
      new_ymin = symin;
      new_ymax = symax;
   }
   y_coarse_g = hold_ygrid;
   y_sections = zoom_area_ysections;

/* Calculate the number of decimal places that will be shown on axis: */
   get_xy_decimal();
}
