	PROGRAM         readkey
        IMPLICIT        NONE
	CHARACTER*10	buff,keys
	INTEGER*4	len,status,nkeys/0/

        CALL READUT(buff,len,status,nkeys,keys)
        RETURN
        END
C.......................................................................C
C									C
C	This routine performs one byte reads according to		C
C  predefined keys. If such keys are not found in the first		C
C  byte, normal read are then continued. Predefined keys, can		C
C  be keys wich denoted return from read if the first character		C
C  read is one of the keys or, it can denote return read if the		C
C  the first character read is not one of the keys			C
C									C
C	Calling convention:						C
C									C
C		CALL READUT (buff,len,status,nkeys,keys)		C
C									C
C	buff = CHARACTER*(*)	user's buffer area			C
C	len  = INTEGER*4	number of bytes read			C
C	status=INTEGER*4	returned status				C
C					0 = completed			C
C					1 = end of file			C
C	nkeys= INTEGER*4	number of predefined keys		C
C				0 if no predefined keys provides;	C
C				returned only on <CR>			C
C	keys = CHARACTER*(*)	The predefined keys			C
C				(optional if nkeys = 0)			C
C									C
C.......................................................................C

	SUBROUTINE readut(buff,len,status,nkeys,keys)
	CHARACTER*(*)	buff,keys
	INTEGER*4	len,status,nkeys

	CHARACTER*1	ch		! character read
	CHARACTER*1	GETKEY		! Function
	INTEGER*4	INDEX		! Function

	ch = CHAR(0)
	len = 0
	DO WHILE((ch .NE. CHAR(13)) .AND. .NOT.
     .	   ((INDEX(keys(:nkeys),ch).NE.0) .AND. (len.EQ.1)))
		ch = GETKEY()
		IF (.NOT.( (len.GE.1) .AND. (ch.EQ.CHAR(13)) )) THEN
			IF(ch .EQ. CHAR(127)) THEN
				buff(len:len) = ' '
				len = len -1
			WRITE(6,'(A,$)') CHAR(8)//CHAR(27)//'[1P'
				CALL FFLUSH(6)
			ELSE
				len = len + 1
				buff(len:len) = ch
				WRITE(6,'(A,$)') ch
				CALL FFLUSH(6)
			ENDIF
		ENDIF
	END DO
	CALL RESTORE()
	RETURN
	END
