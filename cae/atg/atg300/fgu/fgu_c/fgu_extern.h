#define cls()              printf("\033[2J")
#define cursoron()         printf("\033[?25h")
#define cursoroff()        printf("\033[?25l")
/************************ extern functions for rk.c **************************/
extern int readkey();
/********************* extern functions for vt240_new.c **********************/
extern void start_pixel(int x, int y);
extern void draw_line(int x, int y);
extern void graphics_on(void);
extern void graphics_off(void);
extern void change_color(HLS_color *foreground);
extern void set_line_pattern(char *pattern);
extern void set_font_size(char *size);
extern void pixel_printxy(int xpix,int ypix,char *string);
/****************** extern functions from fgu_gr.c ***************************/
extern void get_xy_decimal(void);
extern void update_bar_menu(int letter, char direction);
extern void clear_line(int line_no,int no_of_blanks);
extern void printxy(int col, int row, char *string, short int mode);
extern void refresh_bar_menu(int show_item, int show_all);
extern void erase_bar_menu(void);
extern float y_pixel(float func_val);
extern float y_value(float pix_val);
extern float x_pixel(float func_val);
extern float x_value(float pix_val);
extern float xarrays(int page,int pt);
extern float yarrays(int page,int pt);
extern int draw_labels(void);
extern int refresh_graph(int curve_only);
extern int draw_graph();
extern char *read_value(Ipt location,char type[7],int length,char *result_ptr,
                      int *control); /* Reads a character or numeric string */
/****************** extern variables from fgu_gr.c ***************************/
extern short int NIL;
extern short int UP_LEFT_CORNER_X;
extern short int UP_LEFT_CORNER_Y;
extern short int LO_RIGHT_CORNER_X;
extern short int LO_RIGHT_CORNER_Y;
extern int zoom_status;
extern int table_no, no_of_tables;
extern HLS_color grid_color;      /* Grid color will be yellow */
extern HLS_color curve_color;     /* Curve color will be red   */
extern HLS_color text_color;      /* text color will be yellow */
extern HLS_color background_color;
extern HLS_color foreground_color;
/******************** extern variables for gr_zoom() *************************/
extern float sxmin, sxmax, sxgrid, symin, symax, sygrid;
/****************** extern variables from draw_graph() ***********************/
extern float y_sections, y_coarse_g;
extern float x_sections, x_coarse_g;
extern float new_xmin, new_xmax, new_ymin, new_ymax;
extern int y_decimal, x_decimal;
