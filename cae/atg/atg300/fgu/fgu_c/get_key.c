#include <varargs.h>
#include <smgdef.h>
#include "visa.h"
#include "module.h"

/************************************************************************/
/*									*/
/*                            MODULE GET_KEY 				*/
/* Date  : 09.86							*/
/************************************************************************/


/************************************************************************/
/*                        Function GET_KEY				*/

/*  This function reads a key from the virtual keyboard.		*/
/*  It also gets the graphic cursor position on the screen if the 	*/
/*  coord parameter is present.						*/
/*  Help Refresh and <PERSON><PERSON><PERSON> keys are not returned to the caller 	*/
/*  program, they are interpreted and executed by this function. 	*/

extern int readkey();

int get_key (coord)

Pt *coord;		/* optional parameter */

{

   int key;
   int count;

   Pt  *xcoord;

   va_count(count);
   if (!count)
      xcoord = NULL;
   else
      xcoord = coord;

   switch (key = (xcoord) ? grreturnixy (xcoord) : readkey()) {
      
     case SMG$K_TRM_HELP:
/*       help();  VGS */
/*       if (count) 
          refresh(xcoord);
       else
          refresh ();  VGS */
       break;    

     case SMG$K_TRM_CTRLW: 
/*       if (count) 
          refresh(xcoord);
       else
          refresh ();  VGS */
       break;    

     case SMG$K_TRM_F14: 
/*       hardcopy (ELSE);  VGS */
       break;    
     
   }
   if (count)
      *coord = *xcoord;

   return (key);
 
}
