/***************************************************************************
 * Copyright (c) CAE Electronics Ltd 1992
 ***************************************************************************/

/***************************************************************************
 *
 * FILE          : ovp_ps.c
 *
 * DESCRIPTION   : Generate plots on a generic postsript printer, using the
 *                 postscript language. The following functions are called 
 *                 by the graphic package if POSTSCRIPT is selected. 
 *
 * AUTHOR        : Nick Moscato
 *
 * CREATION DATE : July 3, 1992
 *
 * FUNCTIONS :
 *    - psinit
 *    - ps_send_out
 *    - pscmsize
 *    - pscolor
 *    - pscharstr
 *    - psfontset
 *    - psclear
 *    - psdashon
 *    - psddha
 *    - psddhr
 *    - psddta
 *    - psddtr
 *    - psdoton
 *    - psdraw2i
 *    - psdrawchek2i
 *    - psrmv2i
 *    - psrdr2i
 *    - pssolidon
 *    - ps_border
 *    - ps_draw_ln
 *    - psmove2i
 * 
 ***************************************************************************/

/***************************************************************************
 *
 * REVISION HISTORY :
 * ------------------
 *
 *     3-July-1992     Nick Moscato
 *    - Creation
 *
 ***************************************************************************/


/***************************************************************************
 * Include files
 ***************************************************************************/

#include "ovp_prec.h"
#include <stdio.h>
#include <string.h>
#include "ovp_logname.h"
#include "ovp_defa.h"
#include "ovp.h"
#include "graph.h"
#include "scale.h"
#include "ovp_device.h"
#include "screen.h"
#include "ovp_stru.h"
#include "ovp_extr.h"

#ifdef VAXHOST
#  include <descrip.h>
#  include <file.h>
#endif

/***************************************************************************
 * Local Symbolic Constants and Macros
 ***************************************************************************/

#define TRUE            1
#define FALSE           0
#define FILE_EXT	".ps"		/* Postscript file extension */
#define DIR_LENGTH      100
#define TESTID_LENGTH   13
#define EXT_LENGTH      4

#define XPIXEL_PERCM    24.4
#define YPIXEL_PERCM    24.4
#define STARTX          18              /* Start position of X on page */
#define STARTY          36              /* Start position of Y on page */

#define CHAR_RATIO	0.625		/* character x/y aspect ratio */

#define PAGE_WIDTH	540.0		/* page width on printout */
#define LINE_WIDTH	0.5		/* line width on printout */
#define COLUMN_WIDTH    5.68            /* Num of x pixels per char */
#define ROW_HEIGHT      4.88            /* Num of y pixels per char */ 

#define FAC_X           1.162           /* Factor to scale x coordinate */
#define FAC_Y           1.162           /* Factor to scale y coordinate */

/*-------------------------------------------------------------------------*/
/* Line elements length                                                    */
/*-------------------------------------------------------------------------*/

#define DASH_LEN	 6	/* dash length  */
#define DOT_LEN		 1	/* dot  length  */
#define SPACE_LEN	 3	/* space length */

/***************************************************************************
 * Static Variables
 ***************************************************************************/

/*-------------------------------------------------------------------------*/
/* window_ps is the Postscript page in dots. This is used to calculate     */
/* scaling factors. Those are the coordinate used to place a dot in the    */
/* page.                                                                   */
/*-------------------------------------------------------------------------*/

static Window window_ps;

/*-------------------------------------------------------------------------*/
/* Scaling factors.                                                        */
/*-------------------------------------------------------------------------*/

static Scale virs_ps;	/* virtual screen to Postscript scaling factor */
static Scale ps_virs;	/* Postscript to virtual screen scaling factor */

static int font_size;		/* font size in points */

static Float pscurx_pos;	/* current x position along the x axis */
static Float pscury_pos;        /* current y position along the y axis */

static FILE *psout;		/* dump file pointer */

#ifdef VAXHOST
#  define COMMAND "print/nonotify/queue=vsa$prtq "
#  define OUTPUT  "fgu_hcpy.out"
#endif

#ifdef UNIXHOST
#  ifdef IBM_RT
#     define COMMAND "qprt -dp -l 0 -q"
#  else
#     define COMMAND "lp "
#  endif
#endif

Window virtual_screen=VIRTUAL_SCREEN;

static char outfname[DIR_LENGTH+TESTID_LENGTH+EXT_LENGTH];

Pt scscale();
Pt scscalerel();
Scale scscalefind();


/***************************************************************************
 *
 * FUNCTION : psinit
 *
 * PURPOSE  : Initialize the scaling factors, the printer's dimension, the
 *            printer buffer and the page orientation.
 *
 * ARGUMENTS :
 *    - postscript
 *    - filename
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psinit( postscript, filename )
Window *postscript;
char *filename;
{
extern char *get_testid();
   char equnam[DIR_LENGTH], newname[100];
   int  equlen, ret_code, level = 0;

#ifdef UNIXHOST
extern void rev_curr_new();
   int  status,
        mode = 1,
        deflg = 4;

   Boolean force = FALSE;
#endif

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   strcpy(outfname, "\0");

#  ifdef UNIXHOST
/*
      ret_code = cae_trnl(OVP_C_DIR, &equlen, equnam, &level,
			   strlen(OVP_C_DIR), sizeof(equnam)-1);
*/
      ret_code = -1;

      if (ret_code != 1)
	 strcpy (equnam, DEFA_OVP_CTS);
      else
	 equnam[equlen] = '\0';

      strcpy (outfname, equnam);
#  endif

   strcat (outfname, filename);
   strcat (outfname, FILE_EXT);

#  ifdef VAXHOST
      strcpy (newname, outfname);
#  endif

#  ifdef UNIXHOST
/*
      rev_curr_c(outfname, newname, "    ", &force, &mode, &status,
		 strlen(outfname), sizeof(newname)-1, deflg);
*/
      strcpy (newname, outfname);
      rev_curr_new(newname);
#  endif

   psout = fopen (newname, "w");

   /* calculate scaling factor */
   window_ps = *postscript;

   virs_ps = scscalefind( &virtual_screen, postscript );
   ps_virs = scscalefind( postscript, &virtual_screen );

   fprintf(psout, "newpath\n");
   fprintf(psout, "%.3f setlinewidth\n", LINE_WIDTH);
   fprintf(psout, "/Courier-Bold findfont %d scalefont setfont\n", 
                  VERY_SMALL_FONT);

} /* psinit */



/***************************************************************************
 *
 * FUNCTION : ps_send_out
 *
 * PURPOSE  : Close the file and send it out to printer.
 *
 * ARGUMENTS : None.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

ps_send_out()
{
   int row, col;
   char command[150],output[150],prtnam[150];

#ifdef VAXHOST
   struct dsc$descriptor_s desc_command;
   struct dsc$descriptor_s desc_output;

   strcpy(command,COMMAND);
   strcat(command,outfname);
   strcpy(output,OUTPUT);
#endif

#ifdef UNIXHOST
extern void rev_curr_new();

   char newname[100],
        *lognam = "vsa_prtq",
        psnam[100];

   int  status,
        mode = 1,
        deflg = 4,
        err,
        return_len,
        level=0;

   int  force = FALSE;
#endif

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   fprintf(psout, "stroke\n");
   fprintf(psout, "showpage\n");

   fclose (psout);

#ifdef VAXHOST
   /* set up command string for LIB$SPAWN */
   desc_command.dsc$w_length  = strlen(command);
   desc_command.dsc$b_dtype   = DSC$K_DTYPE_T;
   desc_command.dsc$b_class   = DSC$K_CLASS_S;
   desc_command.dsc$a_pointer = command;

   desc_output.dsc$w_length = strlen( output );
   desc_output.dsc$b_dtype = DSC$K_DTYPE_T;
   desc_output.dsc$b_class = DSC$K_CLASS_S;
   desc_output.dsc$a_pointer = output;

   LIB$SPAWN(&desc_command,0,&desc_output);
#endif

#ifdef UNIXHOST
/*
   err = cae_trnl(lognam, &return_len, prtnam, &level, strlen(lognam),
                   sizeof(prtnam)-1);
   if (err != 1)
      strcpy(prtnam, DEFA_PRT);
   else
      psnam [return_len] =0;

   strcpy (command, COMMAND);
   strcpy (command, psnam);
*/
   strcpy (command, " ");

/*
   rev_curr_c(outfname, newname, "    ", &force, &mode, &status,
               strlen(outfname), sizeof(newname)-1, deflg);
*/
   strcpy (newname, outfname);
   rev_curr_new(newname);

   strcpy (command, COMMAND);
   strcat (command, newname);

   system (command);
#endif

} /* ps_send_out */


/***************************************************************************
 *
 * FUNCTION : pscmsize
 *
 * PURPOSE  : Return the number of virtual coordinates per centimeter.
 *
 * ARGUMENTS :
 *    - xypt 
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

void pscmsize(xypt)

Pt *xypt;
{

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   xypt -> x = XPIXEL_PERCM * FAC_X; 
   xypt -> y = YPIXEL_PERCM * FAC_Y;
   *xypt = scscale (&ps_virs, xypt);

} /* pscmsize */



/***************************************************************************
 *
 * FUNCTION : pscolor
 *
 * PURPOSE  : Select color.
 *
 * ARGUMENTS :
 *    - color : Color. Input only.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

pscolor(color)

int color;
{

   switch (color) {
      case CTS_COLOR :
         fprintf(psout, "stroke\n");
         fprintf(psout, "[%d %d] 0 setdash\n", DOT_LEN, SPACE_LEN);
         break;
      case TOLER_COLOR :
         fprintf(psout, "stroke\n");
         fprintf(psout, "[%d %d] 0 setdash\n", DOT_LEN, SPACE_LEN);
         break;
      case MASTER_COLOR:
         fprintf(psout, "stroke\n");
         fprintf(psout, "[] 0 setdash\n");
         break;
      case GRID_COLOR :
         fprintf(psout, "stroke\n");
         fprintf(psout, "[] 0 setdash\n");
         break;
      }

} /* pscolor */


/***************************************************************************
 *
 * FUNCTION : pschar_str
 *
 * PURPOSE  : Print a character string in the currently selected font. The 
 *            text is printed with the current cursor position being the 
 *            lower left corner of the string.
 *
 * ARGUMENTS :
 *    - text : Address of structure containing the string.  Input only.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

pscharstr(text)

Stringdef *text;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   char output_string[240];
   int  ptr = 0;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   /*----------------------------------------------------------------------*/
   /* The Postscript language treats '(' ')' as special characters.        */
   /* A '\' is used to be able to print these characters                   */
   /*----------------------------------------------------------------------*/

   while (*text->cstring != '\0') {
      if ((*text->cstring == '(') || (*text->cstring == ')')) {
         output_string[ptr] = '\\';
         output_string[++ptr] = *text->cstring;
         }
      else
         output_string[ptr] = *text->cstring;
        
      ptr++;
      text->cstring++;
      }

   output_string[ptr] = '\0';
   fprintf(psout, "%.3f %.3f rmoveto\n", 0.0, -ROW_HEIGHT);
   fprintf(psout, "(%s) show\n", output_string);

} /* pscharstr */


/***************************************************************************
 *
 * FUNCTION : psfontset
 *
 * PURPOSE  : Returns width and height of a character in virtual 
 *            coordinates.
 *
 * ARGUMENTS :
 *    - xy: 
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psfontset(xy)

Pts *xy;

{

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   xy -> xy1.x = (Float) (COLUMN_WIDTH * ps_virs.a);
   xy -> xy1.y = (Float) (ROW_HEIGHT   * ps_virs.e);
   xy -> xy2.x = (Float) (ROW_HEIGHT   * ps_virs.a);
   xy -> xy2.y = (Float) (COLUMN_WIDTH * ps_virs.e);

} /* psfontset */


/***************************************************************************
 *
 * FUNCTION : psclear
 *
 * PURPOSE  : Print the current page and re-initialize the next one.
 *
 * ARGUMENTS : None.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

void psclear(void)
{

   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   static Boolean first_pass = TRUE;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   if (first_pass)
      first_pass = FALSE;
   else {
      fprintf(psout, "stroke\n");
      fprintf(psout, "showpage\n");
   }
   fprintf(psout, "%d %d translate\n", round(PAGE_WIDTH+STARTY), STARTX );
   fprintf(psout, "90 rotate\n");
   fprintf(psout, "newpath\n");
   fprintf(psout, "%.3f setlinewidth\n", LINE_WIDTH);

} /* psclear */


/***************************************************************************
 *
 * FUNCTION : psdashon
 *
 * PURPOSE  : Sets printer into dashed mode
 *
 * ARGUMENTS :
 *    - N/A
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psdashon()
{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   fprintf(psout, "stroke\n");
   fprintf(psout, "[%d %d] 0 setdash\n", DASH_LEN, SPACE_LEN);

} /* psdashon */


/***************************************************************************
 *
 * FUNCTION : psddha
 *
 * PURPOSE  : Draw a dashed line to relative coordinates specified by 
 *            input coordinates.
 *
 * ARGUMENTS :
 *    - xypt
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psddha( xypt )
Pt *xypt;
{

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   psdashon();
   psdraw2i( xypt );
   pssolidon();

} /* psddha */


/***************************************************************************
 *
 * FUNCTION : psddhr
 *
 * PURPOSE  : Draw a dashed line from the current position to a distance
 *            specified by the input coordinates.
 *
 * ARGUMENTS :
 *    - xypt
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psddhr( xypt )
Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   psdashon();
   psrdr2i( xypt );
   pssolidon();

} /* psddhr */


/***************************************************************************
 *
 * FUNCTION : psddta
 *
 * PURPOSE  : Draw a dotted line from the current position to a distance
 *            specified by the input coordinates.
 *
 * ARGUMENTS :
 *    - xypt
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psddta( xypt )
Pt *xypt;
{

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   psdoton();
   psdraw2i( xypt );
   pssolidon();

} /* psddta */


/***************************************************************************
 *
 * FUNCTION : psddtr
 *
 * PURPOSE  : Draw a dotted line to relative coordinates specified by 
 *            input coordinates.
 *
 * ARGUMENTS :
 *    - xypt
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psddtr( xypt )
Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   psdoton();
   psrdr2i( xypt );
   pssolidon();

} /* psddtr */


/***************************************************************************
 *
 * FUNCTION : psdoton
 *
 * PURPOSE  : Set the printer mode to dot
 *
 * ARGUMENTS :
 *    - N/A
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psdoton()
{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   fprintf(psout, "stroke\n");
   fprintf(psout, "[%d %d] 0 setdash\n", DOT_LEN, SPACE_LEN);

} /* psdoton */


/***************************************************************************
 *
 * FUNCTION : psdraw2i
 *
 * PURPOSE  : Draw line from the current position to the input position
 *
 * ARGUMENTS :
 *    - xypt : Screen coordinates.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psdraw2i( xypt )

Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   Pt temp;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   temp = scscale( &virs_ps, xypt );
   ps_draw_ln( temp.x, temp.y );

} /* psdraw2i */


/***************************************************************************
 *
 * FUNCTION : psdrawchek2i
 *
 * PURPOSE  : Draw line and update current position
 *
 * ARGUMENTS :
 *    - xypt : Screen coordinates.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psdrawchek2i( xypt )

Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   Pt temp;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   temp = scscale( &virs_ps, xypt );
   ps_draw_ln( temp.x, temp.y );

} /* psdrawchek2i */


/***************************************************************************
 *
 * FUNCTION : psrmv2i
 *
 * PURPOSE  : Resets the current position in a distance relative to the
 *            previous position
 *
 * ARGUMENTS : 
 *    - xypt : Screen coordinates.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psrmv2i( xypt )
Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   Pt temp;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   temp = scscalerel( &virs_ps, xypt );
   pscurx_pos += temp.x;
   pscury_pos -= temp.y;
}


/***************************************************************************
 *
 * FUNCTION : psrdr2i
 *
 * PURPOSE  : Performs a relative draw.  The Y position is inverted
 *
 * ARGUMENTS : 
 *    - xypt : Screen coordinates.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psrdr2i( xypt )
Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   Pt temp;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   temp = scscalerel( &virs_ps, xypt );
   temp.x += pscurx_pos;
   temp.y += pscury_pos;

   ps_draw_ln( temp.x, temp.y );

} /* psrdr2i */


/***************************************************************************
 *
 * FUNCTION : pssolidon
 *
 * PURPOSE  : Sets the printer to solid line mode.
 *
 * ARGUMENTS : None.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

pssolidon()

{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   fprintf(psout, "stroke\n");
   fprintf(psout, "[] 0 setdash\n");

} /* pssolidon */


/***************************************************************************
 *
 * FUNCTION : ps_border
 *
 * PURPOSE  : Draws a border.
 *
 * ARGUMENTS : None.
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

ps_border ()

{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   pssolidon ();
   fprintf(psout, "%.3f %.3f moveto\n", window_ps.min.x, window_ps.max.y);
   ps_draw_ln(window_ps.min.x, window_ps.min.y);
   ps_draw_ln(window_ps.max.x, window_ps.min.y);
   ps_draw_ln(window_ps.max.x, window_ps.max.y);
   ps_draw_ln(window_ps.min.x, window_ps.max.y);

} /* ps_border */


/***************************************************************************
 *
 * FUNCTION : ps_draw_ln
 *
 * PURPOSE  : Draws a line from the current position to the input
 *            coordinate.
 *
 * ARGUMENTS :
 *    - xpos. X position to draw to
 *    - ypos. Y position to draw to
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

ps_draw_ln( xpos, ypos )

Float xpos, ypos;

{
   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   /* Translate Y since origin is in bottom left corner */

   fprintf(psout, "%.3f %.3f lineto\n", xpos, ypos);
   pscurx_pos = xpos;
   pscury_pos = ypos;

} /* ps_draw_ln */


/***************************************************************************
 *
 * FUNCTION : psmove2i
 *
 * PURPOSE  : Set the current position to the input coordinate.
 *
 * ARGUMENTS :
 *    - xypt
 *
 * RETURN VALUE : None.
 *  
 ***************************************************************************/

psmove2i( xypt )

Pt *xypt;
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   Pt temp;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   temp = scscale( &virs_ps, xypt );
   pscurx_pos = temp.x;
   pscury_pos = temp.y;

   fprintf(psout, "%.3f %.3f moveto\n", temp.x, temp.y);

}


/* eof */
