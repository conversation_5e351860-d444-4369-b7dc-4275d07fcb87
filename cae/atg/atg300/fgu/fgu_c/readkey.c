#include "ovp_prec.h"

/***************** IBM version of readkey() function. ************************/

#ifdef UNIXHOST
/********************  main() function tests the readkey() function.
#include <stdio.h>
#include <string.h>
main()
{
int readkey(void);
  int letter;
  letter = readkey();
  printf("%d\n",letter);
}
*********************************/
int readkey(void);

int readkey(void)
{
#include "readkey.h"
extern void fgetkey();
   int count=0;
   char key_pressed[256];
   char ch;

   fgetkey(&ch);
   if (ch=='\033') {              /* Was a non visable key pressed?  */
      key_pressed[count++] = ch;
      fgetkey(&ch);               /* Yes, get the rest of it's code. */
      key_pressed[count++] = ch;  /* get [ key. */
      fgetkey(&ch);
      key_pressed[count++] = ch;  /* get a letter or number.    */
      if (ch >= 48 && ch <= 57) { /* Was a number received?     */
         while (ch != '~') {      /* Yes, get the whole number. */
            fgetkey(&ch);
            key_pressed[count++] = ch;
         }
      }
      key_pressed[count]='\0';    /* Mark the end of key_pressed. */
   }
/*   frestore();                      Restore screen IO.           */

   if (count <= 1) return((int)ch); /* Return if a visable char pressed. */

/******** Search the list of non visable characters that can be pressed. */
   if (!strcmp(RIGHT_ARROW_CODE,key_pressed))
      return(RIGHT_ARROW_VAL);
   else if (!strcmp(DOWN_ARROW_CODE,key_pressed))
      return(DOWN_ARROW_VAL);
   else if (!strcmp(LEFT_ARROW_CODE,key_pressed))
      return(LEFT_ARROW_VAL);
   else if (!strcmp(UP_ARROW_CODE,key_pressed))
      return(UP_ARROW_VAL);
   else if (!strcmp(SELECT_CODE,key_pressed))
      return(SELECT_VAL);
   else if (!strcmp(REMOVE_CODE,key_pressed))
      return(REMOVE_VAL);
   else if (!strcmp(PREV_CODE,key_pressed))
      return(PREV_VAL);
   else if (!strcmp(NEXT_CODE,key_pressed))
      return(NEXT_VAL);
   else if (!strcmp(INSERT_CODE,key_pressed))
      return(INSERT_VAL);
   else if (!strcmp(DO_CODE,key_pressed))
      return(DO_VAL);
   else if (!strcmp(FIND_CODE,key_pressed))
      return(FIND_VAL);
   else if (!strcmp(PF1_CODE,key_pressed))
      return(PF1_VAL);
   else if (!strcmp(PF2_CODE,key_pressed))
      return(PF2_VAL);
   else if (!strcmp(PF3_CODE,key_pressed))
      return(PF3_VAL);
   else if (!strcmp(PF4_CODE,key_pressed))
      return(PF4_VAL);

   return(0); /* An unrecognized key will result in zero. */
}
#endif

/***************** VAX version of readkey() function. ************************/

#ifdef VAXHOST
/************************************************************************/
/*									*/
/* Author : Gisele Collette						*/
/* Date   : 11.08.86							*/
/* Description : Function "readkey":					*/
/* 	         Reads the key pressed on the keyboard and returns the	*/
/*	         associated integer value.  The function readkey_init() */
/*               must be called before readkey() to initialize the      */
/*               virtual keyboard.                                      */
/*									*/
/* Note   : This function uses the 'VAX VMS Run-Time Library routines	*/
/*          Volume C' page RTL-717.                                     */
/*									*/
/* Revision History :                                                   */
/* Claude Lamoureux May 13/88                                           */
/* Change the MASK_LENGTH for 40 to 32 ( I think 40 is supposed to be   */
/* in octal ).                                                          */
/*                                                                      */
/* Rob Wallace      March 12/91                                         */
/* Setup readkey() function so that it can be called from a Fortran     */
/* program.  Refer to READ_KEY.FOR for an example of this usage.        */
/************************************************************************/

extern unsigned int
	keyboard_id;

#define NUM_COL           80
#define NUM_ROW           21
#define TRM$M_TM_NORECALL 0x00010000
#define MASK_LENGTH       32

unsigned int
         pasteboard_id,
         keyboard_id,
         display1_id,
         display2_id,
         current_display;

#include "visa.h"
#include <descrip.h>
#include <smgdef.h>
#include <ssdef.h>
#include <stsdef.h>
#include <iodef.h>
#include "text.h"

int readkey ()
{
   int return_code,
       max_length;

   unsigned int modifier_mask = IO$M_TRMNOECHO | TRM$M_TM_NORECALL | IO$M_PURGE
                                | IO$M_NOFILTR,
                term_set [2];

   unsigned short int term_code,
                      rec_length;

   struct dsc$descriptor_s desc_text;

   char text [5],
        mask_term_set [MASK_LENGTH] = { '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377',
                                        '\377', '\377', '\377', '\377'};

   desc_text.dsc$w_length  = 4;
   desc_text.dsc$b_dtype   = DSC$K_DTYPE_T;
   desc_text.dsc$b_class   = DSC$K_CLASS_S;
   desc_text.dsc$a_pointer = text;

   max_length = 4;

   term_set [0] = MASK_LENGTH;
   (int *) term_set [1] = mask_term_set;

   return_code = SMG$READ_STRING (&keyboard_id, &desc_text, NULL, &max_length,
                                  &modifier_mask, NULL, term_set, &rec_length,
                                  &term_code, 0, 0);

   if (!(return_code & STS$M_SUCCESS)) {
      printf("\nError %d occured in readkey function.",return_code);
      return (NULL);
      }
   else
      return (term_code);
}

/************************** readkey_init() ******************************/
/*                           						*/
/*  This function creates :						*/
/*     - the pasteboard,						*/
/*     - two virtual displays,						*/
/*     - a virtual keyboard						*/
/*  and pastes the display #1.						*/
/*									*/
/*  It initializes the global variables:				*/
/*     - pasteboard_id,							*/
/*     - keyboard_id,							*/
/*     - display1_id,							*/
/*     - display2_id, 							*/
/*     - current_display.						*/
/************************************************************************/

void readkey_init(void)
{
   int
 		pb_rows,		/* number of rows on the pasteboard	*/
 		pb_columns,		/* number of columns on the pasteboard	*/
 		nb_rows,		/* number of rows on the display	*/
  		nb_columns,		/* number of columns on the display	*/
 		return_code;

   struct dsc$descriptor_s
  		desc_file_spec,		/* logical name of the terminal to be	*/
 					/* used for the virtual keyboard	*/
 		desc_output_device;	/* logical name to which the output	*/
 	   			    	/* associated with the pasteboard will	*/
 					/* be written 				*/

   char
 		*output_device = VISA_TEXT_OUT,
 		*file_spec = VISA_TEXT_IN;

/* Pasteboard Creation */

   desc_output_device.dsc$w_length  = strlen (output_device);
   desc_output_device.dsc$b_dtype   = DSC$K_DTYPE_T;
   desc_output_device.dsc$b_class   = DSC$K_CLASS_S;
   desc_output_device.dsc$a_pointer = output_device;

   pb_rows    = NUM_ROW;
   pb_columns = NUM_COL;

   return_code = SMG$CREATE_PASTEBOARD (&pasteboard_id,
      &desc_output_device, &pb_rows, &pb_columns, NULL);
   if (!(return_code & STS$M_SUCCESS))
      exit (2);

/* Virtual Displays Creation */

   nb_rows    = NUM_ROW;
   nb_columns = NUM_COL;

   return_code = SMG$CREATE_VIRTUAL_DISPLAY (&nb_rows, &nb_columns,
                 &display1_id, NULL, NULL, NULL);
   if (!(return_code & STS$M_SUCCESS))
      exit (2);

   return_code = SMG$CREATE_VIRTUAL_DISPLAY (&nb_rows, &nb_columns,
                 &display2_id, NULL, NULL, NULL);
   if (!(return_code & STS$M_SUCCESS))
      exit (2);

   current_display = display1_id;

/* Virtual Keyboard Creation */

   desc_file_spec.dsc$w_length  = strlen (file_spec);
   desc_file_spec.dsc$b_dtype   = DSC$K_DTYPE_T;
   desc_file_spec.dsc$b_class   = DSC$K_CLASS_S;
   desc_file_spec.dsc$a_pointer = file_spec;

   return_code = SMG$CREATE_VIRTUAL_KEYBOARD (&keyboard_id, &desc_file_spec,
		 NULL, NULL);
   if (!(return_code & STS$M_SUCCESS))
      exit (2);
}
#endif
