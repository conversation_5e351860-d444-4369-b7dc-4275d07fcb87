/************************************************************************/
/*									*/
/*                        INCLUDE FILE OVP_EXTR		 		*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*	      	 							*/
/*  Contains the declaration of the extern global variables. (These 	*/
/*  variables  are declared in the module "GR.C".)			*/
/************************************************************************/

extern Auxiliary aux_master [MAX_MASTER];
extern Page   *aux_page  [MAX_PAGE];

extern int    aux_nb_parameters,
              aux_nb_pages,
              aux_nb_initial;

extern char  aux_initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];

extern Master info_master [MAX_MASTER];
extern Page   *info_page  [MAX_PAGE];

extern int    nb_parameters,
              nb_pages,
              nb_initial;

extern int    init_cond_page_number;
extern Boolean init_cond_page_ready;
extern char   initial_conditions [MAX_INIT_COND] [INIT_COND_LENGTH+1];
