
/* vt240.h is the include library file for global declarations.  This
   file contains most terminal information directly reletated to a 
   DEC vt-240.                                                       */


#define XMAX240 799
#define XMIN240 0
#define YMAX240 479
#define YMIN240 0 
#define XFONT_PIXELS 9
#define YFONT_PIXELS 20
#define XSIDE_PIXELS 20
#define YSIDE_PIXELS 10
#define XPIXEL_PERCM 35
#define YPIXEL_PERCM 33
#define STRBUFFER 80

#define CURSOR_LOC          "[%d,%d]"

#define MAX_CLEAR           80 /* maximum of 80 characters deleted */
#define BLANK               ' '

#define HLS_CODE            "S(I(H%dL%dS%d))\n"
#define RGB_CODE            "S(I(R%dG%dB%d))\n"
#define CHARSTR_CODE1       "T(D%d,S%d)'%s'\n"
#define CHARSTR_CODE2       "T(D%d,S%d)\"%s\"\n"
#define CIRCABS_CODE        "C[%d,%d]\n"
#define CIRCREL_CODE        "C(C)[%d,%d]\n"
#define CLEAR_CODE          "S(E)\n"
#define CLSSTRING_CODE      "W(N1)T'%s'W(N0)\n"
#define COLOR_CODE          "W(I%d)\n"
#define DASHON_CODE         "W(P2(M1))\n"
#define DEFRASTERFONT_CODE  "T(S[%d,%d])\n"
#define DELALLM_CODE        "@.\n"
#define DOTON_CODE          "W(P6(M1))\n"
#define DRAW2I_CODE         "V[%d,%d]\n"
#define DASH_CODE           "W(P2(M1))\n"
#define REGU_CODE           "W(P1)\n"  
#define DRAWREL_CODE        "V[%c%d,%c%d]\n"
#define GCURSEOFF_CODE      "S(C0)\n"
#define GCURSEON_CODE       "S(C1)\n"
#define GEXIT_CODE          "\033\\"
#define GINIT_CODE          "\033Pp"
#define MACCALL_CODE        "@%c\n"
#define MACDEL_CODE         "@:%c@;\n"
#define MACSTART_CODE       "@:%c\n"
#define MACSTOP_CODE        "@;\n"
#define MAPCOLOR_CODE       "s(M%d(%c))\n"
#define MAPHLS_CODE         "S(M%d(H%dL%dS%d))\n"
#define MAPRGB_CODE         "S(M%d(R%dG%dB%d))\n"
#define MOVE2I_CODE         "P[%d,%d]\n"
#define RMV2I_CODE          "P[+%d,+%d]\n"
#define REPORTXY_CODE       "R(P)\n"
#define RINGBELL_CODE       "\033\\\007\033Pp\n"
#define RPIXY_CODE          "R(P(I))\n"
#define SHADEON_CODE        "W(S1)\n"
#define SHADEOFF_CODE       "W(S0)\n"
#define STRING_FIX          "T(D0 S1 D0)"
