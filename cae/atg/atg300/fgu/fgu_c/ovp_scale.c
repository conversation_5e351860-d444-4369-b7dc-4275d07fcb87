/************************************************************************/
/*									*/
/*			       scale					*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/* Routines :								*/
/*	- scscale							*/
/*	- scscalerel							*/
/*	- scscalefind							*/
/*	- scscaling							*/
/*									*/
/* Revision History:                                                    */
/* -----------------							*/
/************************************************************************/

#include <math.h>

#include "ovp.h"
#include "ovp_scale.h"

/************************************************************************/
/*									*/
/*				scscale					*/
/*									*/
/************************************************************************/

Pt scscale(factors,point)

Scale *factors;
Pt *point;

{
   Pt temp;

   temp.x = factors->a*point->x + factors->b*point->y + factors->c;
   temp.y = factors->d*point->x + factors->e*point->y + factors->f;

   return temp;
}			/* scscale */



/************************************************************************/
/*									*/
/*				scscalerel				*/
/*									*/
/************************************************************************/

Pt scscalerel(factors,point)

Scale *factors;
Pt *point;

{
   Pt temp;

   temp.x = factors->a*point->x + factors->b*point->y;
   temp.y = factors->d*point->x + factors->e*point->y;

   return temp;
}			/* scscalerel */




/************************************************************************/
/*									*/
/*				scscalefind				*/
/*									*/
/* finds the scaling factors between two extreme structure   		*/
/************************************************************************/

#define ANGLE 0.0

Scale scscalefind(ext1,ext2)

Window *ext1;
Window *ext2;
{
   Scale scscaling();
                    /* preliminary translation factors             */
   Pt trans1;

                    /* find the scale between the to extreme point */
   Pt scal1;

   Pt trans2;

   trans1.x = -ext1->min.x;
   trans1.y = -ext1->min.y;

   scal1.x = (ext2->max.x - ext2->min.x) / (ext1->max.x- ext1->min.x);
   scal1.y = (ext2->max.y - ext2->min.y) / (ext1->max.y - ext1->min.y);

   trans2.x = ext2->min.x;
   trans2.y = ext2->min.y;

   return scscaling(&trans1,&scal1,&trans2,ANGLE);
}			/* scscalefind */


/************************************************************************/
/*									*/
/*				scscaling				*/
/*									*/
/* Finds the scaling factors                                		*/
/************************************************************************/

Scale scscaling(o,s,t,phy)

Pt *o;                  /* Translation factor I                     */
Pt *s;                  /* scaling factor                           */
Pt *t;                  /* Translation factor II                    */
Float phy;              /* angle of rotation                        */

{
   Scale scal;
   Float a;

/* X */
   scal.a =   (s->x)*(a= cos(phy));
   scal.b =   (s->x)* sin(phy);
   scal.c =   (s->x)* ( (o->x)* cos(phy)+(o->y)* sin(phy) ) + (t->x);

/* Y */
   scal.d =  -(s->y)* sin(phy);
   scal.e =   (s->y)* cos(phy);
   scal.f =   (s->y)* ( (o->y)* cos(phy)-(o->x)* sin(phy) ) + (t->y);

   return scal;
}			/* scscaling */

/*********************************** scale() *********************************/
Pt scale(factors,point)
Scale *factors;
Pt *point;
{
   Pt temp;

   temp.x = factors->a*point->x + factors->b*point->y + factors->c;
   temp.y = factors->d*point->x + factors->e*point->y + factors->f;

   return temp;
}

/******************************** scalerel() *********************************/
Pt scalerel(factors,point)
Scale *factors;
Pt *point;
{
   Pt temp;

   temp.x = factors->a*point->x + factors->b*point->y;
   temp.y = factors->d*point->x + factors->e*point->y;

   return temp;
}

/******************************* scalefind() *********************************/
Scale scalefind(ext1,ext2)
Window *ext1;
Window *ext2;
{                                                   
   Scale scaling(); 

   Pt trans1;
   Pt scal1;
   Pt trans2;

/* preliminary translation factors             */
   trans1.x = -ext1->min.x;
   trans1.y = -ext1->min.y;

/* find the scale between the to extreme point */
   scal1.x = (ext2->max.x - ext2->min.x) / (ext1->max.x- ext1->min.x);
   scal1.y = (ext2->max.y - ext2->min.y) / (ext1->max.y - ext1->min.y);

   trans2.x = ext2->min.x;
   trans2.y = ext2->min.y;

   return scaling(&trans1,&scal1,&trans2,ANGLE);
}

/*********************************** scaling() *******************************
     Finds the scaling factors.
*/
Scale scaling(o,s,t,phy)
Pt *o;     /* Translation factor I  */
Pt *s;     /* scaling factor        */
Pt *t;     /* Translation factor II */
Float phy; /* angle of rotation     */
{
   Scale scal;
   Float a;

/* X */
   scal.a =   (s->x)*(a= cos(phy));
   scal.b =   (s->x)* sin(phy);
   scal.c =   (s->x)* ( (o->x)* cos(phy)+(o->y)* sin(phy) ) + (t->x);

/* Y */
   scal.d =  -(s->y)* sin(phy);
   scal.e =   (s->y)* cos(phy);
   scal.f =   (s->y)* ( (o->y)* cos(phy)-(o->x)* sin(phy) ) + (t->y);

   return scal;
}
