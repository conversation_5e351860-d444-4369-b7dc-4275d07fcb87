/************************************************************************/
/*									*/
/*                         MODULE HP					*/
/*									*/
/* Author      : <PERSON>                                           */
/* Date        : May 1991                                               */
/* Description : This routine is called by a dispatcher routine		*/
/*               from graph.c for the VSA/OVP system.  It is used to    */
/*               generate VISA or OVP plots on a HP LaserJet printer.	*/
/*									*/
/* Revision History :							*/
/* ------------------							*/
/* <PERSON> Toulouse     May 21, 1991					*/
/* o Modified for VAX and UNIX systems					*/
/*                                                                      */
/* <PERSON> (July 1991)                                          */
/* o Modified for LaserJet series II Printer.                           */
/*   This module comes in two versions: VSA_HP.C and OVP_HP.C. VSA_HP.C */
/*   uses the function "id_get_file_id_string()" while in OVP_HP.C it   */
/*   is refered to as "get_testid()".                                   */
/*                                                                      */
/* <PERSON>      July 23, 1991				    	*/
/* o Added the hpcircabs() function to print circles.  Modified calling	*/
/*   arguments of hp_plotchar() function.  Modified hpinit()            */
/*   function for VGS.                                                  */
/*                                                                      */
/************************************************************************/
#include "ovp_prec.h"
#include <stdio.h>
#include <math.h>
#include "ovp_logname.h"
#include "ovp_defa.h"
#include "ovp.h"
#include "graph.h"
#include "ovp_hp.h"
#include "scale.h"
#include "ovp_device.h"
#include "screen.h"
#include "ovp_stru.h"
#include "ovp_extr.h"

#ifdef VAXHOST
/*#  include <vms/descrip.h>*/
/*#  include <sys/file.h>   */
#  include <descrip.h>
#  include <file.h>
#endif

#define PI             3.141592654
#define FILE_ERROR     "File creation error"
#define HP_X_SIZE      768  /* Maximum number of dots in a page */
#define HP_Y_SIZE       76  /* Maximum number of characters in a line */
#define XPLUS            1  /* Right */
#define XMINUS           2  /* Left  */
#define YPLUS            4  /* Down  */
#define YMINUS           8  /* Up    */
#define IOERROR         -1  /* File Input & output operation error */
#define COLUMN_WIDTH     8  /* Number of x pixels per character */
#define ROW_HEIGHT       8  /* Number of y pixels per character */
#define XPIXEL_PERCM    29.5  /* Number of x pixels per centimeter */
#define YPIXEL_PERCM    29.5  /* Number of y pixels per centimeter */
#define HORIZONTAL       0  /* Character in horizontal direction */
#define VERTICAL         1  /* Character in vertical direction */
#define BLANK          ' '  /* Blank character */
#define BIT_ON           1  /* To set the printer buffer's bit on */
#define BIT_OFF          0  /* To set the printer buffer's bit off */
#define FAC_X         1.0   /* Factor to scale x coordinate */
#define FAC_Y         1.0   /* Factor to scale y coordinate */
#define DOT_LINE         6  /* Spacing between the dot line */
#define DASH_LINE        3  /* Spacing between the dash line */
#define SOLID_LINE       1  /* Spacing between the solid line */
#define round(i)    ((int)((i>0) ? (i+0.5) : (i-0.5)))

#define HP_PAGE_SIZE  18.5  /* Height (cm) of plotting area on the page */

#define FALSE 0
#define TRUE  1

#define FILE_EXT      ".hp"
#define DIR_LENGTH    100
#define TESTID_LENGTH 13
#define EXT_LENGTH    4

#ifdef VAXHOST
#  define COMMAND "print/nonotify/passall/queue=vsa$hpq "
#  define OUTPUT  "fgu_hcpy.out"
#endif

#ifdef UNIXHOST
#  ifdef IBM_RT
#     define COMMAND "qprt -dp -l 0 -q"
#  else
#     define COMMAND "lp "
#  endif
#endif

Window virtual_screen=VIRTUAL_SCREEN;

/******************************/
/* current hp cursor position */
/******************************/

static int hpcurx_pos;
static int hpcury_pos;

/*********************/
/* Printer linestyle */
/*********************/

Linestyle hp_ln_style;

/*********************/
/* character masking */
/*********************/

char hp_mask[8] = { 0x80,0x40,0x20,0x10,0x08,0x04,0x02,0x01 };

/********************/
/* printer's buffer */
/********************/

char hpplotln[HP_X_SIZE][HP_Y_SIZE];
char hp_buffer[100];

/***********************/
/* Output File Pointer */
/***********************/

FILE *hpout;

static char name[DIR_LENGTH+TESTID_LENGTH+EXT_LENGTH];

/***********************************************/
/* scaling factor for vt240 to hplaser printer */
/***********************************************/

static Scale virs_hp,
             hp_virs;
       Scale scscalefind();

static Window window_hp;

Pt orientw();
Pt scale_hp_coord();
Pt scscale();
Pt scscalerel();

/***************************************************/
/* global variables used for a patch in hp_draw_ln */
/***************************************************/

static int hp_pixel_skip = 0;
static int hp_max_pixel_skip = 0;
static Boolean hp_in_send = FALSE;

/*********************************************/
/* This routine returns the bit value of the */
/* specified location of the character byte  */
/*********************************************/

hp_is_bit_on( c, bit_pos )
char c;
int bit_pos;
{
   return( (int)((c>>bit_pos) & 0x01) );
}

/**************************************************/
/* Draw a circle by setting the corresponding bit */
/* location to 1.                                 */
/**************************************************/

hpcircabs( xypt, rad )
Pt *xypt;
float rad;
{
Pt scscale(Scale *factors, Pt *point);
Pt orientw(Pt *xy, Window *w_window);
Pt scale_hp_coord(Pt *pt);
int hpsetbit(int x, int y, int bit_on );
   float i;
   float x1,y1;
   Pt temp = scscale( &virs_hp, xypt );
   Pt temp1 = orientw( &temp, &window_hp );

   temp1 = scale_hp_coord( &temp1 );

   for( i=0; i<=360; i += 1.0 )  {
      x1 = (hpcurx_pos + rad*cos(i*PI/180.0));
      y1 = (hpcury_pos + rad*sin(i*PI/180.0));
      hpsetbit( round( x1), round( y1 ), BIT_ON );
   }
}

/********************************************************/
/* Only the plot mode is used.  Therefore the character */
/* is stored as a 8x8 dot matrix in the array of ascii  */
/* code. This routine looks  for the location of the    */
/* character in the array and set corresponding bit to  */
/* 1.                                                   */
/********************************************************/

hp_plotchar( c, dir )
char c;
int  dir;
{
int hpsetbit(int x, int y, int bit_on );
   int i,j,k,index;
   char *pwd, *p[8];
   int hp_is_bit_on();

   index = c - 32;
   pwd = (char *)&word[index][0];

   for( i=0, j=0, k=0; i<4; i++ )  {
#     ifdef VAXHOST
	 p[j++] = pwd+k+1;
	 p[j++] = pwd+k;
#     endif

#     ifdef UNIXHOST
	 p[j++] = pwd+k;
	 p[j++] = pwd+k+1;
#     endif

      k=j;
   }
   for( i=0; i<COLUMN_WIDTH; i++ )  {
      for( j=0; j> (-ROW_HEIGHT); j-- ) {
         if( hp_is_bit_on( *(p[i]), 7+j ) ){
            hpsetbit( hpcurx_pos+i, hpcury_pos+j, BIT_ON);
            }
         else{
            hpsetbit( hpcurx_pos+i, hpcury_pos+j, BIT_OFF );
            }
      }
   }
}

/****************************************************/
/* hpcharstr is for displaying text on the printer. */
/****************************************************/

hpcharstr( text )
Stringdef *text;
{
   int i, x, y;
   char *p;

   p = text->cstring;

   hpcury_pos += ROW_HEIGHT;
   hpcurx_pos += 1;

   while( *p ) {
      if ((*p)>=32 && (*p)<=127 )
         hp_plotchar( *p );
      hpcurx_pos += COLUMN_WIDTH;
      p++;
   }
}

/************************************************************************/
/* This function is necessary because the HPs' maximum page width is    */
/* 18.5 centimeters. If one graph exceeds the length allowed (position  */
/* in y + length in y is over 17.0), every graph on the page is shifted */
/* down so that they all fit on the page                                */
/************************************************************************/

/*****************************************
void hpcheck_page_width(page)
int  page;
{
   Float d,
         diff = 0;
   int i;

   for (i = 0;  i < info_page[page]-> nb_parameters; i++) {
      d = info_page[page]-> master[i]-> context-> graph_position_in_cm.y +
          info_page[page]-> master[i]-> context-> graph_size.y -
          HP_PAGE_SIZE;
      if (d > diff) diff = d;
   }

   if (diff > 0)
      for (i = 0; i < info_page[page]-> nb_parameters; i++)
         info_page[page]-> master[i]-> context-> graph_position_in_cm.y -= diff;

}
********************************************/

/*********************************************************/
/* A new page is created so the printer buffer is stored */
/* and is clear for new data later                       */
/*********************************************************/

hpclear()
{
   static Boolean first_pass = TRUE;
   int row, col;

   /* To avoid a blank page at the beginning of the file */

   if ( hp_in_send  )
      return;

   if ( first_pass ) {
      first_pass = FALSE;
      return;
   }

   hp_buffer[0]= HP_ESC;      /* Reset Printer */
   hp_buffer[1]= HP_UPE;

   hp_buffer[2]= HP_ESC;      /* Set landscape mode   ESC&l1O */
   hp_buffer[3]= HP_AMPER;
   hp_buffer[4]= HP_LOWL;
   hp_buffer[5]= HP_ONE;
   hp_buffer[6]= HP_UPO;

   hp_buffer[7]= HP_ESC;      /* Set dots per inch   ESC*t75R */
   hp_buffer[8]= HP_ASTERISK;
   hp_buffer[9]= HP_LOWT;
   hp_buffer[10]= HP_SEVEN;
   hp_buffer[11]= HP_FIVE;
   hp_buffer[12]= HP_UPR;

   hp_buffer[13]= HP_ESC;     /* Disable line feed */
   hp_buffer[14]= HP_AMPER;
   hp_buffer[15]= HP_LOWL;
   hp_buffer[16]= HP_ZERO;
   hp_buffer[17]= HP_UPC;

   hp_buffer[18]= HP_ESC;     /* Disable perforation skip */
   hp_buffer[19]= HP_AMPER;
   hp_buffer[20]= HP_LOWL;
   hp_buffer[21]= HP_ZERO;
   hp_buffer[22]= HP_UPL;

   hp_buffer[23]= HP_ESC;     /* Position cursor at (3125,0) */
   hp_buffer[24]= HP_ASTERISK;
   hp_buffer[25]= HP_LOWP;
   hp_buffer[26]= HP_THREE;
   hp_buffer[27]= HP_ONE;
   hp_buffer[28]= HP_TWO;
   hp_buffer[29]= HP_FIVE;
   hp_buffer[30]= HP_LOWX;
   hp_buffer[31]= HP_ZERO;
   hp_buffer[32]= HP_UPY;

   hp_buffer[33] = HP_ESC;     /* set left raster margin  ESC*r1A */
   hp_buffer[34] = HP_ASTERISK;
   hp_buffer[35] = HP_LOWR;
   hp_buffer[36] = HP_ONE;
   hp_buffer[37] = HP_UPA;

  fwrite(hp_buffer, 38*sizeof(char), 1, hpout);

   for (col=0;col<HP_X_SIZE;col++){

      hpplotln[col][0] = 10;       /* change record */
      hpplotln[col][1] = HP_ESC;   /* send raster data ESC*b69W */
      hpplotln[col][2] = HP_ASTERISK;
      hpplotln[col][3] = HP_LOWB;
      hpplotln[col][4] = HP_SIX;
      hpplotln[col][5] = HP_NINE;
      hpplotln[col][6] = HP_UPW;

      /*--------------------------------------------------------*/
      /* Replace bit pattern 00001010 with 00001110 so it won't */
      /* be interpreted as a line feed                          */
      /*--------------------------------------------------------*/
      for (row=7;row<HP_Y_SIZE;row++){
	 if (hpplotln[col][row] == 0x0A)
	    hpplotln[col][row] = 0x0E;
	 }
      }

   fwrite(hpplotln[0], (HP_Y_SIZE)*sizeof(char), HP_X_SIZE, hpout);

   hp_buffer[0] = HP_ESC;      /* raster data completed ESC*rB */
   hp_buffer[1] = HP_ASTERISK;
   hp_buffer[2] = HP_LOWR;
   hp_buffer[3] = HP_UPB;

   hp_buffer[4] = HP_ESC;     /* form feed */
   hp_buffer[5] = HP_EQUAL;

   fwrite(hp_buffer, 6*sizeof(char), 1, hpout);

   hp_initialize_buffer();
}

/*******************************************************************/
/* This routine clears a rectangular portion of the printer buffer */
/*******************************************************************/

hpclregion( xypt )
Pts *xypt;
{
int hpsetbit(int x, int y, int bit_on );
   int row, col;
   Pt temp;
   Pt lower_corner;
   Pt temp1;
   Pt upper_corner;

   temp = scscale( &virs_hp, xypt->xy1 );
   lower_corner = orientw( &temp, &window_hp );
   temp1 = scscale( &virs_hp, xypt->xy2 );
   upper_corner = orientw( &temp1, &window_hp );

   lower_corner = scale_hp_coord( &lower_corner );
   upper_corner = scale_hp_coord( &upper_corner );

   for ( row=lower_corner.y; row<=upper_corner.y; row++ ) {
      for ( col=lower_corner.x; col<=upper_corner.x; col++ ) {
         hpsetbit( col, row, BIT_OFF );
      }
   }
}

/***********************************************************/
/* This routine is used for clearing an area on the screen */
/***********************************************************/

/*
hpclstring( cls )
Cls *cls;
{
   Stringdef text;
   int i;

   text.cstring = ( char * )calloc( 1, (HP_X_SIZE+1)*sizeof(char) );

   for ( i=0; i<(cls->max); i++ ) {
      *(text.cstring+i) = BLANK;
   }
   *(text.cstring+i) = '\0';

   text.rotangle = 0;
   hp_grmovabs( &(cls->pt) );
   hpcharstr( &text );
}*/

/************************************************************/
/* Returns the number of virtual coordinates per centimeter */
/************************************************************/

hpcmsize (xypt)
Pt *xypt;
{
   xypt -> x = XPIXEL_PERCM;
   xypt -> y = YPIXEL_PERCM;
   *xypt = scscale (&hp_virs, xypt);
}

/***********************************************************************/
/* This routine sets the current line drawing pattern depending on the */
/* desired drawing color.  There aren't as many patterns as there are  */
/* colors, so some colors will share the same line pattern.            */
/***********************************************************************/

hpcolor(color)
int color;
{

   switch (color) {
/*      case GRID_COLOR:
         hpdashon();
         hp_set_pixel_skip();
         break;*/
      case CTS_COLOR :
         hpsolidon();
         hp_set_pixel_skip();
         break;
      case MASTER_COLOR:
         hpdashon();
         hp_set_pixel_skip();
         break;
      case LABEL_COLOR:
         hpsolidon();
         hp_set_pixel_skip();
         break;
      default :
         hpsolidon();
         hp_set_pixel_skip();
         break;
      }
}

/******************************************************/
/* This routine puts the HP laser printer into a mode */
/* such that the lines drawn are dashed.              */
/******************************************************/

hpdashon()
{
   hp_ln_style = DASH;
}

/*******************************************/
/* Same as above but draw the line in dash */
/*******************************************/

hpddha( xypt )
Pt *xypt;
{

   hpdashon();
   hpdraw2i( xypt );
   hpsolidon();
}

/***************************************************/
/* Draw a dashed line from the current position to */
/* a distance specified by the input coordinate    */
/***************************************************/

hpddhr( xypt )
Pt *xypt;
{
   hpdashon();
   hprdr2i( xypt );
   hpsolidon();
}

/*************************************************************/
/* same as hpdrawdasha excepts the line is drawn in dot mode */
/*************************************************************/

hpddta( xypt )
Pt *xypt;
{

   hpdoton();
   hpdraw2i( xypt );
   hpsolidon();
}

/***************************************************************/
/* draw a dotted line to relative coordinates specified by the */
/* input coordinates.                                          */
/***************************************************************/

hpddtr( xypt )
Pt *xypt;
{
   hpdoton();
   hprdr2i( xypt );
   hpsolidon();
}

/*******************************/
/* set the printer mode to dot */
/*******************************/

hpdoton()
{
   hp_ln_style = DOT;
}

/****************************************************/
/* A line is drawn from the current position to the */
/* input position                                   */
/****************************************************/

hpdraw2i( xypt )
Pt *xypt;
{
   Pt temp;
   Pt temp1;

   temp = scscale( &virs_hp, xypt );
   temp1 = orientw( &temp, &window_hp );
   temp1 = scale_hp_coord( &temp1 );

   hp_draw_ln( round(temp1.x), round(temp1.y) );
}

/*********************************************************/
/* Same as above but update the current x and y position */
/*********************************************************/

hpdrawchek2i( xypt )
Pt *xypt;
{
   Pt temp;
   Pt temp1;

   temp = scscale( &virs_hp, xypt );
   temp1 = orientw( &temp, &window_hp );
   temp1 = scale_hp_coord( &temp1 );

   hp_draw_ln( round(temp1.x), round(temp1.y) );
}

/******************************************************************/
/* Returns width and height of a character in virtual coordinates */
/******************************************************************/

hpfontset (xy)
Pts *xy;
{
   xy -> xy1.x = (Float) (COLUMN_WIDTH * hp_virs.a);
   xy -> xy1.y = (Float) (ROW_HEIGHT   * hp_virs.e);
   xy -> xy2.x = (Float) (ROW_HEIGHT   * hp_virs.a);
   xy -> xy2.y = (Float) (COLUMN_WIDTH * hp_virs.e);
}

/***********************************************************/
/* hpinit is used to initialize the static scaling factor  */
/* virs_hp, the printer's dimension and the printer buffer */
/* to blank.                                               */
/***********************************************************/

hpinit( hplaser, filename )
Window *hplaser;
char *filename;
{
#ifdef UNIXHOST
   extern void rev_curr_new(char *filename);
#endif
   extern Window virtual_screen;

   int i,len;
   char equnam[DIR_LENGTH],
        *id_get_file_id_string(),
	newname[100],hold[DIR_LENGTH+TESTID_LENGTH+EXT_LENGTH];

#ifdef UNIXHOST
   int  status,
        mode = 1,
        deflg = 4;

   Boolean force = FALSE;
#endif

   int  equlen, ret_code, level = 0;

/*#  ifdef VAXHOST*/
/*      strcpy (name, OVP_C_DIR);  */
/*      strcat (name, ":");        */
/*#  endif*/

#  ifdef UNIXHOST
/*
      ret_code = cae_trnl(OVP_C_DIR, &equlen, equnam, &level,
			   strlen(OVP_C_DIR), sizeof(equnam)-1);
*/
      ret_code = -1;

      if (ret_code != 1)
	 strcpy (equnam, DEFA_OVP_CTS);
      else
	 equnam[equlen] = '\0';
      strcpy (name, equnam);
#  endif

/*   strcat (name, id_get_file_id_string()); */
   strcpy(hold,filename);
   if (strcmp(name,strcat(hold,FILE_EXT))) {
      strcat (name, filename);
      strcat (name, FILE_EXT);
   }

#  ifdef UNIXHOST
/*      rev_curr_c(name, newname, "    ", &force, &mode, &status,
		  strlen(name), sizeof(newname)-1, deflg);*/
      strcpy(newname,name);
      rev_curr_new(newname);
#  endif
#  ifdef VAXHOST
      strcpy (newname, name);
#  endif

   hpout = fopen (newname, "wb");

   /* calculate scaling factor */
   window_hp = *hplaser;

   virs_hp = scscalefind( &virtual_screen, hplaser );
   hp_virs = scscalefind( hplaser, &virtual_screen );

   hp_initialize_buffer();
}

/*************************************************/
/* This routine resets the current position in a */
/* distance relative to the previous position    */
/*************************************************/

hprmv2i( xypt )
Pt *xypt;
{
   Pt temp;

   temp = scscalerel( &virs_hp, xypt );
   temp = scale_hp_coord( &temp );

   hpcurx_pos += round(temp.x);
   hpcury_pos -= round(temp.y);
}

/********************************************************/
/* This routine is to perform a relative draw.  The y's */
/* position is inverted.                                */
/********************************************************/

hprdr2i( xypt )
Pt *xypt;
{
   Pt temp;

   temp = scscalerel( &virs_hp, xypt );
   temp = scale_hp_coord( &temp );

   temp.x += hpcurx_pos;
   temp.y =  hpcury_pos - temp.y;

   hp_draw_ln( round(temp.x), round(temp.y) );
}

/****************************************************/
/* This routine sets the printer to solid line mode */
/****************************************************/

hpsolidon()
{
   hp_ln_style = SOLID;
}

/******************/
/* Draws a border */
/******************/

hp_border ()

{
   Pt init_position,
      end_position;

   hpsolidon ();

   init_position  = window_hp.min;
   end_position.x = window_hp.min.x;
   end_position.y = window_hp.max.y-1;
   hp_line_border (init_position, end_position);

   init_position.x =
   end_position.x  = window_hp.max.x-1;
   hp_line_border (init_position, end_position);

   init_position  = window_hp.min;
   end_position.x = window_hp.max.x-1;
   end_position.y = window_hp.min.y;
   hp_line_border (init_position, end_position);

   init_position.y =
   end_position.y  = window_hp.max.y-1;
   hp_line_border (init_position, end_position);
}

/************************************************************/
/* This routine draw a line from the current postion to the */
/* input coordinate.                                        */
/************************************************************/

hp_draw_ln( xpos, ypos )
int xpos, ypos;
{
int hpsetbit(int x, int y, int bit_on );
   int dx, dy, error, inc = 1;
   int xamount, yamount;
   char xmotion, ymotion, operation;
   Boolean hv_line = FALSE;

   if ( (xpos == hpcurx_pos) ||
        (ypos == hpcury_pos) )
      hv_line = TRUE;

   if ( (dx = (xpos - hpcurx_pos )) < 0 ) {
      dx = -dx;
      xmotion = XMINUS;
   } else {
      xmotion = XPLUS;
   }

   if ( (dy = (ypos - hpcury_pos )) < 0 ) {
      dy = -dy;
      ymotion = YMINUS;
   } else {
      ymotion = YPLUS;
   }

   if ( dy > dx )   {    /* flip x and y actions */
      error = dx;
      dx = dy;
      dy = error;
      operation = xmotion;
      xmotion = ymotion;
      ymotion = operation;
   }

   if (hp_pixel_skip == hp_max_pixel_skip) {
      hpsetbit( hpcurx_pos, hpcury_pos, BIT_ON );
      hp_pixel_skip = (hp_pixel_skip + 1) % (hp_max_pixel_skip+1);
   }

   if ( hv_line )  {    /* draw horizontal or vertical line */
      while( dx > 0)  {
         if ( xmotion & XPLUS )
            hpcurx_pos = (hpcurx_pos + inc <= xpos) ?
                          hpcurx_pos + inc : xpos;

         if ( xmotion & XMINUS )
            hpcurx_pos = (hpcurx_pos - inc >= xpos) ?
                          hpcurx_pos - inc : xpos;

         if ( xmotion & YPLUS )
            hpcury_pos = (hpcury_pos + inc <= ypos) ?
                          hpcury_pos + inc : ypos;

         if ( xmotion & YMINUS )
            hpcury_pos = (hpcury_pos - inc >= ypos) ?
                          hpcury_pos - inc : ypos;

         if (hp_pixel_skip == hp_max_pixel_skip)
            hpsetbit( hpcurx_pos, hpcury_pos, BIT_ON );

         hp_pixel_skip = (hp_pixel_skip + 1) % (hp_max_pixel_skip+1);
         dx -= inc;
      }
   } else {   /* any other type of line */
      error = dx >> 1;
      yamount = dy;
      xamount = dx;
      while( dx > 0 && dx >= inc )  {
         operation = xmotion;
         if ( (error -= yamount) <= 0 )  {
            error += xamount;
            operation |= ymotion;
            dy -= inc;
         }
         if ( operation & XPLUS )  hpcurx_pos = (hpcurx_pos + inc <= xpos) ?
                                                hpcurx_pos + inc : xpos;
         if ( operation & XMINUS ) hpcurx_pos = (hpcurx_pos - inc >= xpos) ?
                                                hpcurx_pos - inc : xpos;
         if ( operation & YPLUS )  hpcury_pos = (hpcury_pos + inc <= ypos) ?
                                                hpcury_pos + inc : ypos;
         if ( operation & YMINUS ) hpcury_pos = (hpcury_pos - inc >= ypos) ?
                                                hpcury_pos - inc : ypos;
         if (hp_pixel_skip == hp_max_pixel_skip)
            hpsetbit( hpcurx_pos, hpcury_pos, BIT_ON );
         hp_pixel_skip = (hp_pixel_skip + 1) % (hp_max_pixel_skip+1);
         dx -= inc;
      }
   }
}

/************************************************************/
/* initialize the printer buffer to blank.  Note that the   */
/* character is stored internally from bit 0 to bit 7.  Bit */
/* 0 is the right most bit.                                 */
/************************************************************/

hp_initialize_buffer()
{
   int row, column;

   for( column=0; column<HP_X_SIZE; column++ ) {
      for( row=0; row<HP_Y_SIZE; row++ ) {
         hpplotln[column][row] = 0X00;
      }
   }
}

hp_line_border (init, end)

Pt init,
   end;

{
   hpcurx_pos = round (init.x);
   hpcury_pos = round (init.y);
   hp_draw_ln (round (end.x), round (end.y));
}

hp_reset_pixel_skip ()
{
   hp_max_pixel_skip =
   hp_pixel_skip     = 0;
}

/********************************************************/
/* Write the printer buffer to the specific output file */
/********************************************************/

hp_send_out()
{
#ifdef UNIXHOST
   extern void rev_curr_new(char *filename);
#endif

   int row, col;

   char command[150],output[150],prtnam[150];

#  ifdef VAXHOST
      struct dsc$descriptor_s desc_command;
      struct dsc$descriptor_s desc_output;
#  endif

#  ifdef UNIXHOST
      char newname[100],
           *lognam = "vsa_hpq",
           hpnam[100];

      int  status,
	   mode = 1,
	   deflg = 4,
           err,
           return_len,
           level=0;

      int  force = FALSE;
#  endif


   /* save the last printer buffer, i.e. the last screen */

   hp_in_send = TRUE;

   hp_buffer[0]= HP_ESC;      /* Reset Printer */
   hp_buffer[1]= HP_UPE;

   hp_buffer[2]= HP_ESC;      /* Set landscape mode   ESC&l1O */
   hp_buffer[3]= HP_AMPER;
   hp_buffer[4]= HP_LOWL;
   hp_buffer[5]= HP_ONE;
   hp_buffer[6]= HP_UPO;

   hp_buffer[7]= HP_ESC;      /* Set dots per inch   ESC*t75R */
   hp_buffer[8]= HP_ASTERISK;
   hp_buffer[9]= HP_LOWT;
   hp_buffer[10]= HP_SEVEN;
   hp_buffer[11]= HP_FIVE;
   hp_buffer[12]= HP_UPR;

   hp_buffer[13]= HP_ESC;     /* Disable line feed */
   hp_buffer[14]= HP_AMPER;
   hp_buffer[15]= HP_LOWL;
   hp_buffer[16]= HP_ZERO;
   hp_buffer[17]= HP_UPC;

   hp_buffer[18]= HP_ESC;     /* Disable perforation skip */
   hp_buffer[19]= HP_AMPER;
   hp_buffer[20]= HP_LOWL;
   hp_buffer[21]= HP_ZERO;
   hp_buffer[22]= HP_UPL;

   hp_buffer[23]= HP_ESC;     /* position cursor at (3125,0) */
   hp_buffer[24]= HP_ASTERISK;
   hp_buffer[25]= HP_LOWP;
   hp_buffer[26]= HP_THREE;
   hp_buffer[27]= HP_ONE;
   hp_buffer[28]= HP_TWO;
   hp_buffer[29]= HP_FIVE;
   hp_buffer[30]= HP_LOWX;
   hp_buffer[31]= HP_ZERO;
   hp_buffer[32]= HP_UPY;

   hp_buffer[33] = HP_ESC;     /* set left raster margin  ESC*r1A */
   hp_buffer[34] = HP_ASTERISK;
   hp_buffer[35] = HP_LOWR;
   hp_buffer[36] = HP_ONE;
   hp_buffer[37] = HP_UPA;

   fwrite(hp_buffer, 38*sizeof(char), 1, hpout);

   for (col=0;col<HP_X_SIZE;col++){

      hpplotln[col][0] = 10;         /* change record */
      hpplotln[col][1] = HP_ESC;     /* send raster data ESC*b69W */
      hpplotln[col][2] = HP_ASTERISK;
      hpplotln[col][3] = HP_LOWB;
      hpplotln[col][4] = HP_SIX;
      hpplotln[col][5] = HP_NINE;
      hpplotln[col][6] = HP_UPW;

      /*--------------------------------------------------------*/
      /* Replace bit pattern 00001010 with 00001110 so it won't */
      /* be interpreted as a line feed                          */
      /*--------------------------------------------------------*/
      for (row=7;row<HP_Y_SIZE;row++){
	 if (hpplotln[col][row] == 0x0A)
	    hpplotln[col][row] = 0x0E;
      }
   }

   fwrite(hpplotln[0], (HP_Y_SIZE)*sizeof(char), HP_X_SIZE, hpout);

   hp_buffer[0] = HP_ESC;      /* raster data completed ESC*rB */
   hp_buffer[1] = HP_ASTERISK;
   hp_buffer[2] = HP_LOWR;
   hp_buffer[3] = HP_UPB;

   hp_buffer[4] = HP_ESC;      /* reset printer*/
   hp_buffer[5] = HP_UPE;

   hp_buffer[6] = HP_ESC;      /* form feed */
   hp_buffer[7] = HP_EQUAL;

   fwrite(hp_buffer, 8*sizeof(char), 1, hpout);

   fclose (hpout);

#  ifdef VAXHOST
      strcpy (command, COMMAND);
      strcat (command, name);
      strcpy (output, OUTPUT);

      /* set up command string for LIB$SPAWN */
      desc_command.dsc$w_length  = strlen(command);
      desc_command.dsc$b_dtype   = DSC$K_DTYPE_T;
      desc_command.dsc$b_class   = DSC$K_CLASS_S;
      desc_command.dsc$a_pointer = command;

      desc_output.dsc$w_length  = strlen(output);
      desc_output.dsc$b_dtype   = DSC$K_DTYPE_T;
      desc_output.dsc$b_class   = DSC$K_CLASS_S;
      desc_output.dsc$a_pointer = output;

      LIB$SPAWN(&desc_command,0,&desc_output);
#  endif

#  ifdef UNIXHOST
/*
      err = cae_trnl(lognam, &return_len, prtnam, &level, strlen(lognam),
                      sizeof(prtnam)-1);
      if (err != 1)
         strcpy(prtnam, DEFA_PRT);
      else
         hpnam [return_len] =0;

      strcpy (command, COMMAND);
      strcpy (command, hpnam);
*/
      strcpy (command, " ");

/*      rev_curr_c_(name, newname, "    ", &force, &mode, &status,
		  strlen(name), sizeof(newname)-1, deflg);*/
      strcpy (newname,name);
      rev_curr_new (newname);

      strcpy (command, COMMAND);
      strcat (command, newname);

      system (command);
#  endif

}

hp_set_pixel_skip ()
{
   switch (hp_ln_style) {
      case DOT:
         hp_pixel_skip = DOT_LINE - 1;
         break;
      case DASH:
         hp_pixel_skip = DASH_LINE - 1;
         break;
      case SOLID:
         hp_pixel_skip = SOLID_LINE - 1;
         break;
      default:
         hp_pixel_skip = SOLID_LINE - 1;
   }
   hp_max_pixel_skip = hp_pixel_skip;
}

/**************************************************/
/* To scale the x and y in printer's coordinates. */
/**************************************************/

Pt scale_hp_coord( pt )
Pt *pt;
{
   Pt temp;

   temp.x =  pt -> x * FAC_X;
   temp.y =  pt -> y * FAC_Y;
   return temp;

}

/*************************************************/
/* This routine sets the location of the printer */
/* buffer to TRUE                                */
/*************************************************/

hpsetbit( x, y, bit_on )
int x, y;
int bit_on;
{
   int byte, bit;
   byte = (y/COLUMN_WIDTH);
   bit  = (y%COLUMN_WIDTH);

   if ( (byte<0) || (byte>=HP_Y_SIZE) ||
        (x<0) || (x>=HP_X_SIZE) )  {
      return;
   }
   if ( bit_on )
      hpplotln[HP_X_SIZE-x-1][byte+7] |= hp_mask[bit];
   else
      hpplotln[HP_X_SIZE-x-1][byte+7] &= ~hp_mask[bit];
}

/****************************************************/
/* set the current position to the input coordinate */
/****************************************************/

hpmove2i( xypt )
Pt *xypt;
{
   Pt temp;
   Pt temp1;

   temp = scscale( &virs_hp, xypt );
   temp1 = orientw( &temp, &window_hp );
   temp1 = scale_hp_coord( &temp1 );

   hpcurx_pos = round( temp1.x);
   hpcury_pos = round( temp1.y);
}
