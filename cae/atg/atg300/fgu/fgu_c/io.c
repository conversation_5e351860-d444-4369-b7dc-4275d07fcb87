/***********************************************************************/
/*    Author: <PERSON> - <PERSON>. 24                                */
/*    Date:   91/4/26                                                  */
/*    File:   io.c                                                     */
/*    Decription:                                                      */
/*                                                                     */
/*    Revision history:                                                */
/***********************************************************************/

#include <stdio.h>
#include <ctype.h>
#include <string.h>

#define eof  '\377'

static FILE *file[99];

/************************* io_open() ******************************************
     The io_open() function simulates the Fortran OPEN statement.  Presently,
this function will only open a file for sequential access.

Arguments:
unit     - is an integer between 0 & 99 (No units are reserved such as 5 & 6)
filename - is a string constant or a string pointer representing the file
           name to open.
type     - is a string constant or a string pointer specifying the files 
           status.  Either "new", "old", "unknown" or "readonly".

     The io_open() function returns an integer either true (1) or false (0) 
indicating weather the open was successful or unsuccessful.
*/

int io_open(unit,filename,type)
int  unit;
char *filename;
char *type;
{
   char *to_upper_case(char *string);
   short int string_compare(char *string1,char *string2,int len);
   FILE *file_ptr;
   char *mode;
   int iostat;

   to_upper_case(type);
   if (string_compare(type,"NEW",3))
      mode = "w+";    /* Open a new file for sequential write */
   else if (string_compare(type,"OLD",3))
      mode = "r+";    /* Open an existing file for seq. read & write */
   else if (string_compare(type,"READONLY",8))
      mode = "r";    /* Open an existing file for seq. read & write */
   else if (string_compare(type,"UNKNOWN",7))
      mode = "a+";    /* Open a new or existing file for seq. write */
   else return 0;    /* Return false */

   /*********** Open file: *************/
   if ((file_ptr = fopen(filename,mode)) == NULL) {
      iostat = 0;    /* The open was not successful */
      return iostat;  /* Return false                */
   } else iostat = 1;
   file[unit] = file_ptr;
   return iostat;
}

/************************* io_read() ******************************************
     The io_read() function simulates a Fortran sequential unformatted READ.

Arguments:
unit   - is an integer between 0 & 99 specifying a unit which has been
         preveously opend by the io_open() function.
string - is a char pointer pointing to a string large enough to hold the
         value read and a null character ('\0').  The io_read() function places
         the null character at the logical end of string.

     The io_read() function returns an integer either true (1) if EOF was not
reached, or false (0) if EOF was encountered and no characters were read.
*/

int io_read(unit,string)
int unit;
char *string;
{
   int sub1=0;
   char c;

   c = getc(file[unit]);              /* Read a character from file.    */
   while (c!=EOF && c!=10 && c!=eof) { /* While not end of line and EOF  */
      *(string+sub1) = c;             /* No, place character in string. */
      c = getc(file[unit]);           /* Read a character from file.    */
      sub1++;                         /* Increment string subscript     */
   }
   *(string+sub1) = '\0';             /* Mark end of line.              */
   if ((c==EOF || c==eof) && sub1==0) /* To have an EOF condition, no   */
      return 0;                       /* characters must has been read. */
   else
      return 1;
}

/************************* io_write() *****************************************
     The io_write() function simulates a Fortran sequential unformatted WRITE.

Arguments:
unit   - is an integer between 0 & 99 specifying a unit which has been
         preveously opend by the io_open() function.
string - is a char pointer pointing to the string that will be written to the
         file.  The io_write() function continues to write string to the file
         until the null character ('\0') is encountered.  If there is no null
         character in string the results written to the file are unknown.

     The io_write() function returns an integer either true (1) if the write
was successful, or false (0) if unsuccessful.
*/

int io_write(unit,string)
int unit;
char *string;
{
   int iostat=1;

   iostat = fprintf(file[unit],"%s\n",string);

   if (iostat<0)
      return 0;
   else
      return 1;
}

/************************* io_close() *****************************************
     The io_close() function simulates a Fortran CLOSE statement.

Arguments:
unit   - is an integer between 0 & 99 specifying a unit which has been
         preveously opend by the io_open() function.

     The io_close() function flushes all buffers associated with the file to
disk before closing.  It is an error to try and close a file that is already
closed.  The io_close() function returns an integer either true (1) if the
close was successful, or false (0) if unsuccessful.
*/

int io_close(unit)
int unit;
{
#ifdef VAXHOST
  #define NULL 0
#endif
   int iostat=1;

   iostat = fclose(file[unit]);
   if (iostat == 0) {
      file[unit] = NULL;
      return 1;
   }
   else
      return 0;
}

/************************ string_compare() **************************/
short int string_compare(string1,string2,len)
char *string1,*string2;
int len;
{
   short int same=0; /* false */
   int count;

   for (count = 1; count <= len; count++) {
      if (*string1 == *string2)
         same = 1; /* true */
      else {
         same = 0; /* false */
         break;
      }
      string1++;
      string2++;
   }
   return(same);
}

/************************ to_upper_case() ***************************/
char *to_upper_case(string)
char *string;
{
   char line[133];
   int i,len=strlen(string);

   for (i=0; i<=len; i++) {
      line[i] = *(string+i);
      if (line[i] >= 97 && line[i] <= 122)
         line[i] -= 32;
      *(string+i) = line[i];
   }

   return(string);
}
