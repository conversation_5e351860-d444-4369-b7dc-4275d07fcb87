/************************************************************************/
/*									*/
/*			INCLUDE FILE HP					*/
/* Author : <PERSON> Toulouse						*/
/* Date   : May 1991						        */
/************************************************************************/

#define HP_ESC      0x1B
#define HP_SPACE    0x20
#define HP_EXCLAM   0x21
#define HP_QUOTE    0x22
#define HP_NUMSIGN  0x23
#define HP_DOLLS    0x24
#define HP_PERSIGN  0x25
#define HP_AMPER    0x26
#define HP_APOSTR   0x27
#define HP_OPENP    0x28
#define HP_CLOSEP   0x29
#define HP_ASTERISK 0x2A
#define HP_PLUS     0x2B
#define HP_COMMA    0x2C
#define HP_HYPHEN   0x2D
#define HP_PERIOD   0x2E
#define HP_SLANT    0x2F
#define HP_ZERO     0x30
#define HP_ONE      0x31
#define HP_TWO      0x32
#define HP_THREE    0x33
#define HP_FOUR     0x34
#define HP_FIVE     0x35
#define HP_SIX      0x36
#define HP_SEVEN    0x37
#define HP_EIGHT    0x38
#define HP_NINE     0x39
#define HP_COLON    0x3A
#define HP_SEMI     0x3B
#define HP_LESS     0x3C
#define HP_EQUAL    0x3D
#define HP_GREATER  0x3E
#define HP_QUESTION 0x3F
#define HP_AT       0x40
#define HP_UPA      0x41
#define HP_UPB      0x42
#define HP_UPC      0x43
#define HP_UPD      0x44
#define HP_UPE      0x45
#define HP_UPF      0x46
#define HP_UPG      0x47
#define HP_UPH      0x48
#define HP_UPI      0x49
#define HP_UPJ      0x4A
#define HP_UPK      0x4B
#define HP_UPL      0x4C
#define HP_UPM      0x4D
#define HP_UPN      0x4E
#define HP_UPO      0x4F
#define HP_UPP      0x50
#define HP_UPQ      0x51
#define HP_UPR      0x52
#define HP_UPS      0x53
#define HP_UPT      0x54
#define HP_UPU      0x55
#define HP_UPV      0x56
#define HP_UPW      0x57
#define HP_UPX      0x58
#define HP_UPY      0x59
#define HP_UPZ      0x5A
#define HP_LOWA     0x61
#define HP_LOWB     0x62
#define HP_LOWC     0x63
#define HP_LOWD     0x64
#define HP_LOWE     0x65
#define HP_LOWF     0x66
#define HP_LOWG     0x67
#define HP_LOWH     0x68
#define HP_LOWI     0x69
#define HP_LOWJ     0x6A
#define HP_LOWK     0x6B
#define HP_LOWL     0x6C
#define HP_LOWM     0x6D
#define HP_LOWN     0x6E
#define HP_LOWO     0x6F
#define HP_LOWP     0x70
#define HP_LOWQ     0x71
#define HP_LOWR     0x72
#define HP_LOWS     0x73
#define HP_LOWT     0x74
#define HP_LOWU     0x75
#define HP_LOWV     0x76
#define HP_LOWW     0x77
#define HP_LOWX     0x78
#define HP_LOWY     0x79
#define HP_LOWZ     0x7A

typedef enum { SOLID, DOT, DASH } Linestyle;

/*---------------------------------------*/
/* Define characters for raster graphics */
/*---------------------------------------*/
static short word[96][4] = {
                0x0000,0x0000,0x0000,0x0000,    /* space */
                0x0000,0x5F00,0x0000,0x0000,    /* ! */   
                0x0007,0x0007,0x0000,0x0000,    /* " */
                0x147F,0x147F,0x1400,0x0000,    /* # */
                0x242A,0x7F2A,0x1200,0x0000,    /* $ */
                0x6313,0x0864,0x6300,0x0000,    /* % */
                0x304E,0x5926,0x5000,0x0000,    /* & */
                0x0000,0x0403,0x0000,0x0000,    /* ' */
                0x1C22,0x4100,0x0000,0x0000,    /* ( */
                0x0000,0x4122,0x1C00,0x0000,    /* ) */
                0x1408,0x3E08,0x1400,0x0000,    /* * */
                0x0808,0x3E08,0x0800,0x0000,    /* + */
                0x0000,0x8060,0x0000,0x0000,    /* ' */
                0x0808,0x0808,0x0800,0x0000,    /* - */
                0x0000,0x4000,0x0000,0x0000,    /* . */
                0x6010,0x0804,0x0300,0x0000,    /* / */
                0x3E51,0x4945,0x3E00,0x0000,    /* 0 */
                0x0042,0x7F40,0x0000,0x0000,    /* 1 */
                0x6251,0x4949,0x4600,0x0000,    /* 2 */
                0x2141,0x494D,0x3300,0x0000,    /* 3 */
                0x1814,0x127F,0x1000,0x0000,    /* 4 */
                0x2745,0x4545,0x3900,0x0000,    /* 5 */
                0x3C4A,0x4949,0x3100,0x0000,    /* 6 */
                0x0171,0x0905,0x0300,0x0000,    /* 7 */
                0x3649,0x4949,0x3600,0x0000,    /* 8 */
                0x4649,0x4929,0x1E00,0x0000,    /* 9 */
                0x0000,0x6600,0x0000,0x0000,    /* : */
                0x0040,0x3200,0x0000,0x0000,    /* ; */
                0x0814,0x2241,0x0000,0x0000,    /* < */
                0x0014,0x1414,0x1400,0x0000,    /* = */
                0x0041,0x2214,0x0800,0x0000,    /* > */
                0x0201,0x5109,0x0600,0x0000,    /* ? */
                0x3249,0x5121,0x5E00,0x0000,    /* @ */
                0x7C12,0x1112,0x7C00,0x0000,    /* A */
                0x7F49,0x4949,0x3600,0x0000,    /* B */
                0x3E41,0x4141,0x2200,0x0000,    /* C */
                0x417F,0x4141,0x3E00,0x0000,    /* D */
                0x7F49,0x4949,0x4100,0x0000,    /* E */
                0x7F09,0x0901,0x0100,0x0000,    /* F */
                0x3E41,0x4151,0x7200,0x0000,    /* G */
                0x7F08,0x0808,0x7F00,0x0000,    /* H */
                0x0041,0x7F41,0x0000,0x0000,    /* I */
                0x2040,0x4040,0x3F00,0x0000,    /* J */
                0x7F08,0x1422,0x4100,0x0000,    /* K */
                0x7F40,0x4040,0x4000,0x0000,    /* L */
                0x7F02,0x0C02,0x7F00,0x0000,    /* M */
                0x7F04,0x0810,0x7F00,0x0000,    /* N */
                0x3E41,0x4141,0x3E00,0x0000,    /* O */
                0x7F09,0x0909,0x0600,0x0000,    /* P */
                0x3E41,0x5121,0x5E00,0x0000,    /* Q */
                0x7F09,0x1929,0x4600,0x0000,    /* R */
                0x2649,0x4949,0x3200,0x0000,    /* S */
                0x0101,0x7F01,0x0100,0x0000,    /* T */
                0x3F40,0x4040,0x3F00,0x0000,    /* U */
                0x0718,0x6018,0x0700,0x0000,    /* V */
                0x7F20,0x1820,0x7F00,0x0000,    /* W */
                0x6314,0x0814,0x6300,0x0000,    /* X */
                0x0304,0x7804,0x0300,0x0000,    /* Y */
                0x6151,0x4945,0x4300,0x0000,    /* Z */
                0x007F,0x4141,0x0000,0x0000,    /* [ */
                0x0204,0x0810,0x0200,0x0000,    /*   */
                0x0041,0x417F,0x0000,0x0000,    /* ] */
                0x0402,0x0102,0x0400,0x0000,    /* ^ */
                0x4040,0x4040,0x4000,0x0000,    /*   */
                0x0002,0x0408,0x0000,0x0000,    /*   */
                0x2054,0x5454,0x2800,0x0000,    /* a */
                0x7B44,0x4444,0x3800,0x0000,    /* b */
                0x3844,0x4444,0x0800,0x0000,    /* c */
                0x3844,0x4444,0x7B00,0x0000,    /* d */
                0x3854,0x5454,0x1800,0x0000,    /* e */
                0x047E,0x0501,0x0200,0x0000,    /* f */
                0x4854,0x5454,0x6A00,0x0000,    /* g */
                0x7B04,0x0404,0x7800,0x0000,    /* h */
                0x447D,0x4000,0x0000,0x0000,    /* i */
                0x2040,0x4040,0x3D00,0x0000,    /* j */
                0x7F10,0x2844,0x0000,0x0000,    /* k */
                0x417F,0x4000,0x0000,0x0000,    /* l */
                0x7C04,0x7804,0x7C00,0x0000,    /* m */
                0x7C04,0x0478,0x0000,0x0000,    /* n */
                0x3844,0x4444,0x3800,0x0000,    /* o */
                0x6C14,0x1414,0x0800,0x0000,    /* p */
                0x3844,0x5424,0x5800,0x0000,    /* q */
                0x0478,0x0404,0x0800,0x0000,    /* r */
                0x0854,0x5454,0x2000,0x0000,    /* s */
                0x043F,0x4440,0x2000,0x0000,    /* t */
                0x3C40,0x407C,0x0000,0x0000,    /* u */
                0x0C30,0x4030,0x0C00,0x0000,    /* v */
                0x7C20,0x1020,0x7C00,0x0000,    /* w */
                0x4428,0x1028,0x4400,0x0000,    /* x */
                0x0C50,0x5050,0x2C00,0x0000,    /* y */
                0x4464,0x544C,0x4400,0x0000,    /* z */
                0x0808,0x3641,0x4100,0x0000,    /* { */
                0x0000,0x7700,0x0000,0x0000,    /* | */
                0x4141,0x3608,0x0000,0x0000,    /* } */
                0x0601,0x0206,0x0100,0x0000,    /* ~ */
                0x7F7F,0x7F7F,0x7F00,0x0000     /* block */ 
           };
