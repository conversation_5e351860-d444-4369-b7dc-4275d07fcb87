#include <smgdef.h>
#include <ssdef.h>
#include <descrip.h>
#include <stdio.h>
  
/************************************************************************/
/*									*/
/* Author : <PERSON><PERSON><PERSON>						*/
/* Date   : 09.86							*/
/* Description : Function "readstring":					*/
/* 	         Reads the a string on the keyboard and returns it.	*/
/*									*/
/* Note   : This function uses the VAX VMS Run-Time Library routines.	*/
/*									*/
/************************************************************************/
extern unsigned int
	keyboard_id;
  
readstring (string, length)
  
char *string;
int  length;
  
{
  
   int
	max_length,
	return_code;
  
   unsigned short
	rec_length,
	term_code;
  
   struct dsc$descriptor_s
	desc_rec_text;
  
 
   desc_rec_text.dsc$w_length  = length;
   desc_rec_text.dsc$b_dtype   = DSC$K_DTYPE_T;
   desc_rec_text.dsc$b_class   = DSC$K_CLASS_S;
   desc_rec_text.dsc$a_pointer = string;
   string [ length - 1 ]       = '\0';
  
   max_length = length;
  
   return_code = SMG$READ_STRING (&keyboard_id, &desc_rec_text, NULL,
		 &max_length, NULL, NULL, NULL, &rec_length, &term_code,
		 NULL, NULL);
  
   if (!(return_code & 1)) {
/*      print_message (return_code);  VGS */
      string [0] = '\0';
      }
  
}
