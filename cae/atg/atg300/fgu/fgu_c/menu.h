
#define  WHOLE                 24
#define  ONE                    1
#define  BOLD_UNDERLINING       3
#define  BOLD_INV_UNDERLINING  -3
#define  NORMAL                 2 
#define  INVERSE               -2
#define  BOLD                  -1
#define  BOLD_INVERSE           1

#define  MENUACTIVE(SELECT,MENU)                   \
               SELECT   = menuactive (MENU),       \
               SELECT.x = (MENU->winsize.x == 1 && \
               SELECT.x  != EOF) ? 1  : SELECT.x


struct  menuopt_struc  {         /*  for one menu option                  */
        char *label;             /*  pointer to  character string         */ 
        int   color;             /*  indicates how to display an option   */
        int   size;              /*  size of an option in characters      */
} ;
typedef struct menuopt_struc  Menuoption;

struct  list_struc {             /*  for one row of menu                  */ 
        int         current;     /*  integer describing
                                     which option is displayed as first
                                     (at 0 x-coordinate)                  */
        int         total;       /*  integer describing the total
                                     number  of elements per row          */
        Menuoption  *opt;        /*  pointer to array of one line of
                                     menu options */
};
typedef  struct  list_struc List;

struct  menu_struc {             /*  for  menu                            */
        Ipt         winsize;     /*  #of lines and  # of columns in menu  */
        Ipt         location;    /*  starting position  of  menu          */
        Ipt         select;      /*  which option was selected            */
        List        lista[WHOLE+1];/*  array of all menuoptions             */
};

typedef  struct menu_struc Menu;

typedef  int (*Function)();

typedef enum { 
       Main, Cts, Validate, Input, Plot, Option } Menulocation;

struct func_menu_struc {
       Menulocation name;
       int kind;
       Menu *menu;
       Function *func_ptr;  /* Pointer to option Selected */
};

typedef  struct func_menu_struc Func_menu;

