#include <stdio.h>
#include <ctype.h>
#include <math.h>
#include "ovp_prec.h"
#include "ovp.h"
#include "graph.h"
#include "ovp_stru.h"
#ifdef VAXHOST
#  include <descrip.h>  /* Get layout of descriptors used in fgu_gr() */
#endif

/***** Printer global variables from fgu_print.c *****/
extern int to_printer; /* Logical to redirect output to print buffer. */
extern float increase; /* Multiplied to the picture size in the print buffer.*/
extern int y_margin;   /* Bottom of page margin in dots. 121.6 dots = 1"     */
extern int x_margin;   /* Left of page margin in dots.    85.0 dots = 1"     */

/***** Menuing variables *****/
Ipt  menu_pos[2][1000];
char menu_item[2][1000][40], menu_labels[1000][40];
HLS_color menu_color[2][1000];
int  current_menu_item[2], starting_item[2]={0,0};
int  ending_item[2]={0,0}, menu_in_use=1;
char menu_file_record[200][80];
short int menu_file_size;

/***** extern variables from gr.c *****/
extern short int UP_LEFT_CORNER_X;
extern short int UP_LEFT_CORNER_Y;
extern short int LO_RIGHT_CORNER_X;
extern short int LO_RIGHT_CORNER_Y;

extern short int COMMAND_MENU;
extern short int LABEL_MENU;

extern int zoom_status;

/************************* scale_point() ************************************
     The function scale_point makes sure that a function curve stays
within the graph on the screen.   This is required when a zoom area is setup
with the 'ZOOM' option.  Linear slope calculations are required when a
function curve goes beyond the graph.
     This function returns false (0) if both points contained in 'line' are
not within the graph area.  This would indicate that the line should not be
drawn.

Note:  The points specified in 'line' must be in pixel corrdinates.

     The size and location of the graph is known by the following pre-defined
global varibles (in pixels):

         UP_LEFT_CORNER_X
         UP_LEFT_CORNER_Y
         LO_RIGHT_CORNER_X
         LO_RIGHT_CORNER_Y

     This function has been modified to keep the curve within the pixel
limits indicated by the 'SCREEN' variables.
*/
int scale_point(line)
Pts *line;
{
/* Logicals indicating if the two points in 'line' are within the graph. */
   int first_point=1, second_point=1; /* Initialized to TRUE. */

   int SCREEN_UP_LEFT_X  =   1;
   int SCREEN_UP_LEFT_Y  = 479;
   int SCREEN_LO_RIGHT_X = 659;
   int SCREEN_LO_RIGHT_Y =  42;

/* Slope variables: */
/* The following slope variables are calculated if one point in the variable */
/* 'line' is outside the graph and one is inside.   These slopes are         */
/* calculated from one of the four corners of the graph to the point in      */
/* 'line' which is inside the graph. */
   float up_left_slope, lo_left_slope, up_right_slope, lo_right_slope;

/* line_slope is the slope of the variable 'line'.  This slope is calculated */
/* when one point in 'line' is outside the graph and one is inside. */
   float line_slope;

/* newx & newy will contain a calculated point which lands somewhere on */
/* one of the four graph border lines. */
   double newx, newy;

/* xin & yin will contain a point which lands inside the graph. */
   double xin, yin;

/* xout & yout will contain a point which lands outside the graph. */
   double xout, yout;

   if (zoom_status) {
      SCREEN_UP_LEFT_X  = UP_LEFT_CORNER_X;
      SCREEN_UP_LEFT_Y  = UP_LEFT_CORNER_Y;
      SCREEN_LO_RIGHT_X = LO_RIGHT_CORNER_X;
      SCREEN_LO_RIGHT_Y = LO_RIGHT_CORNER_Y;
   }

   /* Check if points in 'line' are within the graph area. */
   if ((*line).xy1.y>SCREEN_UP_LEFT_Y+1 || (*line).xy1.y<SCREEN_LO_RIGHT_Y-1 ||
       (*line).xy1.x>SCREEN_LO_RIGHT_X+1 || (*line).xy1.x<SCREEN_UP_LEFT_X-1)
      first_point=0;
   if ((*line).xy2.y>SCREEN_UP_LEFT_Y+1 || (*line).xy2.y<SCREEN_LO_RIGHT_Y-1 ||
       (*line).xy2.x>SCREEN_LO_RIGHT_X+1 || (*line).xy2.x<SCREEN_UP_LEFT_X-1)
      second_point=0;

   /* Check if both points are outside the graph. */
   if (!first_point && !second_point) {
      /**** Check if both points are outside the graph but the line passes   */
      /**** through the graph area.  Six different occurrences are possible. */
      /*
                /\
             --/-|\-- <--- Given that the box in the center is the graph.
             |1  | 2|      The other lines represent all six possibilities
             /   |  \      of a line that can passes through the graph area.
            /|   6  |\
           /_|_5_|__|_\
           \ |   |  | /
            4|   |  |3
             \   |  /
             |\  | /|
             --\-|/--
                \/
      */

      /**** Do a quick check to see if the line might pass through the graph.*/
      if ((*line).xy1.x<=SCREEN_UP_LEFT_X && 
          (*line).xy2.x<=SCREEN_UP_LEFT_X)
         return(0);
      else if ((*line).xy1.x>=SCREEN_LO_RIGHT_X && 
               (*line).xy2.x>=SCREEN_LO_RIGHT_X)
         return(0);
      else if ((*line).xy1.y<=SCREEN_LO_RIGHT_Y && 
               (*line).xy2.y<=SCREEN_LO_RIGHT_Y)
         return(0);
      else if ((*line).xy1.y>=SCREEN_UP_LEFT_Y && 
               (*line).xy2.y>=SCREEN_UP_LEFT_Y)
         return(0);
      else {
      /**** Rats! It might pass through the graph. */
         short int lfirst=0;
         float m, b;

         /**** Prevent division by zero. */
         if ((*line).xy1.x == (*line).xy2.x)
            (*line).xy1.x += 0.00000001;
         if ((*line).xy1.y == (*line).xy2.y)
            (*line).xy1.y += 0.00000001;

         /**** Calculate variables for equation of line. */
         m = ((*line).xy2.y - (*line).xy1.y) / ((*line).xy2.x - (*line).xy1.x);
         b = (*line).xy1.y - m * (*line).xy1.x;

         /**** Does line pass through the graph area? */
         if (SCREEN_LO_RIGHT_Y < m * SCREEN_UP_LEFT_X + b &&
             m * SCREEN_UP_LEFT_X + b < SCREEN_UP_LEFT_Y) {
            (*line).xy1.x = SCREEN_UP_LEFT_X;
            (*line).xy1.y = m * SCREEN_UP_LEFT_X + b;
            lfirst = 1;
         }
         if (SCREEN_UP_LEFT_X < (SCREEN_LO_RIGHT_Y - b) / m &&
             (SCREEN_LO_RIGHT_Y - b) / m < SCREEN_LO_RIGHT_X) {
            if (lfirst) {
               (*line).xy2.x = (SCREEN_LO_RIGHT_Y - b) / m;
               (*line).xy2.y = SCREEN_LO_RIGHT_Y;
            }
            else {
               (*line).xy1.x = (SCREEN_LO_RIGHT_Y - b) / m;
               (*line).xy1.y = SCREEN_LO_RIGHT_Y;
               lfirst = 1;
            }
         }
         if (SCREEN_LO_RIGHT_Y < m * SCREEN_LO_RIGHT_X + b &&
             m * SCREEN_LO_RIGHT_X + b < SCREEN_UP_LEFT_Y) {
            if (lfirst) {
               (*line).xy2.x = SCREEN_LO_RIGHT_X;
               (*line).xy2.y = m * SCREEN_LO_RIGHT_X + b;
            }
            else {
               (*line).xy1.x = SCREEN_LO_RIGHT_X;
               (*line).xy1.y = m * SCREEN_LO_RIGHT_X + b;
               lfirst = 1;
            }
         }
         if (SCREEN_UP_LEFT_X < (SCREEN_UP_LEFT_Y - b) / m &&
             (SCREEN_UP_LEFT_Y - b) / m < SCREEN_LO_RIGHT_X && lfirst) {
            (*line).xy2.x = (SCREEN_UP_LEFT_Y - b) / m;
            (*line).xy2.y = SCREEN_UP_LEFT_Y;
         }
         if (!lfirst)
            return(0);
         else {
            start_pixel((int)(*line).xy1.x,(int)(*line).xy1.y);
            return(1);
         }
      }
   }

   /* Check if both points are inside the graph. */
   if (first_point && second_point)
      return(1);

   /* One point is inside and one point is outside. */
   /* Note:  The fraction 0.00000001 is subtracted from the line corrdinates */
   /*        to prevent a division by zero when calculating the slopes. */
   if (first_point) {   /* Is first point in 'line' within the graph? */
      xin = (*line).xy1.x - 0.00000001; /* Yes. */
      yin = (*line).xy1.y;
      xout= (*line).xy2.x + 0.00000001;
      yout= (*line).xy2.y;
   }
   else {               /* No, the second point is within the graph. */
      xout= (*line).xy1.x - 0.00000001;
      yout= (*line).xy1.y;
      xin = (*line).xy2.x + 0.00000001;
      yin = (*line).xy2.y;
   }

   if (fabs(xout-xin) <= 0)     /* Prevent division by zero when */
      xout = xin + 0.00000001;  /* calculating line_slope. */
   if (fabs(yout-yin) <= 0)
      yout = yin - 0.00000001;
   line_slope=(yout-yin)/(xout-xin);

   /* Decide which slop (from the four corners of the graph) to use. */
   if (yout>=yin && xout<=xin) {       /* Use up_left_slope. */
      up_left_slope=(SCREEN_UP_LEFT_Y-yin)/(SCREEN_UP_LEFT_X-xin);
      if (line_slope>=up_left_slope) { /* If true x is known, y is not. */
         newx=SCREEN_UP_LEFT_X;
         newy=line_slope*(SCREEN_UP_LEFT_X-xin)+yin;
      }
      else {                           /* y is known, x is not. */
         newy=SCREEN_UP_LEFT_Y;
         newx=(1/line_slope)*(SCREEN_UP_LEFT_Y-yin)+xin;
      }
   }
   else if (yout<=yin && xout<=xin) { /* Use lo_left_slope. */
      lo_left_slope=(SCREEN_LO_RIGHT_Y-yin)/(SCREEN_UP_LEFT_X-xin);
      if (line_slope<=lo_left_slope) { /* If true x is known, y is not. */
         newx=SCREEN_UP_LEFT_X;
         newy=line_slope*(SCREEN_UP_LEFT_X-xin)+yin;
      }
      else {                           /* y is known, x is not. */
         newy=SCREEN_LO_RIGHT_Y;
         newx=(1/line_slope)*(SCREEN_LO_RIGHT_Y-yin)+xin;
      }
   }
   else if (yout>=yin && xout>=xin) { /* Use up_right_slope. */
      up_right_slope=(SCREEN_UP_LEFT_Y-yin)/(SCREEN_LO_RIGHT_X-xin);
      if (line_slope<=up_right_slope) { /* If true x is known, y is not. */
         newx=SCREEN_LO_RIGHT_X;
         newy=line_slope*(SCREEN_LO_RIGHT_X-xin)+yin;
      }
      else {                           /* y is known, x is not. */
         newy=SCREEN_UP_LEFT_Y;
         newx=(1/line_slope)*(SCREEN_UP_LEFT_Y-yin)+xin;
      }
   }
   else if (yout<=yin && xout>=xin) { /* Use lo_right_slope. */
      lo_right_slope=(SCREEN_LO_RIGHT_Y-yin)/(SCREEN_LO_RIGHT_X-xin);
      if (line_slope>=lo_right_slope) { /* If true x is known, y is not. */
         newx=SCREEN_LO_RIGHT_X;
         newy=line_slope*(SCREEN_LO_RIGHT_X-xin)+yin;
      }
      else {                           /* y is known, x is not. */
         newy=SCREEN_LO_RIGHT_Y;
         newx=(1/line_slope)*(SCREEN_LO_RIGHT_Y-yin)+xin;
      }
   }

   /* Place new point in 'line'. */
   if (!first_point) {
      (*line).xy1.x = newx;
      (*line).xy1.y = newy;
      start_pixel((int)newx,(int)newy);
   }
   else {
      (*line).xy2.x = newx;
      (*line).xy2.y = newy;
   }
   return(1);
}

/************************** read_value() ****************************
     This function reads input from the keyboard in one of two types:
NUMERIC, or ALPHNUM.  The function requires a screen location from
which the characters entered will be echoed to the screen.  A length is
required to know when the limit of the string has been reached.  The
results of the input are placed in the string that 'result_ptr' points
to. */

char *read_value(location,type,length,result_ptr,control)
Ipt location;     /* Where cursor will appear. */
char type[7];     /* Type of string to read: NUMERIC, ALPHNUM */
int length;       /* Length of string to read. */
char *result_ptr; /* Points to final result of this function. */
int *control;    /* Points to ASCII value of last non-visable char pressed. */
{
/* CFORMAT = The escape sequence for printing ONE character any where
             on the screen in reverse. */
/* SFORMAT = The escape sequence for printing a string any where
             on the screen in reverse. */
/*#define DEBUG       "\033[%d;%dH%3d"*/
#define CFORMAT      "\033[%d;%dH%s%c"
#define SFORMAT      "\033[%d;%dH%s%s%s"
#define REVERSE1     "\033[1;7m"
#define REVERSE2     "\033[0;7m"
#define NORMAL       "\033[0m"
#define LOCATE       "\033[%d;%dH"
#define NUMERIC     1
#define ALPHNUM     0

   char letter;            /* Holds value of keyboard char pressed. */
   int ascii;              /* holds ascii value of key pressed. */
   int size=0;             /* Size of string currently entered. */
   short int mode;
   extern short int term_status; /* From vaxgraph.c */

   if (!strncmp(type,"NUMERIC",7))  /* Was NUMERIC specified? */
      mode = NUMERIC;
   else
      mode = ALPHNUM;

   while (size != length) {
      /* Positions cursor on the screen */
      fprintf(stdout,LOCATE,location.y,location.x);
#     ifdef UNIXHOST
         fflush(stdout);
#     endif
      ascii = readkey(); /* Wait for key stroke */
      letter = ascii;
      /*
            Check for Return key, Backspace, Arrow keys, ect...
      */
      /* Was a non-visable character pressed? (ex: Return) */
      if ((ascii >= 0 && ascii <= 31) || (ascii >= 274 && ascii <= 316)) {
         size = length;
         break;
      }
      /*
            Look for regular keyboard characters (Numbers & Letters):
      */
      else if (letter > 31 && letter < 127) {   /* Accept only visable chars */
         if (mode == NUMERIC) { /* Was NUMERIC specified? */
            /* Accept only numeric input */
            if (isdigit(letter)!=0 || letter=='.' || letter=='-') {
               if (size==0)           /* If first character, clear the field */
                  fprintf(stdout,"\033[%d;%dH%s%*s",location.y,
                     location.x+1,REVERSE1,length-1,"");
               fprintf(stdout,CFORMAT,location.y,location.x,
                  REVERSE2,letter); /* Echo character to screen */
               *result_ptr++ = letter; /*Advance result pointer keeping value*/
               location.x++;           /* Advance cursor */
               size++;                 /* Increase current string size */
            }
         }
         else if (mode == ALPHNUM) { /* Was ALPHNUM specified? */
            fprintf(stdout,CFORMAT,location.y,location.x,REVERSE2,letter);
            *result_ptr++ = letter;
            location.x++;
            size++;
         }
      }
      else if (letter == 127 && size > 0) { /* Backspace key? */
            size--;
            fprintf(stdout,CFORMAT,location.y,location.x-1,REVERSE1,' ');
            location.x--;
            *result_ptr--;
            /* Positions cursor on the screen */
            fprintf(stdout,LOCATE,location.y,location.x);
      }
      else if (mode != ALPHNUM && mode != NUMERIC) {
         fprintf(stdout,SFORMAT,24,14,REVERSE1,
            "Character type specified in read_value() is unknown.",
            NORMAL);
         size = length;
         break;
      }
/*      fprintf(stdout,DEBUG,22,1,letter);  Temporary line */
   }
   *control = ascii;
   *result_ptr++ = '\0';

   fprintf(stdout,NORMAL); /* Set terminal attributes to normal (lowlight). */
   term_status = 5;        /* Terminal status 5 = lowlight. */

   return(result_ptr);
}

/************************** erase_graph() *******************************/
void erase_graph(void)
{
   int i;

   graphics_on();
   fprintf(stdout,"P[0,0]");   /* Set starting point. */
   fprintf(stdout,"W(S1)");    /* Turn shading ON.    */
   fprintf(stdout,"V(W(I0))"); /* Temporary vector command. (color I0) */
   fprintf(stdout,"[,+459]");  /* Box in area to erase:     */
   fprintf(stdout,"[+658]");
   fprintf(stdout,"[,-459]");
   fprintf(stdout,"[-658]");
   fprintf(stdout,"W(S0)");    /* Turn shading OFF.   */
}

/************************ erase_bar_menu() ********************************/
void erase_bar_menu(void)
{
   int i;

   graphics_on();
   fprintf(stdout,"P[660,0]"); /* Set starting point. */
   fprintf(stdout,"W(S1)");    /* Turn shading ON.    */
   fprintf(stdout,"V(W(I0))"); /* Temporary vector command. (color I0) */
   fprintf(stdout,"[,+479]");  /* Box in area to erase:     */
   fprintf(stdout,"[+799]");
   fprintf(stdout,"[,-479]");
   fprintf(stdout,"[-139]");
   fprintf(stdout,"W(S0)");    /* Turn shading OFF.   */
}

/*************************** y_value() **************************************
Scaling formula:
                       (D - C)     AD - BC
                   Y = (-----) X + -------
                       (B - A)      A - B

 Y   = function value.
 X   = pixel location.
 A,B = min, max pixel values.
 C,D = new_min, new_max function values.
****************************************************************************/
float y_value(float pix_val)
{
extern float new_xmin, new_xmax, new_ymin, new_ymax;
   float yval;

   yval = ((new_ymax-new_ymin)/(UP_LEFT_CORNER_Y - LO_RIGHT_CORNER_Y)) *
          pix_val+((new_ymax*LO_RIGHT_CORNER_Y - new_ymin*UP_LEFT_CORNER_Y)/
          (LO_RIGHT_CORNER_Y - UP_LEFT_CORNER_Y));
   return(yval);
}

/*************************** x_value() **************************************
Scaling formula:
                       (D - C)     AD - BC
                   Y = (-----) X + -------
                       (B - A)      A - B

 Y   = function value.
 X   = pixel location.
 A,B = min, max pixel values.
 C,D = new_min, new_max function values.
****************************************************************************/
float x_value(float pix_val)
{
extern float new_xmin, new_xmax, new_ymin, new_ymax;
   float xval;

   xval = ((new_xmax-new_xmin)/(LO_RIGHT_CORNER_X - UP_LEFT_CORNER_X)) *
          pix_val+((new_xmax*UP_LEFT_CORNER_X - new_xmin*LO_RIGHT_CORNER_X)/
          (UP_LEFT_CORNER_X - LO_RIGHT_CORNER_X));
   return(xval);
}

/*************************** y_pixel() **************************************
Scaling formula:
                       (D - C)     AD - BC
                   Y = (-----) X + -------
                       (B - A)      A - B

 Y   = pixel location.
 X   = function value.
 A,B = new_min, new_max function values.
 C,D = min, max pixel values.
****************************************************************************/
float y_pixel(float func_val)
{
extern float new_xmin, new_xmax, new_ymin, new_ymax;
   float ypix;

   ypix = ((UP_LEFT_CORNER_Y - LO_RIGHT_CORNER_Y)/(new_ymax-new_ymin)) *
          func_val+((new_ymin*UP_LEFT_CORNER_Y - new_ymax*LO_RIGHT_CORNER_Y)/
          (new_ymin - new_ymax));
   return(ypix);
}

/*************************** x_pixel() **************************************
Scaling formula:
                       (D - C)     AD - BC
                   Y = (-----) X + -------
                       (B - A)      A - B

 Y   = pixel location.
 X   = function value.
 A,B = new_min, new_max function values.
 C,D = min, max pixel values.
****************************************************************************/
float x_pixel(float func_val)
{
extern float new_xmin, new_xmax, new_ymin, new_ymax;
   float xpix;

   xpix = ((LO_RIGHT_CORNER_X - UP_LEFT_CORNER_X)/(new_xmax-new_xmin)) *
          func_val+((new_xmin*LO_RIGHT_CORNER_X - new_xmax*UP_LEFT_CORNER_X)/
          (new_xmin - new_xmax));
   return(xpix);
}

/************************* read_menu_file() ******************************
     This function reads a 'menu' file and places its contents into three
global arrays:  menu_labels, menu_pos and menu_item.  Each array has the
same dimensions and their elements are consecutively related to each other.
Example: menu_labels[0] related to menu_pos[0] related to menu_item[0].  The
subscript variable used to increment the arrays is 'ending_item'.  Its final
value is equal to the number of elements read into each array.  When reading
a second menu file 'ending_item' should be reset to zero.
     The menu_labels array contains the names of the option to be display
on a menu.
     The menu_pos array contains the row and col corrdinats where the
menu_labels is to appear.
     The menu_item array contains a heiarchical number that can be used to
keep track of nested or multiple menus.

     The function update_bar_menu() can be used to move through the menu
arrays.

     Menu files currently have three instructions '~menu', '~print' and 
'~item'.  The '~menu' statement identifies an individual menu.  This
instruction makes it possible to have many menus contained in one menu file
identified.  The '~print' statement alone simply prints its contents at the
given corrdinates and is NOT placed into the arrays.  A '~print' statement
following an '~item' statement is treated as a label and is placed into the
arrays. The syntax for all statements are below:

~menu <string identifying this menu, ending with a semi-colon>;
~print col row
<string to print ending with a semi-colon>;
~item #.#.#.#.#.#

Menu file example:

~menu MAIN_MENU;   ! '~menu' statement identifies this menu.
~print 71  2       ! Prints ' Main Menu' on the screen.  This will NOT be
 Main Menu;        ! a selectable item.
~item *******.0.0  ! '~item' statement identifies a menu option.  A '~print'
~print 71  4       ! statement must follow indicating where the item will
  load    ;        ! appear.
~item 2.0.0.0.0.0
~print 71  6
  modify  ;
~menu SUB_MENU     ! Identifies the end of 'MAIN_MENU' and the start of a
                   ! new menu called 'SUB_MENU'.  The end-of-file also marks
                   ! the end of a menu.

NOTE:  Menu names are case sensitive.  Menu file instructions are not.
*/

int read_menu_file(menu_id)
char *menu_id;
{
int fstrcpy(char *string1,int x1,int y1,char *string2,int x2,int y2);
short int string_compare(char *string1,char *string2,int len);
char *to_upper_case(char *string);

   char id[80],instruction[3];
   short int item_flag=0, menu_id_found=0, sub=0;
   int i, j;
   extern HLS_color text_color; /* From gr.c */

   while (sub < menu_file_size) {
      if (string_compare("~",menu_file_record[sub],1)) {
         fstrcpy(instruction,1,3,menu_file_record[sub],2,4);
         if (string_compare("MEN",to_upper_case(instruction),3)) {
            j = 0;
            for (i=0; menu_file_record[sub][i]!=' '; i++);
            for (; menu_file_record[sub][i]==' '; i++);
            for (; menu_file_record[sub][i]!=';' && 
                   menu_file_record[sub][i]!='\0'; i++)
               id[j++] = menu_file_record[sub][i];
            id[j] = '\0';
            if (!strcmp(menu_id,id)) {
               menu_id_found = 1;
               break;
            }
         }
      }
      sub++;
   }
   while (menu_id_found && (++sub < menu_file_size)) {
      if (string_compare("~",menu_file_record[sub],1)) {
         fstrcpy(instruction,1,3,menu_file_record[sub],2,4);
         if (string_compare("MEN",to_upper_case(instruction),3))
            break;
         if (string_compare("PRI",to_upper_case(instruction),3)) {
            for (i=0; !isdigit(menu_file_record[sub][i]); i++);
            sscanf(&menu_file_record[sub][i],"%2d",
               &menu_pos[menu_in_use][ending_item[menu_in_use]].x);
            i = strlen(menu_file_record[sub]);
            sscanf(&menu_file_record[sub][i-2],"%2d",
               &menu_pos[menu_in_use][ending_item[menu_in_use]].y);
            if (++sub < menu_file_size) {
               for (i=0; i<=80; i++) 
                  menu_labels[ending_item[menu_in_use]][i]='\0';
               fstrcpy(menu_labels[ending_item[menu_in_use]],1,80,
                  menu_file_record[sub],1,strlen(menu_file_record[sub])-1);
               if (item_flag) {
                  ending_item[menu_in_use]++;
                  item_flag=0;
               }
            }
         }
         else if (string_compare("ITE",to_upper_case(instruction),3)) {
            menu_color[menu_in_use][ending_item[menu_in_use]] = text_color;
            for (i=0; !isdigit(menu_file_record[sub][i]); i++);
            fstrcpy(menu_item[menu_in_use][ending_item[menu_in_use]],1,80,
               menu_file_record[sub],i+1,strlen(menu_file_record[sub]));
            item_flag=1;
         }
      }
   }
   ending_item[menu_in_use]--;
}

/************************* load_menu_file() ***********************************
     This function reads a menu file copying its contents into a global array
called 'menu_file_record'.  This array is used by the 'read_menu_file()'
function to select a specific menu within the array.  The global variable
'menu_file_size' indicates the number of records read into the
'menu_file_record' array.
*/
int load_menu_file(char *filename)
{
#include <stdlib.h>
extern void printxy(int col,int row,char *string,short int mode);
   char read_only[9]={"readonly"};
   char string[100], fgu_menus[100];

#ifdef UNIXHOST
   strcpy(fgu_menus,getenv("fgu_menus"));
   if (strlen(fgu_menus))
      if (fgu_menus[strlen(fgu_menus)-1] != '/')
         strcat(fgu_menus,"/");
#endif
#ifdef VAXHOST
   strcpy(gr_menus,"fgu$menus:");
#endif

   strcpy(string, filename);
   if (strlen(fgu_menus))
      strcpy(filename, fgu_menus);
   else
      filename[0] = '\0';
   strcat(filename, string);

   menu_file_size = 0;
   if (io_open(10,filename,read_only)) {
      while (io_read(10,&menu_file_record[menu_file_size]))
         if (strlen(menu_file_record[menu_file_size]) > 1)
            menu_file_size++;
      io_close(10);
   }
   else {
      printxy(17,23,"Could not open file 'fgu1.mnu'.",2);
#ifdef UNIXHOST
      printxy(12,24,"Check environment variable 'fgu_menus'.",2);
#endif
#ifdef VAXHOST
      printxy(20,24,"Check logical 'fgu$menus'.",2);
#endif
   }
}

/******************************* menu_label() ********************************
     This function returns a pointer to a string containing the name of the 
menu item indicated by 'menu_in_use' and 'sub'.  A function is needed to do
this because the label names for the LABEL_MENU are stored in a large Fortran
array.  Rather than duplicating this array in C this function retrieves the 
requested element from the Fortran array.
     The Fortran array containing the function names (table names) is not
sorted.  Each time GR is call from Fortran the logical 'sort_labels' is set
to TRUE causing the Fortran array to be sorted then the logical is set to 
FALSE.  This is done so that the function names are sorted only once each 
time GR is called.
*/
char *menu_label(int menu_in_use, int sub)
{
extern void get_menu_label();
#ifdef VAXHOST
   auto $DESCRIPTOR(string, "                   ");
#endif
   extern int sort_labels;  /* From fgu_gr.c */
   extern int no_of_tables; /* From fgu_gr.c */
   short int i;
   char label[20];

  /* Note: The COMMAND_MENU is not stored in a Fortran array. */
   if (menu_in_use == COMMAND_MENU) {
      return(menu_labels[sub]);
   }
   else if (menu_in_use == LABEL_MENU) {
      sub++;                   /* Fortran arrays do not start at zero. */
      if (sub > no_of_tables)  /* Return NULL if label does not exist. */
         return("\0");
#     ifdef VAXHOST
         get_menu_label(&sub, &string, &sort_labels);
         for (i=0; i < string.dsc$w_length; i++)
            label[i] = string.dsc$a_pointer[i];
#     endif
#     ifdef UNIXHOST
         get_menu_label(&sub, label, &sort_labels);
#     endif
   }

  /* Place a NULL terminator on the 'label' string.*/
   for (i=0; label[i]!=' ' && i<19; i++);
   label[i] = '\0';

  /* Set 'sort_labels' logical to FALSE.  Labels should only be sorted once */
  /* for each GR session.*/
   if (sort_labels)
      sort_labels = 0;

   return(label);
}

/************************** round_off() *****************************
     Rounds-off a floating point number.
*/
int round_off(float num)
{
   int i;
   char hold_string[80];

   sprintf(hold_string,"%-.6f",num);
   sscanf(hold_string,"%d",&i);
   return(i);
}

/**************************** round_up() *****************************
     Rounds a floating point number to the next GREATER whole number.
*/
int round_up(float num)
{
float fraction(float num);
   int i;

   if (fabs(fraction(num))>0) {
      i = fabs(num)-fabs(fraction(num));
      if (num<0) {
         i--;
         i *= -1;
      }
      else i++;
   }
   return(i);
}

/************************** fraction() *****************************
     Returns the fractional part of a 'float' type number.
*/
float fraction(float num)
{
   float abs_num;
   int i;

   abs_num = fabs(num);
   for (i=0; i<=abs_num; i++);
   abs_num = fabs(num) - (i-1);
   if (num < 0) abs_num = abs_num * -1;
   return(abs_num);
}

/***************************** fstrcpy() ******************************
     This function simulates the Fortran system of string manipulation.
For example:  Fortran-  STRING1(1:10) = STRING2(11:20)
              fstrcpy-  fstrcpy(string1,1,10,string2,11,20);

     If the contents of string2 overflows string1 the results are
unpredictable.  If the number of character being copied from string2
is greater than the amount specified in string1 fstrcpy returns false.
fstrcpy returns false whenever an error is found.  Nothing is copied if
false is returned.
*/
int fstrcpy(string1,x1,y1,string2,x2,y2)
char *string1;
int x1;
int y1;
char *string2;
int x2;
int y2;
{
   int no_error=1,i;

   if (x1>y1 || x2>y2) /* y must always be greater or equal to x */
      no_error=0;
   else if ((y2-x2) > (y1-x1)) /* string2 cannot be greater than string1 */
         no_error=0;
   else if (x1<1 || x2<1 || y1<1 || y2<1) /* Zero not allowed for x or y */
         no_error=0;
   else {
      string1 += x1-1;
      string2 += x2-1;
      for (i=1; i<=(y2-x2+1); i++)
         *string1++ = *string2++; /* Copy string2 to string1     */
   }
   return(no_error);
}

/****************************** clear_line ********************************
     Writes 'no_of_blanks' on the specified line starting in column 1.  After
clearing the line the cursor is placed at the begining of the same line.  The
specified line must be an integer between 1 and 24 otherwise nothing is
cleared.
*/
void clear_line(int line_no,int no_of_blanks)
{
extern short int term_status; /* From vaxgraph.c */

#ifdef UNIXHOST
   if (line_no>=1 && line_no<=24)
      fprintf(stdout,"\033[0m\033[%d;1H%*s\033[%d;1H",line_no,
              no_of_blanks,NULL,line_no);
#endif
#ifdef VAXHOST
   if (line_no>=1 && line_no<=24)
      fprintf(stdout,"\033[0m\033[%d;1H%*s\033[%d;1H",line_no,
              no_of_blanks,"",line_no);
#endif
#ifdef PCHOST
   if (line_no>=1 && line_no<=24)
      fprintf(stdout,"\033[0m\033[%d;1H%*s\033[%d;1H",line_no,
              no_of_blanks,NULL,line_no);
#endif

   term_status = 5; /* Normal video or lowlight has been set. */
   return;
}

/*************************** same_color() **********************************
*/
int same_color(HLS_color *color1, HLS_color *color2)
{
   if (color1->hue == color2->hue &&
       color1->light == color2->light &&
       color1->shade == color2->shade)
      return(1);
   return(0);
}

/**************************** trim_string() **********************************/
char *trim_string(char *string)
{
   int i, len;

   len = strlen(string);
   if (len) {
      for (i=len-1; i>0 && *(string+i)==' '; i--)
         *(string+i) = '\0';
   }
   return(string);
}
