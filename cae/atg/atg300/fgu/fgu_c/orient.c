#include "visa.h"
#include "vt240.h"

/************************************************************************/
/*									*/
/*                            MODULE ORIENT				*/
/*									*/
/* Date  : 06.86							*/
/*	      	 							*/
/*  These functions are needed when the point (0,0) for the terminal is */
/*  the upper left corner and the point (0,0) for the software is the	*/
/*  lower left corner.	These functions modify the y value of a point	*/
/*  according to that.							*/
/************************************************************************/

Pt orient(xy)
Pt *xy;
{
   Pt temp;
   temp.x = xy->x;
   temp.y = YMAX240 - xy->y;
   return(temp);
}


Pt orientw(xy,w_window)
Pt *xy;
Window *w_window;
{
   Pt temp;
   temp.x = xy->x;
   temp.y = w_window->max.y - xy->y;
   return(temp);
}
