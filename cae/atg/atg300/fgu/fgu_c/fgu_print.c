/***********************************************************************/
/*    Author: <PERSON> - <PERSON>. 24                                */
/*    Date:   92/08/21                                                 */
/*    File:   fgu_print.c                                              */
/*    Decription:                                                      */
/*                                                                     */
/*    Revision history:                                                */
/***********************************************************************/
#include <stdio.h>
#include "ovp_prec.h"
#include "ovp.h"
#include "graph.h"
#include "ovp_stru.h"
#include "fgu_extern.h"

/***** Printer global variables *****/
int to_printer=0;   /* Logical to redirect output to print buffer.    */
float increase=1.5; /* Multiplied to the picture size in the print buffer. */
int y_margin=90;    /* Bottom of page margin in dots. 121.6 dots = 1"      */
int x_margin=50;    /* Left of page margin in dots.    85.0 dots = 1"      */
#define PRINTRONIX_EXTREME { {  0,   0}, { 635,  532} }
#define HPLASER_EXTREME    { {  0,   0}, { 768,  546} }
#define POSTSCRIPT_EXTREME { {  0,   0}, { 768,  546} }

/***** Menuing variables from fgu_lib.c *****/
extern Ipt  menu_pos[2][1000];
extern char menu_item[2][1000][40];
extern char *menu_label(int menu_in_use, int sub);
extern HLS_color menu_color[2][1000];
extern int  current_menu_item[2], starting_item[2];
extern int  ending_item[2], menu_in_use;
extern char menu_file_record[200][80];
extern short int menu_file_size;
/***** Menuing variables from fgu_gr.c *****/
extern char menu_file[30];
extern Ipt  screen_pos;
extern int  sort_labels;

extern short int COMMAND_MENU;
extern short int LABEL_MENU;

extern int curr_page;
extern int no_of_pages;

#define  PRINT_ONE     0
#define  PRINT_ALL     1
#define  PRINT_SELECT  2

/***************************** hardcopy() ************************************/
int hardcopy(void)
{
#ifdef VAXHOST
#  include <descrip.h>
#  include <ssdef.h>
#  define  VSA_PRTQ  "VSA$PRTQ"
   struct dsc$descriptor_s dsc_equnam,
                           dsc_lognam;
#endif
#ifdef UNIXHOST
#  define  VSA_PRTQ  "vsa_prtq"
   char temp_str[80];
   int output_len, level;
#endif
extern int read_menu_file(char *menu_id);
extern char *trim_string(char *string);
short int string_compare(char *string1,char *string2,int len);
void update_bar_menu(int letter,char direction);
int print_functions(short int option);
   char item[4][15], value[80], result[100], queue_setup[80];
   int letter, item_no, i;
   Ipt loc[4], location;

   to_printer = 0;

/* Get the value of the queue name logical: */
#ifdef UNIXHOST
   cae_trnl(VSA_PRTQ, &output_len, result, &level,
            strlen(VSA_PRTQ), sizeof(result)-1);
   for (i=0; result[i]!=' ' && result[i]!='\0'; i++);
   result[i] = '\0';
   strcpy(queue_setup, result);
#endif
#ifdef VAXHOST
/****************************************************************************/
/* VAX library function 'LIB$SYS_TRNLOG' and 'LIB$SET_LOGICAL' can be found */
/* in the manual:  VAX/VMS Run-Time Library Routines Reference Manual V4.2  */
/*                 Pages  RTL-319 and RTL-263 respecively                   */
   dsc_equnam.dsc$b_dtype   = DSC$K_DTYPE_T;
   dsc_equnam.dsc$b_class   = DSC$K_CLASS_S;
   dsc_equnam.dsc$w_length  = 20;
   dsc_equnam.dsc$a_pointer = result;

   dsc_lognam.dsc$b_dtype   = DSC$K_DTYPE_T;
   dsc_lognam.dsc$b_class   = DSC$K_CLASS_S;

   dsc_lognam.dsc$w_length  = strlen(VSA_PRTQ);
   dsc_lognam.dsc$a_pointer = VSA_PRTQ;
   LIB$SYS_TRNLOG (&dsc_lognam,'\0',&dsc_equnam,'\0','\0','\0');
   for (i=0; result[i]!=' ' && result[i]!='\0'; i++);
   result[i] = '\0';
   strcpy(queue_setup, result);
#endif

/* Display queue prompt: */
   if (!strlen(queue_setup))
      strcpy(queue_setup, "printronix"); /* Default queue name. */
   clear_line(23,65);
   printxy(4,23,"Confirm the name of the printer queue:",2);
   printxy(43,23,"                    ",1);
   printxy(43,23,queue_setup,1);
   location.x = 43;
   location.y = 23;
   cursoron();
   read_value(location,"ALPHNUM",20,result,&letter);
   cursoroff();
   if (letter==313) { /* REMOVE key pressed? */
      clear_line(23,65);
      return;
   }
   if (strlen(trim_string(result)))
      strcpy(queue_setup, result);

/* Reset the queue name logical: */
#ifdef VAXHOST
   dsc_lognam.dsc$b_dtype   = DSC$K_DTYPE_T;
   dsc_lognam.dsc$b_class   = DSC$K_CLASS_S;
   dsc_lognam.dsc$w_length  = strlen(VSA_PRTQ);
   dsc_lognam.dsc$a_pointer = VSA_PRTQ;

   dsc_equnam.dsc$b_dtype   = DSC$K_DTYPE_T;
   dsc_equnam.dsc$b_class   = DSC$K_CLASS_S;
   dsc_equnam.dsc$w_length  = strlen(queue_setup);
   dsc_equnam.dsc$a_pointer = queue_setup;

   LIB$SET_LOGICAL(&dsc_lognam,&dsc_equnam,'\0','\0','\0');
#endif
#ifdef UNIXHOST
   strcpy(temp_str, "setenv ");
   strcat(temp_str, VSA_PRTQ);
   strcat(temp_str, " """);
   strcat(temp_str, queue_setup);
   strcat(temp_str, """");
   system(temp_str);
#endif

  /* Display printer selections:  POSTSCRIPT COLORMASTER HP PRINTRONIX */
   clear_line(23,65);
   strcpy(item[0],"POSTSCRIPT");  /* Item names. */
   strcpy(item[1],"COLORMASTER");
   strcpy(item[2],"HP");
   strcpy(item[3],"PRINTRONIX");
   loc[0].x = 15; loc[0].y = 23;  /* Item x & y screen locations. */
   loc[1].x = 26; loc[1].y = 23;
   loc[2].x = 38; loc[2].y = 23;
   loc[3].x = 41; loc[3].y = 23;
   printxy(loc[0].x, loc[0].y, item[0], 1); /* Print items on screen. */
   printxy(loc[1].x, loc[1].y, item[1], 2);
   printxy(loc[2].x, loc[2].y, item[2], 2);
   printxy(loc[3].x, loc[3].y, item[3], 2);
   item_no = 0;
   while(1) {
      letter = 0;
      while(letter!=276 && letter!=277 && letter!=314 && 
            letter!=13 && letter!=313)
         letter = readkey();
      if (letter==276) {
         printxy(loc[item_no].x,loc[item_no].y,item[item_no],2);
         if (item_no==0)
            item_no = 3;
         else
            item_no--;
         printxy(loc[item_no].x,loc[item_no].y,item[item_no],1);
      }
      else if (letter==277) {
         printxy(loc[item_no].x,loc[item_no].y,item[item_no],2);
         if (item_no==3)
            item_no = 0;
         else
            item_no++;
         printxy(loc[item_no].x,loc[item_no].y,item[item_no],1);
      }
      else if (letter==314 || letter==13)
         break;
      else if (letter==313) {
         clear_line(23,65);
         return;
      }
   }
   if (item_no==0)
      to_printer = 4;  /* PostScript printer */
   else if (item_no==1)
      to_printer = 2;  /* ColorMaster printer */
   else if (item_no==2)
      to_printer = 3;  /* HP printer */
   else if (item_no==3)
      to_printer = 1;  /* Printronix printer */

   if (to_printer) {
     /* Display printer selections:  PRINT_ONE PRINT_ALL PRINT_SELECT  */
      clear_line(23,65);
      strcpy(item[0],"PRINT_ONE");  /* Item names. */
      strcpy(item[1],"PRINT_ALL");
      strcpy(item[2],"PRINT_SELECT");
      loc[0].x = 16; loc[0].y = 23;  /* Item x & y screen locations. */
      loc[1].x = 26; loc[1].y = 23;
      loc[2].x = 36; loc[2].y = 23;
      printxy(loc[0].x, loc[0].y, item[0], 1); /* Print items on screen. */
      printxy(loc[1].x, loc[1].y, item[1], 2);
      printxy(loc[2].x, loc[2].y, item[2], 2);
      item_no = 0;
      while(1) {
         letter = 0;
         while(letter!=276 && letter!=277 && letter!=314 && 
               letter!=13 && letter!=313)
            letter = readkey();
         if (letter==276) {
            printxy(loc[item_no].x,loc[item_no].y,item[item_no],2);
            if (item_no==0)
               item_no = 2;
            else
               item_no--;
            printxy(loc[item_no].x,loc[item_no].y,item[item_no],1);
         }
         else if (letter==277) {
            printxy(loc[item_no].x,loc[item_no].y,item[item_no],2);
            if (item_no==2)
               item_no = 0;
            else
               item_no++;
            printxy(loc[item_no].x,loc[item_no].y,item[item_no],1);
         }
         else if (letter==314 || letter==13)
            break;
         else if (letter==313) {
            clear_line(23,65);
            return;
         }
      }
      print_functions(item_no);
   }

   to_printer = 0;
   return(letter);
}

/************************** print_functions() ********************************/
int print_functions(short int option)
{
int graph_to_printer(int *caller, int *printer);
extern void cls_refresh_menus(void);
extern void update_bar_menu(int letter, char direction);
extern void refresh_bar_menu(int show_item, int show_all);
extern void build_label_menu(char direction);
extern void attributes_on(int item);
extern void erase_bar_menu(void);
   int i, letter,caller=2;

   if (option == PRINT_ONE) {
      clear_line(23,65);
      printxy(11,23,"Print currently selected function? ([y]/n)",2);
      letter = 0;
      while (letter!='y' && letter!='Y' && letter!=13 && letter!=314 &&
             letter!='n' && letter!='N' && letter!=313)
         letter = readkey();
      if (letter=='n' || letter=='N' || letter==313) {
         clear_line(23,65);
         return(1);
      }
      menu_in_use = LABEL_MENU;
      menu_color[menu_in_use][table_no] = foreground_color;
      graph_to_printer(&caller, &to_printer);
      menu_color[menu_in_use][table_no] = text_color;
      menu_in_use = COMMAND_MENU;
   }
   else if (option == PRINT_ALL) {
      clear_line(23,65);
      printxy(18,23,"Print ALL functions? (y/[n])",2);
      letter = 0;
      while (letter!='y' && letter!='Y' && letter!=13 && letter!=314 &&
             letter!='n' && letter!='N' && letter!=313)
         letter = readkey();
      if (letter==314 || letter==13 || letter==313 || 
          letter=='n' || letter=='N') {
         clear_line(23,65);
         return(1);
      }
      menu_in_use = LABEL_MENU;
      for (i=0; i<no_of_tables; i++)
        menu_color[menu_in_use][i] = foreground_color;
      graph_to_printer(&caller, &to_printer);
      for (i=0; i<no_of_tables; i++)
        menu_color[menu_in_use][i] = text_color;
      menu_in_use = COMMAND_MENU;
   }
   else if (option == PRINT_SELECT) {
      short int function_selected=0;

      clear_line(23,65);
      printxy(2,23,
           "Use 'Select' key to select or un-select a function to print.",2);
     /***** Access the LABEL_MENU. *****/
      menu_in_use = LABEL_MENU;
      refresh_bar_menu(1,0);
      while (1) {          /* Loop until break.  */
         letter=readkey(); /* Read a key stroke. */
         /* Remove key pressed? */
         if (letter==313) {
            attributes_on(current_menu_item[menu_in_use]);
            printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                    menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                    menu_label(menu_in_use,current_menu_item[menu_in_use]),0);
            break;
         }
         else if (letter==274 || letter==275) { /* Up or Down arrow pressed? */
            update_bar_menu(letter, 'V'); /* If an arrow key was pressed. */

         /* Check through the menu_item array to decide what action to take. */
         /* menu_item=## <number> if a label name was selected. */
         }
         else if (letter==13 || letter==314) {
            sscanf(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                   "%d",&i);
            if (same_color(&menu_color[menu_in_use][i], &foreground_color))
               menu_color[menu_in_use][i] = text_color;
            else
               menu_color[menu_in_use][i] = foreground_color;
            update_bar_menu(275, 'V'); /* If an arrow key was pressed. */
         }
         else if (letter==316) {  /* Next screen pressed? */
            if (no_of_tables>24) {
               build_label_menu('F');
               erase_bar_menu();
               refresh_bar_menu(1,1);
            }
         }
         else if (letter==315) {  /* Prev screen pressed? */
            if (no_of_tables>24) {
               build_label_menu('B');
               erase_bar_menu();
               refresh_bar_menu(1,1);
            }
         }
         else if (letter==127) {  /* Destructive backspace key pressed? */
            cls_refresh_menus();  /* Yes, clear screen and refresh the menus. */
            printxy(2,23,
            "Use 'Select' key to select or un-select a function to print.",2);
         }
      }
     /**********************************/
      for (i=0; i<MAX_MASTER; i++) {
         if (same_color(&menu_color[menu_in_use][i], &foreground_color)) {
            function_selected = 1;
            break;
         }
      }
      if (function_selected) {
         clear_line(23,65);
         printxy(14,23,"Print the selected functions? ([y]/n)",2);
         letter = 0;
         while (letter!='y' && letter!='Y' && letter!=13 && letter!=314 &&
                letter!='n' && letter!='N' && letter!=313)
            letter = readkey();
         if (letter=='y' || letter=='Y' || letter==13 || letter==314)
            graph_to_printer(&caller, &to_printer);
         else
            clear_line(23,65);
         for (i=0; i<no_of_tables; i++)
           menu_color[menu_in_use][i] = text_color;
         erase_bar_menu();
         refresh_bar_menu(0,1);
      }
      else {
         clear_line(23,65);
         printxy(12,23,"Nothing selected?  PRINT_SELECT canceled.",2);
      }
      menu_in_use = COMMAND_MENU;
      refresh_bar_menu(1,0);
   }

   return(1);
}

/************************ graph_to_printer() *****************************
     This function is called from the Fortran module and from C.  When
called from C the 'caller' argument should be equal to the value 2.  This
indicates a C caller using the 'instruction' argument.  The 'instruction'
argument may have one of two values:

  1 = print the whole function.
  2 = print from the current page (indicated by the curr_page global var)
      to end of the function.
  (any other number) = print only this page (indicated by curr_page).

     The argument 'printer' is ignored when the 'caller' argument is equal
to 2.  The global variable 'to_printer' will indicate the printer used.

     If the 'caller' argument is set to 1 then the whole function is
printed and the 'printer' argument is used.  There are two possible values
for the 'printer' argument:

  4 = PostScript Laser
  3 = HP LaserJet II
  2 = ColorMaster
  1 = Printronix
**************************************************************************/
int graph_to_printer(caller, printer)
int *caller, *printer;
{
extern int select_function();
extern int same_color(HLS_color *color1, HLS_color *color2);
int refresh_graph(int curve_only);
void update_legend(void);
   Window printronix_window = PRINTRONIX_EXTREME,
          hewlett_window    = HPLASER_EXTREME,
          postscript_window = POSTSCRIPT_EXTREME;
   int i, letter, save_page, save_table;
   int print_total=0, print_count, page_count, file_count, count;
   char filename[100];

   save_page = curr_page;
   if (*caller == 1)          /* Function called from Fortran? */
      to_printer = *printer;  /* Yes. */

/* Calculate the total number of functions that will be printed. */
   for (i=0; i<MAX_MASTER; i++) {
      if (same_color(&menu_color[menu_in_use][i], &foreground_color))
         print_total++;
   }

/* Initialize variables */
   print_count = 0;
   page_count = 0;
   file_count = 1;
   save_page = curr_page;
   save_table = table_no;
   strcpy(filename, "fgu01");

   if (to_printer==1)                       /* Was Printronix selected? */
      prtinit(&printronix_window,filename); /* Initialize a new print file. */
   else if (to_printer==3)                  /* Was HP_Laser selected?   */
      hpinit(&hewlett_window,filename);     /* Initialize a new print file. */
   else if (to_printer==4)                  /* Was PostScript selected? */
      psinit(&postscript_window,filename);          /* Initialize a new print file. */

   for (count=0; count<no_of_tables && print_count<=print_total; count++) {
      if (same_color(&menu_color[menu_in_use][count], &foreground_color)) {
         sscanf(menu_item[menu_in_use][count], "%d",&table_no);
         select_function(&table_no);
         if (page_count >= 20) {
            char str[5];

            if (to_printer==1)
               prt_send_out();       /* Queue .plo file to printer */
            else if (to_printer==3)
               hp_send_out();        /* Queue .hp file to printer */
            else if (to_printer==4)
               ps_send_out();        /* Queue .ps file to printer */
            file_count++;
            sprintf(str,"%2d",file_count);
            if (str[0] == ' ')
               str[0] = '0';
            strcat(strcpy(filename, "fgu"), str);
            page_count = 0;
            if (to_printer==1)
               prtinit(&printronix_window,filename);
            else if (to_printer==3)
               hpinit(&hewlett_window,filename);
            else if (to_printer==4)
               psinit(&postscript_window,filename);
         }
         print_count++;
         if (*caller==2) {
            char mess[80];

            if (to_printer==1)
               sprintf(mess, "Printing function %-d of %-d into file '%s.plo'.",
                       print_count, print_total, filename);
            else if (to_printer==3)
               sprintf(mess, "Printing function %-d of %-d into file '%s.hp'.",
                       print_count, print_total, filename);
            else if (to_printer==4)
               sprintf(mess, "Printing function %-d of %-d into file '%s.ps'.",
                       print_count, print_total, filename);
            clear_line(23,65);
            printxy(7,23,mess,2);
         }
        /* Define Printonix print window. */
         for (curr_page=0; curr_page<no_of_pages; curr_page++) {
            if (to_printer==1)
               prtclear();              /* Start a new page. */
            else if (to_printer==3)
               hpclear();               /* Start a new page. */
            else if (to_printer==4)
               psclear();               /* Start a new page. */
            update_legend();
            letter=refresh_graph(0); /* Place graph in print buffer.*/
            page_count++;
         }
      }
   }
   if (page_count) {
      if (to_printer==1)
         prt_send_out();
      else if (to_printer==3)
         hp_send_out();
      else if (to_printer==4)
         ps_send_out();
   }

   if (*caller==2) {
      clear_line(23,65);
      printxy(11,23,"All functions have been queued to printer.",2);
   }
   table_no = save_table;        /* Restore lost function. */
   curr_page = save_page;        /* Restore lost curr_page value. */
   select_function(&table_no);   /* Retrieve the function. */
   update_legend();              /* Restore lost legend. */

   return(letter);
}

/************************** prtdrawline() ******************************
     When printing a graph, the values must be re-scaled for the dimensions
of the printer.

          The screen has the dimensions:
              x-axis = 0-799, y-axis = 0-479
          The printronix printer has the dimensions:
              x-axis = 0-635, y-axis = 0-532

     The graph was calculated using the screen dimensions, so the following
conversion must take place to obtain proper printer coordinates:

          percent = x-axis-screen-value / 800
          x-axis-printer-value = int(635 * percent)
          percent = y-axis-screen-value / 480
          y-axis-printer-value = int(532 * percent)
*/
void prtdrawline(line)
Pts *line;
{
   Pt prt_position, prt_line;
   float percent;

   percent = ((*line).xy1.x*increase) / 800;
   prt_position.x = x_margin+(635 * percent);
   percent = ((*line).xy1.y*increase) / 480;
   prt_position.y = y_margin+(532 * percent);

   percent = ((*line).xy2.x*increase) / 800;
   prt_line.x = x_margin+(635 * percent);
   percent = ((*line).xy2.y*increase) / 480;
   prt_line.y = y_margin+(532 * percent);

   if (to_printer==1) {
      prtmove2i(&prt_position);    /* Set position in print buffer */
      prtdraw2i(&prt_line);        /* Draw line in print buffer */
   }
   else if (to_printer==3) {
      hpmove2i(&prt_position);     /* Set position in print buffer */
      hpdraw2i(&prt_line);         /* Draw line in print buffer */
   }
   else if (to_printer==4) {
      psmove2i(&prt_position);     /* Set position in print buffer */
      psdraw2i(&prt_line);         /* Draw line in print buffer */
   }
}

/*************************** prtplotchar() *******************************/
void prtplotchar(char letter,int xpix,int ypix)
{
   float percent;
   Pt prt_position;

   percent = (xpix*increase) / 800;
   prt_position.x = x_margin+(635 * percent);
   percent = (ypix*increase) / 480;
   prt_position.y = y_margin+(532 * percent);

   if (to_printer==1) {
      prtmove2i(&prt_position); /* Set position in print buffer */
      plotchar(letter,0);
   }
   else if (to_printer==3) {
      hpmove2i(&prt_position);  /* Set position in print buffer */
      hp_plotchar(letter,0);
   }
   else if (to_printer==4) {
      Stringdef text;
      char str[2];

      psmove2i(&prt_position);  /* Set position in print buffer */
      str[0] = letter;
      str[1] = '\0';
      text.cstring = str;
      pscharstr(&text);
   }
}
