/************************************************************************/
/*									*/
/*                         INCLUDE FILE SCREEN 				*/
/* Author : <PERSON>     		     	  			*/
/* Date :   June 1990							*/
/*	      	 							*/
/*  Contains the definitions of :					*/
/*     - windows size, 							*/
/*     - colors used in the system,					*/
/*     - color of each particular element.				*/
/************************************************************************/

#define VIRTUAL_SCREEN   {0, 0, 999, 999}

/*
C'Revision_History
*/
#define VISTA_WINDOW      {-640+1, -512+1, 639-1, 511-1} 
#ifdef QOVP
#  define SGI_WINDOW        {0, 0, 950,  1023 }
#else
#  define SGI_WINDOW        {0, 0, 950,  820 }
#endif
#define PRINTRONIX_WINDOW { {0, 0}, {635, 532} } /* 619, 532 */
#define CALCOMP_WINDOW    { {0, 0}, {1999, 1599} }
#define HPLASER_WINDOW    { {0, 0}, { 768,  546} }

#ifdef VISTA
#  define BACKGROUND       0
#  define BLUE             4
#  define RED              1
#  define YELLOW           3
#  define GREY		   4 
#  define GREEN            2
#  define BLACK            0
#  define CYAN             6
#  define WHITE            7
#else
#  ifdef SGI
#    define BACKGROUND       0
#    define RED              1
#    define GREEN            2
#    define YELLOW           3
#    define BLUE             4
#    define MAGENTA	     5
#    define CYAN             6
#    define WHITE 	     7
#    define GREY	     8
#    define LTGREY	     9
#    define ORANGE	    10
#    define PINK	    11
#    define BLACK           12
#    define LTGREEN         13
#    define BROWN	    14
#    define AMBER	    15
#  else
#    define BACKGROUND       0
#    define BLUE             1
#    define RED              2
#    define YELLOW           3
#    define GREY	     4
#    define GREEN            4
#    define BLACK            5
#    define CYAN             6
#    define WHITE            7
#  endif
#endif

#ifdef VISTA
#  define GRID_COLOR       WHITE
#  define AXIS_COLOR       WHITE
#else
#  ifdef SGI
#    define GRID_COLOR       WHITE
#    define AXIS_COLOR       WHITE
#  else
#    define GRID_COLOR       BLUE
#    define AXIS_COLOR       BLUE
#  endif
#endif

#ifdef SGI
#  define MEDIUM_FONT      3
#  define SMALL_FONT       2 
#  define VERYSMALL_FONT   1
#else
#  define VERY_SMALL_FONT  9
#  define SMALL_FONT	   11
#  define MEDIUM_FONT	   14
#  define LARGE_FONT	   18
#endif

#define  TITLE_COLOR   WHITE
#define  LABEL_COLOR   WHITE
#define  TOLER_COLOR   RED
#define  CTS_COLOR     CYAN
#define  MASTER_COLOR  YELLOW
#define  DASH_TYPE     0
#define  DOT_TYPE      1
#define  SOLID_TYPE    2

#define MAX_COLOR        5
#define COLOR_0          {0,0,2,4}
#define COLOR_1          {1,0,15,15}
#define COLOR_2          {2,15,0,0}
#define COLOR_3          {3,15,15,0}
#define COLOR_4		 {4,10,10,10}
#define MAX_VT_COLOR     4

#define MAX_MAP 4
#define MAP0       {0, 300, 25,  25}
#define MAP1       {1, 300, 80,  25}
#define MAP2       {2,  90, 50, 100}
#define MAP3       {3, 180, 65,  60}

#define VIRTUAL_EXTREME    { {  0,   0}, { 999,  999} }
#define VT240_EXTREME      { {  0,  60}, { 799,  479} }
#define PRINTRONIX_EXTREME { {  0,   0}, { 635,  532} }
/*#define HPLASER_EXTREME    { {  0,   0}, {1536, 1092} }*/
#define HPLASER_EXTREME    { {  0,   0}, { 768,  546} }
/*#define CALCOMP_WINDOW     { {  0,   0}, {1999, 1599} }*/
/*#define VIRTUAL_SCREEN     { {100, 100}, { 900,  900} }*/
#define Z_VIRTUAL_SCREEN   { { 50, 100}, { 750,  900} }
#define GRAPH_SCREEN       { {  0,   0}, {1000, 1000} }

#define LABEL_LINE  21
#define MEN_SIZE    2
