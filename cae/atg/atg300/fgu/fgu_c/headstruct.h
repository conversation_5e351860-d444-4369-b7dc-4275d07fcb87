/************************************************************************/
/*									*/
/*                        INCLUDE FILE HEADSTRUCT 			*/
/* Author: G<PERSON><PERSON> Collette     		     	  			*/
/* Date  : 08.86							*/
/*	      								*/
/*  This include file contains the description of the structure		*/
/*  Header which contains the header informations for a curve.		*/
/*                                                                      */
/* Revision History                                                     */
/* o January 1990    Nick <PERSON>to                                       */
/*   -Changed field lengths for McDonnell Douglas data entry            */
/************************************************************************/


#define PROJECT_LEN     30   
#define ORIGIN_LEN      25   
#define AIRCRAFT_LEN    10 
#define TITLE_LEN       50     
#define REFNO_LEN       10 /* was 5 */
#define NAME_LEN        40     
#define LABEL_LEN       25    
#define FLOAT_LEN       15     
#define PAGE_LEN        12 /* was 8 */       
#define FIGURE_LEN       8    
#define DOC_LEN         15 /* was 10 */
#define NUMPTS_LEN       5
#define UNITS_LEN       10
#define ORIENTATION_LEN  1
#define DIM_LEN          5       
#define TYPE_LEN         1
#define NB_FIELDS       36


   struct header_struct
   {
      char project     [PROJECT_LEN     + 1];
      char origin      [ORIGIN_LEN      + 1];
      char aircraft    [AIRCRAFT_LEN    + 1];
      char title       [TITLE_LEN       + 1];
      char subtitle    [TITLE_LEN       + 1];
      char refno       [REFNO_LEN       + 1];
      char page        [PAGE_LEN        + 1];
      char figure      [FIGURE_LEN      + 1];
      char document    [DOC_LEN         + 1];
      char orientation [ORIENTATION_LEN + 1];
      char page_width  [DIM_LEN         + 1];
      char page_height [DIM_LEN         + 1];
      char type        [TYPE_LEN        + 1];
      char xname       [NAME_LEN        + 1];
      char yname       [NAME_LEN        + 1];
      char xlabel      [LABEL_LEN       + 1];
      char ylabel      [LABEL_LEN       + 1];
      char xlen        [FLOAT_LEN       + 1];
      char ylen        [FLOAT_LEN       + 1];
      char xgridc      [FLOAT_LEN       + 1];
      char ygridc      [FLOAT_LEN       + 1];
      char xgridf      [FLOAT_LEN       + 1];
      char ygridf      [FLOAT_LEN       + 1];
      char xmin        [FLOAT_LEN       + 1];
      char ymin        [FLOAT_LEN       + 1];
      char xmax        [FLOAT_LEN       + 1];
      char ymax        [FLOAT_LEN       + 1];
      char tol_perc    [FLOAT_LEN       + 1];
      char tol_abs     [FLOAT_LEN       + 1];
      char smplrt      [FLOAT_LEN       + 1];
      char num_pts     [NUMPTS_LEN      + 1];
      char xunits      [UNITS_LEN       + 1];
      char yunits      [UNITS_LEN       + 1];
      char xpage       [FLOAT_LEN       + 1];
      char ypage       [FLOAT_LEN       + 1];
   };

   typedef struct header_struct Header;
