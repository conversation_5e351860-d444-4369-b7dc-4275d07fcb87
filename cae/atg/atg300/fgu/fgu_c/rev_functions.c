#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>
#include <string.h>

/*** Commented function! ********** main() **********************************
main()
{
void rev_curr_new(char *filename);
void rev_next_new(char *filename);
   char filename[80]={"rob.dat"};

   rev_curr_new(filename);
   rev_next_new(filename);
}*/

/****************************** rev_next_new() *******************************/
void rev_next_new(char *filename)
{
void rev_curr_new(char *filename);
void increment_rev(char *filename);

   if (strlen(filename) <= 2)
      return;
   rev_curr_new(filename);
   increment_rev(filename);
}

/****************************** rev_curr_new() *******************************/
void rev_curr_new(char *filename)
{
void remove_rev(char *filename);
void deincrement_rev(char *filename);
   FILE *file_ptr;
   short int count=250;

   if (strlen(filename) <= 2)
      return;
   remove_rev(filename);
   strcat(filename,".250");

   while (count > 0) {
      if ((file_ptr = fopen(filename,"r+")) == NULL)
         deincrement_rev(filename);
      else {
         fclose(file_ptr);
         break;
      }
      count--;
   }
}

/***************************** increment_rev() ******************************
*/
void increment_rev(char *filename)
{
   short int i=1,j=0,k,len,flag=0,revno;
   char num[10]={"         "};

   len = strlen(filename);
   while (len-i >= 1 && isdigit(filename[len-i])) {
      if (len-i-1 >= 1 && filename[len-i-1] == '.') {
         k = len-i;
         while (filename[k]) {
            num[j] = filename[k];
            k++;
            j++;
         }
         revno = atoi(num)+1;
         for (k=0; k<=9; k++) num[k]='\0';
         sprintf(num,"%-d",revno);
         k = 0;
         while (num[k]) {
            filename[len-i] = num[k];
            k++;
            i--;
         }
         filename[len-i] = '\0';
         flag = 1;
         break;
      }
      i++;
   }
   if (!flag)
      strcat(filename,".1");
}

/**************************** deincrement_rev() *****************************
*/
void deincrement_rev(char *filename)
{
   short int i=1,j=0,k,len,revno;
   char num[10]={"         "};

   len = strlen(filename);
   while (len-i >= 1 && isdigit(filename[len-i])) {
      if (len-i-1 >= 1 && filename[len-i-1] == '.') {
         k = len-i;
         while (filename[k]) {
            num[j] = filename[k];
            k++;
            j++;
         }
         revno = atoi(num)-1;
         if (revno > 0) {
            for (k=0; k<=9; k++) num[k]='\0';
            sprintf(num,"%-d",revno);
            k = 0;
            while (num[k]) {
               filename[len-i] = num[k];
               k++;
               i--;
            }
         }
         else
            i++;
         filename[len-i] = '\0';
         break;
      }
      i++;
   }
}

/******************************* remove_rev() *******************************
     This function removes the revision number from the end of 'filename' (if
there is one).
*/
void remove_rev(char *filename)
{
   short int i=1,len;

   len = strlen(filename);
   while (len-i >= 1 && isdigit(filename[len-i])) {
      if (len-i-1 >= 1 && filename[len-i-1] == '.') {
         i = len-1;
         while (filename[i] != '.') {
            filename[i] = '\0';
            i--;
         }
         filename[i] = '\0';
         break;
      }
      i++;
   }
}
