
/***********************************************************************/
/*    Author: <PERSON>                                            */
/*    Date:   86/04/29                                                 */
/*    Decription: This Include file contains all major definition use  */ 
/*    throughout the package.                                          */
/***********************************************************************/

#include <stdio.h>


#define NUM_KIND    3   /* number of kind        */

#define RAMP_KIND   0   /* access the ramp  list */
#define CTS_KIND    1   /* access the cts   list */
#define INPUT_KIND  2   /* access the input list */


/***************************************************************************/ 
/*   option functions return codes                                         */
/***************************************************************************/ 

#define ERROR_CODE       (-1) /* Error code            */
#define EXIT_CODE        0    /* Getting out code      */
#define REACTIVATE_CODE  1    /* Reactive code         */
#define REDRAW_CODE      2    /* Redraw and reactivate */
#define REPAINT_CODE     3    /* repaint the screen and reactivate */
#define CLEAR_NEXT_CODE  4    /* Before drawing next curve, clear curve */
                              /* portion of the screen                  */

extern FILE *visaout;   /* Output file descriptor for all screen output */

/***********************************************************************/
/*    The IPT structure defines the coordinate data structure for      */
/*    integer                                                          */
/***********************************************************************/

struct integer_coordinate {
   int x;
   int y;
};

typedef struct integer_coordinate  Ipt;

typedef double Float;


/***********************************************************************/
/*    The PT structure defines the coordinate data structure for       */
/*    float                                                            */
/***********************************************************************/

struct point_struc 
   	{
   	Float x;
   	Float y;
   	};                                                            

typedef struct point_struc Pt;  

typedef int Boolean;

typedef struct {
   Pt min;
   Pt max;
   }   Window;


typedef struct {
   Pt xy1;
   Pt xy2;
   }   Pts;

typedef struct {
   Ipt xy1;
   Ipt xy2;
   }   Ipts;



/*  Input output description */

#define  OUTPUT   "VISAOUT"  
#define  INPUT    "VISAIN"  

#define VISA_TEXT_OUT OUTPUT
#define VISA_TEXT_IN  INPUT

#define VISA_GRAPH_IN  visain 
#define VISA_GRAPH_OUT visaout                

#define VISA_MENU_IN  visain 
#define VISA_MENU_OUT visaout 

#define TABLET_IN     "tabletin"
#define TABLET_OUT    "tabletout"

/*  Enumerated type used for the tolerance_code */

enum tolerance_code_value
   { in, maybe, out};


#define round(i)    ((int)((i>0) ? (i+0.5) : (i-0.5)))

