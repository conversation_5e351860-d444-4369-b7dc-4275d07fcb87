/***********************************************************************/
/*    Author: <PERSON> - <PERSON>. 24                                */
/*    Date:   91/06/20                                                 */
/*    File:   fgu_gr.c                                                 */
/*    Decription:                                                      */
/*                                                                     */
/*    Revision history:                                                */
/***********************************************************************/
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <ctype.h>
#include "ovp_prec.h"
#ifdef VAXHOST
#  include <descrip.h>  /* Get layout of descriptors used in fgu_gr() */
#  include <file.h>
#endif
#ifdef UNIXHOST
#  include "readkey.c"
#endif
#include "ovp.h"
#include "data_struc.h"
#include "ovp_datastruct.h"
#include "vt240.h"
#include "lldevice.h"
#include "screen.h"
#include "text.h"
#include "menu.h"
#include "hlscolor.h"

#define TRUE           1
#define FALSE          0
#define MAX_PAGES      50   /* Maximum number of pages a function can have. */
#define MAXBP          2000 /* Maximum number of points in a schedule.      */
                            /* Note that MAXBP should be set to the same    */
                            /* number as the 'Maxbp' PARAMETER defined in   */
                            /* the Fortran include file 'finfo.inc'. */

/* Define the location and size of the graph. */
short int UP_LEFT_CORNER_X =  105;
short int UP_LEFT_CORNER_Y =  455;
short int LO_RIGHT_CORNER_X=  620;
short int LO_RIGHT_CORNER_Y=  100;

/***** Printer global variables from fgu_print.c *****/
extern int to_printer; /* Logical to redirect output to print buffer. */
extern float increase; /* Multiplied to the picture size in the print buffer.*/
extern int y_margin;   /* Bottom of page margin in dots. 121.6 dots = 1"     */
extern int x_margin;   /* Left of page margin in dots.    85.0 dots = 1"     */

/***** bar_menu() global vairables *****/
int mnu_file=10, switch_stat=0;

/***** bar_menu() global variables *****/
short int COMMAND_MENU= 0;
short int LABEL_MENU  = 1;

/***** Menuing variables from fgu_lib.c *****/
extern Ipt  menu_pos[2][1000];
extern char menu_item[2][1000][40];
extern char *menu_label(int menu_in_use, int sub);
extern HLS_color menu_color[2][1000];
extern int  current_menu_item[2], starting_item[2];
extern int  ending_item[2], menu_in_use;
extern char menu_file_record[200][80];
extern short int menu_file_size;
char menu_file[100];
Ipt  screen_pos;
int  sort_labels;  /* Logical used by menu_label() */

/***** fgu_gr() global variables *****/
Ipt cursor;
/* Auto grid scaling is performed when zoom_status=0 (false).  User specified */
/* grid scaling is used when zoom_status=1 (true).  The function zoom_prompt */
/* is used to obtain a user specified scaling. */
int zoom_status=0;     /* Auto scaling ON=0/OFF=1   */
int overlay=0;         /* Overlay logical */
HLS_color grid_color;  /* Grid color will be yellow */
HLS_color curve_color; /* Curve color will be red   */
HLS_color text_color;  /* text color will be yellow */
HLS_color background_color;
HLS_color foreground_color;

struct overlays_on_screen { /* Stores the curves that are currently on screen.*/
   short int no[99];        /* Holds the curve number. */
   short int type[99];      /* Holds the curve type. Not used in FGU */
};
typedef struct overlays_on_screen Overlay;
Overlay curve_on_screen;
short int curve_count = 0;
int table_no;               /* Indicates the currently selected function. */

float xmin, xmax, ymin, ymax;
int array_chosen, edits_made, graph_on_screen;
char label[6][80];

/***** draw_graph() global variables: *****/
float y_sections, y_coarse_g;
float x_sections, x_coarse_g;
/* The following variables hold the re-scaled x and y min/max values after */
/* the draw_graph() function has been called. */
float new_xmin, new_xmax, new_ymin, new_ymax;
int y_decimal, x_decimal;

/***** zoom_prompt() global variables: *****/
float sxmin, sxmax, sxgrid, symin, symax, sygrid;

/***** receive_function_info() global variables: *****/
int curr_page=0; /* Indicates the current function page. Max=MAX_PAGES */
int array_size=1, curves_per_page=1, times_called=1, initialize=1,
    array_count=1, no_of_pages=1, no_of_tables=1;
char curr_func_name[15];

/***** Global functions *****/
int bar_menu(void);

/* Printer functions. */
extern void prtdrawline(Pts *line); /* Line drawing function for printer. */
extern void prtplotchar(char letter, int xpix, int ypix);

#ifdef UNIXHOST
   extern void frestore();
#endif
extern void clear_line(int line_no,int no_of_blanks);
extern int readkey();
extern float get_x_point();
extern float get_y_point();
float xarrays(int page,int curve,int point);
float yarrays(int page,int curve,int point);

/***** Global extern graphic functions from vt240_new.c *****/
extern void start_pixel(int x, int y);
extern void draw_line(int x, int y);
extern void graphics_on(void);
extern void graphics_off(void);
extern void hls_cls(void);
extern void set_colors(HLS_color *foreground, HLS_color *background,
                       HLS_color *red, HLS_color *blue);
extern void change_color(HLS_color *foreground);
extern void set_font_size(char *size);
extern void set_line_pattern(char *pattern);
extern void pixel_printxy(int xpix,int ypix,char *string);
extern void vtgcurseon(void);
extern void vtgcurseoff(void);
extern void printxy(int col, int row, char *string, short int mode);

/***** Global extern IO functions from io.c *****/
extern int io_open(int  unit, char *filename, char *type);
extern int io_read(int unit, char *string);
extern int io_write(int unit, char *string);
extern int io_close(int unit);

/************************************************************************/
/************************* Functions Start ******************************/
/************************************************************************/

/************************* receive_function_info() ****************************
     This function was designed to be called from Fortran.  It is the
initializing function for fgu_gr() module.  It performes the following
task:

- Receives certain information about the FGEN-function to be graphed.  The
  information passed is placed in global variables which indicate the 
  following:

  array_size  = the size of the x-breakpoint-schedule.
  no_of_pages = the number of two-dimensional pages that can be viewed
                from this FGEN-function.
                no_of_pages = size_of_thrid_BPsched*size_of_fourth_BPsched
  curves_per_page = the number of curves that can be displayed on one page. 
                    Equal to the size of the y-breakpoint-schedule.
  no_of_tables = the number of table names (functions) available from the
                 label menu (vertical menu).
  curr_func_name = Holds the table name (function) name currently in use.

- The labels for the graph are copied from the Fortran string descriptor
  structures to a global two-dimensional 'C' array of type char.  The labels
  are placed on the graph by the draw_labels() function.

     This information is required because the FGEN-function to be graphed (or
edited) is never passed to this C module.  An FGEN-function is graphed using the
information already stored in the Fortran arrays.  The process of retrieving 
the FGEN-function values stored in the Fortran arrays has been simplified with
the use of two global C functions:

     float xarrays(int page,int curve,int point);
                      and
     float yarrays(int page,int curve,int point);

     These two functions return a single point in the FGEN-function to be
graphed.  By indicating a page in the FGEN-function, a particular curve on this
page, and which point on this curve - xarrays & yarrays will return the
appropriate values for the FGEN-function being graphed.  So, the information
passed to this function describes the limits of the FGEN-function being graphed.
Indicating a page, curve, or point which does not exist in the FGEN-function
will either return garbage or crash the program.

     When then Fortran subroutine 'Select_Function' is called it changes the
selected function in FGU then it calls this C function passing the required 
information back to GR.
*/
int receive_function_info(a_size, c_per_page, no_of_p, no_of_t, title, 
                          flabel, xlabel, ylabel, zlabel, tlabel, function)
int *a_size, *c_per_page, *no_of_p, *no_of_t;
#ifdef VAXHOST
   struct dsc$descriptor_s *title;
   struct dsc$descriptor_s *flabel;
   struct dsc$descriptor_s *xlabel;
   struct dsc$descriptor_s *ylabel;
   struct dsc$descriptor_s *zlabel;
   struct dsc$descriptor_s *tlabel;
   struct dsc$descriptor_s *function;
#endif
#ifdef UNIXHOST
   char *title;
   char *flabel;
   char *xlabel;
   char *ylabel;
   char *zlabel;
   char *tlabel;
   char *function;
#endif
{
   int i, j;

   array_chosen = -1;
   curr_page    = 0;

/* Keep info. passed in global variables. */
/*   if (times_called==1) {  Only need to do this onces for each function. */
      curves_per_page = *c_per_page;
      no_of_pages     = *no_of_p;
      array_size      = *a_size;
      no_of_tables    = *no_of_t;
/*   }*/

/* Clear label array */
   for (i=0; i<=5; i++)
      for (j=0; j<=79; j++)
         label[i][j]='\0';

#ifdef VAXHOST
/* Place string pointer values into 'label' array */
   for (i=0; i < title->dsc$w_length; i++)
      label[0][i] = title->dsc$a_pointer[i];
   for (i=0; i < flabel->dsc$w_length; i++)
      label[1][i] = flabel->dsc$a_pointer[i];
   for (i=0; i < xlabel->dsc$w_length; i++)
      label[2][i] = xlabel->dsc$a_pointer[i];
   for (i=0; i < ylabel->dsc$w_length; i++)
      label[3][i] = ylabel->dsc$a_pointer[i];
   for (i=0; i < zlabel->dsc$w_length; i++)
      label[4][i] = zlabel->dsc$a_pointer[i];
   for (i=0; i < tlabel->dsc$w_length; i++)
      label[5][i] = tlabel->dsc$a_pointer[i];
   for (i=0; i < function->dsc$w_length; i++)
      curr_func_name[i] = function->dsc$a_pointer[i];
#endif
#ifdef UNIXHOST
/* Place string pointer values into 'label' array */
   strcpy(label[0],title);
   strcpy(label[1],flabel);
   strcpy(label[2],xlabel);
   strcpy(label[3],ylabel);
   strcpy(label[4],zlabel);
   strcpy(label[5],tlabel);
   strcpy(curr_func_name, function);
#endif

   for (i=strlen(curr_func_name)-1; curr_func_name[i]==' ' && i>0; i--)
      curr_func_name[i] = '\0';

   return(1);
}

/**************************** fgu_gr() ***********************************
     This function is the main module of this program.  It is designed to
be called from Fortran.  The following tasks are performed:

- Initializes the graphics screen the first time fgu_gr() is called from
  the FGU Fortran.

- An auto-scaled graph is draw by the draw_graph() function.  The variable
  'chosen_array' indicates if only one specific function curve is to be drawn,
  or all the function curves should be draw on the graph.  If 'chosen_array'
  is equal to zero then all the curves are draw.  A positive number indicates
  a specific curve.

- Control of the program is then passed to the bar_menu() function.
*/
int fgu_gr(chosen_array, starting_page)
int *chosen_array;  /* Indicates if an individual curve was chosen. 0=ALL */
int *starting_page;
{
extern float y_pixel(float func_val);
extern float x_pixel(float func_val);
int draw_graph(void);
int draw_labels(void);
#ifdef VAXHOST
   extern void readkey_init(void);
#endif
  /* line.xy1.x, line.xy1.y, line.xy2.x, line.xy2.y */
   Pts line={{659,  1}, {659,479}};
   int status, i, j, k, l;

/* No edit have been made yet.  Make sure the edits flag is false.    */ 
/* When the edit_made variable is equal to 38 edits were made to this */
/* functoin. */
   edits_made=0;
   sort_labels = 1; /* Logical causes function names to be sorted. */

/* Initialize graphics only once during the execution of FGU. */
   if (initialize) {
      curve_color= HLS_RED_MEDIUM_VIOLET; /* Curve color will be red   */
      text_color = HLS_YELLOW_BRIGHT;     /* text color will be yellow */
      foreground_color = HLS_GRAY_LIGHT;
      background_color = HLS_GRAY_DARK_SLATE;
      grid_color = foreground_color;/* Grid color will be same as foreground */
#     ifdef VAXHOST
         readkey_init();
#     endif
      initialize = 0;
   }
   cls();
   graphics_on();
   set_colors(&foreground_color, &background_color,
              &curve_color, &text_color);
   change_color(&text_color);
   start_pixel(line.xy1.x, line.xy1.y);
   draw_line(line.xy2.x, line.xy2.y);

   array_chosen = *chosen_array-1;

   cursoroff();
   new_xmin=0;
   new_xmax=0;
   new_ymin=0;
   new_ymax=0;

/* If a specific curve was chosen then the starting page is unknown.    */
/* In this case the starting page sent from the Fortran module is used. */
   if (array_chosen>=0)
      curr_page = *starting_page - 1;

   status=draw_graph();

   sxmin=new_xmin;    /* Set the default values for the 'Scale' option.  */
   sxmax=new_xmax;    /* The default values are derived through auto     */
   sxgrid=x_coarse_g; /* scaling performed in the draw_graph() function. */
   symin=new_ymin;
   symax=new_ymax;
   sygrid=y_coarse_g;

   if (status>0) { /* Did draw_graph() function execute OK? */
      change_color(&curve_color);      /* Set curve graphics color. */
/* The following 'if' statement decides whether an individual curve */
/* was chosen or all the curves are to be displayed. */
      k = curves_per_page-1;
      if (array_chosen<0)
         l=0;
      else {
         k=array_chosen;
         l=array_chosen;
      }
      for (i=l; i<=k; i++) {
         line.xy1.y = y_pixel(yarrays(curr_page,i,0));
         line.xy1.x = x_pixel(xarrays(curr_page,i,0));
         start_pixel(line.xy1.x, line.xy1.y);
         for (j=1; j<array_size; j++) {  /* Draw the function curve.  */
            line.xy2.y = y_pixel(yarrays(curr_page,i,j));
            line.xy2.x = x_pixel(xarrays(curr_page,i,j));
            if (to_printer)
               prtdrawline(&line);
            else
               draw_line(line.xy2.x, line.xy2.y);
         }
      }
      graph_on_screen = 1;
   }
   else {        /* draw_graph() function had an error! */
      graphics_off();
      cls();
      cursoron();
      return(status);
   }

/* Label the grid. */
   status=draw_labels();

   strcpy(menu_file,"fgu1.mnu");

   if (status>0)         /* If everything is OK at this point pass */
      status=bar_menu(); /* control of program to bar_menu(). */

   cls();
   graphics_on();
   set_colors(&text_color, &background_color,
              &curve_color, &foreground_color);
   graphics_off();
   cursoron();
   curr_page=0;

  /* Set video attributes back to normal using 'printxy' function. */
   printxy(1,24," ",5);

#  ifdef UNIXHOST
      fflush(stdout);
      frestore();
#  endif

  /* Return proper status code. */
   if (edits_made==38 && status>0)
      return(edits_made);
   else
      return(status);
}

/**************************** bar_menu() ***********************************/
int bar_menu(void)
{
extern int zoom_prompt(void);
extern int read_menu_file(char *menu_id);
extern int hardcopy(void);
short int string_compare(char *string1,char *string2,int len);
void update_bar_menu(int letter,char direction);
void switch_axis(void);
void cls_refresh_menus(void);
void refresh_bar_menu(int show_item, int show_all);
void update_legend(void);
void build_label_menu(char direction);
void attributes_on(int item);
int bar_menu_ylabels(void);
int edit_function(void);
int refresh_graph(int curve_only);
#define MAIN_MENU   0
#define HCPY_MENU   1
   int letter;

   menu_in_use = LABEL_MENU;
   build_label_menu('F');
   refresh_bar_menu(0,1);

  /* Each time fgu_gr() is called from the Fortran dot prompt the name */
  /* of the current function must be match with its corresponding name */
  /* in the label menu.  This will initialize the global variable      */
  /* 'table_no' which should always indicate the current function. */
   for (table_no=0; table_no<no_of_tables; table_no++)
      if (!memcmp(menu_label(menu_in_use,table_no),
                  curr_func_name, strlen(curr_func_name)))
         break;

   menu_in_use = COMMAND_MENU;
   ending_item[menu_in_use]=0;
   current_menu_item[menu_in_use]=0;
   load_menu_file(menu_file);
   read_menu_file("MAIN_MENU");
   refresh_bar_menu(1,1);

   while (1) {          /* Loop until break.  */
      letter=readkey(); /* Read a key stroke. */
      /* Up or Down arrow pressed? */
      if (letter==274 || letter==275) {
         if (no_of_tables<=0) {
            clear_line(23,65);
            printxy(27,23,"File not loaded.",2);
         }
         else {
            attributes_on(current_menu_item[menu_in_use]);
            printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                    menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                    menu_label(menu_in_use,current_menu_item[menu_in_use]),0);
            menu_in_use = LABEL_MENU;
            letter=bar_menu_ylabels();
            menu_in_use = COMMAND_MENU;
            refresh_bar_menu(1,0);
         }
      }
      else if (letter>=274 && letter<=277)  /* Right or Left arrow pressed? */
         update_bar_menu(letter, 'H'); /* If an arrow key was pressed. */

      /* Check through the menu_item array to decide what action to take. */
      /* menu_item=1.0.0.0.0.0 if Exit is selected.        */
      /* menu_item=2.0.0.0.0.0 if Next_Pg is selected.     */
      /* menu_item=3.0.0.0.0.0 if Edit is selected.        */
      /* menu_item=4.0.0.0.0.0 if Switch-axis is selected. */
      /* menu_item=5.0.0.0.0.0 if Zoom is selected.        */
      /* menu_item=6.0.0.0.0.0 if Hardcopy is selected.    */
      /* menu_item=7.0.0.0.0.0 if Prev_Pg is selected.     */
      /* menu_item=8.0.0.0.0.0 if OVLY is selected.        */

      else if (((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "1.0.0.0.0.0",11)) || letter==313) {
         letter=32; /* Condition 32 exits fgu_gr and returns to Fortran. */
         zoom_status=0; /* If auto scaling is OFF, turn it back ON. */
         break;
      }
      else if (((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "2.0.0.0.0.0",11)) || letter==316) {
         if (no_of_pages==1) {
            clear_line(23,65);
            printxy(17,23,"Only one page in this function.",2);
         }
         else {
            curr_page++;
            if (curr_page <= no_of_pages-1) {
               update_legend();
               letter=refresh_graph(0);
               if (curr_page == no_of_pages-1) {
                  printxy(15,23,"Positioned on last page of function.",2);
               }
            }
            else if (curr_page > no_of_pages-1) {
               curr_page=0;
               update_legend();
               letter=refresh_graph(0);
               printxy(14,23,"Positioned on first page of function.",2);
            }
         }
      }
      else if ((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "3.0.0.0.0.0",11)) {
         if (!switch_stat)
            letter=edit_function(); /* Edit break point values */
         else {
            clear_line(23,65);
            printxy(15,23,"Cannot edit while axis are switched.",2);
         }
      }
      else if ((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "4.0.0.0.0.0",11)) {
         if (switch_stat)  /* Toggle 'switch_stat' TRUE or FALSE. */
            switch_stat=0;
         else
            switch_stat=1;
         switch_axis();
         letter=refresh_graph(0);
      }
      else if ((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "5.0.0.0.0.0",11)) {
         zoom_prompt();
      }
      else if ((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "6.0.0.0.0.0",11)) {
         letter = hardcopy();
      }
      else if (((letter==13 || letter==314) &&
            string_compare(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                           "7.0.0.0.0.0",11)) ||
            letter==315) {
         if (no_of_pages==1) {
            clear_line(23,65);
            printxy(17,23,"Only one page in this function.",2);
         }
         else {
            curr_page--;
            if (curr_page <= -1) {
               curr_page = no_of_pages-1;
               update_legend();
               letter=refresh_graph(0);
               printxy(15,23,"Positioned on last page of function.",2);
            }
            else if (curr_page >= 0) {
               update_legend();
               letter=refresh_graph(0);
               if (curr_page == 0)
                  printxy(14,23,"Positioned on first page of function.",2);
            }
         }
      }
      else if (letter==127)   /* Destructive backspace key pressed? */
         cls_refresh_menus(); /* Yes, clear screen and refresh the menus. */
      if (letter<1)
         break; /* Check if error occured. */
   }
   return(letter);
}

/************************* bar_menu_ylabels() ********************************/
int bar_menu_ylabels(void)
{
short int string_compare(char *string1,char *string2,int len);
int refresh_graph(int curve_only);
int draw_labels(void);
void cls_refresh_menus(void);
void update_bar_menu(int letter, char direction);
void refresh_bar_menu(int show_item, int show_all);
void build_label_menu(char direction);
void attributes_on(int item);
extern int select_function();
extern void erase_bar_menu(void);
extern void erase_graph(void);
extern void savefunc();     /* From FGU_CMDS.FOR module. */
   int letter;

   refresh_bar_menu(1,0);
   while (1) {          /* Loop until break.  */
      letter=readkey(); /* Read a key stroke. */
      /* Remove or Right/Left arrow key pressed? */
      if (letter==276 || letter==277 || letter==313) {
         attributes_on(current_menu_item[menu_in_use]);
         printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                 menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                 menu_label(menu_in_use,current_menu_item[menu_in_use]),0);
         break;
      }
      else if (letter==274 || letter==275)  /* Up or Down arrow pressed? */
         update_bar_menu(letter, 'V'); /* If an arrow key was pressed. */

      /* Check through the menu_item array to decide what action to take. */
      /* menu_item=## <number> if a label name was selected. */

      else if (letter==256) {  /**** PF1 key pressed? ****/
      }
      else if (letter==257) {  /**** PF2 key pressed? ****/
      }
      else if (letter==258) {  /**** PF3 key pressed? ****/
      }
      else if (letter==259) {  /**** PF4 key pressed? ****/
      }
      else if (letter==13 || letter==314) {
         if (edits_made==38) {
            edits_made=0;      /* If the previous function was edited save */
            savefunc();        /* it before selecting a new function. */
         }
         if (!overlay && graph_on_screen) {
            erase_graph();
            curve_count = 0;
         }
         sscanf(menu_item[menu_in_use][current_menu_item[menu_in_use]],
                "%d",&table_no);

         if (!select_function(&table_no)) {
            clear_line(23,65);
            printxy(20,23,"Could not find table name?",2);
         }
         else {
            if (refresh_graph(0) > 0) {
               curve_on_screen.no[curve_count] = table_no;
               curve_on_screen.type[curve_count] = -1; /* Not used in FGU */
               curve_count++;
            }

            sxmin = new_xmin;
            sxmax = new_xmax;
            sxgrid = x_coarse_g;
            symin = new_ymin;
            symax = new_ymax;
            sygrid = y_coarse_g;

#           ifdef UNIXHOST
                fflush(stdout);
#           endif
         }
      }
      else if (letter==316) {  /* Next screen pressed? */
         if (no_of_tables>24) {
            build_label_menu('F');
            erase_bar_menu();
            refresh_bar_menu(1,1);
         }
         else {
            clear_line(23,65);
            printxy(18,23,"Only one page of table names.",2);
         }
      }
      else if (letter==315) {  /* Prev screen pressed? */
         if (no_of_tables>24) {
            build_label_menu('B');
            erase_bar_menu();
            refresh_bar_menu(1,1);
         }
         else {
            clear_line(23,65);
            printxy(18,23,"Only one page of table names.",2);
         }
      }
      else if (letter==127)   /* Destructive backspace key pressed? */
         cls_refresh_menus(); /* Yes, clear screen and refresh the menus. */
      if (letter<1)
         break; /* Check if error occured. */
   }
   return(letter);
}

/************************** switch_axis() ********************************/
void switch_axis(void)
{
void swapf(float *num1, float *num2);
void swapi(int *num1, int *num2);
   char hold[80];
   int i, j, k;

   min_max_arrays();

   swapf(&xmin, &ymin); /* Swap x and y min/max values. */
   swapf(&xmax, &ymax);

   swapf(&sxgrid, &sygrid); /* Swap grid scaling values. */
   swapf(&sxmin, &symin);
   swapf(&sxmax, &symax);
   swapf(&new_xmin, &new_ymin);
   swapf(&new_xmax, &new_ymax);
   swapf(&x_coarse_g, &y_coarse_g);
   swapf(&x_sections, &y_sections);
   swapi(&x_decimal, &y_decimal);

   /* Swap label 1 and 2 (labels for x and y axis). */
   for (i=0; i<80; i++) {
      hold[i]=label[2][i];
      label[2][i]=label[1][i];
      label[1][i]=hold[i];
   }
}

/************************* min_max_arrays() ******************************
     Finds the minimum and maximum x and y values for the current function 
page.  The function page used is indicated by the global variable 
'curr_page'.
*/
int min_max_arrays(void)
{
   int i, j, status=1;

   xmin= 1.7 * pow(10,38);   /* Maximum value possible for a float. */
   xmax= -20000000;          /* Minimum value possible for a float. */
   ymin= 1.7 * pow(10,38);   /* Maximum value possible for a float. */
   ymax= -20000000;          /* Minimum value possible for a float. */

   for (i=0; i<curves_per_page; i++) {
      for (j=0; j<array_size; j++) {
        /* Get x-axis min/max values */
         if (xarrays(curr_page,i,j) < xmin)
             xmin = xarrays(curr_page,i,j);
         if (xarrays(curr_page,i,j) > xmax) 
             xmax = xarrays(curr_page,i,j);
       /* Get y-axis min/max values */
         if (yarrays(curr_page,i,j) < ymin) 
            ymin = yarrays(curr_page,i,j);
         if (yarrays(curr_page,i,j) > ymax)
            ymax = yarrays(curr_page,i,j);
      }
   }

/* Check if min/max values are equal. */
   if (xmin==xmax) {
      if (xmin==0) {
        xmin--;
        xmax++;
      }
      else if (xmin>0) {
        xmin=xmin-xmin;
        xmax=xmax+xmax;
      }
      else {
        xmin=xmin+xmin;
        xmax=xmax-xmax;
      }
   }
   if (ymin==ymax) {
      if (ymin==0) {
        ymin--;
        ymax++;
      }
      else if (ymin>0) {
         ymin=ymin-ymin;
         ymax=ymax+ymax;
      }
      else {
         ymin=ymin+ymin;
         ymax=ymax-ymax;
      }
   }

   return(status);
}

/***************************** swapf() ************************************/
void swapf(num1, num2)
float *num1, *num2;
{
   float hold;

   hold=(*num1);
   *num1=(*num2);
   *num2=hold;
}

/***************************** swapi() ************************************/
void swapi(num1, num2)
int *num1, *num2;
{
   int hold;

   hold=(*num1);
   *num1=(*num2);
   *num2=hold;
}

/************************* edit_function() *******************************/
int edit_function(void)
{
#define MOVE2I_CODE         "P[%d,%d]\n"
extern void send_graph_edits(int *page, int *curve, float *edited_yarray);
extern void get_y_breakpoint();
extern int scale_point(Pts *line);
extern float y_value(float pix_val);
extern float y_pixel(float func_val);
extern float x_pixel(float func_val);
extern int round_off(float num);
int refresh_graph(int curve_only);
   float new_val, edited_yarray[MAXBP], breakpoint;
   Pt  gr_cursor;
   Pts line;
   char ylabel_name[20];
   int curve_chosen=0;
   int i, j, k, l, status, step, default_step=5, changes=33;

   clear_line(23,65);

   if (array_chosen < 0) {
      strcpy(ylabel_name,label[3]);
      for (i=0; i<strlen(ylabel_name) && ylabel_name[i]!='='; i++);
      ylabel_name[i]='\0';
      fprintf(stdout,
         "\033[23;1HUse arrows to select curve. Current curve %s=",
            ylabel_name);
      while(1) {
         int letter;

     /* Find first break point that is within the graph area setting 'i'. */
     for (i=0; (y_pixel(yarrays(curr_page,curve_chosen,i))>=UP_LEFT_CORNER_Y ||
               y_pixel(yarrays(curr_page,curve_chosen,i))<=LO_RIGHT_CORNER_Y ||
               x_pixel(xarrays(curr_page,curve_chosen,i))>=LO_RIGHT_CORNER_X ||
               x_pixel(xarrays(curr_page,curve_chosen,i))<=UP_LEFT_CORNER_X) &&
               i<array_size; i++);

         j = curr_page+1;
         k = curve_chosen+1;
         get_y_breakpoint(&j,&k,&breakpoint);
         fprintf(stdout,
            "\033[23;%dH%-5.6f",45+strlen(ylabel_name),breakpoint);
         gr_cursor.x = x_pixel(xarrays(curr_page,curve_chosen,i));
         gr_cursor.y = 480-y_pixel(yarrays(curr_page,curve_chosen,i));
         graphics_on();
         fprintf(stdout,MOVE2I_CODE,round_off(gr_cursor.x),
            round_off(gr_cursor.y)); /* Position cursor. */
         vtgcurseon();      /* Turn cursor on. */

         letter = readkey();
         if (letter==314 || letter==13 || letter==14) /* SELECT pressed? */
            break;
         else if (letter==313) { /* REMOVE pressed? */
            vtgcurseoff();       /* Turn cursor on. */
            clear_line(23,65);
            return(33);
         }
         else if (letter==274) { /* UP ARROW pressed? */
            curve_chosen++;
            if (curve_chosen >= curves_per_page)
               curve_chosen = 0;
         }
         else if (letter==275) { /* DOWN ARROW pressed? */
            curve_chosen--;
            if (curve_chosen < 0)
               curve_chosen = curves_per_page-1;
         }
      }
   }
   else {
     curve_chosen = array_chosen;
     /* Find first break point that is within the graph area setting 'i'. */
     for (i=0; (y_pixel(yarrays(curr_page,curve_chosen,i))>=UP_LEFT_CORNER_Y ||
               y_pixel(yarrays(curr_page,curve_chosen,i))<=LO_RIGHT_CORNER_Y ||
               x_pixel(xarrays(curr_page,curve_chosen,i))>=LO_RIGHT_CORNER_X ||
               x_pixel(xarrays(curr_page,curve_chosen,i))<=UP_LEFT_CORNER_X) &&
               i<array_size; i++);
   }

   /* If there are no break points within the graph area, you cannot edit. */
   if (i==array_size) {
      clear_line(23,65);
      printxy(15,23,"No break points within graph area?",2);
      graphics_on();
      vtgcurseoff();      /* Turn cursor off. */
      return(33);
   }

   clear_line(23,65);

   /* Place curve to edit into 'edited_yarray'. */
   for (j=0; j<array_size; j++)
      edited_yarray[j]=yarrays(curr_page,curve_chosen,j);

   status=0;
   step = default_step;
   while (status != 313) { /* Loop while Remove key not pressed. */
      fprintf(stdout,"\033[23;20HX=%-5.6f Y=%-5.6f                 \033[1;1H"
         ,xarrays(curr_page,curve_chosen,i)
         ,edited_yarray[i]); /* Print X and Y corrdinates on screen. */
      /* Position the graphics cursor on a break point. */
      gr_cursor.x = x_pixel(xarrays(curr_page,curve_chosen,i)); /* Convert function values to  */
      gr_cursor.y = 480-y_pixel(edited_yarray[i]); /* a pixel value. */
      graphics_on();
      fprintf(stdout,MOVE2I_CODE,round_off(gr_cursor.x),
         round_off(gr_cursor.y)); /* Position cursor. */
      vtgcurseon();      /* Turn cursor on. */

      status=readkey();  /* Read a key stroke. */
      if (status==277) { /* Right arrow key pressed? */
         if (i < array_size-1)
            i++;
         else
            i=0;
      }
      else if (status==276) { /* Left arrow key pressed? */
         if (i > 0)
            i--;
         else
            i=array_size-1;
      }
      else if (status>=49 && status<=57){ /* # pressed?  Pressing a # changes */
         step=pow(status-48,2);           /* the cursor stepping speed.       */
         default_step=step;
      }
      else if (status==274 || status==275) { /* Up or Down arrow pressed? */
         new_val = edited_yarray[i];
         while (status != 312) {  /* While Insert key not pressed. */
            if (status>=49 && status<=57){/* # pressed?  Pressing a # changes */
               step=pow(status-48,2);     /* the cursor stepping speed.       */
               default_step=step;
            }
            else if (status==275)      /* Down arrow? */
               new_val = y_value(480-(gr_cursor.y+=step));
            else if (status==274)      /* Up arrow? */
               new_val = y_value(480-(gr_cursor.y-=step));
            else if (status==277 || status==276 || status==313) { /* Left or Right arrow? */
               status=277;/* Make sure status doesn't stay equal to 313. */
               break; /* Pressing Remove, or left/right arrow will abort changes. */
            }
            if (status==274 || status==275) {
               /* Print X and Y corrdinates on screen. */
               fprintf(stdout,
                  "\033[23;20HX=%-5.6f Y=%-5.6f                 \033[1;1H"
                  ,xarrays(curr_page,curve_chosen,i),new_val);
               graphics_on();
               fprintf(stdout,MOVE2I_CODE,round_off(gr_cursor.x),
                  round_off(gr_cursor.y)); /* Position cursor. */
               vtgcurseon();     /* Turn cursor on.    */
            }
            status=readkey();  /* Read a key stroke. */
            if (status==312) { /* Was Insert key just pressed? */
               changes=38;     /* Changes have been made.  Set flag. */
               change_color(&background_color);  /* Set line color to background color. */
               if (i<array_size-1) { /* Erase line to the right? */
                  line.xy1.y = 
                     y_pixel(edited_yarray[i]);
                  line.xy2.y =
                     y_pixel(edited_yarray[i+1]);
                  line.xy1.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i));
                  line.xy2.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i+1));
                  if (scale_point(&line)) {
                     if (to_printer)
                        prtdrawline(&line);
                     else {
                        start_pixel(line.xy1.x, line.xy1.y);
                        draw_line(line.xy2.x, line.xy2.y);
                     }
                  }
               }
               if (i>0) { /* Erase line to the left? */
                  line.xy1.y =
                     y_pixel(edited_yarray[i]);
                  line.xy2.y =
                     y_pixel(edited_yarray[i-1]);
                  line.xy1.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i));
                  line.xy2.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i-1));
                  if (scale_point(&line)) {
                     if (to_printer)
                        prtdrawline(&line);
                     else {
                        start_pixel(line.xy1.x, line.xy1.y);
                        draw_line(line.xy2.x, line.xy2.y);
                     }
                  }
               }
               change_color(&curve_color); /* Set line color to curve color. */
               edited_yarray[i]=new_val; /* Place the edited value into yarray. */
               if (i<array_size-1) { /* Draw line to the right? */
                  line.xy1.y =
                     y_pixel(edited_yarray[i]);
                  line.xy2.y =
                     y_pixel(edited_yarray[i+1]);
                  line.xy1.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i));
                  line.xy2.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i+1));
                  if (scale_point(&line)) {
                     if (to_printer)
                        prtdrawline(&line);
                     else {
                        start_pixel(line.xy1.x, line.xy1.y);
                        draw_line(line.xy2.x, line.xy2.y);
                     }
                  }
               }
               if (i>0) { /* Draw line to the left? */
                  line.xy1.y =
                     y_pixel(edited_yarray[i]);
                  line.xy2.y =
                     y_pixel(edited_yarray[i-1]);
                  line.xy1.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i));
                  line.xy2.x =
                     x_pixel(xarrays(curr_page,curve_chosen,i-1));
                  if (scale_point(&line)) {
                     if (to_printer)
                        prtdrawline(&line);
                     else {
                        start_pixel(line.xy1.x, line.xy1.y);
                        draw_line(line.xy2.x, line.xy2.y);
                     }
                  }
               }
            }
         }
      }
   }

   graphics_on();
   vtgcurseoff(); /* Turn cursor off.   */
   /* Erase x and y corrdinates from screen. */
   clear_line(23,65);
   if (changes==38) {
      printxy(20,23,"Keep these changes? (y/n)",2);
      status=0;
      while (status!=89 && status!=121 &&
             status!=78 && status!=110 && status!=313) {
         status=readkey();
         if (status==89 || status==121) { /* Y or y was pressed. */
            i=curr_page+1;
            j=curve_chosen+1;
            send_graph_edits(&i,&j,edited_yarray);
            printxy(14,23,"Function values have been modified...",2);
            edits_made=38;
         }
         else if (status==78 || status==110 || status==313) {
            printxy(15,23,"Refresh graph with old curve? (y/n)",2);
            status=0;
            while (status!=89 && status!=121 &&
                   status!=78 && status!=110 && status!=313) {
               status=readkey();
               if (status==89 || status==121)  /* Y or y was pressed. */
                  changes=refresh_graph(0);
               else if (status==78 || status==110 || status==313)
                  clear_line(23,65);
            }
         }
      }
   }
#  ifdef UNIXHOST
      fflush(stdout);
#  endif
   return(changes);
}

/************************ refresh_graph() ********************************/
int refresh_graph(int curve_only)
{
extern void erase_graph(void);
extern int scale_point(Pts *line);
extern float y_pixel(float func_val);
extern float x_pixel(float func_val);
int draw_graph(void);
   Pts line; /* line.xy1.x, line.xy1.y, line.xy2.x, line.xy2.y */
   int status=1, i, j, k, l;

   if (!to_printer)
      if (graph_on_screen)
         erase_graph();
   if (!curve_only) 
      status=draw_graph();

   if (status>0) { /* Did draw_graph() function execute OK? */
      change_color(&curve_color);          /* Set curve graphics color. */
/* The following 'for i' statement decides whether an individual curve */
/* was chosen or all the curves are to be displayed.                   */
      k = curves_per_page-1;
      if (array_chosen<0)
         l=0;
      else {
         k=array_chosen;
         l=array_chosen;
      }
      for (i=l; i<=k; i++) {
         line.xy1.y = y_pixel(yarrays(curr_page,i,0));
         line.xy1.x = x_pixel(xarrays(curr_page,i,0));
         start_pixel(line.xy1.x, line.xy1.y);
         for (j=1; j<array_size; j++) {  /* Draw the function curve.  */
            line.xy2.y = y_pixel(yarrays(curr_page,i,j));
            line.xy2.x = x_pixel(xarrays(curr_page,i,j));
            /* Don't allow the function curve to go beyond the grid. */
            if (scale_point(&line)) {
               if (to_printer)
                  prtdrawline(&line);
               else
                  draw_line(line.xy2.x, line.xy2.y);
            }
            line.xy1.y = y_pixel(yarrays(curr_page,i,j));
            line.xy1.x = x_pixel(xarrays(curr_page,i,j));
         }
      }
      graph_on_screen = 1;
   }
   else        /* draw_graph() function had an error! */
      return(status);

/* Label the grid. */
   status=draw_labels();

   return(status);
}

/************************* draw_labels() ************************************/
int draw_labels(void)
{
   int i, j, status=1, center_point;

   change_color(&text_color);           /* Set text graphics color. */

/* Draw the title on the screen */
   center_point = UP_LEFT_CORNER_X+((LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/2);
   cursor.x = center_point-((10*strlen(label[0]))/2);
   if (cursor.x < 1) cursor.x=1;
   cursor.y = UP_LEFT_CORNER_Y+35;
   if (cursor.y > 479) cursor.y=479;
   if (to_printer)
      for (i=0; i<=strlen(label[0]); i++)
         prtplotchar(label[0][i], cursor.x+=10, cursor.y);
   else
      pixel_printxy(cursor.x,cursor.y,label[0]);

/* Draw 'flabel' along the y-axis */
   center_point = LO_RIGHT_CORNER_Y+((UP_LEFT_CORNER_Y-LO_RIGHT_CORNER_Y)/2);
   cursor.x = UP_LEFT_CORNER_X-115;
   if (cursor.x < 1) cursor.x=1;
   if (to_printer) {
      cursor.y = (strlen(label[1])*20)/2+center_point;
      for (i=0; i<=strlen(label[1]); i++) {
         prtplotchar(label[1][i], cursor.x, cursor.y);
         cursor.y -= 20;
      }
   }
   else {
      cursor.y = center_point-((strlen(label[1])*8)/2);
      start_pixel(cursor.x,cursor.y);
      fprintf(stdout,"T(B)");
      fprintf(stdout,"(D90,S1)");
      fprintf(stdout,"'%s'",label[1]);
      fprintf(stdout,"(E)");
   }

/* Draw 'xlabel' along the x-axis */
   center_point = UP_LEFT_CORNER_X+((LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/2);
   cursor.x = center_point-((10*strlen(label[2]))/2);
   if (cursor.x < 1) cursor.x=1;
   cursor.y = LO_RIGHT_CORNER_Y-35;
   if (cursor.y > 479) cursor.y=479;
   if (to_printer)
      for (i=0; i<=strlen(label[2]); i++)
         prtplotchar(label[2][i], cursor.x+=10, cursor.y);
   else
      pixel_printxy(cursor.x,cursor.y,label[2]);

/* Decide which string is the longest. */
   j = strlen(label[3]);
   if (strlen(label[4])>j) j = strlen(label[4]);
   if (strlen(label[5])>j) j = strlen(label[5]);

/* Draw 'ylabel' if it has a value */
   if (strlen(label[3])>0) {
      cursor.x = LO_RIGHT_CORNER_X-((j*10)+15);
      if (cursor.x < 1) cursor.x=1;
      cursor.y = UP_LEFT_CORNER_Y-10;
      if (to_printer)
         for (i=0; i<=strlen(label[3]); i++)
            prtplotchar(label[3][i], cursor.x+=10, cursor.y-10);
      else
         pixel_printxy(cursor.x,cursor.y,label[3]);
   }

/* Draw 'zlabel' if it has a value */
   if (strlen(label[4])>0) {
      cursor.x = LO_RIGHT_CORNER_X-((j*10)+15);
      if (cursor.x < 1) cursor.x=1;
      cursor.y = UP_LEFT_CORNER_Y-30;
      if (to_printer)
         for (i=0; i<=strlen(label[4]); i++)
            prtplotchar(label[4][i], cursor.x+=10, cursor.y);
      else
         pixel_printxy(cursor.x,cursor.y,label[4]);
   }

/* Draw 'tlabel' if it has a value */
   if (strlen(label[5])>0) {
      cursor.x = LO_RIGHT_CORNER_X-((j*10)+15);
      if (cursor.x < 1) cursor.x=1;
      cursor.y = UP_LEFT_CORNER_Y-50;
      if (to_printer)
         for (i=0; i<=strlen(label[5]); i++)
            prtplotchar(label[5][i], cursor.x+=10, cursor.y+10);
      else
         pixel_printxy(cursor.x,cursor.y,label[5]);
   }

   return(status);
}

/************************* draw_graph() ******************************/
int draw_graph()
{
#define Y_GRID_ADJ   30  /* Y coarse grid adjustment. min=20 max=80 */
#define X_GRID_ADJ   40  /* X coarse grid adjustment. min=20 max=90 */
extern int round_off(float num);
extern void aline_zoom(void);
extern void autoscale();
int min_max_arrays(void);
void get_xy_decimal(void);
/*************************************************************************/
/* autoscale arguments to pass:                                          */
/*                                                                       */
/* min = a VARIABLE indicating the minimum function value.  On return,   */
/*       this variable will contain the minimum autoscaled value (the    */
/*       minimum value that will be shown on the graph).                 */
/* max = a VARIABLE indicating the maximum function value.  On return,   */
/*       this variable will contain the maximum autoscaled value (the    */
/*       maximum value that will be shown on the graph).                 */
/* coarse_grid = a VARIABLE to recieve the autoscaled coarse grid value. */
/* min_grid_num = a value which indicates the minimum coarse grid that   */
/*                will be produce by autoscale.  If any coarse grid is   */
/*                suitable this value should be zero.                    */
/* sections = a VARIABLE indicating the maximum number of sections that  */
/*            the autoscaled coarse grid can produce.  On return, this   */
/*            variable will contain the actual number of sections the    */
/*            coarse grid will produce.                                  */
/* mode = can equal the value 0 or 1.  Mode 0 will NOT truncate any      */
/*        unused sections.  Thus the value pass in the variable          */
/*        'sections' will be the value returned in 'sections'.  Mode 1   */
/*        WILL truncate any sections that the function will not fall in. */
/*                                                                       */
/* extern void autoscale(float *min, float *max, float *coarse_grid,     */
/*                      float *min_grid_num, int *sections, int *mode);  */
/*************************************************************************/

   Window vt240_window = VT240_EXTREME,
          vt_window    = VIRTUAL_EXTREME;
   Pts line; /* line.xy1.x, line.xy1.y, line.xy2.x, line.xy2.y */
   float y_range, x_range, min_grid_num=0, log_num, count, m;
   char num_string[20],last_num[2];
   int i, j, k, l, digit, mode=1;

/* Get min/max values for the current function page. */
   if (digit=min_max_arrays() < 1) 
      return(digit);

/* Check that the size of the y-axis is not too small. */
   if (UP_LEFT_CORNER_Y - LO_RIGHT_CORNER_Y < Y_GRID_ADJ*2) return(0);
/* Check that the size of the x-axis is not too small. */
   if (LO_RIGHT_CORNER_X - UP_LEFT_CORNER_X < X_GRID_ADJ*2) return(-1);

/*   grwindow_scale_find(&vt240_window,&vt_window);*/
   graphics_on();
   change_color(&grid_color);           /* Set grid graphics color */

   if (!zoom_status) { /* If zoom_status is false, perform auto scaling. */

/****************************************************************************/
/* Calculate the number of sections the y-axis will have by calculating the */
/* number of lines that can fit along its axis.                             */
/* (20 pixels = height of 1 line)                                           */
   y_range = ymax - ymin;
/* Raising the division factor Y_GRID_ADJ will produce a more coarse grid. */
   y_sections = (UP_LEFT_CORNER_Y - LO_RIGHT_CORNER_Y)/Y_GRID_ADJ;
   if (y_sections > 16) y_sections = 16; /* Set a maximum of 16 sections. */
/* Get the coarse grid for the y-axis. */
   new_ymin=ymin;
   new_ymax=ymax;
   digit=(int)y_sections;
   autoscale(&new_ymin,&new_ymax,&y_coarse_g,&min_grid_num,&digit,&mode);
   y_sections=(float)digit;

/**********************************************************/
/* Calculate the number of sections the x-axis will have. */
/* (10 pixels = width of 1 column)                        */
   x_range = xmax - xmin;
/* Raising the division factor X_GRID_ADJ will produce a more coarse grid. */
   x_sections = (LO_RIGHT_CORNER_X - UP_LEFT_CORNER_X)/X_GRID_ADJ;
   if (x_sections > 20) x_sections = 20; /* Set a maximum of 20 sections. */
/* Get the coarse grid for the x-axis. */
   new_xmin=xmin;
   new_xmax=xmax;
   digit=(int)x_sections;
   autoscale(&new_xmin,&new_xmax,&x_coarse_g,&min_grid_num,&digit,&mode);
   x_sections=(float)digit;

   }
   else
      aline_zoom();

/* Calculate the number of decimal places that will be shown on axis: */
   get_xy_decimal();

/* Start drawing graph.   */
/* Draw horizontal lines: */
   line.xy1.y = UP_LEFT_CORNER_Y;
   line.xy1.x = UP_LEFT_CORNER_X;
   line.xy2.y = UP_LEFT_CORNER_Y;
   line.xy2.x = LO_RIGHT_CORNER_X;
   if (to_printer)
      prtdrawline(&line);
   else {
      start_pixel(line.xy1.x, line.xy1.y);
      draw_line(line.xy2.x, line.xy2.y);
   }
   m = (UP_LEFT_CORNER_Y-LO_RIGHT_CORNER_Y)/y_sections;
   for (i=1; i<=y_sections; i++) {
      line.xy1.y -= m;
      line.xy2.y -= m;
      if (to_printer)
         prtdrawline(&line);
      else {
         start_pixel(line.xy1.x, line.xy1.y);
         draw_line(line.xy2.x, line.xy2.y);
      }
   }
/* The number of grid lines may be incorrect.  Check grid. */
   if (line.xy1.y != LO_RIGHT_CORNER_Y) {
      line.xy1.y = LO_RIGHT_CORNER_Y;
      line.xy2.y = LO_RIGHT_CORNER_Y;
      if (to_printer)
         prtdrawline(&line);
      else {
         start_pixel(line.xy1.x, line.xy1.y);
         draw_line(line.xy2.x, line.xy2.y);
      }
   }

/* The following code segment checks the number of sections the x-axis has  */
/* and makes sure that the sections are spaced far enough appart so that    */
/* the numbers printed do not touch or overlap.  If they do, the number     */
/* of section are decreased sufficiently. This is done only if auto scaling */
/* is ON (zoom_status=false). */
   if (!zoom_status) {
      for (j=0; j<=19; j++) num_string[j] = '\0';
      sprintf(num_string,"%.*f", x_decimal, new_xmin);
      while (1) {
         count = (float)((LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/(int)x_sections);
         if ((strlen(num_string)*10)/2 > count-5) {
            if (((int)x_sections)%2) {
               x_sections = x_sections/2.5;
               x_coarse_g = x_coarse_g*2.5;
            }
            else {
               x_sections = x_sections/2;
               x_coarse_g = x_coarse_g*2;
            }
         }
         else
            break;
      }
   }

/* Draw vertical lines: */
   line.xy1.y = UP_LEFT_CORNER_Y;
   line.xy1.x = UP_LEFT_CORNER_X;
   line.xy2.y = LO_RIGHT_CORNER_Y;
   line.xy2.x = UP_LEFT_CORNER_X;
   if (to_printer)
      prtdrawline(&line);
   else {
      start_pixel(line.xy1.x, line.xy1.y);
      draw_line(line.xy2.x, line.xy2.y);
   }
   m = (LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/x_sections;
   for (i=1; i<=x_sections; i++) {
      line.xy1.x += m;
      line.xy2.x += m;
      if (to_printer)
         prtdrawline(&line);
      else {
         start_pixel(line.xy1.x, line.xy1.y);
         draw_line(line.xy2.x, line.xy2.y);
      }
   }
/* The number of grid lines may be incorrect.  Check grid. */
   if (line.xy1.x != LO_RIGHT_CORNER_X) {
      line.xy1.x = LO_RIGHT_CORNER_X;
      line.xy2.x = LO_RIGHT_CORNER_X;
      if (to_printer)
         prtdrawline(&line);
      else {
         start_pixel(line.xy1.x, line.xy1.y);
         draw_line(line.xy2.x, line.xy2.y);
      }
   }

/* Draw numbers for y-axis: */
   if ((int)y_sections % 2 == 0) /* Check if y_sections odd or even. */
      digit = 0;
   else
      digit = 1;

   cursor.y = LO_RIGHT_CORNER_Y-10;
   count = new_ymin;
   for (i=1; i<=(((int)y_sections-digit)/2)+1; i++) {
      for (j=0; j<=19; j++) num_string[j] = '\0';
      sprintf(num_string,"%.*f", y_decimal, count);
      k = strlen(num_string);
      if (k>8) {
         for (j=0; j<=19; j++) num_string[j] = '\0';
         sprintf(num_string,"%1.3e", count);
         k = strlen(num_string);
      }
      cursor.x = (UP_LEFT_CORNER_X-15)-(10*k);
/*      if (cursor.x <= 0) return(-2);*/
      if (cursor.x <= 0) cursor.x = 0;
      if (to_printer)
         for (l = 0; l <= k-1; l++)
            prtplotchar(num_string[l], cursor.x+=10, cursor.y+5);
      else
         pixel_printxy(cursor.x+10,cursor.y+20,num_string);
      cursor.y += ((UP_LEFT_CORNER_Y-LO_RIGHT_CORNER_Y)/y_sections)*2;
      count += y_coarse_g*2;
   }

/* Draw numbers for x-axis: */
   if ((int)x_sections % 2 == 0) /* Check if x_sections odd or even. */
      digit = 0;
   else
      digit = 1;
   cursor.y = LO_RIGHT_CORNER_Y+10;
   count = new_xmin;
   m = 1;
   for (i=1; i<=(((int)x_sections-digit)/2)+1; i++) {
      for (j=0; j<=19; j++) num_string[j] = '\0';
      sprintf(num_string,"%.*f", x_decimal, count);
      k = strlen(num_string);
      l = ((LO_RIGHT_CORNER_X-UP_LEFT_CORNER_X)/x_sections)*
          (m-1)+UP_LEFT_CORNER_X;
      cursor.x = l-(15*k)/2;
      if (cursor.x <= 0) return(-3);
      if (to_printer)
         for (l = 0; l <= k-1; l++)
            prtplotchar(num_string[l], cursor.x+=10, cursor.y-30);
      else
         pixel_printxy(cursor.x+10,cursor.y-20,num_string);
/*            fprintf(stdout,"P[%d,%d]\n",cursor.x+=10,500-cursor.y);
            fprintf(stdout,"T'%c'",num_string[l]);*/
      count += x_coarse_g*2;
      m += 2;
   }
   return(1);
}

/************************ refresh_bar_menu() *********************************
     Refreshes the bar-menu indicated by the global variable 'menu_in_use'.
The parameters 'show_item' and 'show_all' are logicals which perform the
following function:

show_item: when TRUE will cause the menu label indicated by 'current_menu_item'
           to be displayed in reverse vidio.  When FALSE the menu labels are
           refreshed without indication of the 'current_menu_item'.
show_all:  when TURE all the labels of the menu are refreshed.  When FLASE
           only the label indicated by 'current_menu_item' is refreshed.

NOTE:  If both logicals are set FALSE nothing will be refreshed.
*/
void refresh_bar_menu(int show_item, int show_all)
{
void attributes_on(int item);
   int i;

   for (i=starting_item[menu_in_use]; i<=ending_item[menu_in_use]; i++) {
      if (i==current_menu_item[menu_in_use] && show_item)
         printxy(menu_pos[menu_in_use][i].x,
                 menu_pos[menu_in_use][i].y,
                 menu_label(menu_in_use,i),1);
      else if (show_all) {
         attributes_on(i);
         printxy(menu_pos[menu_in_use][i].x,
                 menu_pos[menu_in_use][i].y,
                 menu_label(menu_in_use,i),0);
      }
   }
}

/************************* update_bar_menu *****************************
     This function is called to update (move through) a bar menu previously
created by the read_menu_file() function.  It requires a 'letter' parameter
which is equal to the ascii value of an arrow key pressed (aquired using the
readkey() function).  The second paremeter, 'direction', indicates the
direction you have positioned the bar menu on the screen.  Either 'V' for
vertical or 'H' for horizontal.  Specifying 'H' for the direction will use
the left and right arrow key to scroll through the bar menu.  Specifying
'V' will use the up and down arrow keys.
*/
void update_bar_menu(int letter,char direction)
{
void attributes_on(int item);

   if ((direction=='H' && letter==277) || (direction=='V' && letter==275)) {
      attributes_on(current_menu_item[menu_in_use]);
      printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
              menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
              menu_label(menu_in_use,current_menu_item[menu_in_use]),0);
      if (current_menu_item[menu_in_use] == ending_item[menu_in_use]) {
         current_menu_item[menu_in_use] = starting_item[menu_in_use];
         printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                 menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                 menu_label(menu_in_use,current_menu_item[menu_in_use]),1);
      }
      else {
         current_menu_item[menu_in_use]++;
         printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                 menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                 menu_label(menu_in_use,current_menu_item[menu_in_use]),1);
      }
   }
   else if ((direction=='H'&&letter==276) || (direction=='V'&&letter==274)) {
      attributes_on(current_menu_item[menu_in_use]);
      printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
              menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
              menu_label(menu_in_use,current_menu_item[menu_in_use]),0);
      if (current_menu_item[menu_in_use] == starting_item[menu_in_use]) {
         current_menu_item[menu_in_use] = ending_item[menu_in_use];
         printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                 menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                 menu_label(menu_in_use,current_menu_item[menu_in_use]),1);
      }
      else {
         current_menu_item[menu_in_use]--;
         printxy(menu_pos[menu_in_use][current_menu_item[menu_in_use]].x,
                 menu_pos[menu_in_use][current_menu_item[menu_in_use]].y,
                 menu_label(menu_in_use,current_menu_item[menu_in_use]),1);
      }
   }
}

/**************************** get_xy_decimal() ********************************
     Calculates the number of decimal places that will be shown on the x & y
axis of the graph.  The global variables x_decimal & y_decimal hold the
result of this calculation.
*/
void get_xy_decimal(void)
{
extern int round_off(float num);
extern float fraction(float num);
   float log_num;
   int i;
 
   x_decimal = 0;
   if (fraction(x_coarse_g)) {
      x_decimal = 2;
      log_num = log10(fabs(fraction(x_coarse_g)*2));
      if (fabs(log_num)+0.5 >= 1)
         x_decimal = fabs(log_num)-fraction(log_num)+1;
   }

   y_decimal = 0;
   if (fraction(y_coarse_g)) {
      y_decimal = 2;
      log_num = log10(fabs(fraction(y_coarse_g)*2));
      if (fabs(log_num)+0.5 >= 1)
         y_decimal = fabs(log_num)-fraction(log_num)+1;
   }
}

/*************************** update_legend() ******************************/
void update_legend(void)
{
extern void get_graph_legend();
   int i,j;
   float zvalue,tvalue;
   char new_num[10];

   if (strlen(label[4])<=0) return; /* Return if function < 3 dimensions. */
   for (i=0; i<10; i++) new_num[i]='\0';
   j = curr_page+1;
   get_graph_legend(&j,&zvalue,&tvalue);

   sprintf(new_num,"%-.3f",zvalue);
   i=0;
   while (label[4][i]!='=') i++;
   label[4][i+1]='\0';
   strcat(label[4],new_num);

   if (strlen(label[5])<=0) return; /* Return if function < 4 dimensions. */
   for (i=0; i<10; i++) new_num[i]='\0';

   sprintf(new_num,"%-.2f",tvalue);
   i=0;
   while (label[5][i]!='=') i++;
   label[5][i+1]='\0';
   strcat(label[5],new_num);
   return;
}

/************************** build_label_menu() *******************************
*/
void build_label_menu(char direction)
{
   static short int init_color=1;
   int i,j,items,first_item,loop,count;
   extern HLS_color Background; /* From ???graph.c */

   if (init_color) {
      for (i=0; i<1000; i++)
         menu_color[menu_in_use][i]=text_color;
      init_color=0;
   }

   if (ending_item[menu_in_use] > 0) {
      if (no_of_tables>=24) {
         first_item = starting_item[menu_in_use];
         if (direction == 'B') {
            if (first_item == 0) {
               /* Wrap around to last page. */
               starting_item[menu_in_use] = first_item;
               while ((starting_item[menu_in_use]+24) < no_of_tables)
                  starting_item[menu_in_use] += 24;
            }
            else
               /* Go to prev page. */
               starting_item[menu_in_use] = first_item - 24;
         }
         else if (direction == 'F') {
            if (first_item+24 < no_of_tables)
               /* Go to next page. */
               starting_item[menu_in_use] = first_item + 24;
            else
               starting_item[menu_in_use] = 0;
         }
      }
   }

   /* Default page is the first page. */
   current_menu_item[menu_in_use] = starting_item[menu_in_use];
   count=0;
   for (i=starting_item[menu_in_use]; i<=no_of_tables-1 && count<24; i++) {
      sprintf(menu_item[menu_in_use][i],"%d",i);
      menu_pos[menu_in_use][i].y = count+1;
      menu_pos[menu_in_use][i].x = 68;
      count++;
   }
   ending_item[menu_in_use] = i-1;
}

/************************* attributes_on() **********************************
     This function activates the text attributes for a particular menu item.
Since changing colors has no affect on non graphical text the colors indicated
in the 'menu_color' array are substituted with text attributes (such as high-
light).

Currently: term_status 0 = print using current video attributes.
           term_status 1 = print using reverse video attributes.
           term_status 2 = print using bold attributes.
           term_status 3 = print using bold-underline attributes.
           term_status 4 = print using underline attributes.
           term_status 5 = print using normal video attributes (lowlight).
*/
void attributes_on(int item)
{
extern int same_color(HLS_color *color1, HLS_color *color2);
extern short int term_status;  /* From vaxgraph.c */
extern HLS_color Background;   /* From vaxgraph.c */

   if (same_color(
       &menu_color[menu_in_use][item],
       &foreground_color) && term_status!=5) {
      fprintf(stdout,"\033[0m");
      term_status=5;               /* term_status 5 = normal or lowlight */
   }
   else if (same_color(
       &menu_color[menu_in_use][item],
       &curve_color) && term_status!=4) {
      fprintf(stdout,"\033[0;4m"); /* term_status 4 = lowlight underline */
      term_status=4;
   }
   else if (same_color(
       &menu_color[menu_in_use][item],
       &text_color) && term_status!=2) {
      fprintf(stdout,"\033[0;1m"); /* term_status 2 = Bold or hilight */
      term_status=2;
   }
   else if (same_color(
       &menu_color[menu_in_use][item],
       &background_color) && term_status!=3) {
      fprintf(stdout,"\033[1;4m"); /* term_status 3 = Bold underline */
      term_status=3;
   }
}

/************************* cls_refresh_menus() *******************************/
void cls_refresh_menus(void)
{
void refresh_bar_menu(int show_item, int show_all);
   Pts line={{659,  1}, {659,479}};
   int current_menu;

   current_menu = menu_in_use;

   cls();
   graphics_on();
   change_color(&text_color);
   start_pixel(line.xy1.x, line.xy1.y);
   draw_line(line.xy2.x, line.xy2.y);
   menu_in_use = LABEL_MENU;
   if (current_menu == LABEL_MENU)
      refresh_bar_menu(1,1);
   else
      refresh_bar_menu(0,1);
   menu_in_use = COMMAND_MENU;
   if (current_menu == COMMAND_MENU)
      refresh_bar_menu(1,1);
   else
      refresh_bar_menu(0,1);
   graph_on_screen = 0;
   curve_count = 0;

   menu_in_use = current_menu;
}

/******************************* xarrays() ***********************************/
float xarrays(page,curve,point)
int page,curve,point;
{
   float num;

/* If 'switch_stat' is TRUE then y-axis values are return. */
   page++;  /* Subscripts in 'C' start at 0 so all values must be incremented */
   curve++; /* to comply with Fortran subscripts. */
   point++;
   if (!switch_stat)
      num = get_x_point(&page,&curve,&point);
   else
      num = get_y_point(&page,&curve,&point);
   return(num);
}

/******************************* yarrays() ***********************************/
float yarrays(page,curve,point)
int page,curve,point;
{
   float num;

/* If 'switch_stat' is TRUE then x-axis values are return. */
   page++;
   curve++;
   point++;
   if (!switch_stat)
      num = get_y_point(&page,&curve,&point);
   else
      num = get_x_point(&page,&curve,&point);
   return(num);
}

/******************************** readreal() *******************************/
int readreal(string, result)
#ifdef VAXHOST
   struct dsc$descriptor_s *string;
#endif
#ifdef UNIXHOST
   char *string;
#endif
float *result;
{
#ifdef VAXHOST
   int i;
   char c_string[80]={"\0"};

   /* Place string pointer value into 'c_string' array */
   for (i=0; i < string->dsc$w_length; i++)
      c_string[i] = string->dsc$a_pointer[i];
   if (sscanf(c_string,"%f",result))
      return(1);
   else
      return(0);
#endif
#ifdef UNIXHOST
   if (sscanf(string,"%f",result))
      return(1);
   else
      return(0);
#endif
}
