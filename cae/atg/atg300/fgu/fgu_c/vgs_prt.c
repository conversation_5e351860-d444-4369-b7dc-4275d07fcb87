/*****************************************************************/
/*                                                               */
/* Author : <PERSON><PERSON>e                                               */
/* Date   : Nov. 11, 1986.                                       */
/* Description : This routine is called by a dispatcher routine  */
/*               from graph.c for the VSA system.  It is used to */
/*               generate VISA graphs on a PRINTRONIX printer.   */
/*                                                               */
/* Revision 1.1, <PERSON><PERSON><PERSON><PERSON>, March 14, 1989.          */
/*               Modified the command send by prt_send_out() to  */
/*               specify the printer queue logical name          */
/*               (VSA$PRTQ) when printing the plot file.         */
/*             , <PERSON>, August 14, 1989.                  */
/*               Modified definition of TRUE and FALSE to comply */
/*               with GNU C                                      */
/*****************************************************************/
#include "ovp_prec.h"
#include <stdio.h>
#include <math.h>
#include "visa.h"
#include "graph.h"
#include "prt.h"
#include "scale.h"
#include "lldevice.h"
#include "screen.h"

#ifdef VAXHOST
/*#  include <vms/descrip.h>*/
/*#  include <sys/file.h>   */
#  include <descrip.h>
#  include <file.h>
#endif

Window virtual_screen=VIRTUAL_SCREEN;

#define TRUE             1
#define FALSE            0
#define FILE_ERROR     "File creation error"
#define PRT_X_SIZE     110  /* Maximum number of characters in a line */
#define PRT_Y_SIZE     566  /* Maximum number of dots in a page */
#define XPLUS          001  /* Right */
#define XMINUS         002  /* Left  */
#define YPLUS          004  /* Up    */
#define YMINUS           8  /* Down  */
#define IOERROR         -1  /* File Input & output operation error */
#define COLUMN_WIDTH     6  /* Number of x pixels per character */
#define ROW_HEIGHT       8  /* Number of y pixels per character */
#define XPIXEL_PERCM    23.6  /* Number of x pixels per centimeter */
#define YPIXEL_PERCM    28.3  /* Number of y pixels per centimeter */
#define HORIZONTAL       0  /* Character in horizontal direction */
#define VERTICAL         1  /* Character in vertical direction */
#define BLANK          ' '  /* Blank character */
#define BIT_ON           1  /* To set the printer buffer's bit on */
#define BIT_OFF          0  /* To set the printer buffer's bit off */
#define NUM_PRT_MODE     2  /* Number of printer mode required */
#define PI            3.141592654
#define FAC_X         1.0 /*0.70 */ /* Factor to scale x coordinate */
#define FAC_Y         1.0 /*0.86*/  /* Factor to scale y coordinate */
#define DOT_LINE         6  /* Spacing between the dot line */
#define DASH_LINE        3  /* Spacing between the dash line */
#define SOLID_LINE       1  /* Spacing between the solid line */
#define round(i)    ((int)((i>0) ? (i+0.5) : (i-0.5)))

#ifdef VAXHOST
#  define COMMAND "print/nonotify/nofeed/queue=VSA$PRTQ "
#  define OUTPUT  "fgu_hcpy.out"
#endif
#ifdef UNIXHOST
#  ifdef IBM_RT
#     define COMMAND "qprt -dp -l 0 -q"
#  else
#     define COMMAND "lp "
#  endif
#endif

#define FILE_EXT      ".plo"
#define DIR_LENGTH    100
#define TESTID_LENGTH 12
#define EXT_LENGTH    4

/***********************************/
/* current vt240's cursor position */
/***********************************/

/*static int cur_x_pos;*/
/*static int cur_y_pos;*/
int cur_x_pos;
int cur_y_pos;

/**********************/
/* Printer linestyle */
/*********************/

Linestyle ln_style;
  
/********************/
/* output file name */
/********************/
  
static char *outfname;

/*********************/
/* character masking */
/*********************/

char mask[6] = { 0x01,0x02,0x04,0x08,0x10,0x20 };

/*****************************************************/
/* printer's buffer, the extra two columns are for   */
/* the PRINTRONIX's plot mode and line feed commands */
/*****************************************************/

char plotln[PRT_Y_SIZE][PRT_X_SIZE + NUM_PRT_MODE];

/***********************/
/* Output file pointer */
/***********************/

/*static char name[DIR_LENGTH+TESTID_LENGTH+EXT_LENGTH];*/
/*static FILE *prtout;*/
char name[DIR_LENGTH+TESTID_LENGTH+EXT_LENGTH];
FILE *prtout;

/**************************************************/
/* scaling factor for vt240 to printronix printer */
/**************************************************/

static Scale virs_prt,
             prt_virs;

static Window window_prt; /* = { {0,0}, {660,599} }; */

static Boolean first_pass = TRUE;

Pt orientw();
Pt scale_prt_coord();

/****************************************************/
/* global variables used for a patch in prt_draw_ln */
/****************************************************/

static int pixel_skip = 0;
static int max_pixel_skip = 0;

/***********************************************************/
/* prtinit is used to initialize the static scaling factor */
/* virs_prt, the printer's dimension and the printer buffer*/
/* to blank.                                               */
/***********************************************************/

prtinit( printronix, filename )
Window *printronix;
char *filename;
{
   extern Window virtual_screen;
   int tempfile;
   static char buffer[256];

   strcpy(name, "\0");
   strcat (name, filename);
   strcat (name, FILE_EXT);

   /* UNIX I/O function is used here to create a */
   /* new version of the ?.plo file. */

   if ( (tempfile = creat( name, 0 )) == IOERROR )  {
      perror( name );
   }

/*   if ( (prtout =  fopen(name, "+r") ) == (void *) IOERROR ) {*/
#ifdef UNIXHOST
   if ( (prtout =  fopen((char *)tempfile, "a") ) == (void *) IOERROR ) {
#endif
#ifdef VAXHOST
   if ( (prtout =  fdopen(tempfile, "a") ) == (void *) IOERROR ) {
#endif
      perror( name );
   }

   /* To get the file specification of prtout */

#ifdef VAXHOST
   outfname = (void *) fgetname( prtout, buffer );
#endif

   /* calculate scaling factor */

   window_prt = *printronix;

   virs_prt = scalefind( &virtual_screen, printronix );
   prt_virs = scalefind( printronix, &virtual_screen );

   prt_initialized_buffer();
   first_pass = 1;
}

/***********************************************************/
/* initialize the printer buffer to blank.  Note that the  */
/* character is stored internally from bit 0 to bit 7.  Bit*/
/* 0 is the right most bit.  Bit 6 must be initialized to  */
/* 1 to indicate plot mode.                                */
/***********************************************************/

prt_initialized_buffer()
{
   int row, column;
   char plot_mode = 0x05;
   char line_feed = 0x0A;

   for( row=0; row<PRT_Y_SIZE; row++ ) {
      for( column=0; column<PRT_X_SIZE; column++ ) {
         plotln[row][column] = 0X40;
      }
      plotln[row][column] = plot_mode;
      plotln[row][++column] = line_feed;
   }
}

/**********************************************************/
/* prtcharstr is for displaying text on the screen.       */
/* There are only two ways to display the string.  0      */
/* direction represents horizontally, vertical otherwise. */
/**********************************************************/

prtcharstr( text )
Stringdef *text;
{
   int i, x, y, dir;
   char *p;

   p = text->cstring;
   dir = (text->rotangle==0) ? 0 : 1;

   cur_y_pos += ROW_HEIGHT;
   cur_x_pos += 1;

   while( *p ) {
      if ((*p)>=32 && (*p)<=127 ) {
         plotchar( *p, dir );
      }
      if ( dir == HORIZONTAL )
         cur_x_pos += COLUMN_WIDTH;
      else
         cur_y_pos -= ROW_HEIGHT;
      p++;
   }
}

/********************************************************/
/* Only the plot mode is used.  Therefore the charater  */
/* is stored as a 6x8 dot matrix in the array of ascii  */
/* code. This routine looks  for the location of the    */
/* character in the array and set corresponding bit to  */
/* 1.                                                   */
/********************************************************/

plotchar( c, dir )
char c;
int dir;
{
   int i,j,k,index;
   char *pwd, *p[6];
   int is_bit_on();

   index = c - 32;
   pwd = (char *)&word[index][0];

   for( i=0, j=0, k=0; i<3; i++ )  {
      p[j++] = pwd+k+1;
      p[j++] = pwd+k;
      k=j;
   }

   for( i=0; i<COLUMN_WIDTH; i++ )  {
      for( j=0; j> (-ROW_HEIGHT); j-- ) {
         if( is_bit_on( *(p[i]), 7+j ) )
            setbit( cur_x_pos + ( (dir) ? j:i),
                    cur_y_pos + ( (dir) ? (-i):j), BIT_ON);
         else
            setbit( cur_x_pos + ( (dir) ? j:i),
                    cur_y_pos + ( (dir) ? (-i):j), BIT_OFF );
      }
   }
}

/*********************************************/
/* This routine returns the bit value of the */
/* sepecified location of the charater byte  */
/*********************************************/

is_bit_on( c, bit_pos )
char c;
int bit_pos;
{
   return( (int)((c>>bit_pos) & 0x01) );
}

/**************************************************/
/* Draw a circle by setting the corresponding bit */
/* location to 1.                                 */
/**************************************************/

prtcircabs( xypt, rad )
Pt *xypt;
float rad;
{
   float i;
   float x1,y1;
   Pt temp = scale( &virs_prt, xypt );
   Pt temp1 = orientw( &temp, &window_prt );

   temp1 = scale_prt_coord( &temp1 );

   for( i=0; i<=360; i += 1.0 )  {
      x1 = (cur_x_pos + rad*cos(i*PI/180.0));
      y1 = (cur_y_pos + rad*sin(i*PI/180.0));
      setbit( round( x1), round( y1 ), BIT_ON );
   }
}

/*********************************************************/
/* A new page is created so the printer buffer is stored */
/* and is clear for new data later                       */
/*********************************************************/

prtclear()
{
   char form_feed = 0X0C;

   /* To avoid a blank page at the beginning of the file */

   if ( first_pass ) {
      first_pass = FALSE;
      return;
   }

   fwrite( plotln[0], (PRT_X_SIZE+2)*sizeof( char ),
           PRT_Y_SIZE, prtout);

   fputc( form_feed, prtout );

   prt_initialized_buffer();
}

/***********************************************************/
/* This routine is used for clearing an area on the screen */
/***********************************************************/

/*
prtclstring( cls )
Cls *cls;
{
   Stringdef text;
   int i;

   text.cstring = ( char * )calloc( 1, (PRT_X_SIZE+1)*sizeof(char) );

   for ( i=0; i<(cls->max); i++ ) {
      *(text.cstring+i) = BLANK;
   }
   *(text.cstring+i) = '\0';

   text.rotangle = 0;
   prt_grmovabs( &(cls->pt) );
   prtcharstr( &text );
}*/

/*******************************************************************/
/* This routine clears a rectangular portion of the printer buffer */
/*******************************************************************/

prtclregion( xypt )
Pts *xypt;
{
   int row, col;
   Pt temp = scale( &virs_prt, xypt->xy1 );
   Pt lower_corner = orientw( &temp, &window_prt );
   Pt temp1 = scale( &virs_prt, xypt->xy2 );
   Pt upper_corner = orientw( &temp1, &window_prt );

   lower_corner = scale_prt_coord( &lower_corner );
   upper_corner = scale_prt_coord( &upper_corner );

   for ( row=lower_corner.y; row<=upper_corner.y; row++ ) {
      for ( col=lower_corner.x; col<=upper_corner.x; col++ ) {
         setbit( col, row, BIT_OFF );
      }
   }
}


/********************************************************/
/* This routine puts the PRINTRONIX printer into a mode */
/* such that the lines drawn are dashed.                */
/********************************************************/

prtdashon()
{
   ln_style = DASH;
}

/*******************************/
/* set the printer mode to dot */
/*******************************/

prtdoton()
{
   ln_style = DOT;
}

/****************************************************/
/* A line is drawn from the current position to the */
/* input position                                   */
/****************************************************/

prtdraw2i( xypt )
Pt *xypt;
{
   Pt temp = scale( &virs_prt, xypt );
   Pt temp1 = orientw( &temp, &window_prt );

   temp1 = scale_prt_coord( &temp1 );

   prt_draw_ln( round(temp1.x), round(temp1.y) );
}

/*********************************************************/
/* Same as above but update the current x and y position */
/*********************************************************/

prtdrawchek2i( xypt )
Pt *xypt;
{
   Pt temp = scale( &virs_prt, xypt );
   Pt temp1 = orientw( &temp, &window_prt );

   temp1 = scale_prt_coord( &temp1 );

   prt_draw_ln( round(temp1.x), round(temp1.y) );
/*   cur_x_pos = temp1.x;
   cur_y_pos = temp1.y; */
}


/*******************************************/
/* Same as above but draw the line in dash */
/*******************************************/

prtdrawdasha( xypt )
Pt *xypt;
{
   prtdashon();
   prtdraw2i( xypt );
   prtsolidon();
}

/***************************************************/
/* Draw a dashed line from the current position to */
/* a distance specified by the input coordinate    */
/***************************************************/

prtdrawdashr( xypt )
Pt *xypt;
{
   prtdashon();
   prtrdr2i( xypt );
   prtsolidon();
}

/**************************************************************/
/* same as prtdrawdasha excepts the line is drawn in dot mode */
/**************************************************************/

prtdrawdota( xypt )
Pt *xypt;
{
   prtdoton();
   prtdraw2i( xypt );
   prtsolidon();
}

/****************************************************************/
/* draw a dotted line to relative coordinates specified by the  */
/* input coordinates.                                           */
/****************************************************************/

prtdrawdotr( xypt )
Pt *xypt;
{
   prtdoton();
   prtrdr2i( xypt );
   prtsolidon();
}

/********************************************************/
/* Write the printer buffer to the specific output file */
/********************************************************/

prt_send_out()
{
   int return_code, i;
   char command[150],output[150],prtnam[150];

#ifdef VAXHOST
   int LIB$SPAWN();
   struct dsc$descriptor_s  desc_command;
   struct dsc$descriptor_s  desc_output;

   strcpy(command,COMMAND);
   strcat(command,outfname);
   strcpy(output,OUTPUT);
#endif

#ifdef UNIXHOST
extern void rev_curr_new(char *filename);

   char newname[100],
        *lognam = "vsa_prtq";
   int  status,
        mode = 1,
        deflg = 4,
        err,
        return_len,
        level=0;
   int  force = FALSE;
#endif

/* save the last printer buffer, i.e. the last screen */

   for (i = 0; i < PRT_X_SIZE; plotln [PRT_Y_SIZE - 1] [i++] = 0x20)
      ;
   plotln [PRT_Y_SIZE - 1] [PRT_X_SIZE] = 0x0A;
   plotln [PRT_Y_SIZE - 1] [PRT_X_SIZE + 1] = 0x0D;

   fwrite( plotln[0], (PRT_X_SIZE+2)*sizeof( char ),
           PRT_Y_SIZE, prtout );

   fclose( prtout );

#  ifdef VAXHOST
   /* set up command string for LIB$SPAWN */
   desc_command.dsc$w_length = strlen( command );
   desc_command.dsc$b_dtype = DSC$K_DTYPE_T;
   desc_command.dsc$b_class = DSC$K_CLASS_S;
   desc_command.dsc$a_pointer = command;

   desc_output.dsc$w_length = strlen( output );
   desc_output.dsc$b_dtype = DSC$K_DTYPE_T;
   desc_output.dsc$b_class = DSC$K_CLASS_S;
   desc_output.dsc$a_pointer = output;

   return_code = LIB$SPAWN( &desc_command,0,&desc_output );
#  endif

#  ifdef UNIXHOST
/*
    err = cae_trnl(lognam, &return_len, prtnam, &level, strlen(lognam),
                    sizeof(prtnam)-1);
*/
    err = -1;

    if (err != 1)
       strcpy(prtnam, '\0');
    else
       prtnam [return_len] =0;

    /* set up command string for system call */
    strcpy (command, prtnam);
    strcat (command, " ");

    strcpy (newname,name);
    rev_curr_new (newname);

    strcat (command, newname);

    system (command);
#endif
}
  
/****************************************************/
/* set the current position to the input coordinate */
/****************************************************/
  
prtmove2i( xypt )
Pt *xypt;
{
   Pt temp = scale( &virs_prt, xypt );
   Pt temp1 = orientw( &temp, &window_prt );
  
   temp1 = scale_prt_coord( &temp1 );
  
   cur_x_pos = round( temp1.x);
   cur_y_pos = round( temp1.y);
}

/*************************************************/
/* This routine resets the current position in a */
/* distance relative to the previous position    */
/*************************************************/

prtrmv2i( xypt )
Pt *xypt;
{
   Pt temp = scalerel( &virs_prt, xypt );

   temp = scale_prt_coord( &temp );

   cur_x_pos += round(temp.x);
   cur_y_pos -= round(temp.y);
}

/***********************************************************/
/* This routine is to perform a relative draw.  The y's    */
/* position is inverted.                                   */
/***********************************************************/

prtrdr2i( xypt )
Pt *xypt;
{
   Pt temp = scalerel( &virs_prt, xypt );

   temp = scale_prt_coord( &temp );

   temp.x += cur_x_pos;
   temp.y =  cur_y_pos - temp.y;

   prt_draw_ln( round(temp.x), round(temp.y) );
}

/****************************************************/
/* This routine sets the printer to solid line mode */
/****************************************************/

prtsolidon()
{
   ln_style = SOLID;
}

/************************************************************/
/* This routine draw a line from the current postion to the */
/* input coordinate.                                        */
/************************************************************/

prt_draw_ln( xpos, ypos )
int xpos, ypos;
{
   int dx, dy, error, inc = 1;
   int xamount, yamount;
   char xmotion, ymotion, operation;
   Boolean hv_line = FALSE;


   if ( (xpos == cur_x_pos) ||
        (ypos == cur_y_pos) )
      hv_line = TRUE;

   if ( (dx = (xpos - cur_x_pos )) < 0 ) {
      dx = -dx;
      xmotion = XMINUS;
   } else {
      xmotion = XPLUS;
   }

   if ( (dy = (ypos - cur_y_pos )) < 0 ) {
      dy = -dy;
      ymotion = YMINUS;
   } else {
      ymotion = YPLUS;
   }

   if ( dy > dx )   {    /* flip x and y actions */
      error = dx;
      dx = dy;
      dy = error;
      operation = xmotion;
      xmotion = ymotion;
      ymotion = operation;
   }

   if (pixel_skip == max_pixel_skip) {
      setbit( cur_x_pos, cur_y_pos, BIT_ON );
      pixel_skip = (pixel_skip + 1) % (max_pixel_skip+1);
   }

   if ( hv_line )  {    /* draw horizontal or vertical line */
      while( dx > 0)  {
         if ( xmotion & XPLUS )  cur_x_pos = (cur_x_pos + inc <= xpos) ?
                                              cur_x_pos + inc : xpos;
         if ( xmotion & XMINUS ) cur_x_pos = (cur_x_pos - inc >= xpos) ?
                                              cur_x_pos - inc : xpos;
         if ( xmotion & YPLUS )  cur_y_pos = (cur_y_pos + inc <= ypos) ?
                                              cur_y_pos + inc : ypos;
         if ( xmotion & YMINUS ) cur_y_pos = (cur_y_pos - inc >= ypos) ?
                                              cur_y_pos - inc : ypos;
         if (pixel_skip == max_pixel_skip)
            setbit( cur_x_pos, cur_y_pos, BIT_ON );
         pixel_skip = (pixel_skip + 1) % (max_pixel_skip+1);
         dx -= inc;
      }
   } else {   /* any other type of line */
      error = dx >> 1;
      yamount = dy;
      xamount = dx;
      while( dx > 0 && dx >= inc )  {
         operation = xmotion;
         if ( (error -= yamount) <= 0 )  {
            error += xamount;
            operation |= ymotion;
            dy -= inc;
         }
         if ( operation & XPLUS )  cur_x_pos = (cur_x_pos + inc <= xpos) ?
                                                cur_x_pos + inc : xpos;
         if ( operation & XMINUS ) cur_x_pos = (cur_x_pos - inc >= xpos) ?
                                                cur_x_pos - inc : xpos;
         if ( operation & YPLUS )  cur_y_pos = (cur_y_pos + inc <= ypos) ?
                                                cur_y_pos + inc : ypos;
         if ( operation & YMINUS ) cur_y_pos = (cur_y_pos - inc >= ypos) ?
                                                cur_y_pos - inc : ypos;
         if (pixel_skip == max_pixel_skip)
            setbit( cur_x_pos, cur_y_pos, BIT_ON );
         pixel_skip = (pixel_skip + 1) % (max_pixel_skip+1);
         dx -= inc;
      }
   }

}

/*************************************************/
/* This routine sets the location of the printer */
/* buffer to TRUE                                */
/*************************************************/

setbit( x, y, bit_on )
int x, y;
int bit_on;
{
   int byte, bit;

   byte = (x/COLUMN_WIDTH);
   bit  = x%COLUMN_WIDTH;

   if ( (byte<0) || (byte>=PRT_X_SIZE) ||
        (y<0) || (y>=PRT_Y_SIZE) )  {
      return;
   }

   if ( bit_on )
      plotln[y][byte] |= mask[bit];
   else
      plotln[y][byte] &= ~mask[bit];
}

/**************************************************/
/* To scale the x and y in printer's coordinates. */
/**************************************************/

Pt scale_prt_coord( pt )
Pt *pt;
{
   Pt temp;

   temp.x = pt->x * FAC_X;
   temp.y = pt->y * FAC_Y;

   return temp;
}


/************************************************************/
/* Returns the number of virtual coordinates per centimeter */
/************************************************************/

prtcmsize (xypt)
Pt *xypt;
{
   Pt scale ();

   xypt -> x = XPIXEL_PERCM;
   xypt -> y = YPIXEL_PERCM;
   *xypt = scale (&prt_virs, xypt);
}


/******************************************************************/
/* Returns width and height of a character in virtual coordinates */
/******************************************************************/

prtfontset (xy)
Pts *xy;
{
   xy -> xy1.x = (Float) (COLUMN_WIDTH * prt_virs.a);
   xy -> xy1.y = (Float) (ROW_HEIGHT   * prt_virs.e);
   xy -> xy2.x = (Float) (ROW_HEIGHT   * prt_virs.a);
   xy -> xy2.y = (Float) (COLUMN_WIDTH * prt_virs.e);
}


/******************/
/* Draws a border */
/******************/

prt_border ()

{
   Pt init_position,
      end_position;

   prtsolidon ();

   init_position  = window_prt.min;
   end_position.x = window_prt.min.x;
   end_position.y = window_prt.max.y;
   prt_line_border (init_position, end_position);

   ++init_position.x;
   ++end_position.x;
   prt_line_border (init_position, end_position);

   init_position.x =
   end_position.x  = window_prt.max.x;
   prt_line_border (init_position, end_position);

   --init_position.x;
   --end_position.x;
   prt_line_border (init_position, end_position);

   init_position  = window_prt.min;
   end_position.x = window_prt.max.x;
   end_position.y = window_prt.min.y;
   prt_line_border (init_position, end_position);

   ++init_position.y;
   ++end_position.y;
   prt_line_border (init_position, end_position);

   init_position.y =
   end_position.y  = window_prt.max.y;
   prt_line_border (init_position, end_position);

   --init_position.y;
   --end_position.y;
   prt_line_border (init_position, end_position);
}


prt_line_border (init, end)

Pt init,
   end;

{
   cur_x_pos = round (init.x);
   cur_y_pos = round (init.y);
   prt_draw_ln (round (end.x), round (end.y));
}


prt_set_pixel_skip ()
{
   switch (ln_style) {
      case DOT:
         pixel_skip = DOT_LINE - 1;
         break;
      case DASH:
         pixel_skip = DASH_LINE - 1;
         break;
      case SOLID:
         pixel_skip = SOLID_LINE - 1;
         break;
      default:
         pixel_skip = SOLID_LINE - 1;
   }
   max_pixel_skip = pixel_skip;
}


prt_reset_pixel_skip ()
{
   max_pixel_skip =
   pixel_skip     = 0;
}
