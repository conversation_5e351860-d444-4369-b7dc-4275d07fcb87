#define UP_ARROW_CODE       "\033[A"
#define UP_ARROW_VAL        274
#define DOWN_ARROW_CODE     "\033[B"
#define DOWN_ARROW_VAL      275
#define LEFT_ARROW_CODE     "\033[D"
#define LEFT_ARROW_VAL      276
#define RIGHT_ARROW_CODE    "\033[C"
#define RIGHT_ARROW_VAL     277
#define DO_CODE             "\033[29~"
#define DO_VAL              296
#define FIND_CODE           "\033[1~"
#define FIND_VAL            311
#define INSERT_CODE         "\033[2~"
#define INSERT_VAL          312
#define REMOVE_CODE         "\033[3~"
#define REMOVE_VAL          313
#define SELECT_CODE         "\033[4~"
#define SELECT_VAL          314
#define PREV_CODE           "\033[5~"
#define PREV_VAL            315
#define NEXT_CODE           "\033[6~"
#define NEXT_VAL            316
#define PF1_CODE            "\033OP"
#define PF1_VAL             256
#define PF2_CODE            "\033OQ"
#define PF2_VAL             257
#define PF3_CODE            "\033OR"
#define PF3_VAL             258
#define PF4_CODE            "\033OS"
#define PF4_VAL             259
