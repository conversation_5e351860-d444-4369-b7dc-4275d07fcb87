
/******************************** readreal() *******************************/
int readreal(string, result)
#ifdef VAXHOST
   struct dsc$descriptor_s *string;
#endif
#ifdef UNIXHOST
   char *string;
#endif
float result;
{
#ifdef VAXHOST
   int i;
   char c_string[80]={"\0"};

   /* Place string pointer value into 'c_string' array */
   for (i=0; i < string->dsc$w_length; i++)
      c_string[i] = string->dsc$a_pointer[i];
   if (sscanf(c_string,"%f",result))
      return(1);
   else
      return(0);
#endif
#ifdef UNIXHOST
   if (sscanf(string,"%f",result))
      return(1);
   else
      return(0);
#endif
}
