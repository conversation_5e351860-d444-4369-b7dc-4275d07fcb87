typedef enum { SOLID, DOT, DASH } Linestyle;

static short word[96][3] = {
                0x0000,0x0000,0x0000,    /* space */
                0x0000,0x5F00,0x0000,    /* ! */   
                0x0007,0x0007,0x0000,    /* " */
                0x147F,0x147F,0x1400,    /* # */
                0x242A,0x7F2A,0x1200,    /* $ */
                0x6313,0x0864,0x6300,    /* % */
                0x304E,0x5926,0x5000,    /* & */
                0x0000,0x0403,0x0000,    /* ' */
                0x1C22,0x4100,0x0000,    /* ( */
                0x0000,0x4122,0x1C00,    /* ) */
                0x1408,0x3E08,0x1400,    /* * */
                0x0808,0x3E08,0x0800,    /* + */
                0x0000,0x8060,0x0000,    /* ' */
                0x0808,0x0808,0x0800,    /* - */
                0x0000,0x4000,0x0000,    /* . */
                0x6010,0x0804,0x0300,    /* / */
                0x3E51,0x4945,0x3E00,    /* 0 */
                0x0042,0x7F40,0x0000,    /* 1 */
                0x6251,0x4949,0x4600,    /* 2 */
                0x2141,0x494D,0x3300,    /* 3 */
                0x1814,0x127F,0x1000,    /* 4 */
                0x2745,0x4545,0x3900,    /* 5 */
                0x3C4A,0x4949,0x3100,    /* 6 */
                0x0171,0x0905,0x0300,    /* 7 */
                0x3649,0x4949,0x3600,    /* 8 */
                0x4649,0x4929,0x1E00,    /* 9 */
                0x0000,0x6600,0x0000,    /* : */
                0x0040,0x3200,0x0000,    /* ; */
                0x0814,0x2241,0x0000,    /* < */
                0x0014,0x1414,0x1400,    /* = */
                0x0041,0x2214,0x0800,    /* > */
                0x0201,0x5109,0x0600,    /* ? */
                0x3249,0x5121,0x5E00,    /* @ */
                0x7C12,0x1112,0x7C00,    /* A */
                0x7F49,0x4949,0x3600,    /* B */
                0x3E41,0x4141,0x2200,    /* C */
                0x417F,0x4141,0x3E00,    /* D */
                0x7F49,0x4949,0x4100,    /* E */
                0x7F09,0x0901,0x0100,    /* F */
                0x3E41,0x4151,0x7200,    /* G */
                0x7F08,0x0808,0x7F00,    /* H */
                0x0041,0x7F41,0x0000,    /* I */
                0x2040,0x4040,0x3F00,    /* J */
                0x7F08,0x1422,0x4100,    /* K */
                0x7F40,0x4040,0x4000,    /* L */
                0x7F02,0x0C02,0x7F00,    /* M */
                0x7F04,0x0810,0x7F00,    /* N */
                0x3E41,0x4141,0x3E00,    /* O */
                0x7F09,0x0909,0x0600,    /* P */
                0x3E41,0x5121,0x5E00,    /* Q */
                0x7F09,0x1929,0x4600,    /* R */
                0x2649,0x4949,0x3200,    /* S */
                0x0101,0x7F01,0x0100,    /* T */
                0x3F40,0x4040,0x3F00,    /* U */
                0x0718,0x6018,0x0700,    /* V */
                0x7F20,0x1820,0x7F00,    /* W */
                0x6314,0x0814,0x6300,    /* X */
                0x0304,0x7804,0x0300,    /* Y */
                0x6151,0x4945,0x4300,    /* Z */
                0x007F,0x4141,0x0000,    /* [ */
                0x0204,0x0810,0x0200,    /*   */
                0x0041,0x417F,0x0000,    /* ] */
                0x0402,0x0102,0x0400,    /* ^ */
                0x4040,0x4040,0x4000,    /*   */
                0x0002,0x0408,0x0000,    /*   */
                0x2054,0x5454,0x2800,    /* a */
                0x7B44,0x4444,0x3800,    /* b */
                0x3844,0x4444,0x0800,    /* c */
                0x3844,0x4444,0x7B00,    /* d */
                0x3854,0x5454,0x1800,    /* e */
                0x047E,0x0501,0x0200,    /* f */
                0x4854,0x5454,0x6A00,    /* g */
                0x7B04,0x0404,0x7800,    /* h */
                0x447D,0x4000,0x0000,    /* i */
                0x2040,0x4040,0x3D00,    /* j */
                0x7F10,0x2844,0x0000,    /* k */
                0x417F,0x4000,0x0000,    /* l */
                0x7C04,0x7804,0x7C00,    /* m */
                0x7C04,0x0478,0x0000,    /* n */
                0x3844,0x4444,0x3800,    /* o */
                0x6C14,0x1414,0x0800,    /* p */
                0x3844,0x5424,0x5800,    /* q */
                0x0478,0x0404,0x0800,    /* r */
                0x0854,0x5454,0x2000,    /* s */
                0x043F,0x4440,0x2000,    /* t */
                0x3C40,0x407C,0x0000,    /* u */
                0x0C30,0x4030,0x0C00,    /* v */
                0x7C20,0x1020,0x7C00,    /* w */
                0x4428,0x1028,0x4400,    /* x */
                0x0C50,0x5050,0x2C00,    /* y */
                0x4464,0x544C,0x4400,    /* z */
                0x0808,0x3641,0x4100,    /* { */
                0x0000,0x7700,0x0000,    /* | */
                0x4141,0x3608,0x0000,    /* } */
                0x0601,0x0206,0x0100,    /* ~ */
                0x7F7F,0x7F7F,0x7F00     /* block */ 
           };
