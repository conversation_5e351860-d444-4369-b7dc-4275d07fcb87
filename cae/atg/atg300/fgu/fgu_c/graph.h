/************************************************************************/
/*									*/
/*			INCLUDE FILE GRAPH				*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/* graph.h is the include library file for global declarations.		*/
/************************************************************************/

/*********************************************************************/
/******* Necessary Data Structures for interface with graphics.  *****/
/*********************************************************************/

typedef struct hls_tag {
   int h,l,s;
} Hls;

typedef struct rgb_tag {
   int r,g,b;
} Rgb;

typedef struct toler_tag {
   double plus;
   double minus;
} Toler;

typedef struct colormap_tag {
   int num;
   char colvalue;
} Colormap;

typedef struct hlsmap_tag {
   int num,h,l,s;
} Hlsmap;

typedef struct rgbmap_tag {
   int num,r,g,b;
} Rgbmap;

typedef struct cls_struct_tag {
   Pt  pt;
   int max;
} Cls;

typedef struct tolerance_tag {
   Toler absolute;
   Toler percentage;
   Pt *point;
   int numpts;
   int band_color;
   int color_toggle;
   short int band_on;
   short int color_toggle_on;
} Tolerance;

typedef struct curve_tag {
   Pt *xypt;
   int stop,color;
   Tolerance *tolerance;
} Grcurve;

typedef struct grid_tag {
   Pts limits;
   short int ud,tick;
   double spacing;
} Grid;

typedef struct label_tag {
   Pt   pt;
   int  rot;
   char *charstring;
} Label;

typedef struct xlabel_tag {
   Pt  pt,
   limits;
   double length;
   int col,ud,rfield;
} Xlabel;

typedef struct circle_tag {
   Pt xy;
   double rad;
} Circle;

typedef struct stringdef_tag {
   char *cstring;
   int rotangle;
} Stringdef;

typedef struct gscreen_tag {
   char *titlename;
   int titlecolor;
   Pts axis;
   int axiscolor;
   int labelcolor;
   char *xlabel,*xlabelunits;
   char *ylabel,*ylabelunits;
   Pts spacing;
   short int xgrid,ygrid;
   int gridcolor;
} Gscreen;

typedef struct tol_min_max_tag {
   short int flagabs,flagper;
   Toler pertol,abstol;
   double value;
} Tol_min_max;
