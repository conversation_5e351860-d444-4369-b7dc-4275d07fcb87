/************************************************************************/
/*									*/
/*                           INCLUDE FILE OVP_DATASTRUCT 		*/
/* Author: <PERSON><PERSON><PERSON>lette     		     	  			*/
/* Date  : 01.87							*/
/*	      	 							*/
/*  Contains the definition of the data structures used to keep the	*/
/*  informations loaded from the master and cts binary files.		*/
/*                                                                      */
/* Revision History:                                                    */
/* o January 1990    Nick <PERSON>to                                       */
/* - Changed Field lengths to allow McDonnell Douglas data entry        */
/************************************************************************/
 
			/* Maximum length of character string field	*/
#define PROJECT_LEN     30   
#define ORIGIN_LEN      25   
#define AIRCRAFT_LEN    10 
#define TITLE_LEN       50     
#define REFNO_LEN       10  /* was 5 */
#define DATE_LEN        11
#define REVISION_LEN    10
#define NAME_LEN        40     
#define LABEL_LEN       25    
#define PAGE_LEN        12  /* was 8 */       
#define FIGURE_LEN       8    
#define DOC_LEN         15  /* was 10 */
#define UNITS_LEN       10
#define ORIENTATION_LEN  1


#define MAX_PAGE        26
#define MAX_MASTER      26

#define INIT_COND_LENGTH 42
#define MAX_INIT_COND    40  /* was 20 */


struct ovp_header_struct
   {
      char project     [PROJECT_LEN  + 1];
      char origin      [ORIGIN_LEN   + 1];
      char aircraft    [AIRCRAFT_LEN + 1];
      char title       [TITLE_LEN    + 1];
      char subtitle    [TITLE_LEN    + 1];
      char refno       [REFNO_LEN    + 1];
      char date        [DATE_LEN     + 1];
      char revision    [REVISION_LEN + 1];
      char figure      [FIGURE_LEN   + 1];
      char document    [DOC_LEN      + 1];
      char xname       [NAME_LEN     + 1];
      char yname       [NAME_LEN     + 1];
      char xlabel      [LABEL_LEN    + 1];
      char ylabel      [LABEL_LEN    + 1];
      char xunits      [UNITS_LEN    + 1];
      char yunits      [UNITS_LEN    + 1];
   };

typedef struct ovp_header_struct Ovp_Header;



struct ovp_context_struct
   {
      Gscreen   graph;
      Window    world_location;
      Window    graph_location;
      Pt        graph_size;
      Pt        graph_position_in_cm;
   };

typedef struct ovp_context_struct Ovp_Context;



struct ovp_points_struct
   {
      Pt        *point;
      int       num_pts;
      int       colour;
      Float     smplrt;
      Tolerance *tolerance;
      enum tolerance_code_value tolerance_code;
   };

typedef struct ovp_points_struct Ovp_Points;



struct master_struct
   {
      Ovp_Header    *header;
      Ovp_Points    *points;
      Ovp_Points    *cts_curve;
      Ovp_Context   *context;
   };

typedef struct master_struct Master;




struct page_struct
   {
      char      page_number [PAGE_LEN        + 1];
      Pt        page_size;
      char      orientation [ORIENTATION_LEN + 1];
      Window    virtual_coord;
      Master    **master;
      int       nb_parameters;
      int       terminal_page_number;
      Boolean   page_ready;
   };

typedef struct page_struct Page;

