# include <termio.h>
# define TRUE 	1
# define FALSE	0

#if defined(_IBMR2)
#define getkey_ getkey
#define restore_ restore
#endif

static struct termio tbufsave;
static short first = TRUE;
static char rvlstr[] = "$Revision: getkey V1.0 04/01/90 $";

char getkey_(arg,larg)
char *arg;
int larg;
{
	static char buf[10],cha[2];
	static int next,total;
	struct termio tbuf;

	if (first){
	      	first = FALSE;
	      	if (ioctl(0,TCGETA,&tbuf) == -1)
	      		printf("\n ioctl error (GET)");
	      	tbufsave = tbuf;
		tbuf.c_lflag  &= ~(ICANON |ECHO);
		tbuf.c_cc[4] = sizeof(buf); /*min */
		tbuf.c_cc[5] = 2; /*time */

	      	if (ioctl(0,TCSETAF,&tbuf) == -1)
	      		printf("\n ioctl error (SET)");
		}
	if (next >= total)
	      	switch(total=read(0,buf,sizeof(buf))){
	      	case -1:
	      		printf("\n read = -1");
	      	case 0:
	      		printf("\n Mysterious EOF read = 0");
	      	default:
	      		next = 0;
	      	}
	strncpy(&cha[0],&buf[next++],1);
	if(cha[0] == '\n') strcpy(&cha[0],"\r");
	strcpy(arg,&cha[0]);
}

void restore_()
{
	first = TRUE;
	if (ioctl(0,TCSETAF,&tbufsave) == -1)
		printf("\n ioctl error (RESTORE)");
}
