/************************************************************************/
/*									*/
/*			INCLUDE FILE SCALE				*/
/*									*/
/* Author : <PERSON>						*/
/* Date :   June 1990							*/
/*									*/
/*   Structure use to store the scaling factor                  	*/
/************************************************************************/

struct scaling_struct
   	{ 
/* x */
   	Float a;
   	Float b;
   	Float c;
/* y */
   	Float d;
   	Float e;
   	Float f;
 	};

typedef struct scaling_struct Scale;

                            /* Functions declaration */
Pt orient();

/* Scale X or Y macro                              */

#define SCALX(FAC,X,Y)          (X*FAC.a+Y*FAC.b+FAC.c)
#define SCALY(FAC,X,Y)          (X*FAC.d+Y*FAC.e+FAC.f)

#define PSCALX(FAC,X,Y)          (X*FAC->a+Y*FAC->b+FAC->c)
#define PSCALY(FAC,X,Y)          (X*FAC->d+Y*FAC->e+FAC->f)


/* Finds the angle of rotation                                     */

#define ROTATION(AA)   atan(AA.y/AA.x)

