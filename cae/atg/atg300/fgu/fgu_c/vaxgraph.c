#include <stdio.h>
#include <string.h>
#include "ovp.h"
#include "hlscolor.h"
#include "ovp_prec.h"

/* For a complete list of HLS colors refer to the 'VT330/VT340 Programmer */
/* Reference Manual' page 264. */

HLS_color Background, Foreground;

/* 'term_status' holds a number indicating the current status of the    */
/* terminals vidio attributes.  Continuously changing vidio attributes  */
/* slows down the terminal so it should be done as little as possible.  */
/* Functions such as 'printxy()' and 'attributes_on()' change the vidio */
/* attributes. */
short int term_status= -1;

void start_pixel(int x, int y);
void draw_line(int x, int y);
void draw_circle(int x, int y, int rad);
void graphics_on(void);
void graphics_off(void);
void hls_cls(void);
void set_colors(HLS_color *foreground, HLS_color *background,
                HLS_color *red, HLS_color *blue);
void change_color(HLS_color *foreground);
void set_font_size(char *size);
void set_line_pattern(char *pattern);
void pixel_printxy(int xpix,int ypix,char *string);
void vtgcurseon(void);
void vtgcurseoff(void);

/*********** main() is an example of the graphic functions in this module.
main(void)
{
  Pts line;
  char string[80]={"This is a test\0"};

  Background=HLS_GRAY_DARK_SLATE;
  Foreground=HLS_YELLOW_BRIGHT;

  graphics_on();
  set_colors(&Foreground, &Background, &HLS_RED, &HLS_BLUE);
  hls_cls();

  line.xy1.x=1;
  line.xy1.y=1;
  line.xy2.x=200;
  line.xy2.y=200;
  draw_line(&line);

  change_color(&HLS_RED);
  line.xy1.x=200;
  line.xy1.y=200;
  line.xy2.x=400;
  line.xy2.y=300;
  draw_line(&line);

  change_color(&HLS_BLUE);
  line.xy1.x=400;
  line.xy1.y=300;
  line.xy2.x=500;
  line.xy2.y=470;
  draw_line(&line);

  change_color(&HLS_GREEN);
  pixel_printxy(200,260,string);
  graphics_off();
}*/

/****************************** hls_cls() **************************************
     The hls_cls() function clears a graphic terminal (vt240 or vt340) in ReGIS
mode.  The screen is cleared using the current background color.

     Entering and exiting ReGIS mode is acomplished by calling the following
function:

     graphics_on() and graphics_off()
*/
void hls_cls(void)
{
/*  fprintf(stdout,"S(I0)\n");    Make background color permanent. */
  fprintf(stdout,"S(I0,E)\n"); /* Clear the screen with background color. */
}

/************************* set_colors() ************************************
     This function changes the foreground and background colors currently
on the screen and makes the changes permanent.  Note that this function
changes the colors of everything currently on the screen the instant it is
executed.
     Changing the foreground and background colors is acomplished by sending
the address of two variables defined as 'HLS_color'.  The two variables can
be set manually or by assigning them a predefined value listed in the include
file 'HLS_colors.h'.  Colors can only be set while in ReGIS mode.

     Entering and exiting ReGIS mode is acomplished by calling the following
function:

     graphics_on() and graphics_off()
*/
void set_colors(foreground, background, red, blue)
HLS_color *foreground, *background, *red, *blue;
{
/* Set background color of both VT240 & VT340. */
  fprintf(stdout,"S(M0(AH%dL%dS%d))\n",background->hue,
                                       background->light,
                                       background->shade);
/* Set color for RED. (vt240) */
  fprintf(stdout,"S(M1(AH%dL%dS%d))\n",red->hue,
                                       red->light,
                                       red->shade);
/* Set color for BLUE. (both vt240 & vt340) */
  fprintf(stdout,"S(M2(AH%dL%dS%d))\n",blue->hue,
                                       blue->light,
                                       blue->shade);
/* Set foreground color of VT240. */
  fprintf(stdout,"S(M3(AH%dL%dS%d))\n",foreground->hue,
                                       foreground->light,
                                       foreground->shade);
/* Set foreground color of VT340. */
  fprintf(stdout,"S(M7(AH%dL%dS%d))\n",foreground->hue,
                                       foreground->light,
                                       foreground->shade);

/*  fprintf(stdout,"S(I0)\n");   Make background color permanent.  */
/*  fprintf(stdout,"S(I1)\n");   Make color RED permanent. (vt240) */
/*  fprintf(stdout,"S(I2)\n");   Make color BLUE permanent.        */
/*  fprintf(stdout,"S(I3)\n");   Make forground color permanent (vt240). */
/*  fprintf(stdout,"S(I7)\n");   Make forground color permanent (vt340). */
}

/************************* change_color() **********************************
     This function truns the graphic screen ON and changes the foreground 
color currently in use.  The color attributes currently on the screen are not
changed.
     Changing the foreground color is acomplished by sending the address of
a variable defined as 'HLS_color'.
*/
void change_color(HLS_color *foreground)
{
/* Turn graphics on. */
  fprintf(stdout,"\033P0p"); /* Equal to: ESC P0p */
/* Set foreground color. */
  fprintf(stdout,"W(I(AH%dL%dS%d))\n",foreground->hue,
                                      foreground->light,
                                      foreground->shade);
}

/************************* graphics_on() ***********************************
     Places a graphic terminal (vt240 or vt340) into ReGIS mode.
*/
void graphics_on(void)
{
static short int first_pass=1;

  /* Issue code to activate graphics. */
   fprintf(stdout,"\033Pp");     /* Equal to: ESC Pp */
   if (first_pass) {
  /* Set the graphical font size to normal and set the line pattern to solid */
      fprintf(stdout,"W(P1)T(S1)");
      first_pass=0;
   }
}

/************************* graphics_off() **********************************
     Takes a graphic terminal (vt240 or vt340) out of ReGIS mode.
*/
void graphics_off(void)
{
  fprintf(stdout,"\033\\"); /* Equal to: ESC \ */
}

/*************************** start_pixel() ***********************************
     This function sets the relative starting point for a line being drawn.
A line is drawn using the 'draw_line()' function.
*/
void start_pixel(int x, int y)
{
   fprintf(stdout,"P[%d,%d]",x,479-y);
}

/*************************** draw_line() ***********************************
     This function draws a line from a relative starting point to the indicated
x,y pixel corrdinates.  To set the relative starting point for the line, the
function 'start_pixel()' should be used.

     The screen has the following pixel dimensions:

         x-axis (horizontal) 0-799
         y-axis (vertical)   0-479

     Corrdinate (0,0) is located at the lower left corner of the screen.
*/
void draw_line(int x, int y)
{
  fprintf(stdout,"V[%d,%d]",x,479-y);
}

/**************************** draw_circle() **********************************
*/
void draw_circle(int x, int y, int rad)
{
   fprintf(stdout,"P[%d,%d]",x,479-y);
   fprintf(stdout,"C[+%d]",rad);
}

/************************* set_line_pattern() ********************************
     Sets the current graphical line pattern to one of 10 patterns.  The
'pattern' string has the following format:  "P#"

     The letter 'P' activates the pattern option of the terminal.  The '#' 
sign is a number from 0 to 9.  The following patterns are available:

P0 = All-off write pattern (solid line in background color)
P1 = All-on write pattern  (solid line in foreground color)
P2 = Dash pattern (_ _ _ _ _)
P3 = Dash-dot pattern (_._._._._)
P4 = Dot pattern (........)
P5 = Dash-dot-dot pattern (_.._.._..)
P6 = Sparse dot pattern (. . . . . .)
P7 = Asymmetrical sparse dot pattern (.. .. .. .. ..)
P8 = Sparse dash-dot pattern (_. _. _. _. _.)
P9 = Sparse dot-dash pattern (._ ._ ._ ._ ._)

Example: A solid line pattern would be, set_line_pattern("P1");
         A dot pattern would be,        set_line_pattern("P6");
*/
void set_line_pattern(char *pattern)
{
   fprintf(stdout,"W(%s)",pattern);
}

/*************************** set_font_size() ********************************
     Sets the size of the graphics font.  The 'size' string has the following
format:  "S#"

     The letter 'S' activates the size option of the terminal.  The '#' sign
is a number from 0 to 16.

Example: The smallest font setting would be,  set_font_size("S0");
         The largest font setting would be,   set_font_size("S16");
*/
void set_font_size(char *size)
{
   fprintf(stdout,"T(%s)",size);
}

/*************************** pixel_printxy() ********************************
*/
void pixel_printxy(int xpix,int ypix,char *string)
{
   int i,j;

   j=strlen(string);
   for (i=0; i<j; i++)     /* Replace any occurence of "'" with "`"  to */
      if (string[i] == 39) /* eliminate graphical incompatability. */
         string[i] = 96;
   fprintf(stdout,"P[%d,%d]\nT'%*s'",xpix,480-ypix,j,string);
}

/**********************************************************************/
/* vtgcurseoff turns off the graphics cursor.                         */
/**********************************************************************/
void vtgcurseoff(void)
{
  fprintf(stdout,"S(C0)\n");
}

/**********************************************************************/
/* vtgcurseon turns on the graphics cursor.                           */
/**********************************************************************/
void vtgcurseon(void)
{
  fprintf(stdout,"S(C1)\n");
}

/****************************** printxy() *********************************
     This function prints the value that 'string' points to at the
specified row and column.  The parameter 'mode' selects a video attribute
to use when printing 'string'.  Note that this function will not reset the
video attributes if they are already in the correct mode.  The video mode is
saved in a global variable call 'term_status'.  If any other function alters
the video attributes 'term_status' should be set accordingly for 'printxy' to
function properly.

'row' is and integer between 1 and 24.
'col' is and integer between 1 and 80.
'string' points to the string to be printed.
'mode' indicates a specific attribute to use.

     Currently: mode 0  = print using current video attributes.
                mode 1  = print using reverse video attributes.
                mode 2  = print using bold attributes.
                mode 3  = print using bold-underline attributes.
                mode 4  = print using underline attributes.
                mode 5  = print using normal video attributes (lowlight).

     If row or col is invalid nothing is printed.
*/
void printxy(int col,int row,char *string,short int mode)
{
#define REVERSE                "\033[7m"
#define NOT_REVERSE            "\033[27m"
#define BOLD_UNDERLINE         "\033[1;4m"
#define UNDERLINE              "\033[4m"
#define NORMAL                 "\033[0m"
#define HIGHLITE               "\033[0;1m"
#define NOT_HIGHLITE           "\033[22m"
/*#define INBOLD                 "\033[7;1m"*/
/*#define NOT_INBOLD             "\033[27;22m"*/
/*#define BLINKING               "\033[5m"*/
/*#define NOT_BLINKING           "\033[25m"*/
/*#define BOLD_INV_UNDERLINE     "\033[1;4;7m"*/
/*#define NOT_BOLD_INV_UNDERLINE "\033[0m"*/
/*#define NOT_UNDERLINE          "\033[24m"*/
   char output[100];

   strcpy(output, string);

   if (row>=1 && row<=24 && col>=1 && col<=80) {
      if (term_status==mode || mode==0)
         fprintf(stdout,"\033[%d;%dH%*s", row, col, strlen(output), output);
      else if (mode==1 && term_status!=mode) {
         fprintf(stdout,"\033[%d;%dH\033[0m%s%s%*s", row, col,
            HIGHLITE, REVERSE, strlen(output), output);
         term_status = mode;
      }
      else if (mode==2  && term_status!=mode) {
         fprintf(stdout,"\033[%d;%dH\033[0m%s%*s", row, col,
            HIGHLITE, strlen(output), output);
         term_status = mode;
      }
      else if (mode==3  && term_status!=mode) {
         fprintf(stdout,"\033[%d;%dH\033[0m%s%*s", row, col,
            BOLD_UNDERLINE, strlen(output), output);
         term_status = mode;
      }
      else if (mode==4  && term_status!=mode) {
         fprintf(stdout,"\033[%d;%dH\033[0m%s%*s", row, col,
            UNDERLINE, strlen(output), output);
         term_status = mode;
      }
      else if (mode==5  && term_status!=mode) {
         fprintf(stdout,"\033[%d;%dH\033[0m%*s", row, col,
            strlen(output), output);
         term_status = mode;
      }
#     ifdef UNIXHOST
         fflush(stdout);
#     endif
   }
}
