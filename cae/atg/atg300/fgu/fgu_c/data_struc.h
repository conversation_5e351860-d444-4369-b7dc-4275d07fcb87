/***********************************************************************/
/*    Author: <PERSON><PERSON><PERSON>, <PERSON>                            */
/*    Date:   86/08/22                                                 */
/*    Decription: This include defines the major Data Structure use    */
/*                in the Vsa program.                                  */
/*                                                                     */
/*  Revision History: Nick Moscato  February 1990                      */
/*  Maximum initial conditions set from 20 to 40                       */
/***********************************************************************/

/***********************************************************************/
/*    Describe the kind code use in the datastructure access routine   */
/***********************************************************************/

#include "headstruct.h"
#include "graph.h"

#define  NOT_DISPLAY        0
#define  DISPLAY_NOT_ACTIVE 1
#define  DISPLAY_ACTIVE     2
#define  TO_BE_DISPLAY      3

#define  INIT_COND_LENGTH  42
#define  MAX_INIT_COND     40

/***********************************************************************/
/*    Curve block description                                          */
/***********************************************************************/

struct context_struc {
   Gscreen graph;
   Window  world_location;
   Window  graph_location;
   Window  graph_coord;
   Pt      title_location;       /* Mobile Title */
   Boolean title_display;
   char    *title;
};

typedef struct context_struc Context;

struct points_struc {
   Boolean       function;
   Pt            *point;
   int           num_pts_used;
   int	         num_pts_alloc;
   int           colour;
   Tolerance     *tolerance;
   Window        extreme;
};

typedef struct points_struc Points;

struct curve_struc {

  Header    *header;     /* header information         		*/
  Context   *context;    /* graphic description        		*/
  Points    *points;     /* data points                		*/
  struct curve_struc *master;     /* corresponding master block */
  struct curve_struc *next_curve; /* link list pointer          */
  char       *file_name;
  Boolean    modified;   /* modified flag              		*/
  int        display;    /* 0 = curve not display		*/
			 /* 1 = curve display and not active 	*/
			 /* 2 = curve display and active	*/
};                                         

typedef  struct curve_struc Curve;

/***********************************************************************/
/*    Head to curve block description                                  */
/***********************************************************************/


struct tete_struc {
  int        num;        /* number of element in list */
  Curve     *ptr;        /* pointer to list           */
};

typedef struct tete_struc Tete;

/***********************************************************************/
/*    Head to curve block description                                  */
/***********************************************************************/

struct file_id_struc {
  char      *id;              /* Curve Id                    */
  Tete      curve[3];         /* Pointers to curve structure */ 
  char      init_cond [MAX_INIT_COND] [INIT_COND_LENGTH+1];
			      /* initial conditions from CTS files */
};

typedef  struct file_id_struc File_id;

