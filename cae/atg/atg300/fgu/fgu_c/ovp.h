#include <stdio.h>

/*extern FILE *visaout;   Output file descriptor for all screen output */
FILE *visaout;   /* Output file descriptor for all screen output */

/***********************************************************************/
/*    The IPT structure defines the coordinate data structure for      */
/*    integer                                                          */
/***********************************************************************/

struct integer_coordinate {
   int x;
   int y;
};

typedef struct integer_coordinate  Ipt;

/***********************************************************************/
/*    The HLS_color structure defines the three values required when   */
/*    setting <PERSON><PERSON>, Lightness, and Shade for HLS coloring.              */
/***********************************************************************/

struct HLS_color_codes {
   int hue;
   int light;
   int shade;
};
typedef struct HLS_color_codes HLS_color;

typedef double Float;


/***********************************************************************/
/*    The PT structure defines the coordinate data structure for       */
/*    float                                                            */
/***********************************************************************/

struct point_struc 
   	{
   	Float x;
   	Float y;
   	};                                                            

typedef struct point_struc Pt;  

typedef int Boolean;

typedef struct {
   Pt min;
   Pt max;
   }   Window;


typedef struct {
   Pt xy1;
   Pt xy2;
   }   Pts;

typedef struct {
   Ipt xy1;
   Ipt xy2;
   }   Ipts;

#define round(i)    ((int)((i>0) ? (i+0.5) : (i-0.5)))

/*  Input-output file description */

#define  OUTPUT   "VISAOUT"  
#define  INPUT    "VISAIN"
#define  MSG_OUTPUT "MSGOUT"


/*  Enumerated type used for the tolerance_code */

enum tolerance_code_value
   { in, maybe, out};

