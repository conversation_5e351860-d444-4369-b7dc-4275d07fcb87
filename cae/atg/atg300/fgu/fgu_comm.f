C
C============================================================================
C
C============================================================================
C
      SUBROUTINE CommonSchedName
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER*80 String
      CHARACTER*12 SchedName(Maxfunc,4)
      INTEGER      SchedCount,CurrentFunc,CurrentDimen,I,J,K,L,M
      INTEGER      CurrSchedSize,Dimen,Count
      REAL         CurrentSched(Maxbp)
      LOGICAL      SameSched,SchedDone(Maxfunc,4)
C
C Save the previous function if it was modified
C
      IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
C
      DO I=1,Maxfunc
        DO J=1,4
          SchedDone(I,J)=.FALSE.
          SchedName(I,J)='            '
        ENDDO
      ENDDO
C
C The following structure reads through the entire data file giving identical
C breakpoint schedules the same name.  The array 'SchedName' will be loaded
C with the new names assigned for every schedule in every function.  The next
C structure will rewrite all the functions with their new sched. names.
C
      SchedCount=1
      DO M = 1,Nfunction  ! Search through every function.
        WRITE(6,10) M     ! Print var num after every pass to prevent logout.
10      FORMAT('+',I4)
        Findex=M
        IF (.NOT. Fdelete(Findex)) THEN
          CALL LoadFuncInfo(.FALSE.) ! Get all information on this function.
          Count=Dimension
          DO Dimen=1,Count
            IF (.NOT.SchedDone(M,Dimen)) THEN
              CurrSchedSize=0
              SchedDone(M,Dimen)=.TRUE.
              CurrSchedSize=Schedule_size(Dimen)
              DO J=1,Schedule_size(Dimen)
                CurrentSched(J)=Brkpt_schedule(Dimen,J)
              ENDDO
C Make the new schedule name based on 'SchedCount'. Example: S1, S2, S3
C Currently, the maximum schedule name that can be created is 'S999'.
              IF (SchedCount.LE.9) THEN
                WRITE(SchedName(M,Dimen),'(I2)') SchedCount
              ELSE IF (SchedCount.LE.99) THEN
                WRITE(SchedName(M,Dimen),'(I3)') SchedCount
              ELSE
                WRITE(SchedName(M,Dimen),'(I4)') SchedCount
              ENDIF
              SchedName(M,Dimen)(1:1)='S'
C Look for other functions with the same breakpoint schedule as CurrentSched.
C If found, give that schedule the same name as CurrentSched.
              IF (M+1 .LE. Nfunction) THEN
                DO Findex = M+1,Nfunction
                  IF (.NOT. Fdelete(Findex)) THEN
                    CALL LoadFuncInfo(.FALSE.)     ! Get all information on this function.
                    DO I=1,Dimension
                      IF (.NOT.SchedDone(Findex,I)) THEN
                        IF (Schedule_size(I).EQ.CurrSchedSize) THEN
                          J=1
                          SameSched=.TRUE.
                          DO WHILE (J.LE.CurrSchedSize .AND. SameSched)
                          IF (CurrentSched(J).NE.
     &                        Brkpt_schedule(I,J)) THEN
                              SameSched=.FALSE.
                            ENDIF
                            J = J + 1
                          ENDDO
                          IF (SameSched) THEN
                            SchedName(Findex,I)=SchedName(M,Dimen)
                            SchedDone(Findex,I)=.TRUE.
                          ENDIF
                        ENDIF
                      ENDIF
                    ENDDO
                  ENDIF
                ENDDO
              ENDIF
C
              SchedCount=SchedCount+1
            ENDIF
            Findex=M
            CALL LoadFuncInfo(.FALSE.)     ! Get all information on this function.
          ENDDO
        ENDIF
      ENDDO
C
C Rewrite all the functions with their new schedule names.
C
      DO Findex = 1,Nfunction
        IF (.NOT. Fdelete(Findex)) THEN
           CALL LoadFuncInfo(.TRUE.)
           DO I=1,Dimension
             Schedule_name(I)=SchedName(Findex,I)
           ENDDO
           Funit(Findex) = uScratch
           CALL SaveFunc
        ENDIF
      ENDDO
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE CommonBreakpt(Input)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      INCLUDE 'comm.inc'
      INTEGER Maxalias
      PARAMETER (Maxalias = 10)
      CHARACTER*80 String
      CHARACTER*12 Alias(MaxVar,Maxalias),CurrentVar,Word
      CHARACTER*5 Option,Options(2)/'ALIAS','ALL  '/,Input*(*)
      REAL Value
      INTEGER I,J,K,L,M,N,ios,Strln
      INTEGER Count1,Count2,AliasPtr
      LOGICAL ReadReal,Getcmd,All,Dummy,SearchList,Search,Removed
C
      VarInErrorSize=0
      UselessSize=0
      DO I = 1,Maxvar
        Var_Name(I)='            '
        Var_AliasPos(I)=0
        Var_Commonized(I)=.FALSE.
      ENDDO
C
      All = .FALSE.
      Tolperc1 = 0.0
      DO I = 1,Maxvar
        Tolperc2(I) = -1.0
        DO J = 1,Maxalias
          Alias(I,J)='************'
        ENDDO
      ENDDO
C
      IF (.NOT. Getcmd(Option,Input)) THEN
        CALL Mess(' Error in CommonBreakpt: Missing command options')
        Error = .TRUE.
        RETURN
      ENDIF
C
      IF (.NOT. SearchList(Option,5,Options,2)) THEN
        CALL Mess(' Error in CommonBreakpt: Unknown or misplaced command
     & option')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Commonize all breakpoint schedules. If the percent tolerance is not
C specified, then assume 0.0 %.
C
      IF (Option .EQ. 'ALL') THEN
        IF (Getcmd(Option,Input)) THEN
          I = INDEX(Option,'%')
          IF (I .GT. 0) THEN
            Option(I:I) = ' '
            IF (ReadReal(Option,Value,'(BN,F5.1)')) THEN
              TolPerc1 = Value
            ELSE
              CALL Mess(' Error in CommonBreakpt: Cannot decode toleranc
     &e value')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
            Dummy = Getcmd(Option,Input)
          ENDIF
        ELSE
          Tolperc1 = 0.0
          Dummy = Getcmd(Option,Input)
        ENDIF
        All = .TRUE.
      ENDIF
C
C Commonize only the breakpoint schedules with the following independent
C variables. An alias is used to identify independent variables with
C different names which are to be commonized together.
C
      Count1 = 0
      DO WHILE (Option .EQ. 'ALIAS')
        Count1 = Count1 + 1
        IF (Count1 .GT. Maxvar) THEN
          CALL Mess(' Error in CommonBreakpt: Number of independent vari
     &ables exceeded')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
        Count2 = 0
        DO WHILE (Getcmd(Word,Input))
          CALL Upper(Word)
          IF (Word.EQ.'ALIAS' .OR. INDEX(Word,'%').GT.0) GOTO 10
          Count2 = Count2 + 1
          IF (Count2 .GT. Maxalias) THEN
            CALL Mess(' Error in CommonBreakpt: Number of aliases exceed
     &ed')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
          Alias(Count1,Count2) = Word
        ENDDO
        IF (Count2 .EQ. 0) THEN
          CALL Mess(' Error in CommonBreakpt: No independent variable sp
     &ecified for alias')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
10      CONTINUE
        IF (INDEX(Word,'%') .GT. 0) THEN
          I = INDEX(Word,'%')
          Word(I:I) = ' '
          IF (ReadReal(Word,Value,'(BN,F12.4)')) THEN
            TolPerc2(Count1) = Value
          ELSE
            CALL Mess(' Error in CommonBreakpt: Cannot decode tolerance 
     &value')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ELSE
          Tolperc2(Count1) = Tolperc1
        ENDIF
        IF (Word .NE. 'ALIAS') Dummy = Getcmd(Option,Input)
        CALL Upper(Option)
      ENDDO
C
      IF (.NOT.All .AND. Count1.EQ.0) THEN
        CALL Mess(' Error in CommonBreakpt: No independent variables wer
     &e specified for commonizing breakpoints')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Save the previous function if it was modified
C
      IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
      Select = .FALSE.
C
C Load the Var_Name and the Var_AliasPos array.
C
C The Var_Name array will contain every different variable name in the
C data file (no names are repeated).
C The Var_AliasPos array will indicate where a particular variable is
C located in the Alias & Tolperc2 array.  Var_AliasPos will be equal the
C subscript of the first dimension in both the Alias & Tolprec2 array.
C
      J=0
      DO Findex = 1,Nfunction ! Search through every function.
        CALL LoadFuncInfo(.FALSE.)  ! Get header info for this function
        DO I = 1,Dimension
          Search=.FALSE.
          DO K = 1,Maxvar
            IF (Var_Name(K) .EQ. Variable_name(1,I)) Search=.TRUE.
          ENDDO
          IF (.NOT.Search) THEN
            J=J+1
            IF (J .GT. MaxVar) THEN
              CALL Mess(' Error in CommonBreakpt: Number of independent 
     &variable names exceeded')
              Error = .TRUE.
              RETURN
            ENDIF
            Var_Name(J)=Variable_name(1,I)
            DO K=1,Maxvar     ! Find out if this variable has an alias.
              DO L=1,Maxalias
                IF (Var_Name(J) .EQ. Alias(K,L)) Var_AliasPos(J)=K
              ENDDO
            ENDDO
          ENDIF
        ENDDO
      ENDDO
      DifferentVars = J
C
C Make sure that any alias specified is in the data file.
C
      DO I=1,Maxvar
        IF (Alias(I,1).NE.'************') THEN
           K=1
           Search=.FALSE.
           DO WHILE (Alias(I,K).NE.'************' .AND. K.LE.Maxalias)
             J=1
             DO WHILE (.NOT.Search .AND. J.LE.Maxvar)
               IF (Alias(I,K).EQ.Var_Name(J)) Search=.TRUE.
               J=J+1
             ENDDO
             IF (.NOT.Search) THEN
               String=' Error in CommonBreakpt: The alias '//Alias(I,K)
     &(1:Strln(Alias(I,K)))//' was not found in the data file'
               CALL Mess(String)
               Error = .TRUE.
               RETURN
             ENDIF
             Search=.FALSE.
             K=K+1
           ENDDO
        ENDIF
      ENDDO
C
C Find all the functions that use the variable (or alias) stored in the 
C array Alias and keep the location of those functions (as a subscript
C for the array Frec) in the array FrecLoc.  At the same time, common
C break point schedules are created from all the functions found using the
C same variables or alias.  Removal of points based on percent tolerance
C is done afterwards.
C
      IF (All) THEN                 ! Are all variables to be commonized?
        DO M=1,DifferentVars        ! Loop once for every different var.
          FunctionsFound(M) = 0     ! Initialize variables used for this var.
          CommSchedSize(M) = 0
          IF (.NOT.Var_Commonized(M)) THEN ! Has a common sched been made?
            DO Findex = 1,Nfunction ! Search through every function for var.
              CALL LoadFuncInfo(.TRUE.) ! Get all information on this function.
              DO I = 1,Dimension    ! Commonize every variable in this func.
                IF (Var_AliasPos(M).GT.0) THEN ! Does Variable have aliases?
                  DO J=1,Maxalias              ! Yes, loop thru all aliases.
                    IF (Alias(Var_AliasPos(M),J).EQ.
     &                  Variable_name(1,I)) THEN ! Func. found with this var?
                      CALL CombineScheds(CommonSched,
     &                                   CommSchedSize,I,M)
                      FunctionsFound(M)=FunctionsFound(M)+1
                      FrecLoc(M,FunctionsFound(M))=Findex ! Save index location.
                      FuncDimension(M,FunctionsFound(M))=I ! Save dimension.
                    ENDIF
                  ENDDO
                ELSE
                  IF (Var_Name(M).EQ.Variable_name(1,I)) THEN
                    CALL CombineScheds(CommonSched,
     &                                 CommSchedSize,I,M)
                    FunctionsFound(M)= FunctionsFound(M)+1
                    FrecLoc(M,FunctionsFound(M)) = Findex
                    FuncDimension(M,FunctionsFound(M)) = I ! Save dimension.
                  ENDIF
                ENDIF
              ENDDO
            ENDDO
            IF (Var_AliasPos(M).LE.0) THEN ! Does Variable have aliases?
              Var_Commonized(M)=.TRUE.     ! No, mark this var. as commonized.
            ELSE
              DO I=1,Maxvar                ! Yes, mark all aliases.
                IF (Var_AliasPos(M) .EQ. Var_AliasPos(I)) THEN
                  Var_Commonized(I) = .TRUE.
                ENDIF
              ENDDO
            ENDIF
          ENDIF
        ENDDO
      ELSE !*********************** Not all variables are to be commonized.
        DO M=1,DifferentVars
          FunctionsFound(M) = 0
          CommSchedSize(M) = 0
          IF (Var_AliasPos(M).GT.0) THEN ! Does Variable have aliases?
            IF (.NOT.Var_Commonized(M)) THEN
              DO Findex = 1,Nfunction ! Search through every function for var.
                CALL LoadFuncInfo(.TRUE.) ! Get all information on this function.
                DO I = 1,Dimension    ! Commonize every variable in this func.
                  DO J=1,Maxalias
                    IF (Alias(Var_AliasPos(M),J).EQ.
     &                  Variable_name(1,I)) THEN
                      CALL CombineScheds(CommonSched,
     &                                   CommSchedSize,I,M)
                      FunctionsFound(M)=FunctionsFound(M)+1
                      FrecLoc(M,FunctionsFound(M))=Findex
                      FuncDimension(M,FunctionsFound(M))=I ! Save dimension.
                    ENDIF
                  ENDDO
                ENDDO
              ENDDO
              DO I=1,Maxvar                ! Mark all aliases TURE.
                IF (Var_AliasPos(M) .EQ. Var_AliasPos(I)) THEN
                  Var_Commonized(I) = .TRUE.
                ENDIF
              ENDDO
            ENDIF
          ENDIF
        ENDDO
      ENDIF
C
      CALL ReduceSched
C
C Start inserting/writing the Commonized schedules to the scratch file.
C Each function is not written to the scratch file until all of its
C commonized schedules have been replaced.
C
      DO M=1,DifferentVars
        IF (FunctionsFound(M).GT.0) THEN
          DO L=1,FunctionsFound(M)
            Findex = FrecLoc(M,L)
            CALL LoadFuncInfo(.TRUE.) ! Get all information on this function.
            DO I=1,Dimension
              DO J=1,DifferentVars
                IF (Var_Name(J).EQ.Variable_name(1,I) .AND.
     &              Var_Commonized(J)) THEN
                  String(1:12) = Fname(Findex)(1:12)
                  String(13:24)= Var_Name(J)
                  Removed=.FALSE.        ! Check if this var was in error
                  DO K=1,VarInErrorSize  ! the in the ReduceSched Subroutine.
                    IF (String(1:24).EQ.VarInError(K)) THEN
                      Removed=.TRUE.     ! Var was removed...
                    ENDIF
                  ENDDO
                  IF (.NOT.Removed) THEN
                    IF (CommSchedSize(J).EQ.0) THEN ! If this alias does not have
                      N=1                   ! a schedule find the alias that does.
                      DO WHILE (Var_AliasPos(J).NE.Var_AliasPos(N) .AND.
     &                          CommSchedSize(N).LE.0)
                        N=N+1
                      ENDDO
                    ELSE
                      N=J
                    ENDIF
                    DO K=1,CommSchedSize(N)
                      CurrentSched(K)=CommonSched(N,K)
                    ENDDO
                    CALL SetSchedule(I,CommSchedSize(N),
     &                CurrentSched,.FALSE.,ios)
                  ENDIF
                ENDIF
              ENDDO
            ENDDO
            I=Schedule_size(1)*Schedule_size(2)*
     &        Schedule_size(3)*Schedule_size(4)
            IF (I .GT. Maxval) THEN
              J = Strln(Fname(Findex))
              String=' Warning: The function '//Fname(Findex)(1:J)//
     &' is too large to commonize'
              CALL Mess(String)
              CALL Clear(String)
              String='          Number of function values required: '
              WRITE(String(47:52),'(BN,I6)') I
              CALL Mess(String)
            ELSE
              Funit(Findex) = uScratch
              CALL SaveFunc
            ENDIF
          ENDDO
        ENDIF
      ENDDO
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE ReduceSched
      IMPLICIT NONE
      INCLUDE  'finfo.inc'
      INCLUDE  'inout.inc'
      INCLUDE  'comm.inc'
      REAL     Point1(2),Point2(2),Point3(2),Y_Weight
      REAL     Percent
      INTEGER  ios
      INTEGER  I, J, K, L, M, N, Size, Dimen
      INTEGER  K1, K2, K3, K4, Ptr, Strln
      CHARACTER*80 String
C
      LOGICAL  ReducePoint, In_List ! Functions
      LOGICAL  FirstPass
C
      DO M=1,DifferentVars               ! Loop thru every different var.
        FirstPass=.TRUE.
        UselessSize=0
        IF (FunctionsFound(M).GT.0) THEN ! Any functions comizd with this var.?
          IF (Var_AliasPos(M).LE.0) THEN ! Does this var have an alias?
            N=M                          ! No, it does not.
            Percent = Tolperc1
          ELSE
            N=1                   ! Locate which alias has the comizd schedule.
            DO WHILE (Var_AliasPos(M).NE.Var_AliasPos(N))
              N=N+1
            ENDDO
            Percent = Tolperc2(Var_AliasPos(M))
          ENDIF
111       L = 1
112       DO WHILE (Percent .GT. 0 .AND.
     &              L.LE.FunctionsFound(M)) ! Loop thru every func. found with
            Findex = FrecLoc(M,L)           ! this var. or alias.
            WRITE(6,10) M  ! Print var num after every pass to prevent logout.
10          FORMAT('+',I4)
            CALL LoadFuncInfo(.TRUE.)          ! Get all information on this function.
            DO K=1,CommSchedSize(N)            ! Place comizd sched. into a
              CurrentSched(K)=CommonSched(N,K) ! one dimensional array.
            ENDDO
            CALL SetSchedule(FuncDimension(M,L),CommSchedSize(N),
     &                       CurrentSched,.FALSE.,ios)
            IF (ios.GT.0) THEN                  ! This function can't be commonized.
              IF (FunctionsFound(M).GT.1) THEN  ! Remove the loction of this
                DO K=FunctionsFound(M),L+1,-1   ! function from the FrecLoc &
                  FrecLoc(M,K-1) = FrecLoc(M,K) ! FuncDimension array.
                  FuncDimension(M,K-1)=FuncDimension(M,K)
                ENDDO
              ENDIF
              VarInErrorSize = VarInErrorSize + 1
              VarInError(VarInErrorSize)(1:12) =Fname(Findex)(1:12)
              VarInError(VarInErrorSize)(13:24)=Var_name(M)(1:12)
              String=' Warning: The variable '//Var_name(M)
     &(1:Strln(Var_name(M)))//' will not be commonized in the function '
     &//Fname(Findex)
              CALL Mess(String)
              FunctionsFound(M)=FunctionsFound(M)-1
              GO TO 112                         ! Try again with another func.
            ENDIF
            IF (FuncDimension(M,L).EQ.1) THEN
              DO K4 = 1,Schedule_size(4)
                DO K3 = 1,Schedule_size(3)
                  DO K2 = 1,Schedule_size(2)
                    Y_Weight=0
                    Size  = Schedule_size(1)
                    Dimen = FuncDimension(M,L)
                    DO K1 = 1,Size-1
                      Y_Weight = 
     &                  Y_Weight+(ABS(Fvalues(Ptr(K1,K2,K3,K4)))+
     &                  ABS(Fvalues(Ptr(K1+1,K2,K3,K4))))*
     &                  (Brkpt_schedule(Dimen,K1+1)-
     &                  Brkpt_schedule(Dimen,K1))
                    ENDDO
                    Y_Weight = (Y_Weight*0.5)/
     &                         (Brkpt_schedule(Dimen,Size) -
     &                          Brkpt_schedule(Dimen,1))
                    DO K1 = 1,Schedule_size(1)
                      IF (K1+2 .LE. Schedule_size(1)) THEN
                        Point2(1)=
     &                    Brkpt_schedule(FuncDimension(M,L),K1+1)
                        IF (In_List(Point2,UselessPoint,
     &                      UselessSize) .OR. FirstPass) THEN
                          Point1(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K1)
                          Point1(2)=Fvalues(Ptr(K1,K2,K3,K4))
                          Point2(2)=Fvalues(Ptr(K1+1,K2,K3,K4))
                          Point3(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K1+2)
                          Point3(2)=Fvalues(Ptr(K1+2,K2,K3,K4))
                          CALL UpdateList(UselessPoint,UselessSize,
     &                      Point2,ReducePoint(Point1,Point2,Point3,
     &                              Percent,Y_Weight),FirstPass)
                        ENDIF
                      ENDIF
                    ENDDO
                    FirstPass=.FALSE.
                  ENDDO
                ENDDO
              ENDDO
            ELSE IF (FuncDimension(M,L).EQ.2) THEN
              DO K4 = 1,Schedule_size(4)
                DO K3 = 1,Schedule_size(3)
                  DO K1 = 1,Schedule_size(1)
                    Y_Weight=0
                    Size  = Schedule_size(2)
                    Dimen = FuncDimension(M,L)
                    DO K2 = 1,Size-1
                      Y_Weight = 
     &                  Y_Weight+(ABS(Fvalues(Ptr(K1,K2,K3,K4)))+
     &                  ABS(Fvalues(Ptr(K1,K2+1,K3,K4))))*
     &                  (Brkpt_schedule(Dimen,K2+1)-
     &                  Brkpt_schedule(Dimen,K2))
                    ENDDO
                    Y_Weight = (Y_Weight*0.5)/
     &                         (Brkpt_schedule(Dimen,Size) -
     &                          Brkpt_schedule(Dimen,1))
                    DO K2 = 1,Schedule_size(2)
                      IF (K2+2 .LE. Schedule_size(2)) THEN
                        Point2(1)=
     &                    Brkpt_schedule(FuncDimension(M,L),K2+1)
                        IF (In_List(Point2,UselessPoint,
     &                      UselessSize) .OR. FirstPass) THEN
                          Point1(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K2)
                          Point1(2)=Fvalues(Ptr(K1,K2,K3,K4))
                          Point2(2)=Fvalues(Ptr(K1,K2+1,K3,K4))
                          Point3(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K2+2)
                          Point3(2)=Fvalues(Ptr(K1,K2+2,K3,K4))
                          CALL UpdateList(UselessPoint,UselessSize,
     &                      Point2,ReducePoint(Point1,Point2,Point3,
     &                              Percent,Y_Weight),FirstPass)
                        ENDIF
                      ENDIF
                    ENDDO
                    FirstPass=.FALSE.
                  ENDDO
                ENDDO
              ENDDO
            ELSE IF (FuncDimension(M,L).EQ.3) THEN
              DO K4 = 1,Schedule_size(4)
                DO K1 = 1,Schedule_size(1)
                  DO K2 = 1,Schedule_size(2)
                    Y_Weight=0
                    Size  = Schedule_size(3)
                    Dimen = FuncDimension(M,L)
                    DO K3 = 1,Size-1
                      Y_Weight = 
     &                  Y_Weight+(ABS(Fvalues(Ptr(K1,K2,K3,K4)))+
     &                  ABS(Fvalues(Ptr(K1,K2,K3+1,K4))))*
     &                  (Brkpt_schedule(Dimen,K3+1)-
     &                  Brkpt_schedule(Dimen,K3))
                    ENDDO
                    Y_Weight = (Y_Weight*0.5)/
     &                         (Brkpt_schedule(Dimen,Size) -
     &                          Brkpt_schedule(Dimen,1))
                    DO K3 = 1,Schedule_size(3)
                      IF (K3+2 .LE. Schedule_size(3)) THEN
                        Point2(1)=
     &                    Brkpt_schedule(FuncDimension(M,L),K3+1)
                        IF (In_List(Point2,UselessPoint,
     &                      UselessSize) .OR. FirstPass) THEN
                          Point1(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K3)
                          Point1(2)=Fvalues(Ptr(K1,K2,K3,K4))
                          Point2(2)=Fvalues(Ptr(K1,K2,K3+1,K4))
                          Point3(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K3+2)
                          Point3(2)=Fvalues(Ptr(K1,K2,K3+2,K4))
                          CALL UpdateList(UselessPoint,UselessSize,
     &                      Point2,ReducePoint(Point1,Point2,Point3,
     &                              Percent,Y_Weight),FirstPass)
                        ENDIF
                      ENDIF
                    ENDDO
                    FirstPass=.FALSE.
                  ENDDO
                ENDDO
              ENDDO
            ELSE IF (FuncDimension(M,L).EQ.4) THEN
              DO K1 = 1,Schedule_size(1)
                DO K3 = 1,Schedule_size(3)
                  DO K2 = 1,Schedule_size(2)
                    Y_Weight=0
                    Size  = Schedule_size(4)
                    Dimen = FuncDimension(M,L)
                    DO K4 = 1,Size-1
                      Y_Weight = 
     &                  Y_Weight+(ABS(Fvalues(Ptr(K1,K2,K3,K4)))+
     &                  ABS(Fvalues(Ptr(K1,K2,K3,K4+1))))*
     &                  (Brkpt_schedule(Dimen,K4+1)-
     &                  Brkpt_schedule(Dimen,K4))
                    ENDDO
                    Y_Weight = (Y_Weight*0.5)/
     &                         (Brkpt_schedule(Dimen,Size) -
     &                          Brkpt_schedule(Dimen,1))
                    DO K4 = 1,Schedule_size(4)
                      IF (K4+2 .LE. Schedule_size(4)) THEN
                        Point2(1)=
     &                    Brkpt_schedule(FuncDimension(M,L),K4+1)
                        IF (In_List(Point2,UselessPoint,
     &                      UselessSize) .OR. FirstPass) THEN
                          Point1(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K4)
                          Point1(2)=Fvalues(Ptr(K1,K2,K3,K4))
                          Point2(2)=Fvalues(Ptr(K1,K2,K3,K4+1))
                          Point3(1)=
     &                      Brkpt_schedule(FuncDimension(M,L),K4+2)
                          Point3(2)=Fvalues(Ptr(K1,K2,K3,K4+2))
                          CALL UpdateList(UselessPoint,UselessSize,
     &                      Point2,ReducePoint(Point1,Point2,Point3,
     &                              Percent,Y_Weight),FirstPass)
                        ENDIF
                      ENDIF
                    ENDDO
                    FirstPass=.FALSE.
                  ENDDO
                ENDDO
              ENDDO
            ENDIF
            L = L + 1
          ENDDO    ! Removes all useless points from the current schedule.
          CALL EliminatePoints(N)
C This 'IF GO TO' structure is used because Fortran does not have a 'DO WHILE'
C structure that makes the looping decision after at lease one loop.
          IF (UselessSize.NE.0) GO TO 111
        ENDIF
      ENDDO
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE EliminatePoints(Sub)
      IMPLICIT   NONE
      INCLUDE    'finfo.inc'
      INCLUDE    'inout.inc'
      INCLUDE    'comm.inc'
      INTEGER    I, J, K, L, M, N, Sub
C
      K=1    ! CommonSched subscript.
      I=1    ! UselessPoint subscript.
      DO WHILE (UselessSize.NE.0 .AND. I.LE.UselessSize)
        IF (UselessPoint(I).GT.CommonSched(Sub,K)) THEN
          K = K + 1
        ELSE
          DO J=K,CommSchedSize(Sub)
            CommonSched(Sub,J)=CommonSched(Sub,J+1)
          ENDDO
          CommSchedSize(Sub) = CommSchedSize(Sub)-1
          DO J=I,UselessSize
            UselessPoint(J) = UselessPoint(J+1)
          ENDDO
          UselessSize = UselessSize - 1
          IF (UselessPoint(I).EQ.CommonSched(Sub,K)) THEN
            K = K + 1
            I = I + 1
          ENDIF
        ENDIF
      ENDDO
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE UpdateList(UselessPoint,UselessSize,Point2,Useless,
     &                      FirstPass)
      IMPLICIT   NONE
      INCLUDE    'finfo.inc'
      INCLUDE    'inout.inc'
      INTEGER    UselessSize, I, J, K, L
      REAL       Point2(2),UselessPoint(Maxbp),xPoint
      LOGICAL    Useless,FirstPass
C
      xPoint = Point2(1)
      IF (Useless.AND.FirstPass) THEN            ! Can this point be eliminated?
C If yes, then place this 'xPoint' value into the 'UselessPoint' array.
        IF (UselessSize.LT.1) THEN ! Are there any points in the array yet?
          UselessPoint(1) = xPoint ! No, place first point into array.
          UselessSize=1
        ELSE
          J = UselessSize ! Fit 'xPoint' into 'UselessPoint' array and
          DO I=1,J        ! keep the numbers ordered from lowest to highest.
            IF (I.EQ.UselessSize .AND. xPoint.GT.UselessPoint(I)) THEN
              UselessPoint(I+1) = xPoint
              UselessSize = UselessSize + 1
            ELSE IF (xPoint.LT.UselessPoint(I)) THEN
              DO K=UselessSize,I,-1
                UselessPoint(K+1)=UselessPoint(K)
              ENDDO
              UselessPoint(I) = xPoint
              UselessSize = UselessSize + 1
            ENDIF
          ENDDO
        ENDIF
      ELSE IF (.NOT.Useless) THEN
C If 'xPoint' is not useless make sure that is not in the 'UselessPoint' array.
C It may have been found useless in a previeous curve.
        IF (UselessSize .GT. 0) THEN
          I=1 ! Search the 'UselessPoint' array for 'xPoint'.
          DO WHILE (UselessPoint(MIN(I,Maxbp)).NE.xPoint
     &              .AND. I.LE.UselessSize)
            I = I + 1
          ENDDO
          IF (I.LE.UselessSize) THEN   ! Was 'xPoint' found in array?
            IF (I.LT.UselessSize) THEN ! Yes, remove it from array.
              DO J=I,UselessSize
                UselessPoint(J) = UselessPoint(J+1)
              ENDDO
            ENDIF
            UselessSize = UselessSize - 1
          ENDIF
        ENDIF
      ENDIF
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      FUNCTION In_List(Point2,UselessPoint,UselessSize)
      IMPLICIT NONE
      INCLUDE  'finfo.inc'
      INCLUDE  'inout.inc'
      INTEGER  UselessSize, I
      REAL     Point2(2),UselessPoint(Maxbp),xPoint
      LOGICAL  In_List
C
      In_List = .FALSE.
      xPoint = Point2(1)
      IF (UselessSize .GT. 0) THEN
        I=1    ! Search the 'UselessPoint' array for 'xPoint'.
        DO WHILE (UselessPoint(MIN(I,UselessSize)).NE.xPoint
     &            .AND. I.LE.UselessSize)
          I = I + 1
        ENDDO
        IF (I.LE.UselessSize) In_List = .TRUE.
      ENDIF
C
      RETURN
      END
C
C=========================== ReducePoint ====================================
C     This function decides whether a point on a line exceeds a certain
C tolerance and returns TRUE or FALSE base on this test.
C     Five arguments are required:  Three consecutive x,y points on a line,
C the acceptable tolerance to eliminate the middle point (Point2), and the
C weight of the line.
C
C Arguments: 'Point1, Point2, and Point3' are two valued one-dimensional arrays
C            containing three consecutive x,y points on a line.  The first
C            element of each array is the x value.
C
C Argument:  'Percent' is the tolerance.
C
C Argument:  'Y_Weight' is the average value of the absolute value of a 
C            function curve.
C============================================================================
C
      FUNCTION ReducePoint(Point1,Point2,Point3,Percent,Y_Weight)
      IMPLICIT     NONE
      REAL         Point1(2),Point2(2),Point3(2),Y_Weight
      REAL         Slope, Value, Err, Percent
      LOGICAL      ReducePoint
C
      ReducePoint=.FALSE.
C
C The following commented code will prevent division by zero when 
C calculating the slope.
C
C Note: To have a division by zero when calculting the slope there would
C have to be two identical break point values.  This should never happen
C however the code to handle this is provided.
C
C      IF (ABS(Point3(1)-Point1(1)) .LT. 0.000000001) THEN
C        Point3(1) = Point1(1) + 0.000000001
C      ENDIF
C
      Slope = (Point3(2)-Point1(2))/(Point3(1)-Point1(1))
      Value = Point1(2) + (Point2(1) - Point1(1)) * Slope
      IF (Y_Weight.EQ.0) Y_Weight = 0.000000001
      Err = ABS((Value - Point2(2)) / Y_Weight) * 100
C
      IF (Err .LT. Percent) ReducePoint = .TRUE.
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE CombineScheds(CommonSched, CommSchedSize, Dim, Sub)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      REAL         CommonSched(Maxvar,Maxbp), NewCommSched(Maxbp)
      INTEGER      CommSchedSize(Maxvar), Dim, I, J, K, L, M, Sub
C
      I = 1
      J = 1
      K = 1
      IF (CommSchedSize(Sub).GE.Schedule_size(Dim)) THEN
        DO WHILE (I.LE.CommSchedSize(Sub))
          IF (CommonSched(Sub,I).LE.Brkpt_schedule(Dim,J)) THEN
            NewCommSched(K) = CommonSched(Sub,I)
            IF (CommonSched(Sub,I).EQ.Brkpt_schedule(Dim,J)) J = J + 1
            I = I + 1
          ELSE
            NewCommSched(K) = Brkpt_schedule(Dim,J)
            IF (J.LE.Schedule_size(Dim)) THEN
              J = J + 1
            ELSE IF (I .LT. CommSchedSize(Sub)) THEN
              I = I + 1
            ENDIF
          ENDIF
          K = K + 1
          IF (I.GT.CommSchedSize(Sub) .AND. J.LT.
     &        Schedule_size(Dim)) THEN
            DO I=J,Schedule_size(Dim)
              NewCommSched(K) = Brkpt_schedule(Dim,I)
              K = K + 1
            ENDDO
            I=CommSchedSize(Sub)+1 ! Exit Do While loop
          ELSE IF (J.GT.Schedule_size(Dim).AND.I.LE.
     &             CommSchedSize(Sub)) THEN
            DO J=I,CommSchedSize(Sub)
              NewCommSched(K) = CommonSched(Sub,J)
              K = K + 1
            ENDDO
            I=CommSchedSize(Sub)+1 ! Exit Do While loop
          ENDIF
        ENDDO
      ELSE
        DO WHILE (I.LE.Schedule_size(Dim))
          IF (Brkpt_schedule(Dim,I).LE.CommonSched(Sub,J)) THEN
            NewCommSched(K) = Brkpt_schedule(Dim,I)
            IF (CommonSched(Sub,J).EQ.Brkpt_schedule(Dim,I)) J = J + 1
            I = I + 1
          ELSE
            NewCommSched(K) = CommonSched(Sub,J)
            IF (J.LE.CommSchedSize(Sub)) THEN
              J = J + 1
            ELSE IF (I .LT. Schedule_size(Dim)) THEN
              I = I + 1
            ENDIF
          ENDIF
          K = K + 1
          IF (CommSchedSize(Sub).EQ.0) THEN
            DO I=1,Schedule_size(Dim)
              CommonSched(Sub,I)= Brkpt_schedule(Dim,I)
            ENDDO
            CommSchedSize(Sub) = Schedule_size(Dim)
            RETURN
          ELSE IF (J.GT.CommSchedSize(Sub).AND.I.LE.
     &             Schedule_size(Dim)) THEN
            DO J=I,Schedule_size(Dim)
              NewCommSched(K) = Brkpt_schedule(Dim,J)
              K = K + 1
            ENDDO
            I=Schedule_size(Dim)+1 ! Exit Do While loop
          ELSE IF (I.GT.Schedule_size(Dim).AND.J.LT.
     &             CommSchedSize(Sub)) THEN
            DO I=J,CommSchedSize(Sub)
              NewCommSched(K) = CommonSched(Sub,I)
              K = K + 1
            ENDDO
            I=Schedule_size(Dim)+1 ! Exit Do While loop
          ENDIF
        ENDDO
      ENDIF
C
      CommSchedSize(Sub) = K - 1
      DO I=1,CommSchedSize(Sub)
        CommonSched(Sub,I) = NewCommSched(I)
      ENDDO
C
      RETURN
      END
C
C
      FUNCTION Get_Y_Point(Page,Curve,Point)
      IMPLICIT NONE
      REAL Get_Y_Point
      INTEGER Page,Curve,Point,Ptr,X_index,Y_index,Z_index,T_index,I
      INCLUDE 'finfo.inc'
      X_index = Point
      Y_index = 1
      Z_index = 1
      T_index = 1
      IF (Dimension .GE. 2) THEN
        IF (Dimension .GE. 3) THEN
          IF (Dimension .EQ. 4) THEN
            T_index = INT(FLOAT(Page)/FLOAT(Schedule_Size(3)) + 0.999)
            Z_index = Page - (T_index-1)*Schedule_size(3)
          ELSE
            Z_index = Page
          ENDIF
        ENDIF
        Y_index = Curve
      ENDIF
      Get_Y_Point = Fvalues(Ptr(X_index,Y_index,Z_index,T_index))
      RETURN
      END
C
      FUNCTION Get_X_Point(Page,Curve,Point)
      IMPLICIT NONE
      REAL Get_X_Point
      INTEGER Page,Curve,Point
      INCLUDE 'finfo.inc'
      Get_X_Point = Brkpt_schedule(1,Point)
      RETURN
      END
C
C
C
      SUBROUTINE Get_Graph_Legend(Page,Z_value,T_value)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INTEGER Page,Z_index,T_index
      REAL Z_value,T_value
      Z_index = 1
      T_index = 1
      IF (Dimension .GE. 3) THEN
        IF (Dimension .EQ. 4) THEN
          T_index = INT(FLOAT(Page)/FLOAT(Schedule_Size(3)) + 0.999)
          Z_index = Page - (T_index-1)*Schedule_size(3)
          Z_value = Brkpt_schedule(3,Z_index)
          T_value = Brkpt_schedule(4,T_index)
        ELSE
          Z_index = Page
          Z_value = Brkpt_schedule(3,Z_index)
        ENDIF
      ELSE
        RETURN
      ENDIF
      RETURN
      END
C
C
C
      SUBROUTINE Get_Y_Breakpoint(Page,Breakno,Breakpoint)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INTEGER Page,Breakno
      REAL Breakpoint
C
      Breakpoint = Brkpt_schedule(2,Breakno)
C
      RETURN
      END
