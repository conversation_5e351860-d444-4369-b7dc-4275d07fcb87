      PROGRAM FGU
      IMPLICIT NONE
      LOGICAL CmdPar/.FALSE./,Quit/.FALSE./
      LOGICAL Getcmd
      CHARACTER Input*1000,Cmd*30,Junk*30
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CALL VAXC$CRTL_INIT
C
C Initializations
C
      CALL Init1
C
C Get command line parameter
C
      CALL GetParam(Input)
      IF (Input(1:1) .NE. ' ') CmdPar = .TRUE.
C
C Program header
C
      CALL Mess('1Function Generation Utility  v. 1.0')
      IF (.NOT.CmdPar) CALL Mess(' Enter a command or help')
      CALL Mess(' ')
C
C Get input
C
      DO WHILE (.NOT. Quit)
        IF (CmdPar) THEN
          Cmd = 'DO'
          CmdPar = .FALSE.
        ELSE
          CALL ReadInput('.',Input)
          IF (Getcmd(Cmd,Input)) CALL VerbSearch(Cmd,Input)
        ENDIF
C
C Commands
C
        IF (Cmd .EQ. 'QUIT') THEN
          Quit = .TRUE.
C
        ELSEIF (Cmd .EQ. 'LIST') THEN
          CALL ListCmd(Input)
C
        ELSEIF (Cmd .EQ. 'DO') THEN
          CALL DoCmd(Input)
C
        ELSEIF (Cmd .EQ. 'LOAD') THEN
          CALL LoadCmd(Input)
C
        ELSEIF (Cmd .EQ. 'SELECT') THEN
          CALL SelectCmd(Input)
C
        ELSEIF (Cmd .EQ. 'SHOW') THEN
          CALL ShowCmd(Input)
C
        ELSEIF (Cmd .EQ. 'SYS') THEN
          CALL SysCmd(Input)
C
        ELSEIF (Cmd .EQ. 'JOURNAL') THEN
          CALL JournalCmd(Input)
C
        ELSEIF (Cmd .EQ. 'SWITCH') THEN
          CALL SwitchCmd(Input)
C
        ELSEIF (Cmd .EQ. 'HELP') THEN
          CALL HelpCmd(Input)
C
        ELSEIF (Cmd .EQ. 'MULTIPLY') THEN
          CALL AddMultCmd(Input,'*')
C
        ELSEIF (Cmd .EQ. 'ADD') THEN
          CALL AddMultCmd(Input,'+')
C
        ELSEIF (Cmd .EQ. 'SET') THEN
          CALL SetCmd(Input)
C
        ELSEIF (Cmd .EQ. 'LIST') THEN
          CALL ListCmd(Input)
C
        ELSEIF (Cmd .EQ. 'PLOT') THEN
          CALL PlotCmd(Input)
C
        ELSEIF (Cmd .EQ. 'SUBMIT') THEN
          CALL SubmitCmd(Input)
C
        ELSEIF (Cmd .EQ. 'EXAMINE') THEN
          CALL ExamineCmd(Input)
C
        ELSEIF (Cmd .EQ. 'DEPOSIT') THEN
          CALL DepositCmd(Input)
C
        ELSEIF (Cmd .EQ. 'GRAPH') THEN
          CALL Graphcmd(Input)
C
        ELSEIF (Cmd .EQ. 'WRITE') THEN
          CALL WriteCmd(Input)
C
        ELSEIF (Cmd .EQ. 'MODIFY') THEN
          CALL ModifyCmd(Input)
C
        ELSEIF (Cmd .EQ. 'DELETE') THEN
          CALL DeleteCmd(Input,Cmd)
C
        ELSEIF (Cmd .EQ. 'UNDELETE') THEN
          CALL DeleteCmd(Input,Cmd)
C
        ELSEIF (Cmd .EQ. 'CONVERT') THEN
          CALL ConvertCmd(Input)
C
        ELSEIF (Cmd .EQ. 'COMMONIZE') THEN
          CALL CommonizeCmd(Input)
C
        ELSEIF (Cmd .EQ. 'CONTINUE') THEN
          CALL ContinueCmd
C
        ELSEIF (Cmd .EQ. 'ABORT') THEN
          CALL AbortCmd
C
        ELSEIF (Cmd .EQ. 'STOP') THEN
          StopProcess = .TRUE.
C
        ENDIF
C
        CALL Clear(Cmd)
C
C Multiple commands per line and/or comments
C
        IF (Getcmd(Junk,Input)) THEN
          CALL Mess(' Warning: Extra characters at end of line')
        ENDIF
      ENDDO
C
C Quit
C
      CALL CloseAll
      CALL Mess(' End processing')
      CALL Mess(' ')
      END
C
C============================================================================
C The function FnF returns a value of .TRUE. if the specified file does not
C exist and .FALSE. if it does.
C============================================================================
C
      FUNCTION FnF(File_name)
      IMPLICIT NONE
      LOGICAL Found,FnF
      CHARACTER*(*) File_name
      FnF = .TRUE.
      INQUIRE (FILE=File_name,EXIST=Found)
      IF (Found) FnF = .FALSE.
      RETURN
      END
C
      SUBROUTINE Strip(String)
      IMPLICIT NONE
      CHARACTER*(*) String
      CHARACTER*1 Blank/' '/
      INTEGER LEN,End
      INTRINSIC LEN
      End = LEN(String)
      DO WHILE (String(1:1).EQ.Blank .AND. End.GE.1)   ! Strip leading blanks
        String = String(2:)
        End = End - 1
      ENDDO
      DO WHILE (String(End:End).EQ.Blank .AND. End.GT.1) ! Strip trailing blanks
        End = End - 1
        String = String(1:End)
      ENDDO
      RETURN
      END
C
C============================================================================
C The subroutine "VerbSearch" compares the command verb (which may be 
C abbreviated) to a list of valid verbs and returns the entire command verb.
C============================================================================
C
      SUBROUTINE VerbSearch(Verb,Input)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INTEGER NCommand
      PARAMETER (NCommand = 28)
      CHARACTER Verb*(*),Input*(*),Cmnd(NCommand)*9
      INTEGER I,Verb_code,Count,strln,Verb_len,LEN
      DATA Cmnd
     &/ 'QUIT     ','LIST     ','DO       ','LOAD     ','SELECT   ',
     &  'SUBMIT   ','HELP     ','SYS      ','SET      ','SWITCH   ',
     &  'INVERT   ','COMMONIZE','CONVERT  ','MODIFY   ','GRAPH    ',
     &  'SHOW     ','JOURNAL  ','ADD      ','MULTIPLY ','WRITE    ',
     &  'PLOT     ','EXAMINE  ','DEPOSIT  ','DELETE   ','CONTINUE ',
     &  'ABORT    ','STOP     ','UNDELETE '/
C
      Verb_code = 0
      Verb_len = Strln(Verb)
      CALL Upper(Verb)
      IF (Verb_len .GT. LEN(Verb)) THEN
        Verb_code = 0
        CALL Mess(' Error: Command verb too long')
        Error = .TRUE.
        CALL Clear(Input)
      ELSE
        Count = 0
        DO I = 1,Ncommand
          IF (Verb(1:Verb_len) .EQ. Cmnd(I)(1:Verb_len)) THEN
            Count = Count + 1
            Verb_code = I
          ENDIF
        ENDDO
        IF (Count .EQ. 0) THEN
          Verb_code = 0
          CALL Mess(' Error: Unknown command verb')
          Error = .TRUE.
          CALL Clear(Input)
        ELSEIF (Count .GT. 1) THEN
          Verb_code = 0
          CALL Mess(' Error: Ambiguous command verb')
          Error = .TRUE.
          CALL Clear(Input)
        ENDIF
      ENDIF
      IF (Verb_code .GT. 0) THEN
        Verb(1:9) =  Cmnd(Verb_code)
        CALL Clear(Verb(10:))
      ELSE
        CALL Clear(Verb)
      ENDIF
      RETURN
      END
C
C============================================================================
C The subroutine RdFuncNames reads the direct access data file created after
C a LOAD command and saves the following information:
C
C  - a list of function names in array Fname
C  - the record number of header card 1 for each function in array Frec
C  - the unit number of the file in which the function is located
C    (initialized to uData) in Funit
C  - the zone of each function in array Fzone
C  - the class of each function in array Fclass
C  - the multiplicity of each function in array Fmult
C  - the dimensionality of each function in array Fdim
C  - the total number of functions: Nfunction
C============================================================================
C
      SUBROUTINE RdFuncNames
      IMPLICIT NONE
      INTEGER I
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CHARACTER Line*80,List(Maxfunc)*12
C
C Loop through data file
C
      I = 1
      Nfunction = 0
      DO WHILE (I .LT. Nrecords)
C
C Look for leading "/"
C
        READ(uData,'(A)',REC=I,ERR=30) Line
        DO WHILE ((Line(1:1).EQ. '*' .OR. Line(1:1).NE.'/') .AND.
     &    (I .LT. Nrecords))
          I = I + 1
          READ(uData,'(A)',REC=I,ERR=30) Line
        ENDDO
C
C Remove any comment lines between "/FUNC" and the header
C
        IF (I .LT. Nrecords) THEN
          IF (Line(1:1) .EQ. '/') THEN
            I = I + 1
            READ(uData,'(A)',REC=I,ERR=30) Line
            DO WHILE (Line(1:1) .EQ. '*')
              I = I + 1
              READ(uData,'(A)',REC=I,ERR=30) Line
            ENDDO
          ENDIF
          CALL Upper(Line)
          Nfunction = Nfunction + 1
C
C Too many functions
C
          IF (Nfunction .GT. Maxfunc) THEN
            CALL Mess(' Error in RdFuncNames: Maximum number of function
     &s exceeded')
            Error = .TRUE.
            Data = .FALSE.
            RETURN
          ENDIF
C
C Fill arrays with data
C
          Fname(Nfunction) = Line(1:12)
          Frec(Nfunction) = I
          Funit(Nfunction) = uData
          Fzone(Nfunction) = Line(73:74)
          Fclass(Nfunction) = Line(77:78)
          READ(Line(75:76),'(BN,I2)',ERR=40) Fmult(Nfunction)
          Fdim(Nfunction) = 1
          IF (Line(64:66) .NE. '   ') THEN
            Fdim(Nfunction) = 2
            IF (Line(67:69) .NE. '   ') THEN
              Fdim(Nfunction) = 3
              IF (Line(70:72) .NE. '   ') Fdim(Nfunction) = 4
            ENDIF
          ENDIF
        ENDIF
      ENDDO
C
      IF (Nfunction .EQ. 0) THEN
        Data = .FALSE.
        CALL Mess(' Error in RdFuncNames: No functions were found in dat
     &a file')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Save the original number of functions loaded
C
      Nfuncp = Nfunction
C
C Check for duplicate table names
C
      DO I = 1,Nfunction
        List(I) = Fname(I)
      ENDDO
      CALL ShellSort(List,Nfunction,12)
      DO I = 1,Nfunction-1
        IF (List(I) .EQ. List(I+1)) THEN
          CALL Mess(' Warning: Duplicate table name exists for '//
     &             List(I+1))
        ENDIF
      ENDDO
C
      RETURN
30    CALL Mess(' Error in RdFuncNames: Unexpected error while reading d
     &ata file')
      Error = .TRUE.
      Data = .FALSE.
      RETURN
40    CALL Mess(' Error in RdFuncNames: Cannot decode function multiplic
     &ity - '//Fname(Nfunction))
      Error = .TRUE.
      Data = .FALSE.
      RETURN
      END
C
C============================================================================
C The function FindFunc searches for a specific function name within the list
C of function names. If found, it returns a value of .TRUE. and returns the
C index of the function (Index). Otherwise, it returns a value of .FALSE.
C and the function pointer remains unaltered.
C============================================================================
C
      FUNCTION FindFunc(Func_name,Index)
      IMPLICIT NONE
      LOGICAL FindFunc
      CHARACTER Func_name*(*)
      INTEGER I,Index
      INCLUDE 'finfo.inc'
C
      CALL Upper(Func_name)
      DO I = 1,Nfunction 
        IF (Func_name .EQ. Fname(I)) THEN
          FindFunc = .TRUE.
          Index = I
          RETURN
        ENDIF
      ENDDO
C
      FindFunc = .FALSE.
      RETURN
      END
C
C============================================================================
C The subroutine ListFunc searches the array of function names for one
C that matches the pattern "Func_name". Func_name may contain a wildcard
C character either before or after the name (not in between). The number of
C matching function names is returned in FlistSize and the list of these 
C functions is returned in Flist.
C===========================================================================
C
      SUBROUTINE ListFunc(Func_name,Flist,FlistSize)
      IMPLICIT NONE
      LOGICAL Wildcard1,Wildcard2,Blank
      CHARACTER*(*) Func_name*(*),Func*12,Flist(*)
      INTEGER I,J,K,L,Strln,FlistSize
      INCLUDE 'finfo.inc'
C
C Wildcard1 indicates a leading "*" in the pattern.
C Wildcard2 indicates a trailing "*" in the pattern.
C
      Wildcard1 = .FALSE.
      Wildcard2 = .FALSE.
      CALL Clear(Func)
      CALL Upper(Func_name)
      Func = Func_name
      FlistSize = 0
C
C Determine wildcard placement
C
      IF (Func(1:1) .EQ. '*') THEN
        Wildcard1 = .TRUE.
        DO I = 1,LEN(Func_name)-1
          Func(I:I) = Func(I+1:I+1)
        ENDDO
        Func(I:I) = ' '
      ENDIF
      I = INDEX(Func,'*')
      IF (I .GT. 0) THEN
        Wildcard2 = .TRUE.
        Func(I:I) = ' '
      ENDIF
      L = Strln(Func)
      Blank = Func(1:1) .EQ. ' '
C
C Pattern has leading and trailing wildcards
C
      IF (Wildcard1 .AND. Wildcard2) THEN
        DO I = 1,Nfunction 
          IF (INDEX(Fname(I),Func(1:L)).GT.0 .OR. Blank) THEN
            FlistSize = FlistSize + 1
            Flist(FlistSize) = Fname(I)
          ENDIF
        ENDDO
C
C Pattern has a leading wildcard only
C
      ELSEIF (Wildcard1) THEN 
        DO I = 1,Nfunction 
          K = Strln(Fname(I))
          IF (K .GE. L) THEN
            IF (Fname(I)(K-L+1:).EQ.Func(1:L) .OR. Blank) THEN
              FlistSize = FlistSize + 1
              Flist(FlistSize) = Fname(I)
            ENDIF
          ENDIF
        ENDDO
C
C Pattern has a trailing wildcard
C
      ELSEIF (Wildcard2) THEN 
        DO I = 1,Nfunction 
          K = Strln(Fname(I))
          IF (K .GE. L) THEN
            IF (Fname(I)(1:L).EQ.Func(1:L) .OR. Blank) THEN
              FlistSize = FlistSize + 1
              Flist(FlistSize) = Fname(I)
            ENDIF
          ENDIF
        ENDDO
C
C Pattern has no wildcards
C
      ELSE
        DO I = 1,Nfunction 
          IF (Func.EQ.Fname(I) .OR. Blank) THEN
            FlistSize = FlistSize + 1
            Flist(FlistSize) = Fname(I)
          ENDIF
        ENDDO
      ENDIF
C
      RETURN
      END
C
C============================================================================
C The subroutine LoadFuncInfo loads complete information on the function
C into memory. It reads the function information from the data file
C whose unit number corresponds to Funit for that particular function.
C If the parameter Readval is set to .TRUE., it also reads in the actual
C function values. Otherwise, it reads in only the header.
C============================================================================
C
      SUBROUTINE LoadFuncInfo(Readval)
      IMPLICIT NONE
      CHARACTER Line*80
      INTEGER I,J,Rpos
      LOGICAL Readval
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
C
      CALL ClearFuncInfo
      Function = Fname(Findex)
      Rpos = Frec(Findex)
      READ (Funit(Findex),'(A)',REC=Rpos,ERR=10) Line
      CALL Upper(Line)
C
C Read the schedule names
C
      Schedule_name(1) = Line(13:24)
      Schedule_name(2) = Line(25:36)
      Schedule_name(3) = Line(37:48)
      Schedule_name(4) = Line(49:60)
C
C Read the zone, class and multiplicity of the function
C
      Zone = Line(73:74)
      Class = Line(77:78)
      READ(Line(75:76),'(BN,I2)') Mult
      IF (Mult .GT. Maxmult) THEN
        CALL Mess(' Error in LoadFuncInfo: Function multiplicity too lar
     &ge')
        Error = .TRUE.
        Select = .FALSE.
        CALL ClearFuncInfo
        RETURN
      ENDIF
C
C Read the dimension and breakpoint schedule sizes of the function
C
      Dimension = 1
      READ(Line(61:63),'(BN,I3)') Schedule_size(1)
      IF (Line(64:66) .NE. '   ') THEN
        READ(Line(64:66),'(BN,I3)')  Schedule_size(2)
        Dimension = 2
      ELSE
        Schedule_size(2) = 1
      ENDIF
      IF (Line(67:69) .NE. '   ') THEN
        READ(Line(67:69),'(BN,I3)')  Schedule_size(3)
        Dimension = 3
      ELSE
        Schedule_size(3) = 1
      ENDIF
      IF (Line(70:72) .NE. '   ') THEN
        READ(Line(70:72),'(BN,I3)')  Schedule_size(4)
        Dimension = 4
      ELSE
        Schedule_size(4) = 1
      ENDIF
      DO I = 1,Mult
        Rpos = Rpos + 1
        CALL Clear(Line)
        READ(Funit(Findex),'(A)',REC=Rpos) Line
        CALL Upper(Line)
        READ(Line,'(5A12)') (Variable_name(I,J), J = 0,Dimension)
      ENDDO
C
C Calculate the total number of function values
C
      Nvalues = Schedule_size(1) * Schedule_size(2)
     &        * Schedule_size(3) * Schedule_size(4)
      IF (Nvalues .GT. Maxval) THEN
        CALL Mess(' Error in LoadFuncInfo: Function too large to process
     &')
        Error = .TRUE.
        Select = .FALSE.
        CALL ClearFuncInfo
        RETURN
      ENDIF
C
C Read the X breakpoint schedule
C
      CALL RdBreakpt(Schedule_size(1),1)
C
C Read the Y breakpoint schedule
C
      IF (Schedule_size(2) .GT. 1) THEN
        CALL RdBreakpt(Schedule_size(2),2)
      ENDIF
C
C Read the Z breakpoint schedule
C
      IF (Schedule_size(3) .GT. 1) THEN
        CALL RdBreakpt(Schedule_size(3),3)
      ENDIF
C
C Read the T breakpoint schedule
C
      IF (Schedule_size(4) .GT. 1) THEN
        CALL RdBreakpt(Schedule_size(4),4)
      ENDIF
C
C Read the function values
C
      IF (Readval) CALL RWFuncVal('R')
C
      RETURN
10    CALL Mess(' Error in LoadFuncInfo: Unexpected error while reading 
     &data file')
      CALL Mess(' Function in Error: '//Function)
      Error = .TRUE.
      Select = .FALSE.
      CALL ClearFuncInfo
      RETURN
      END
C
C============================================================================
C The subroutine RdBreakpt reads a breakpoint schedule. If the schedule
C size is not a multiple of 5, then the rest of the line must be left empty;
C i.e. the next schedule must begin on a new line.
C
CRW Sept 3, 1991
C
CRW This is not good Nick...  If the schedule size is not a multiple of 5,
CRW then the next schedule MIGHT start on the next line or it might start
CRW on the last line of the last schedule.  Standard FGEN format accepts
CRW both.
C============================================================================
C
      SUBROUTINE RdBreakpt(Size,Schedule)
      IMPLICIT  NONE
      INTEGER   No_of_lines,Remainder,I,J,K,Size,Schedule,Rpos
      INTEGER   Fields, Missing/0/
      REAL      Missing_values(5)
      CHARACTER Line*80
C
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
C
      No_of_lines = Size / 5
      Remainder = Size - No_of_lines * 5
      K = -5
C
C Position the record pointer
C
      IF (Schedule .EQ. 1) THEN
        Rpos = Frec(Findex) + Mult + 1
      ELSE
        Rpos = Frec(Findex) + Mult + 1
        DO I = 2,Schedule
          Rpos = Rpos + Schedule_size(I-1)/5
          IF (MOD(Schedule_size(I-1),5) .NE. 0) Rpos = Rpos + 1
        ENDDO
      ENDIF
C
C If values from this schedule are missing recover them from the
C static array 'Missing_values'.
C
      IF (Missing.GT.0) THEN
        K = K + Missing
        J = 1
        DO I = 5-Missing,5
          Brkpt_schedule(Schedule,J) = Missing_values(I)
          J = J + 1
        ENDDO
      ENDIF
C
C Read lines that have 5 breakpoints listed
C
      IF (No_of_lines .NE. 0) THEN 
        DO I = 1,No_of_lines
          K = K + 5
          CALL Clear(Line)
          READ(Funit(Findex),'(A)',REC=Rpos) Line
          Rpos = Rpos + 1
          READ(Line,*,ERR=10) (Brkpt_schedule(Schedule,K+J), J=1,5)
        ENDDO
      ENDIF
C
C Read lines with less than 5 values listed
C
      IF (Remainder .NE. 0) THEN
        K = K + 5
        CALL Clear(Line)
        READ(Funit(Findex),'(A)',REC=Rpos) Line
        Rpos = Rpos + 1
        READ (Line,*,ERR=10)
     &       (Brkpt_schedule(Schedule,K+J),J=1,Remainder)
      ENDIF
      IF (Fields(Line).EQ.5 .AND. Remainder.NE.0) THEN
        Missing = 5 - Remainder
        READ(Line,*,ERR=10) (Missing_values(J), J=1,5)
      ELSE
        Missing = 0
      ENDIF
      RETURN
10    CALL Mess(' Error in RdBreakpt: Unexpected error while reading dat
     &a file')
      Error = .TRUE.
      Select = .FALSE.
      CALL ClearFuncInfo
      RETURN
      END
C
C========================= FIELDS =====================================
C This function returns the number of field (that are separated by
C blanks) in a string.
C======================================================================
      FUNCTION Fields(String)
      INTEGER  Fields
      CHARACTER*(*) String
      LOGICAL Blank/.FALSE./
C
      IF (String(1:1).NE.' ') THEN
        Fields = 1
      ELSE
        Fields = 0
      ENDIF
      DO I = 1,LEN(String)
        IF (String(I:I).EQ.' ') THEN
          Blank = .TRUE.
        ELSE
          Blank = .FALSE.
        ENDIF
        IF (Blank .AND. String(I+1:I+1) .NE. ' '
     *      .AND. I+1 .LE. LEN(String)) Fields = Fields + 1
      ENDDO
      RETURN
      END
C
C============================================================================
C The subroutine WrtBreakpt formats the breakpoint schedule into a user
C friendly format and sends it to the screen and/or journal file.
C============================================================================
C
      SUBROUTINE WrtBreakpt(Size,Schedule)
      IMPLICIT NONE
      INTEGER No_of_lines,Remainder,I,J,K,Size,Schedule
      CHARACTER*80 Output
      INCLUDE 'finfo.inc'
      No_of_lines = Size / 5
      Remainder = Size - No_of_lines * 5
      K = -5
      IF (No_of_lines .NE. 0) THEN 
        DO I = 1,No_of_lines
          K = K + 5
          WRITE(Output,'(5G12.5)')(Brkpt_schedule(Schedule,K+J),J=1,5)
          CALL Mess(' '//Output)
        ENDDO
      ENDIF
      IF (Remainder .NE. 0) THEN
        K = K + 5
        WRITE (Output,'(5G12.5)')
     &        (Brkpt_schedule(Schedule,K+J),J=1,Remainder)
        CALL Mess(' '//Output)
      ENDIF
      RETURN
      END
C
C============================================================================
C The subroutine RWFuncVal reads and writes function values to/from the value
C of Funit for the current function.
C If the "Option" parameter is 'W' (write) then the values are output 5 per 
C line in E14.7 format and separated by a single space. The values are
C written starting where the pointer NextRec is positioned + 1. At the end
C of the subroutine, NextRec is positioned at the last record that was 
C written.   
C If the "Option" parameter is 'R' (read) then the values are read in a
C manner simulating a FORTRAN free format read. These values are stored
C linearly in the array Fvalues.
C============================================================================
C
      SUBROUTINE RWFuncVal(Option)
      IMPLICIT NONE
      CHARACTER Line*80
      INTEGER   No_of_lines,Remainder,I,J,K,L,M,N
      INTEGER   Offset,N1,N2,N3,N4,Rpos
      INTEGER   Fields
      INCLUDE   'inout.inc'
      INCLUDE   'finfo.inc'
      CHARACTER*1 Option
C
C Write function values
C
      Offset = 0
      IF (Option .EQ. 'W') THEN
        No_of_lines = Schedule_size(1) / 5
        Remainder = Schedule_size(1) - No_of_lines * 5
        DO L = 1,Schedule_size(4)
          N4 = (L-1)*Schedule_size(3)*Schedule_size(2)*Schedule_size(1)
          DO K = 1,Schedule_size(3)
            N3 = (K-1)*Schedule_size(2)*Schedule_size(1)
            DO J = 1,Schedule_size(2)
              N2 = (J-1) * Schedule_size(1)
              IF (No_of_lines .NE. 0) THEN 
                DO I = 1,No_of_lines
                  N1 = (I-1) * 5
                  Offset = N1 + N2 + N3 + N4
                  IF (Process) THEN
                    WRITE(uProcess,'(5(1X,E14.7))',ERR=20) 
     &                   (Fvalues(N+Offset),N=1,5)
                  ELSE
                    NextRec = NextRec + 1
                    CALL Clear(Line)
                    WRITE(Line,'(5(1X,E14.7))') 
     &                   (Fvalues(N+Offset),N=1,5)
                    WRITE(Funit(Findex),'(A)',REC=NextRec) Line
                  ENDIF
                ENDDO
              ENDIF
              IF (Remainder .NE. 0) THEN
                IF (No_of_lines .NE. 0) Offset = Offset + 5
                IF (Process) THEN
                  WRITE(uProcess,'(5(1X,E14.7))',ERR=20)
     &                 (Fvalues(N+Offset),N=1,Remainder)
                ELSE
                  NextRec = NextRec + 1
                  CALL Clear(Line)
                  WRITE(Line,'(5(1X,E14.7))')
     &                 (Fvalues(N+Offset),N=1,Remainder)
                  WRITE(Funit(Findex),'(A)',REC=NextRec) Line
                ENDIF
                IF (No_of_lines .EQ. 0) Offset = Offset+Schedule_size(1)
              ENDIF
            ENDDO
          ENDDO
        ENDDO
        NextRec = NextRec + 1
C
C Read function values
C
      ELSEIF (Option .EQ. 'R') THEN
C
C Position the record pointer
C
        Rpos = Frec(Findex) + Mult + 1
        DO I = 1,Dimension
          Rpos = Rpos + Schedule_size(I)/5
          IF (MOD(Schedule_size(I),5) .NE. 0) Rpos = Rpos + 1
        ENDDO
C
C Loop through function values
C
        L = Schedule_size(4)*Schedule_size(3)*
     &      Schedule_size(2)*Schedule_size(1)
        Offset = 0
C
        DO WHILE (Offset .NE. L)
          READ(Funit(Findex),'(A)',REC=Rpos,ERR=10) Line
          READ(Line,*,ERR=10)
     &          (Fvalues(N+Offset),N=1,Fields(Line))
          Offset = Offset + Fields(Line)
          Rpos = Rpos + 1
        ENDDO
      ENDIF
      RETURN
C
C Try list directed I/O on function values
C
10    CONTINUE
C
C Position the record pointer
C
      Rpos = Frec(Findex) + Mult + 1
      DO I = 1,Dimension
        Rpos = Rpos + Schedule_size(I)/5
        IF (MOD(Schedule_size(I),5) .NE. 0) Rpos = Rpos + 1
      ENDDO
C
C Loop through function values
C
      No_of_lines = Nvalues / 5
      Remainder = Nvalues - No_of_lines * 5
      IF (No_of_lines .NE. 0) THEN 
        DO I = 1,No_of_lines
          Offset = 5 * (I-1)
          CALL Clear(Line)
          READ(Funit(Findex),'(A)',REC=Rpos,ERR=30) Line
          READ(Line,'(5(1X,E14.7))',ERR=30) (Fvalues(N+Offset),N=1,5)
          Rpos = Rpos + 1
        ENDDO
      ENDIF
      IF (Remainder .NE. 0) THEN
        Offset = No_of_lines * 5
        CALL Clear(Line)
        READ(Funit(Findex),'(A)',REC=Rpos,ERR=30) Line
        READ(Line,'(5(1X,E14.7))',ERR=30)
     &            (Fvalues(N+Offset),N=1,Remainder)
        Rpos = Rpos + 1
      ENDIF
      RETURN
C
C Could not read function values
C
30    CALL Mess(' Error in RWFuncVal: Unexpected error while reading fun
     &ction values')
      Error = .TRUE.
      Select = .FALSE.
      CALL ClearFuncInfo
      RETURN
20    CALL Mess(' Error in RWFuncVal: Unexpected error while writing fun
     &ction values')
      Error = .TRUE.
      CLOSE(uProcess,STATUS='DELETE')
      Process = .FALSE.
      RETURN
      END
C
C===========================================================================
C The subroutine ShowOutput parses the rest of the line following a SHOW
C command to get the command qualifier. It then outputs the requested
C information to the screen for the currently selected function. This
C includes:
C
C  - the breakpoint schedules
C  - the input variables
C  - the breakpoint schedule names
C  - the function name
C  - dimension, zone, multiplicity and class
C
C If a dimension specifier (X,Y,Z or T) follows the command qualifier,
C the information is displayed for that dimension only.
C===========================================================================
C
      SUBROUTINE ShowOutput(Noun,Input)
      IMPLICIT NONE
      INTEGER I,J,Len,K,Strln
      CHARACTER*(*) Noun,Input
      CHARACTER Label(0:4)*4,Dim*1,Output*80
      LOGICAL Getcmd
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      DATA Label/' F: ',' X: ',' Y: ',' Z: ',' T: '/
C
C Get dimension specifier, if any
C
      IF (Getcmd(Dim,Input)) THEN
        CALL Upper(Dim)
        IF (Dim .EQ. 'F') THEN
          I = 0
        ELSEIF (Dim .EQ. 'X') THEN
          I = 1
        ELSEIF (Dim.EQ.'Y' .AND. Dimension.GT.1) THEN
          I = 2
        ELSEIF (Dim.EQ.'Z' .AND. Dimension.GT.2) THEN
          I = 3
        ELSEIF (Dim.EQ.'T' .AND. Dimension.GT.3) THEN
          I = 4
        ELSE
          CALL Mess(' Show Error: Illegal dimension specifier')
          Error = .TRUE.
          RETURN
        ENDIF
      ELSE
        I = -1
      ENDIF
C
C Show breakpoint schedule
C
      CALL Upper(Noun)
      IF (Noun .EQ. 'BREAKPOINTS') THEN
        IF (I .GT. 0) THEN
          CALL Mess(' '//Variable_name(1,I)(1:Strln(Variable_name(1,I)))
     &                 //' breakpoint schedule')
          CALL WrtBreakpt(Schedule_size(I),I)
        ELSEIF (I .EQ. 0) THEN
          CALL Mess(' Error: Illegal dimension specifier')
          Error = .TRUE.
        ELSE                 ! dimension not specified; show all dimensions
          DO I = 1,Dimension
            CALL Mess(' '//Variable_name(1,I)
     &            (1:Strln(Variable_name(1,I)))//' breakpoint schedule')
            CALL WrtBreakpt(Schedule_size(I),I)
          ENDDO
        ENDIF
C
C Show variable names
C
      ELSEIF (Noun .EQ. 'VARIABLE') THEN
        IF (I .GE. 0) THEN
          DO J = 1,Mult
            CALL Mess(' '//Label(I)//Variable_name(J,I))
          ENDDO
        ELSE
          DO J = 1,Mult
            WRITE (Output,'(5A16)') 
     &            (Label(K)//Variable_name(J,K),K=0,Dimension)
            CALL Mess(' '//Output)
          ENDDO
        ENDIF
C
C Show breakpoint schedule names
C
      ELSEIF (Noun .EQ. 'SCHEDULE') THEN
        IF (I .GT. 0) THEN
          CALL Mess(' '//Label(I)//Schedule_name(I))
        ELSEIF (I .EQ. 0) THEN
          CALL Mess(' Error: Illegal dimension specifier')
          Error = .TRUE.
        ELSE
          WRITE(Output,*) (Label(J)//Schedule_name(J),J=1,Dimension)
          CALL Mess(' '//Output)
        ENDIF
C
C Show function name
C
      ELSEIF (Noun .EQ. 'FUNCTION') THEN
        CALL Mess(' Selected function: '//Function)
C
C Show dimension
C
      ELSEIF (Noun .EQ. 'DIMENSION') THEN
        WRITE(Output,'(I1)') Dimension
        CALL Mess(' Dimension : '//Output)
C
C Show multiplicity
C
      ELSEIF (Noun .EQ. 'MULTIPLICITY') THEN
        WRITE(Output,'(I2)') Mult
        CALL Mess(' Multiplicity : '//Output)
C
C Show class
C
      ELSEIF (Noun .EQ. 'CLASS') THEN
        CALL Mess(' Class : '//Class)
C
C Show zone
C
      ELSEIF (Noun .EQ. 'ZONE') THEN
        CALL Mess(' Zone : '//Zone)
C
C Show size
C
      ELSEIF (Noun .EQ. 'SIZE') THEN
        WRITE(Output,'(I5)') Nvalues
        CALL Mess(' Total number of function values: '//Output(1:5))
C
C Show program limits
C
      ELSEIF (Noun .EQ. 'LIMITS') THEN
        WRITE(Output,'(I4)') Maxfunc
        CALL Mess(' Maximum number of function tables: '//Output(1:4))
        WRITE(Output,'(I6)') Maxval
        CALL Mess(' Maximum number of function values: '//Output(1:6))
        WRITE(Output,'(I4)') Maxbp
        CALL Mess('  Maximum breakpoint schedule size: '//Output(1:4))
        WRITE(Output,'(I3)') Maxmult
        CALL Mess('     Maximum function multiplicity: '//Output(1:3))
C
C Show modified functions
C
      ELSEIF (Noun .EQ. 'MODIFICATIONS') THEN
        DO I = 1,Nfunction
          CALL Clear(Output)
          Output(1:12) = Fname(I)
          IF (Fdelete(I)) THEN
            Output(17:23) = 'Deleted'
          ELSE
            IF (I .GT. Nfuncp) THEN
              Output(17:23) = 'Created'
            ELSE
              IF (Funit(I) .EQ. uData) THEN
                Output(17:26) = 'Unmodified'
              ELSE
                Output(17:24) = 'Modified'
              ENDIF
            ENDIF
          ENDIF
          CALL Mess(' '//Output)
        ENDDO
C
C Unknown show qualifier
C
      ELSE
        CALL Mess(' Show Error: Unknown command qualifier')
        Error = .TRUE.
      ENDIF
C
      RETURN
      END
C
C===========================================================================
C The function Getcmd takes an input string and returns the first word
C in the string delimited by blanks. If found, Getcmd returns a value of
C .TRUE.. The word is then stripped from the input string, which is modified.
C If no word was found, Getcmd returns a value of .FALSE..
C===========================================================================
C
      FUNCTION Getcmd(Cmd,Input)
      IMPLICIT NONE
      LOGICAL Getcmd
      CHARACTER*(*) Cmd,Input,Input2*80
      INTEGER I,J,Maxlen,LEN
      CHARACTER*1 Blank/' '/
      Getcmd = .FALSE.
      CALL Strip(Input)                         ! strip leading blanks
      CALL Clear(Cmd)
C
C Is there anything in the input string ?
C
      DO I = 1,LEN(Input)
        IF (Input(I:I) .NE. Blank) Getcmd = .TRUE.
      ENDDO
C
C Yes, there is
C
      IF (Getcmd) THEN
        Maxlen = LEN(Cmd)
        I = 1
        DO WHILE (Input(I:I).NE.Blank .AND. I.LE.LEN(Input))
          I = I + 1
        ENDDO
        Cmd = Input(1:MIN0(I-1,Maxlen))
C
C If the first word found in the input string is longer than the target
C string, truncate it and print a message
C
        IF (I-1 .GT. Maxlen) THEN
          J = MIN0(80,I-1)
          Input2 = Input(1:J)
          CALL Mess (' Getcmd: Input parameter too long, truncated <'
     &      //Input2(1:J)//'>')
        ENDIF
        Input = Input(I:)
      ELSE
        CALL Clear(Cmd)
      ENDIF
      RETURN
      END
C
C
C==========================================================================
C The subroutine ClearFuncInfo clears all the function header information.
C==========================================================================
C
      SUBROUTINE ClearFuncInfo
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INTEGER I,J
      CHARACTER Blank12*12/'            '/
      Class = '  '
      Zone  = '  '
      Function = Blank12
      DO I = 1,4
        Schedule_name(I) = Blank12
        Schedule_size(I) = 1
        DO J = 1,150
          Brkpt_schedule(I,J) = 0.0
        ENDDO
      ENDDO
      DO I = 0,4
        DO J = 1,20
          Variable_name(J,I) = Blank12
        ENDDO
      ENDDO
      Dimension = 0
      Mult = 0
      RETURN
      END
C
C===========================================================================
C The subroutine Init1 initializes file unit numbers and file open flags.
C===========================================================================
C
      SUBROUTINE Init1
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      Screen   = 6
      Keyboard = 5
      uRead    = Keyboard
      uBatch   = 10
      uJournal = 11
      uData    = 12
      uHelp    = 13
      uScratch = 14
      uPlot    = 15
      uProcess = 16
      Batch    = .FALSE.
      Journal  = .FALSE.
      Data     = .FALSE.
      Help     = .FALSE.
      Scratch  = .FALSE.
      Plot     = .FALSE.
      Process  = .FALSE.
      Findex   = 1
      RETURN
      END
C
C===========================================================================
C The subroutine CloseAll closes all open files. If a plot file has been
C created, it submits it for printing.
C===========================================================================
C
      SUBROUTINE CloseAll
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CLOSE (UNIT=uBatch)
      Batch = .FALSE.
      CLOSE (UNIT=uData,STATUS='DELETE')
      Data = .FALSE.
      CLOSE (UNIT=uJournal)
      Journal = .FALSE.
      CLOSE (UNIT=uHelp)
      Help = .FALSE.
      CLOSE (UNIT=uScratch,STATUS='DELETE')
      Scratch = .FALSE.
      CLOSE (UNIT=uPlot)
      IF (Plot) CALL SysCmd('PRINT/NOFEED/QUEUE=GRAPHICS fgu.plt')
      Plot = .FALSE.
      CLOSE (UNIT=uProcess)
      Process = .FALSE.
      Select = .FALSE.
      RETURN
      END
C
C===========================================================================
C The function Strln returns the length of a character string by searching
C for the first blank space in the string.
C===========================================================================
C
      FUNCTION Strln(String)
      IMPLICIT NONE
      CHARACTER*(*) String,Blank*1/' '/
      INTEGER I,strln
      I = 1
      DO WHILE (String(I:I).NE.Blank .AND. I.LE.LEN(String))
        I = I + 1
      ENDDO
      Strln = I - 1
      RETURN
      END
C
C==========================================================================
C The subroutine Clear replaces all characters in the input string with
C blanks. Maximum length of the input string is 1000 characters.
C==========================================================================
C
      SUBROUTINE Clear(String)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      CHARACTER*(*) String,Blank*1000
      INTEGER LEN
      DATA Blank/'                                                        
     &                                                                   
     &                                                                     
     &                                    '/
      IF (LEN(String) .GT. LEN(Blank)) THEN
        CALL Mess(' Error: Internal error in subroutine Clear')
        Error = .TRUE.
      ELSE
        String = Blank(1:LEN(String))
      ENDIF
      RETURN
      END
C
C==========================================================================
C Given the X,Y,Z and T breakpoint values for a given function, the
C function Ptr returns the position of the function value within the 
C linear array Fvalues.
C==========================================================================
C
      FUNCTION Ptr(X,Y,Z,T)
      IMPLICIT NONE
      INTEGER Ptr,X,Y,Z,T
      INCLUDE 'finfo.inc'
      Ptr = (T-1) * Schedule_size(3)*Schedule_size(2)*Schedule_size(1)
     &    + (Z-1) * Schedule_size(2)*Schedule_size(1)
     &    + (Y-1) * Schedule_size(1)
     &    + X
      RETURN
      END
C
C===========================================================================
C The subroutine SWITCH switches the order of two function dimensions
C such that f(x,y,z,t) becomes f(y,x,z,t) for example. Any two dimensions
C can be switched and the switch command can be used any number of times.
C===========================================================================
C
      SUBROUTINE Switch(Dim1,Dim2)
      IMPLICIT NONE
      INTEGER I,N,Dim1,Dim2,Loop(4),Ptr,K(4),J(4),K1,K2,K3,K4,Temp2
      CHARACTER Temp*12
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      REAL Temp3(Maxbp)
      EQUIVALENCE (K1,K(1)),(K2,K(2)),(K3,K(3)),(K4,K(4))
C
C Note: K(1..4) and K1,K2,K3,K4 are equivalenced because FORTRAN does
C not allow array elements to be used as do loop indicies.
C
C Initialize loops
C
      DO I = 1,4
        Loop(I) = Schedule_size(I)
        J(I) = I
      ENDDO
C
C Set up loops for dimensions which are being switched
C
      Loop(Dim1) = Schedule_size(Dim2)
      Loop(Dim2) = Schedule_size(Dim1)
      J(Dim1) = Dim2
      J(Dim2) = Dim1
C
C Switch the function values
C
      N = 0
      DO K4 = 1,Loop(4)
        DO K3 = 1,Loop(3)
          DO K2 = 1,Loop(2)
            DO K1 = 1,Loop(1)
              N = N + 1
              Ftemp(N) = Fvalues(Ptr(K(J(1)),K(J(2)),K(J(3)),K(J(4))))
            ENDDO
          ENDDO
        ENDDO
      ENDDO
C
      DO I = 1,Nvalues
        Fvalues(I) = Ftemp(I)
      ENDDO
C
C Switch the schedule names
C
      Temp = Schedule_name(Dim1)
      Schedule_name(Dim1) = Schedule_name(Dim2)
      Schedule_name(Dim2) = Temp
C
C Switch the variable names
C
      DO I = 1,Mult
        Temp = Variable_name(I,Dim1)
        Variable_name(I,Dim1) = Variable_name(I,Dim2)
        Variable_name(I,Dim2) = Temp
      ENDDO
C
C Switch the breakpoint schedules
C
      DO I = 1,Schedule_size(Dim1)
        Temp3(I) = Brkpt_schedule(Dim1,I)
      ENDDO
      DO I = 1,Schedule_size(Dim2)
        Brkpt_schedule(Dim1,I) = Brkpt_schedule(Dim2,I)
      ENDDO
      DO I = 1,Schedule_size(Dim1)
        Brkpt_schedule(Dim2,I) = Temp3(I)
      ENDDO
C
C Switch the breakpoint schedule size
C
      Temp2 = Schedule_size(Dim1)
      Schedule_size(Dim1) = Schedule_size(Dim2)
      Schedule_size(Dim2) = Temp2
C
      Funit(Findex) = uScratch
      RETURN
      END
C
C===========================================================================
C The function ZoneCheck checks that the input zone is between 0 and FF 
C hex (0 and 255 decimal).
C===========================================================================
C
      FUNCTION ZoneCheck(Zone)
      IMPLICIT NONE
      LOGICAL ZoneCheck
      CHARACTER Zone*(*),Temp*2
      INTEGER C,I,N,Decimal,Strln
      IF (Strln(Zone) .EQ. 1) THEN
        Temp = '0'//Zone(1:1)
        Zone = Temp
      ENDIF
      Decimal = 0
      DO I = 2,1,-1
        C = ICHAR(Zone(I:I))
        IF (C.GE.48 .AND. C.LE.57) THEN
          N = C - 48
        ELSEIF (C.GE.65 .AND. C.LE.70) THEN
          N = C - 55
        ELSE
          ZoneCheck = .FALSE.
          RETURN
        ENDIF
        Decimal = Decimal + N * 16**(2-I)
      ENDDO
      IF (Decimal.GE.0 .AND. Decimal.LE.255) THEN
        ZoneCheck = .TRUE.
      ELSE
        ZoneCheck = .FALSE.
      ENDIF
      RETURN
      END
C==========================================================================
C The function ClassCheck checks that the input class is between 0 and 99.
C==========================================================================
C
      FUNCTION ClassCheck(Class)
      IMPLICIT NONE
      LOGICAL ClassCheck
      CHARACTER Class*(*)
      INTEGER ClassNum
      READ(Class,'(BN,I2)',ERR=10) ClassNum
      IF (Class .EQ. '  ') ClassNum = 0
      IF (ClassNum.GE.0 .AND. ClassNum.LE.99) THEN
        ClassCheck = .TRUE.
      ELSE
        ClassCheck = .FALSE.
      ENDIF
      RETURN
 10   CONTINUE
      ClassCheck = .FALSE.
      RETURN
      END
C
C==========================================================================
C The subroutine SaveFunc saves function information to the file designated
C by Funit(Findex). The function information is saved starting at NextRec.
C==========================================================================
C
      SUBROUTINE SaveFunc
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      CHARACTER Header(Maxmult+2)*80,Line*80
      INTEGER No_of_lines,Remainder,I,J,K,Size,Dim
C
C Open scratch file if not already open
C
      IF (.NOT. Scratch) THEN
        IF (.NOT. openW(uScratch,'fgu2.tmp','NEW','DIRECT',
     &     'FORMATTED',80)) THEN
          CALL Mess(' Error in SaveFunc: Cannot open scratch file')
          Error = .TRUE.
          RETURN
        ENDIF
        Scratch = .TRUE.
        NextRec = 1
      ENDIF
C
C Reassign record number for this function
C
      IF (.NOT. Process) Frec(Findex) = NextRec
C
C Create header card 1
C
      CALL Clear(Header(1))
      Header(1)(1:12)  = Fname(Findex)
      Header(1)(13:24) = Schedule_name(1)
      Header(1)(25:36) = Schedule_name(2)
      Header(1)(37:48) = Schedule_name(3)
      Header(1)(49:60) = Schedule_name(4)
      WRITE(Header(1)(61:63),'(I3)') Schedule_size(1)
      IF (Dimension .GE. 2) THEN
        WRITE(Header(1)(64:66),'(I3)') Schedule_size(2)
      ELSE
        Header(1)(64:66) = '   '
      ENDIF
      IF (Dimension .GE. 3) THEN
        WRITE(Header(1)(67:69),'(I3)') Schedule_size(3)
      ELSE
        Header(1)(67:69) = '   '
      ENDIF
      IF (Dimension .EQ. 4) THEN
        WRITE(Header(1)(70:72),'(I3)') Schedule_size(4)
      ELSE
        Header(1)(70:72) = '   '
      ENDIF
      Header(1)(73:74) = Zone
      WRITE(Header(1)(75:76),'(I2)') Mult
      Header(1)(77:78) = Class
C
C Create header cards 2 -> Mult+1
C
      DO I = 1,Mult
        CALL Clear(Header(I+1))
        WRITE(Header(I+1),'(5A12)') (Variable_name(I,J),J=0,4)
      ENDDO
C
C Write header
C
      IF (Process) THEN
        WRITE(uProcess,'(A)',ERR=20) Header(1)
      ELSE
        WRITE(Funit(Findex),'(A)',REC=NextRec) Header(1)
      ENDIF
      DO I = 1,Mult
        IF (Process) THEN
          WRITE(uProcess,'(A)',ERR=20) Header(1+I)
        ELSE
          NextRec = NextRec + 1
          WRITE(Funit(Findex),'(A)',REC=NextRec) Header(1+I)
        ENDIF
      ENDDO
C
C Write the breakpoint values
C
      DO Dim = 1,Dimension
        Size = Schedule_size(Dim)
        No_of_lines = Size / 5
        Remainder = Size - No_of_lines * 5
        K = -5
        IF (No_of_lines .NE. 0) THEN 
          DO I = 1,No_of_lines
            K = K + 5
            IF (Process) THEN
              WRITE(uProcess,'(5(1X,E14.7))',ERR=20)
     &             (Brkpt_schedule(Dim,K+J),J=1,5)
            ELSE
              NextRec = NextRec + 1
              CALL Clear(Line)
              WRITE(Line,'(5(1X,E14.7))')
     &             (Brkpt_schedule(Dim,K+J),J=1,5)
              WRITE(Funit(Findex),'(A)',REC=NextRec) Line
            ENDIF
          ENDDO
        ENDIF
        IF (Remainder .NE. 0) THEN
          K = K + 5
          IF (Process) THEN
            WRITE (uProcess,'(5(1X,E14.7))',ERR=20)
     &            (Brkpt_schedule(Dim,K+J),J=1,Remainder)
          ELSE
            NextRec = NextRec + 1
            CALL Clear(Line)
            WRITE(Line,'(5(1X,E14.7))',ERR=20)
     &           (Brkpt_schedule(Dim,K+J),J=1,Remainder)
            WRITE(Funit(Findex),'(A)',REC=NextRec) Line
          ENDIF
        ENDIF
      ENDDO
C
C Write the function values
C
      CALL RWFuncVal('W')
      RETURN
C
20    CALL Mess(' Error in SaveFunc: Unexpected error while writing head
     &er card information')
      Error = .TRUE.
      CLOSE(uProcess,STATUS='DELETE')
      Process = .FALSE.
      RETURN
      END
C
C==========================================================================
C The subroutine PlotSetup sets up the inputs required by the PLOTTER
C subroutine and then calls it to generate a plot file.
C==========================================================================
C
      SUBROUTINE PlotSetup(Xgrid,Ygrid,Xmin,Ymin,Xmax,Ymax,Xlen,Ylen,
     &                     Ref,CIRCLE,GRID,INTERP)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      REAL Xgrid,Ygrid,Xmin,Ymin,Xmax,Ymax,Xlen,Ylen
      CHARACTER Ref*80,Tmp*80,Output1*10,Output2*10
      INTEGER I,J,K,L,Ptr,Strln
      INTEGER Maxplot
      PARAMETER (Maxplot = 50)
C
      Character  TITLE*80
      Character  XAXIS(Maxplot+1)*10, YAXIS(Maxplot+1)*10
      Real*4     X(Maxplot,1500),Y(Maxplot,1500),YMAX1(Maxplot)
      Real*4     XMIN1(Maxplot),XMAX1(Maxplot),YMIN1(Maxplot),XI
      Real*4     XINCH,YINCH
      Integer*4  PLOTS(Maxplot),N(Maxplot),NYI,NPLOTS
      Logical*4  GRID, INTERP, CIRCLE
C
C Arrays of X and Y axis plot values
C
      DO L = 1,Schedule_size(4)
        DO K = 1,Schedule_size(3)
          DO J = 1,Schedule_size(2)
            DO I = 1,Schedule_size(1)
              Y(J,I) = Fvalues(Ptr(I,J,K,L))
              X(J,I) = Brkpt_schedule(1,I)
            ENDDO
C
C Number of x axis plot values
C
            N(J) = Schedule_size(1)
C
C Array of plot numbers to be overlaid
C
            PLOTS(J) = J
C
C Axis min and max
C
            XMIN1(J) = Xmin
            XMAX1(J) = Xmax
            YMIN1(J) = Ymin
            YMAX1(J) = Ymax
          ENDDO
C
C Specify number of plots to be overlaid
C
          NPLOTS = Schedule_size(2)
C
C Size of 1 x axis interval
C
          XI = Xgrid
C
C Number of y axis intervals
C
          NYI = (Ymax - Ymin) / Ygrid
C
C Title
C
          CALL Clear(Title)
          IF (Dimension .LE. 2) THEN
            TITLE = 'TABLE : '//Function(1:Strln(Function))
          ELSEIF (Dimension .EQ. 3) THEN
            WRITE (Output1,'(F10.2)') Brkpt_schedule(3,K)
            TITLE = 'TABLE : '//Function(1:Strln(Function))//'       '//
     &              'for '//Variable_name(1,3)//' = '//Output1
          ELSEIF (Dimension .EQ. 4) THEN
            WRITE (Output1,'(F10.2)') Brkpt_schedule(3,K)
            WRITE (Output2,'(F10.2)') Brkpt_schedule(4,L)
            TITLE = 'TABLE : '//Function(1:Strln(Function))//'       '//
     &              'for '//Variable_name(1,3)//' = '//Output1//'   '//
     &              'and '//Variable_name(1,4)//' = '//Output2
          ENDIF
C
C Reference
C
          CALL Clear(Tmp)
          Tmp = Ref(1:68)
          Ref = 'REFERENCE : '//Tmp(1:68)
C
C Array of x and y variable descriptor strings
C
          DO I = 1,NPLOTS
            XAXIS(I) = Variable_name(1,1)(1:10)
            YAXIS(I) = Variable_name(1,0)(1:10)
          ENDDO
C
C Size in inches of 10 x and y axis intervals
C
          XINCH = 10*Xgrid*Xlen / ((Xmax-Xmin)*2.54)
          YINCH = 10*Ylen / (2.54*NYI)
C
C Call PLOTTER subroutine
C
          CALL PLOTTER(X,Y,N,NPLOTS,PLOTS,XI,NYI,XMIN1,XMAX1,YMIN1,
     &                 YMAX1,TITLE,REF,GRID,INTERP,XAXIS,YAXIS,XINCH,
     &                 YINCH,CIRCLE)
C
        ENDDO
      ENDDO
      RETURN
      END
C
C===========================================================================
C The function SearchList searches a "List" of words of size "ListSize" for
C one that matches a "Word" of length "Wordlen". "Word" may be abbreviated.
C In that case, if an unambiguous match is found, the entire word in placed
C in "Word" and SearchList is set to .TRUE.. If a match is not found or
C more than one match is found, SearchList is set to .FALSE..
C===========================================================================
C
      FUNCTION SearchList(Word,Wordlen,List,ListSize)
      IMPLICIT NONE
      INCLUDE 'inout.inc'
      LOGICAL SearchList
      INTEGER Wordlen,ListSize
      CHARACTER Word*(*),List(ListSize)*(*)
      INTEGER I,Word_code,Count,Strln,Word_len
      CALL Upper(Word)
      Word_code = 0
      Word_len = Strln(Word)
      IF (Word_len .GT. Wordlen) THEN
        Word_code = 0
        CALL Mess(' Error in SearchList: Command qualifier too long')
        Error = .TRUE.
      ELSE
        Count = 0
        DO I = 1,ListSize
          IF (Word(1:Word_len) .EQ. List(I)(1:Word_len)) THEN
            Count = Count + 1
            Word_code = I
          ENDIF
        ENDDO
C
C No matches were found
C
        IF (Count .EQ. 0) THEN
          Word_code = 0
          SearchList = .FALSE.
C
C More than 1 match was found
C
        ELSEIF (Count .GT. 1) THEN
          Word_code = 0
          SearchList = .FALSE.
        ENDIF
      ENDIF
C
C An unambiguous match was found
C
      IF (Word_code .GT. 0) THEN
        Word = List(Word_code)(1:MIN0(LEN(Word),Wordlen))
        IF (LEN(Word) .GT. Wordlen) CALL Clear(Word(Wordlen+1:))
        SearchList = .TRUE.
C
C An unabiguous match was not found
C
      ELSE
        SearchList = .FALSE.
      ENDIF
      RETURN
      END
C
C==========================================================================
C The subroutine ShellSort performs a shell sort of a character 'List' that
C contains 'ListSize' elements with 'Nchar' characters per element. The
C sorted list is placed in 'List'.
C==========================================================================
C
      SUBROUTINE ShellSort(List,ListSize,Nchar)
      IMPLICIT NONE
      INTEGER I,J,T,H(20),S,ListSize,Nchar
      CHARACTER List(ListSize)*(*),Next*12
C
C Create the sort increments
C
      S = 1
      H(s) = 1                           ! Final sort increment
      DO WHILE (H(s) .LT. ListSize)
        S = S + 1
        H(s) = 3*H(s-1) + 1
      ENDDO
      T = S - 2                          ! Number of sort increments
C
C Sort the list
C
      DO S = T,1,-1
        DO J = H(s)+1,ListSize
          I = J - H(s)
          Next = List(J)
20        IF (Next .GE. List(I)) GOTO 10
          List(I+H(s)) = List(I)
          I = I - H(s)
          IF (I .GT. 0) GOTO 20
10        List(I+H(s)) = Next
        ENDDO
      ENDDO
      RETURN
      END
C
C===========================================================================
C The function ParseBrkpt returns the following values:
C
C 0 : all dimensions have been correctly specified
C 1 : only some dimensions have been specified but are all correct
C 2 : any or all dimensions have been specified incorrectly
C
      FUNCTION ParseBrkpt(Input,BPno)
      IMPLICIT NONE
      REAL Tolperc
      PARAMETER (TolPerc = 0.01)
      LOGICAL Getcmd,SearchList
      CHARACTER*(*) Input
      CHARACTER Dim(4)*12,Word*12
      CHARACTER Temp*3,Temp2*14,Temp3*14,Label(4)*12
      INTEGER ListSize,BPno(4),I,J,K,Ptr,Input_type,Strln,ParseBrkpt
      INTEGER ReadReal
      REAL Value,Err
      INCLUDE 'inout.inc'
      INCLUDE 'finfo.inc'
      DATA Dim/
     & 'X           ','Y           ','Z           ','T           '/
C
      DO I = 1,Dimension
        BPno(I) = 0
        Label(I) = Variable_name(1,I)
      ENDDO
      ParseBrkpt = 0
      Input_type = 0
      CALL Clear(Word)
C
C Get input format of dimension specifier
C
      DO I = 1,Dimension
        IF (Getcmd(Word,Input)) THEN
          IF (SearchList(Word,12,Dim,Dimension)) THEN
            Input_type = 1
          ELSEIF (SearchList(Word(1:12),12,Label,Dimension)) THEN
            Input_type = 2
          ELSE
            Input_type = 3
          ENDIF
C
C Look for X,Y,Z,T = breakpoint # 
C
          IF (Input_type .EQ. 1) THEN
            J = 1
            DO WHILE (Word(1:1).NE.Dim(J) .AND. J.LE.Dimension)
              J = J + 1
            ENDDO
            IF (J .LE. Dimension) THEN
              IF (Getcmd(Temp,Input)) THEN
                IF (BPno(J) .GT. 0) THEN
                  CALL Mess(' Error in ParseBrkpt: Function dimension al
     &ready specified')
                  Error = .TRUE.
                ELSE
                  READ(Temp,'(BN,I3)',ERR=995) BPno(J)
                  IF (BPno(J) .LT. 1. OR. 
     &                BPno(J) .GT. Schedule_size(J)) THEN
                    ParseBrkpt = 2
                    CALL Mess(' Error in ParseBrkpt: Invalid breakpoint 
     &number')
                    Error = .TRUE.
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ENDIF
              ELSE
                CALL Mess(' Error in ParseBrkpt: Missing breakpoint numb
     &er')
                Error = .TRUE.
                ParseBrkpt = 2
                CALL Clear(Input)
                RETURN
              ENDIF
            ELSE
              CALL Mess(' Error in ParseBrkpt: Invalid dimension specifi
     &er')
              Error = .TRUE.
              ParseBrkpt = 2
              CALL Clear(Input)
              RETURN
            ENDIF
C
C Look for <variable name> = <breakpoint value>
C
          ELSEIF (Input_type .EQ. 2) THEN
            J = 1
            DO WHILE (Word(1:12).NE.Label(J) .AND. J.LE.Dimension)
              J = J + 1
            ENDDO
            IF (J .LE. Dimension) THEN
              IF (Getcmd(Temp2,Input)) THEN
                IF (BPno(J) .GT. 0) THEN
                  CALL Mess(' Error in ParseBrkpt: Function dimension al
     &ready specified')
                  Error = .TRUE.
                ELSE
                  IF (ReadReal(Temp2,Value) .EQ. 0) THEN
                    CALL Mess(' Error in ParseBrkpt: Cannot decode break
     &point number')
                    Error = .TRUE.
                    ParseBrkpt = 2
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                  DO K = 1,Schedule_size(J)
                    Err = (Brkpt_schedule(J,K) - Value)
                    IF (Brkpt_schedule(J,K) .NE. 0.0) THEN
                      Err = Err / Brkpt_schedule(J,K)
                    ENDIF
                    IF (ABS(Err)*100 .LT. TolPerc) BPno(J) = K
                  ENDDO
                  IF (BPno(J) .LT. 1. OR. 
     &                BPno(J) .GT. Schedule_size(J)) THEN
                    CALL Mess(' Error in ParseBrkpt: Invalid breakpoint 
     &value')
                    Error = .TRUE.
                    ParseBrkpt = 2
                    CALL Clear(Input)
                    RETURN
                  ENDIF
                ENDIF
              ELSE
                ParseBrkpt = 2
                CALL Mess(' Error in ParseBrkpt: Missing breakpoint valu
     &e')
                Error = .TRUE.
                CALL Clear(Input)
                RETURN
              ENDIF
            ELSE
              ParseBrkpt = 2
              CALL Mess(' Error in ParseBrkpt: Invalid dimension specifi
     &er')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
C
C Expecting dimension specifier
C
          ELSEIF (Input_type .EQ. 3) THEN
            READ(Word,'(BN,I3)',ERR=995) BPno(I)
            IF (BPno(I) .LT. 1. OR. 
     &        BPno(I) .GT. Schedule_size(I)) THEN
              CALL Mess(' Error in ParseBrkpt: Invalid breakpoint value'
     &)
              Error = .TRUE.
              ParseBrkpt = 2
              CALL Clear(Input)
              RETURN
            ENDIF
          ENDIF
        ENDIF
      ENDDO
C
C Check if all dimensions have been specified
C
      IF (ParseBrkpt .EQ. 0) THEN
        DO I = 1,Dimension
          IF (BPno(I) .EQ. 0) ParseBrkpt = 1
        ENDDO
      ENDIF
C
C If dimension is < 4, set all other dimensions to 1
C
      IF (Dimension .LT. 4) THEN
        DO I = Dimension+1,4
          BPno(I) = 1
        ENDDO
      ENDIF
      RETURN
C
995   CALL Mess(' Error in ParseBrkpt: Cannot decode breakpoint number')
      Error = .TRUE.
      ParseBrkpt = 2
      CALL Clear(Input)
      RETURN
      END
C
C===========================================================================
C The subroutine DelBreakpt deletes a breakpoint value from a function. If
C the array BPno(1..4) contains a value of 0 for the dimension, then that
C breakpoint schedule is not changed. A value of 1 or greater indicates
C the breakpoint index to be deleted. BPno is set to 1 for unused
C dimensions.
C===========================================================================
C
      SUBROUTINE DelBreakpt(BPno,ios)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      INTEGER I,N,P(4),P1,P2,P3,P4,K(4),K1,K2,K3,K4
      INTEGER BPno(4),Ptr,F,X,Y,Z,T,Size(4),ios
      EQUIVALENCE (P1,P(1)),(P2,P(2)),(P3,P(3)),(P4,P(4))
      EQUIVALENCE (K1,K(1)),(K2,K(2)),(K3,K(3)),(K4,K(4))
      F(X,Y,Z,T) = (T-1) * Size(3)*Size(2)*Size(1)
     &           + (Z-1) * Size(2)*Size(1)
     &           + (Y-1) * Size(1)
     &           + X
C
C A breakpoint can be deleted from each schedule in one command
C
      DO N = 1,Dimension
C
C Initialize schedule sizes and breakpoint numbers
C
        IF (BPno(N) .GT. 0) THEN
          DO I = 1,4
            P(I) = 0
            Size(I) = Schedule_size(I)
          ENDDO
C
C Cannot have less than 2 breakpoints
C
          IF (BPno(N).GT.0 .AND. Schedule_size(N).EQ.2) THEN
            CALL Mess(' Error in DelBreakpt: Cannot delete to less than 
     &2 breakpoints')
            Error = .TRUE.
            ios = 1
            RETURN
          ENDIF
C
C Modify the breakpoint schedule
C
          P(N) = 1
          DO I = BPno(N),Schedule_size(N) - 1
            Brkpt_schedule(N,I) = Brkpt_schedule(N,I+1)
          ENDDO
          Schedule_size(N) = Schedule_size(N) - 1
C
C Modify the function values
C
          DO K4 = 1,Schedule_size(4)
            DO K3 = 1,Schedule_size(3)
              DO K2 = 1,Schedule_size(2)
                DO K1 = 1,Schedule_size(1)
                  IF (K(N) .LT. BPno(N)) THEN
                    Ftemp(Ptr(K1,K2,K3,K4)) = Fvalues(F(K1,K2,K3,K4))
                  ELSE
                    Ftemp(Ptr(K1,K2,K3,K4)) = 
     &                    Fvalues(F(K1+P1,K2+P2,K3+P3,K4+P4))
                  ENDIF
                ENDDO
              ENDDO
            ENDDO
          ENDDO
C
C Recalculate the total number of function values
C
          Nvalues = Schedule_size(1) * Schedule_size(2)
     &            * Schedule_size(3) * Schedule_size(4)
C
C Copy the function values from the temporary array
C
          DO I = 1,Nvalues
            Fvalues(I) = Ftemp(I)
          ENDDO
        ENDIF
      ENDDO
C
      RETURN
      END
C
C==========================================================================
C The subroutine AddBreakpt adds a function breakpoint at the specified
C value and interpolates for the function value. If the EXTRAPOLATE
C option is specified, a breakpoint added at the beginning or end of a
C function causes the function value to be extrapolated. Otherwise, the
C last (or first) value of the function is used. A status value of 0
C indicates success.
C==========================================================================
C
      SUBROUTINE AddBreakpt(Dim,Value,Extrapolate,ios)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      INTEGER I,X,Y,Z,T,K1,K2,K3,K4,K(4),P1,P2,P3,P4,P(4)
      INTEGER Start(4),Ptr,Dim,Total,F,Size(4),ios
      LOGICAL Extrapolate
      REAL Value
      EQUIVALENCE (P1,P(1)),(P2,P(2)),(P3,P(3)),(P4,P(4))
      EQUIVALENCE (K1,K(1)),(K2,K(2)),(K3,K(3)),(K4,K(4))
      F(X,Y,Z,T) = (T-1) * Size(3)*Size(2)*Size(1)
     &           + (Z-1) * Size(2)*Size(1)
     &           + (Y-1) * Size(1)
     &           + X
C
      ios = 0
C
C Check that schedule size is not exceeded
C
      IF (Schedule_size(Dim)+1 .GT. Maxbp) THEN
        ios = 1
        CALL Mess(' Error in AddBreakpt: Maximum breakpoint schedule siz
     &e exceeded')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Assign old schedule sizes
C
      DO I = 1,4
        Size(I) = Schedule_size(I)
      ENDDO
C
C Check that the total function size is not exceeded
C
      Total = 1
      DO I = 1,Dimension
        IF (Dim .EQ. I) THEN
          Total = Total * (Schedule_size(I)+1)
        ELSE
          Total = Total * Schedule_size(I)
        ENDIF
      ENDDO
      IF (Total .GT. Maxval) THEN
        ios = 1
        CALL Mess(' Error in AddBreakpt: Function size limit exceeded')
        Error = .TRUE.
        RETURN
      ELSE
        Nvalues = Total
      ENDIF
C
C Get position in breakpoint schedule
C
      DO I = 1,4
        Start(I) = 1
        P(I) = 0
      ENDDO
      I = 1
      DO WHILE (Value .GT. Brkpt_schedule(Dim,I) .AND.
     &  I .LE. Schedule_size(Dim))
        I = I + 1
      ENDDO
      Start(Dim) = I
      P(Dim) = 1
C
C Shift the breakpoint schedule
C
      Schedule_size(Dim) = Schedule_size(Dim) + 1
      DO I = Schedule_size(Dim),1,-1
        IF (I .EQ. Start(Dim)) THEN
          Brkpt_schedule(Dim,Start(Dim)) = Value
        ELSEIF (I .GT. Start(Dim)) THEN
          Brkpt_schedule(Dim,I) = Brkpt_schedule(Dim,I-1)
        ELSEIF (I .LT. Start(Dim)) THEN
          Brkpt_schedule(Dim,I) = Brkpt_schedule(Dim,I)
        ENDIF
      ENDDO
C
C Add a value to the breakpoint schedule
C
      DO K4 = 1,Schedule_size(4)
        DO K3 = 1,Schedule_size(3)
          DO K2 = 1,Schedule_size(2)
            DO K1 = 1,Schedule_size(1)
              I = K(Dim)
              IF (I .LT. Start(Dim)) THEN
                Ftemp(Ptr(K1,K2,K3,K4)) = Fvalues(F(K1,K2,K3,K4))
              ELSEIF (I .GT. Start(Dim)) THEN
                Ftemp(Ptr(K1,K2,K3,K4)) = 
     &             Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4))
              ELSEIF (I .EQ. Start(Dim)) THEN
                IF (I .EQ. 1) THEN
                  IF (Extrapolate) THEN
                    Ftemp(Ptr(K1,K2,K3,K4)) =
     &                 (Fvalues(F(K1,K2,K3,K4))
     &               -  Fvalues(F(K1+P1,K2+P2,K3+P3,K4+P4)))
     &               / (Brkpt_schedule(Dim,2) - Brkpt_schedule(Dim,3))
     &               * (Brkpt_schedule(Dim,1) - Brkpt_schedule(Dim,2))
     &               + Fvalues(F(K1,K2,K3,K4))
                  ELSE
                    Ftemp(Ptr(K1,K2,K3,K4)) = Fvalues(F(K1,K2,K3,K4))
                  ENDIF
                ELSEIF (I .EQ. Schedule_size(Dim)) THEN
                  IF (Extrapolate) THEN
                    Ftemp(Ptr(K1,K2,K3,K4)) =
     &                (Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4))
     &              -  Fvalues(F(K1-2*P1,K2-2*P2,K3-2*P3,K4-2*P4)))
     &              /(Brkpt_schedule(Dim,I-1) - Brkpt_schedule(Dim,I-2))
     &              *(Brkpt_schedule(Dim,I) - Brkpt_schedule(Dim,I-1))
     &              + Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4))
                  ELSE
                    Ftemp(Ptr(K1,K2,K3,K4)) = 
     &                  Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4))
                  ENDIF
                ELSE
                  Ftemp(Ptr(K1,K2,K3,K4)) = (Fvalues(F(K1,K2,K3,K4))
     &            -  Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4)))
     &            /(Brkpt_schedule(Dim,I+1)-Brkpt_schedule(Dim,I-1))
     &            *(Brkpt_schedule(Dim,I)-Brkpt_schedule(Dim,I-1))
     &            + Fvalues(F(K1-P1,K2-P2,K3-P3,K4-P4))
                ENDIF
              ENDIF
            ENDDO
          ENDDO
        ENDDO
      ENDDO
C
C Copy the new function values
C
      DO I = 1,Nvalues
        Fvalues(I) = Ftemp(I)
      ENDDO
C
      RETURN
      END
C
C
      SUBROUTINE MergeFunc(Input)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input
      INTEGER Ntablemax
      PARAMETER (Ntablemax = 50)
      CHARACTER Table*12,MergeTable*12,Variable*12,Word*12
      CHARACTER*12 Keywords(2)/'VARIABLE    ','NAME        '/
      CHARACTER Merge_Variable(Maxmult,0:4)*12
      INTEGER Ntables,TableList(Ntablemax),Index,I,J,K,L,Merge_Dim
      INTEGER Merge_Index,Merge_Mult,TotalValues,Nbrkpts,ListSize(4)
      INTEGER Merge_Size(4),BreakptList(4,Maxbp),Strln,ReadReal
      REAL Breakpoints(Ntablemax),Value,Merge_Schedule(4,Maxbp)
      LOGICAL Getcmd,FindFunc,Once,SearchList
C
      Ntables = 0
C
C Get the list of tables to merge
C
      DO WHILE (Getcmd(Table,Input))
        IF (SearchList(Table,12,Keywords,2)) THEN
          IF (Table .EQ. 'NAME') GOTO 10
        ENDIF
        IF (FindFunc(Table,Index)) THEN
          Ntables = Ntables + 1
          TableList(Ntables) = Index
        ELSE
          CALL Mess(' Error in MergeFunc: Table name '//
     &Table(1:Strln(Table))//' not found')
          Error = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
      ENDDO
C
C Check that at least 2 tables were specified
C
      IF (Ntables .LE. 1) THEN
        CALL Mess(' Error in MergeFunc: At least 2 tables must be specif
     &ied')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C The keyword NAME was not found
C
      CALL Mess(' Error in MergeFunc: Missing keyword NAME')
      Error = .TRUE.
      RETURN
C
C Get the merged table name
C
10    CONTINUE
      IF (.NOT. Getcmd(MergeTable,Input)) THEN
        CALL Mess(' Error in MergeFunc: New table name not specified')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Check that the merged table name doesn't already exist
C
      IF (FindFunc(MergeTable,Index)) THEN
        CALL Mess(' Error in MergeFunc: Name for merged table already ex
     &ists')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Get the variable name for the new dimension
C
      IF (.NOT. Getcmd(Variable,Input)) THEN
        CALL Mess(' Error in MergeFunc: Missing keyword VARIABLE')
        Error = .TRUE.
        RETURN
      ELSE
        IF (SearchList(Variable,12,Keywords,2)) THEN
          IF (Variable .EQ. 'VARIABLE') THEN
            IF (Getcmd(Variable,Input)) THEN
              Nbrkpts = 0
              DO WHILE (Getcmd(Word,Input))
                IF (ReadReal(Word,Value) .NE. 0) THEN
                  Nbrkpts = Nbrkpts + 1
                  Breakpoints(Nbrkpts) = Value
                  IF (Nbrkpts .GT. 1) THEN
                    IF (Breakpoints(Nbrkpts).LT.Breakpoints(Nbrkpts-1))
     &                THEN
                      CALL Mess(' Error in Mergefunc: Breakpoints are no
     &t in ascending order')
                      Error = .TRUE.
                      CALL Clear(Input)
                      RETURN
                    ENDIF
                  ENDIF
                ELSE
                  CALL Mess(' Error in Mergefunc: Cannot decode breakpoi
     &nt value')
                  Error = .TRUE.
                  CALL Clear(Input)
                  RETURN
                ENDIF
              ENDDO
            ELSE
              CALL Mess(' Error in Mergefunc: Missing variable name')
              Error = .TRUE.
              RETURN
            ENDIF
          ELSE
            CALL Mess(' Error in Mergefunc: Missing keyword VARIABLE')
            Error = .TRUE.
            RETURN
          ENDIF
        ELSE
          CALL Mess(' Error in Mergefunc: Missing keyword VARIABLE')
          Error = .TRUE.
          RETURN
        ENDIF
      ENDIF
C
C Check that the number of tables equals the number of breakpoints
C
      IF (Nbrkpts .EQ. 0) THEN
        CALL Mess(' Error in MergeFunc: Missing breakpoint schedule to m
     &erge onto')
        Error = .TRUE.
        RETURN
      ELSEIF (Nbrkpts .EQ. 1) THEN
        CALL Mess(' Error in MergeFunc: Breakpoint schedule must include
     & at least 2 points')
        Error = .TRUE.
        RETURN
      ELSEIF (Nbrkpts .NE. Ntables) THEN
        CALL Mess(' Error in MergeFunc: Number of tables does not equal 
     &the number of breakpoints')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Save the previous function if it was modified
C
      IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
C
C Extract the first table and then use it to build the merged function.
C Check that the dimension is not 4 since we cannot create functions
C with dimension greater than 4
C
      Findex = TableList(1)
      CALL LoadFuncInfo(.TRUE.)
      IF (Dimension .EQ. 4) THEN
        CALL Mess(' Error in MergeFunc: Cannot create tables with dimens
     &ion greater than 4')
        Error = .TRUE.
        RETURN
      ENDIF
      DO I = 1,4
        ListSize(I) = Schedule_size(I)
        DO L = 1,Schedule_size(I)
          BreakptList(I,L) = L
        ENDDO
      ENDDO
      CALL ExtractFunc(ListSize,BreakptList,MergeTable)
      CALL SaveFunc
C
C Save the function index, schedule sizes and variable names for the
C merged function to compare with the others
C
      Merge_Index = Findex
      Merge_Dim = Dimension
      Merge_Mult = Mult
      TotalValues = Nvalues
      DO J = 1,Mult
        DO I = 0,4
          Merge_variable(J,I) = Variable_name(J,I)
        ENDDO
      ENDDO
      DO I = 1,4
        Merge_size(I) = Schedule_size(I)
        DO J = 1,Merge_size(I)
          Merge_Schedule(I,J) = Brkpt_schedule(I,J)
        ENDDO
      ENDDO
C
C Start merging the tables. The merged function values are stored in
C the Ftemp array.
C
      DO I = 2,Ntables
        Findex = TableList(I)
        CALL LoadFuncInfo(.TRUE.)
C
C Check that the multiplicity is consistent
C
        IF (Mult .NE. Merge_Mult) THEN
          CALL Mess(' Warning: Tables to be merged do not have the same 
     &multiplicity')
        ENDIF
C
C Check that the dimension is consistent
C
        IF (Dimension .NE. Merge_Dim) THEN
          CALL Mess(' Error in MergeFunc: Tables to be merged do not hav
     &e the same dimension')
          Error = .TRUE.
          Fdelete(Merge_Index) = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Check that the schedule sizes are consistent
C
        DO J = 1,Merge_Dim
          IF (Schedule_size(J) .NE. Merge_size(J)) THEN
            CALL Mess(' Error in Mergefunc: Tables to be merged do not h
     &ave the same breakpoint schedules')
            Error = .TRUE.
            Fdelete(Merge_Index) = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
C
C Check that the breakpoint schedules are consistent
C
          DO K = 1,Schedule_size(J)
            IF (Brkpt_Schedule(J,K) .NE. Merge_Schedule(J,K)) THEN
              CALL Mess(' Error in MergeFunc: Tables to be merged do not
     & have the same breakpoint schedules')
              Error = .TRUE.
              Fdelete(Merge_Index) = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ENDDO
        ENDDO
C
C Check that the variable names are consistent
C
        Once = .TRUE.
        DO J = 1,Merge_Mult
          DO K = 1,Merge_Dim
            IF (Once) THEN
              IF (Variable_name(J,K) .NE. Merge_Variable(J,K)) THEN
                CALL Mess(' Warning: Tables to be merged do not have the
     & same independent variable names')
              ENDIF
              Once = .FALSE.
            ENDIF
          ENDDO
        ENDDO
C
        TotalValues = TotalValues + Nvalues
        IF (TotalValues .GT. Maxval) THEN
          CALL Mess(' Error in MergeFunc: Total number of function value
     &s would exceed limits')
          Error = .TRUE.
          Fdelete(Merge_Index) = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Copy function values to temporary array and add them to merge function
C
        DO J = 1,Nvalues
          Ftemp(J) = Fvalues(J)
        ENDDO
C
        Findex = Merge_index
        CALL LoadFuncInfo(.TRUE.)
        Dimension = Merge_Dim + 1
        Schedule_size(Dimension) = Schedule_Size(Dimension) + 1
        Schedule_name(Dimension) = 'MERGED'
        Brkpt_Schedule(Dimension,1) = Breakpoints(1)
        Brkpt_Schedule(Dimension,I) = Breakpoints(I)
        DO J = 1,Merge_mult
          Variable_name(J,Dimension) = Variable
        ENDDO
        Variable_name(1,0) = MergeTable
        DO J = Nvalues+1,TotalValues
          Fvalues(J) = Ftemp(J-Nvalues)
        ENDDO
        Nvalues = TotalValues
        CALL SaveFunc
      ENDDO 
C
      RETURN
      END
C
C===========================================================================
C The subroutine SetSchedule forces the function to use the breakpoint
C schedule "Schedule" for the dimension index "Dim" of size "Size".
C===========================================================================
C
      SUBROUTINE SetSchedule(Dim,Size,Schedule,Extrapolate,ios)
      IMPLICIT NONE
      INTEGER Dim,Size
      REAL    Schedule(Size)
      LOGICAL Extrapolate
      INTEGER ios
      INTEGER I,J,BPno(4)
      LOGICAL Add,Delete
      INCLUDE 'finfo.inc'
C
C Check all current breakpoints to see if they exist. If not, add that
C breakpoint.
C
      DO I = 1,Size
        Add = .TRUE.
        DO J = 1,Schedule_size(Dim)
          IF (Brkpt_schedule(Dim,J) .EQ. Schedule(I)) Add = .FALSE.
        ENDDO
        IF (Add) CALL AddBreakpt(Dim,Schedule(I),Extrapolate,ios)
        IF (ios .GT. 0) RETURN
      ENDDO
C
C To delete a breakpoint, BPno for that particular dimension must be
C set to the breakpoint index that is to be deleted. For example, to
C delete the 3rd Y breakpoint, BPno(2) = 3 (since the Y schedule is
C array index (2). Unused dimensions must have BPno set to 1.
C
      DO I = 1,4
        IF (I .GT. Dimension) THEN
          BPno(I) = 1
        ELSE
          BPno(I) = 0
        ENDIF
      ENDDO
C
C Delete non-required breakpoints. As breakpoints are deleted, then
C breakpoints that used to follow the deleted breakpoint take its
C place. Therefore, I is only incremented when a breakpoint is not
C deleted.
C
      I = 1
      DO WHILE (I .LE. Schedule_size(Dim))
        Delete = .TRUE.
        DO J = 1,Size
          IF (Brkpt_schedule(Dim,I) .EQ. Schedule(J)) Delete = .FALSE.
        ENDDO
        IF (Delete) THEN
          BPno(Dim) = I
          CALL DelBreakpt(BPno,ios)
          IF (ios .GT. 0) RETURN
        ELSE
          I = I + 1
        ENDIF
      ENDDO
C
      RETURN
      END
C
C============================================================================
C The subroutine OffsetFunc 
C
C
      SUBROUTINE OffsetFunc(Input)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input,Word*12,Input2*80
      INTEGER I,BreakptList(4,Maxbp),Dim,Index,ReadSchedule
      INTEGER L,istat,List(Maxbp),Size,ListSize(4),Ptr,BPno(4)
      INTEGER J1,J2,J3,J4,J(4),K1,K2,K3,K4,K(4),Parsebrkpt
      REAL Value,Sign
      LOGICAL Getcmd,InList
      EQUIVALENCE (J1,J(1)),(J2,J(2)),(J3,J(3)),(J4,J(4))
      EQUIVALENCE (K1,K(1)),(K2,K(2)),(K3,K(3)),(K4,K(4))
C
      DO I = 1,4
        ListSize(I) = 0
      ENDDO
C
C Process an input of the form ".modify function offset valpha=1,5,10
C vbeta=-10,0,20 -vflaps=20". In this case the user has specified for
C two dimensions at which breakpoints the curves should be offset.
C
      DO WHILE (Getcmd(Word,Input))
        istat = 0
        DO WHILE (istat .EQ. 0)
          istat = ReadSchedule(Word,Input,Dim,List,Size)
          IF (istat .EQ. 0) THEN
            ListSize(Dim) = Size
            DO I = 1,Size
              BreakptList(Dim,I) = List(I)
            ENDDO
          ENDIF
        ENDDO
C
C Error handling
C
        IF (istat .GT. 1) THEN
          IF (istat .EQ. 3) THEN
            CALL Mess(' Error in OffsetFunc: Missing "+" or "-" when spe
     &cifying offset breakpoint(s)')
            Error = .TRUE.
          ELSEIF (istat .EQ. 4) THEN
            CALL Mess(' Error in OffsetFunc: Missing breakpoint value to
     & use to offset')
            Error = .TRUE.
          ELSE
            CALL Mess(' Error in OffsetFunc: Cannot decode breakpoint va
     &lue or breakpoint non existent')
            Error = .TRUE.
          ENDIF
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Process an input of the form ".modify function offset +vflaps 50".
C In this case the user has not specified the breakpoints from which
C the flaps 50 value is to be offset (added). This is taken to mean
C that it should be offset to every breakpoint value of every dimension.
C
        IF (istat .EQ. 1) THEN
          IF (Word(1:1).EQ.'+' .OR. Word(1:1).EQ.'-') THEN
            IF (Word(1:1) .EQ. '+') Sign =  1.0
            IF (Word(1:1) .EQ. '-') Sign = -1.0
            Input2 = Word(2:)//Input
            Input = Input2
            istat = Parsebrkpt(Input,BPno)
            IF (istat .GE. 2) THEN
              CALL Mess(' Error in OffsetFunc: Incorrect dimension speci
     &fied for offset curve')
              Error = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ELSE
            CALL Mess(' Error in OffsetFunc: Incorrect variable name')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ENDIF
      ENDDO
C
C Use all breakpoints for schedules which were not specified
C
      DO I = 1,4
        IF (ListSize(I) .EQ. 0) THEN
          ListSize(I) = Schedule_size(I)
          DO L = 1,Schedule_size(I)
            BreakptList(I,L) = L
          ENDDO
        ENDIF
      ENDDO
C
C Offset the function values if that particualr index has been specified
C
      DO K4 = 1,Schedule_size(4)
        DO K3 = 1,Schedule_size(3)
          DO K2 = 1,Schedule_size(2)
            DO K1 = 1,Schedule_size(1)
              IF (InList(BreakptList,ListSize(4),4,K4) .AND.
     &            InList(BreakptList,ListSize(3),3,K3) .AND.
     &            InList(BreakptList,ListSize(2),2,K2) .AND.
     &            InList(BreakptList,ListSize(1),1,K1)) THEN
                DO L = 1,4
                  J(L) = K(L)
                  IF (BPno(L) .NE. 0) J(L) = BPno(L)
                ENDDO
                Ftemp(Ptr(K1,K2,K3,K4)) = Fvalues(Ptr(K1,K2,K3,K4))
     &                             + Sign*Fvalues(Ptr(J1,J2,J3,J4))
              ELSE
                Ftemp(Ptr(K1,K2,K3,K4)) = Fvalues(Ptr(K1,K2,K3,K4))
              ENDIF
            ENDDO
          ENDDO
        ENDDO
      ENDDO
C
C Copy temporary function values
C
      DO I = 1,Nvalues
        Fvalues(I) = Ftemp(I)
      ENDDO
C
      Funit(Findex) = uScratch
C
      RETURN
      END
C
      FUNCTION InSchedule(Dim,Value,Index)
      IMPLICIT NONE
      LOGICAL InSchedule
      INTEGER I,Dim,Index
      REAL Value,Err
      INCLUDE 'finfo.inc'
      REAL TolPerc
      PARAMETER (TolPerc = 0.1)
      InSchedule = .FALSE.
      DO I = 1,Schedule_size(Dim)
        Err = (Brkpt_schedule(Dim,I) - Value)
        IF (Brkpt_schedule(Dim,I) .NE. 0.0) THEN
          Err = Err / Brkpt_schedule(Dim,I)
        ENDIF
        IF (ABS(Err)*100 .LT. TolPerc) THEN
          InSchedule = .TRUE.
          Index = I
          RETURN
        ENDIF
      ENDDO
      RETURN
      END
C
C
C
      FUNCTION InVariable(Word,Dim)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      CHARACTER*(*) Word
      INTEGER Dim,I
      LOGICAL InVariable
      InVariable = .FALSE.
      CALL Upper(Word)
      I = 1
      DO WHILE (Word.NE.Variable_name(1,I) .AND. I.LE.Dimension)
        I = I + 1
      ENDDO
      IF (I .LE. Dimension) THEN
        InVariable = .TRUE.
        Dim = I
      ENDIF
      RETURN
      END
C
C===========================================================================
C The function ReadSchedule expects the name of a dimension variable in
C the parameter "Word". If Word is a valid variable name, then it attempts
C to read and decode a series of numbers which represent breakpoint values.
C Once it encounters a number it cannot decode, it returns with the value 0
C (this means that the number it tried to decode may actually be the name
C of the next variable). If it correctly decodes a number but it doesn't
C match any values in that breakpoint schedule, it returns a value of 2.
C If the variable name is not followed by any breakpoint values, it returns
C a value of 3. If the variable name does not exist, it returns a value of 1.
C Dim is the dimension index of the variable whose breakpoints have been
C specified; List is an array containing the index of each breakpoint that
C has been specified; Size is the length of the List array.
C===========================================================================
C
      FUNCTION ReadSchedule(Word,Input,Dim,List,Size)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INTEGER I,Dim,List(Maxbp),Size,ReadSchedule,Index,ReadReal
      CHARACTER Word*(*),Input*(*)
      LOGICAL InVariable,InSchedule,Getcmd
      REAL Value
      ReadSchedule = 0
      Size = 0
C
      IF (Word .EQ. ' ') THEN
        ReadSchedule = 4
        RETURN
      ENDIF
C
      IF (InVariable(Word,Dim)) THEN
        DO WHILE (.TRUE.)
          IF (Getcmd(Word,Input)) THEN
            IF (ReadReal(Word,Value) .NE. 0) THEN
              IF (InSchedule(Dim,Value,Index)) THEN
                Size = Size + 1
                List(Size) = Index
                CALL Clear(Word)
              ELSE
                ReadSchedule = 2    ! Not a valid breakpoint
                RETURN
              ENDIF
            ELSE
              IF (Size .EQ. 0) ReadSchedule = 3
              RETURN
            ENDIF
          ELSE
            IF (Size .EQ. 0) ReadSchedule = 3
            RETURN
          ENDIF
        ENDDO
      ELSE
        ReadSchedule = 1            ! Wrong variable name
        RETURN
      ENDIF
C
      RETURN
      END
C
C
C
      FUNCTION InList(BreakptList,ListSize,Dim,Index)
      IMPLICIT NONE
      INTEGER I,ListSize,BreakptList(4,*),Dim,Index
      LOGICAL InList
      InList = .FALSE.
      DO I = 1,ListSize
        IF (Index .EQ. BreakptList(Dim,I)) THEN
          InList = .TRUE.
          RETURN
        ENDIF
      ENDDO
      RETURN
      END
C
C
C
      SUBROUTINE ExtractFunc(ListSize,BreakptList,Table)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER Table*(*)
      INTEGER I,M,BreakptList(4,Maxbp),Index,L,ListSize(4),Ptr,Dim
      INTEGER J1,J2,J3,J4,J(4),K1,K2,K3,K4,K(4),F,X,Y,Z,T
      LOGICAL Getcmd,InSchedule,InVariable,InList
      EQUIVALENCE (J1,J(1)),(J2,J(2)),(J3,J(3)),(J4,J(4))
      EQUIVALENCE (K1,K(1)),(K2,K(2)),(K3,K(3)),(K4,K(4))
      F(X,Y,Z,T) = (T-1) * ListSize(3)*ListSize(2)*ListSize(1)
     &           + (Z-1) * ListSize(2)*ListSize(1)
     &           + (Y-1) * ListSize(1)
     &           + X
C
      DO I = 1,4
        J(I) = 0
      ENDDO
C
C Create a function with only the specified values
C
      DO K4 = 1,Schedule_size(4)
        IF (InList(BreakptList,ListSize(4),4,K4)) THEN
          IF (J4 .EQ. ListSize(4)) J4 = 0
          J4 = J4 + 1
        ENDIF
        DO K3 = 1,Schedule_size(3)
          IF (InList(BreakptList,ListSize(3),3,K3)) THEN
            IF (J3 .EQ. ListSize(3)) J3 = 0
            J3 = J3 + 1
          ENDIF
          DO K2 = 1,Schedule_size(2)
            IF (InList(BreakptList,ListSize(2),2,K2)) THEN
              IF (J2 .EQ. ListSize(2)) J2 = 0
              J2 = J2 + 1
            ENDIF
            DO K1 = 1,Schedule_size(1)
              IF (InList(BreakptList,ListSize(1),1,K1)) THEN
                IF (J1 .EQ. ListSize(1)) J1 = 0
                J1 = J1 + 1
              ENDIF
              IF (InList(BreakptList,ListSize(4),4,K4) .AND.
     &            InList(BreakptList,ListSize(3),3,K3) .AND.
     &            InList(BreakptList,ListSize(2),2,K2) .AND.
     &            InList(BreakptList,ListSize(1),1,K1)) THEN
                Ftemp(F(J1,J2,J3,J4)) = Fvalues(Ptr(K1,K2,K3,K4))
              ENDIF
            ENDDO
          ENDDO
        ENDDO
      ENDDO
C
C Check that the maximum number of functions is not exceeded
C
      IF (Nfunction+1 .GT. Maxfunc) THEN
        CALL Mess(' Error in ExtractFunc: Maximum number of function tab
     &les exceeded')
        Error = .TRUE.
        RETURN
      ENDIF
C
C Copy the breakpoint schedules
C
      Dim = 0
      DO I = 1,4
        IF (ListSize(I) .GT. 1) THEN
          Dim = Dim + 1
          Schedule_size(Dim) = ListSize(I)
          Schedule_name(Dim) = Schedule_name(I)
          DO L = 1,Mult
            Variable_name(L,Dim) = Variable_name(L,I)
          ENDDO
          DO L = 1,ListSize(I)
            Brkpt_schedule(Dim,L) = Brkpt_schedule(I,BreakptList(I,L))
          ENDDO
        ENDIF
      ENDDO
      IF (Dim .LT. 4) THEN
        DO I = Dim+1,4
          CALL Clear(Schedule_name(I))
          DO M = 1,Mult
            CALL Clear(Variable_name(M,I))
          ENDDO
          Schedule_size(I) = 1
        ENDDO
      ENDIF
      Dimension = Dim
      Nfunction = Nfunction + 1
      Fname(Nfunction) = Table
      Fzone(Nfunction) = Fzone(Findex)
      Fclass(Nfunction) = Fclass(Findex)
      Funit(Nfunction) = uScratch
      Fmult(Nfunction) = Mult
      Fdim(Nfunction) = Dimension
      Findex = Nfunction
      Nvalues = Schedule_size(1) * Schedule_size(2)
     &        * Schedule_size(3) * Schedule_size(4)
C
C Copy temporary function values
C
      DO I = 1,Nvalues
        Fvalues(I) = Ftemp(I)
      ENDDO
C
C Selected function name
C
      CALL Clear(Function)
      Function = Table
      CALL SaveFunc
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE ListSched
      IMPLICIT     NONE
      INCLUDE      'finfo.inc'
      CHARACTER*12 LastVar
      CHARACTER*80 String1/' '/,String2/' '/,SchedVars(Maxvar)
      INTEGER      CurrSchedSize,I,J,K,L,M,N,Old_index,Count,Dimen
      INTEGER      Rstrln,Times,SchedSize
      LOGICAL      SchedShown(Maxfunc,4),SameSched,FirstPass
C
      IF (Select) Old_index = Findex
C
      DO I=1,Maxfunc
        DO J=1,4
          SchedShown(I,J)=.FALSE.
        ENDDO
      ENDDO
C
      String1=' Schedule name:               Size:       Variables: '
      DO M = 1,Nfunction  ! Search through every function.
        Findex=M
        CALL LoadFuncInfo(.FALSE.) ! Get all information on this function.
        Count=Dimension
        DO Dimen=1,Count
          IF (.NOT.SchedShown(M,Dimen)) THEN
            SchedSize=1
            CurrSchedSize=0
            SchedShown(M,Dimen)=.TRUE.
            CurrSchedSize=Schedule_size(Dimen)
C
            String1(17:28)=Schedule_name(Dimen)
            WRITE(String1(37:40),'(I4)') CurrSchedSize
            String1(54:65)=Variable_name(1,Dimen)
            SchedVars(SchedSize)=String1
            SchedSize = SchedSize+1
C
            IF (M+1 .LE. Nfunction) THEN
              DO Findex = M+1,Nfunction
                CALL LoadFuncInfo(.FALSE.) ! Get all information on this function.
                DO I=1,Dimension
                  IF (.NOT.SchedShown(Findex,I)) THEN
                    IF (Schedule_size(I).EQ.CurrSchedSize) THEN
                      SameSched=.TRUE.
                      IF (Schedule_name(I).NE.String1(17:28))
     &                     SameSched=.FALSE.
                      IF (SameSched) THEN
                        SchedShown(Findex,I)=.TRUE.
                        String2(54:65)=Variable_name(1,I)
                        SchedVars(SchedSize)=String2
                        SchedSize = SchedSize+1
                      ENDIF
                    ENDIF
                  ENDIF
                ENDDO
              ENDDO
            ENDIF
            CALL DisplaySched(SchedVars,SchedSize-1)
          ENDIF
          Findex=M
          CALL LoadFuncInfo(.FALSE.)     ! Get all information on this function.
        ENDDO
      ENDDO
C
      IF (Select) THEN
        Findex = Old_index
        CALL LoadFuncInfo(.FALSE.)
      ENDIF
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
      SUBROUTINE DisplaySched(SchedVars,SchedSize)
      IMPLICIT NONE
      INCLUDE      'finfo.inc'
      CHARACTER*12 LastVar
      CHARACTER*80 SchedVars(Maxvar),Output
      INTEGER      I,J,Rstrln,Times
      INTEGER      SchedSize
C
      IF (SchedSize.EQ.1) THEN
        I=Rstrln(SchedVars(1))+1
        SchedVars(1)(I:I+6)=' 1 time'
        CALL Mess(SchedVars(1))
      ELSE
        DO WHILE (SchedSize.GT.0)
          Times=0
          Output=SchedVars(1)
          LastVar=SchedVars(1)(54:65)
          I=1
          DO WHILE (I.LE.SchedSize)
            IF (LastVar.EQ.SchedVars(I)(54:65)) THEN
              Times=Times+1
              DO J=I,SchedSize-1
                SchedVars(J)=SchedVars(J+1)
              ENDDO
              SchedSize=SchedSize-1
            ELSE
              I=I+1
            ENDIF
          ENDDO
          IF (Times.EQ.1) THEN
            I=Rstrln(Output)+1
            Output(I:I+6)=' 1 time'
          ELSE
            I=Rstrln(Output)
            IF (Times.LE.9) THEN
              WRITE(Output(I+1:I+2),'(I2)') Times
              Output(I+4:I+8)='times'
            ELSE IF (Times.LE.99) THEN
              WRITE(Output(I+1:I+3),'(I3)') Times
              Output(I+5:I+9)='times'
            ELSE
              WRITE(Output(I+1:I+4),'(I4)') Times
              Output(I+6:I+10)='times'
            ENDIF
          ENDIF
          CALL Mess(Output)
        ENDDO
      ENDIF
C
      RETURN
      END
C
C============================================================================
C
C============================================================================
C
C
       FUNCTION Rstrln(String)
       INTEGER*4 Rstrln,Len
       CHARACTER*(*) STRING
C
       Rstrln = Len(STRING)
       DO WHILE (String(Rstrln:Rstrln) .EQ. ' '.AND.Rstrln.GT.1)
          Rstrln = Rstrln - 1
       ENDDO
       RETURN
       END
C
C
      SUBROUTINE EvaluateFunc(Input)
      IMPLICIT NONE
      INCLUDE 'finfo.inc'
      INCLUDE 'inout.inc'
      CHARACTER*(*) Input
      INTEGER Ntablemax
      PARAMETER (Ntablemax = 50)
      CHARACTER Table*12,EvalTable*12,Variable*12,Word*12
      CHARACTER Eval_Variable(Maxmult,0:4)*12
      INTEGER Ntables,TableList(Ntablemax),Index,I,J,K,L,Eval_Dim
      INTEGER Eval_Index,Eval_Mult,ListSize(4)
      INTEGER Eval_Size(4),BreakptList(4,Maxbp),Strln
      REAL Operation(Ntablemax),Op
      REAL Breakpoints(Ntablemax),Value,Eval_Schedule(4,Maxbp)
      LOGICAL Getcmd,FindFunc,Once,SearchList
C
      Ntables = 0
      CALL Clear(EvalTable)
C
C Get the list of tables to evaluate
C
      DO WHILE (Getcmd(Table,Input))
        IF (Table(1:1).EQ.'+' .OR. Table(1:1).EQ.'-') THEN
          IF (Table(1:1) .EQ. '+') Op = 1.0
          IF (Table(1:1) .EQ. '-') Op = -1.0
          Table = Table(2:)
          IF (FindFunc(Table,Index)) THEN
            Ntables = Ntables + 1
            TableList(Ntables) = Index
            Operation(Ntables) = Op
          ELSE
            J = Strln(Table)
            IF (J .GT. 0) THEN
              CALL Mess(' Error in EvaluateFunc: Table name '//
     &                 Table(1:J)//' not found')
            ELSE
              CALL Mess(' Error in EvaluateFunc: Missing table name')
            ENDIF
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
        ELSE
C
C Get the result table name. Check that it doesn't already exist.
C
          IF (FindFunc(Table,Index)) THEN
            CALL Mess(' Error in EvaluateFunc: Name for merged table alr
     &eady exists')
            Error = .TRUE.
            CALL Clear(Input)
            RETURN
          ELSE
            EvalTable = Table
          ENDIF
        ENDIF
      ENDDO
C
C Check that at least 2 tables were specified
C
      IF (Ntables .LE. 1) THEN
        CALL Mess(' Error in EvaluateTable: At least 2 tables must be sp
     &ecified')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Check that the result table was specified
C
      IF (EvalTable(1:1) .EQ. ' ') THEN
        CALL Mess(' Error in EvaluateFunc: Missing result table name')
        Error = .TRUE.
        CALL Clear(Input)
        RETURN
      ENDIF
C
C Save the previous function if it was modified
C
      IF (Funit(Findex) .EQ. uScratch) CALL SaveFunc
C
C Extract the first table and then use it to build the result function.
C
      Findex = TableList(1)
      CALL LoadFuncInfo(.TRUE.)
      DO I = 1,4
        ListSize(I) = Schedule_size(I)
        DO L = 1,Schedule_size(I)
          BreakptList(I,L) = L
        ENDDO
      ENDDO
      CALL ExtractFunc(ListSize,BreakptList,EvalTable)
      DO I = 1,Nvalues
        Fvalues(I) = Operation(1)*Fvalues(I)
      ENDDO
      CALL SaveFunc
C
C Save the function index, schedule sizes and variable names for the
C result function to compare with the others
C
      Eval_Index = Findex
      Eval_Dim = Dimension
      Eval_Mult = Mult
      DO J = 1,Mult
        DO I = 0,4
          Eval_variable(J,I) = Variable_name(J,I)
        ENDDO
      ENDDO
      DO I = 1,4
        Eval_size(I) = Schedule_size(I)
        DO J = 1,Eval_size(I)
          Eval_Schedule(I,J) = Brkpt_schedule(I,J)
        ENDDO
      ENDDO
C
C Start evaluating the tables. The result function values are stored in
C the Ftemp array.
C
      DO I = 2,Ntables
        Findex = TableList(I)
        CALL LoadFuncInfo(.TRUE.)
C
C Check that the multiplicity is consistent
C
        IF (Mult .NE. Eval_Mult) THEN
          CALL Mess(' Warning: Tables to be evaluated do not have the sa
     &me multiplicity')
        ENDIF
C
C Check that the dimension is consistent
C
        IF (Dimension .NE. Eval_Dim) THEN
          CALL Mess(' Error in EvaluateFunc: Tables to be evaluated do n
     &ot have the same dimension')
          Error = .TRUE.
          Fdelete(Eval_Index) = .TRUE.
          CALL Clear(Input)
          RETURN
        ENDIF
C
C Check that the schedule sizes are consistent
C
        DO J = 1,Eval_Dim
          IF (Schedule_size(J) .NE. Eval_size(J)) THEN
            CALL Mess(' Error in Evaluatefunc: Tables to be evaluated do
     & not have the same breakpoint schedules')
            Error = .TRUE.
            Fdelete(Eval_Index) = .TRUE.
            CALL Clear(Input)
            RETURN
          ENDIF
C
C Check that the breakpoint schedules are consistent
C
          DO K = 1,Schedule_size(J)
            IF (Brkpt_Schedule(J,K) .NE. Eval_Schedule(J,K)) THEN
              CALL Mess(' Error in EvaluateFunc: Tables to be evaluated 
     &do not & have the same breakpoint schedules')
              Error = .TRUE.
              Fdelete(Eval_Index) = .TRUE.
              CALL Clear(Input)
              RETURN
            ENDIF
          ENDDO
        ENDDO
C
C Check that the variable names are consistent
C
        Once = .TRUE.
        DO J = 1,Eval_Mult
          DO K = 1,Eval_Dim
            IF (Once) THEN
              IF (Variable_name(J,K) .NE. Eval_Variable(J,K)) THEN
                CALL Mess(' Warning: Tables to be evaluated do not have 
     &the same independent variable names')
              ENDIF
              Once = .FALSE.
            ENDIF
          ENDDO
        ENDDO
C
C Copy function values to temporary array and add them to merge function
C
        DO J = 1,Nvalues
          Ftemp(J) = Operation(I)*Fvalues(J)
        ENDDO
C
        Findex = Eval_index
        CALL LoadFuncInfo(.TRUE.)
        DO J = 1,Nvalues
          Fvalues(J) = Fvalues(J) + Ftemp(J)
        ENDDO
        CALL SaveFunc
      ENDDO 
C
      RETURN
      END
C
