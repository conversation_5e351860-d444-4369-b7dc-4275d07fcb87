xlf autoscale.f \
    fgu_c/fgetkey.f \
    -c -O -L/cae/lib -lcae -lm
xlc fgu_c/fgu_gr.c \
    fgu_c/fgu_lib.c \
    fgu_c/fgu_print.c \
    fgu_c/fgu_zoom.c \
    fgu_c/io.c \
    fgu_c/orient.c \
    fgu_c/ovp_scale.c \
    fgu_c/readkey.c \
    fgu_c/readreal.c \
    fgu_c/rev_functions.c \
    fgu_c/vaxgraph.c \
    fgu_c/vgs_hp.c \
    fgu_c/vgs_prt.c \
    fgu_c/vgs_ps.c \
    -c -g -L/cae/lib -lcae -lm
xlf fgu_main.f \
    fgu_comm.f \
    fgu_cmds.f \
    fgu_ibm.f \
    fgu_gr.o \
    fgu_lib.o \
    fgu_print.o \
    fgu_zoom.o \
    fgetkey.o \
    io.o \
    orient.o \
    ovp_scale.o \
    readkey.o \
    readreal.o \
    rev_functions.o \
    vaxgraph.o \
    vgs_hp.o \
    vgs_prt.o \
    vgs_ps.o \
    autoscale.o \
    -L/cae/lib -lcae -lm -qcharlen=1500 -o fgu
