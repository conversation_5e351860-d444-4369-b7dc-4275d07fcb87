      CHARACTER  HelpFile*30
C
      PARAMETER  (HelpFile = '/group/flt/util/fgu/fgu.hlp')
C
      LOGICAL
     &     Batch        ! Batch file open
     &,    Journal      ! Journal file open
     &,    Data         ! Data file open
     &,    Help         ! Help file open
     &,    Scratch      ! Scratch file open
     &,    Plot         ! Plot file open
     &,    Process      ! Processed data file open
     &,    Error        ! Processing error was encountered
     &,    StopProcess  ! Stop file processing
     &,    openW        ! Open file for write access
     &,    openR        ! Open file for read access
C
      INTEGER
     &     Screen       ! Unit number for the screen
     &,    Keyboard     ! Unit number for the keyboard
     &,    uRead        ! Unit number for read
     &,    uBatch       ! Unit number for batch file
     &,    uJournal     ! Unit number for journal file
     &,    uData        ! Unit number for FGEN data file
     &,    uHelp        ! Unit number for help file
     &,    uScratch     ! Unit number for scratch file
     &,    uPlot        ! Unit number for plot file
     &,    uProcess     ! Unit number for processed data file
C
      COMMON /InOut/ Screen,Keyboard,uRead,uBatch,uJournal,uData,uHelp,
     &               uScratch,uPlot,Batch,Journal,Data,Help,Scratch,
     &               Plot,uProcess,Process,Error,StopProcess
